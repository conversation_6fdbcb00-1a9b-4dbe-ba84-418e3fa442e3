{%- unless template.suffix == 'a-configs' and request.design_mode == false -%}

<!doctype html>
{%- liquid
  assign t_name     = request.page_type
  assign iso_code   = request.locale.iso_code
  assign use_rtl    = settings.use_rtl
  if use_rtl == '3'
    assign lis_rtl  = 'ar; dv; ha; he; ku; fa; ur; ug; ps; yi'
    assign iso_code_ck = iso_code | split: '-' | first
  else 
    assign lis_rtl = settings.list_rtl
    assign iso_code_ck = iso_code
  endif
  if use_rtl == '1'
    assign isRTL = true
  elsif lis_rtl contains iso_code_ck and use_rtl == '2' or use_rtl == '3'
    assign isRTL = true
  else
    assign isRTL = false
  endif
  assign body_img = settings.body_bg_image
  if body_img != blank and settings.general_layout == 'boxed'
    assign class_lazy = 'lazyloadt4s'
  endif -%}
{%- capture class_html -%}
t4sp-theme t4s-wrapper__{{ settings.general_layout }}{% if settings.animations_reveal_on_scroll %} hdt-reveal-in-view{% endif %} rtl_{{ isRTL }} swatch_color_style_{{ settings.swatch_color_style }} pr_border_style_{{ settings.pr_border_style }} pr_img_effect_{{ settings.pr_img_effect }} enable_eff_img1_{{ settings.enable_eff_img1 }} badge_shape_{{ settings.badge_shape }} css_for_wis_app_{{ settings.enable_css_wis }}{% if settings.use_cus_lz and settings.cus_lz %} t4s-lzcus-true{% endif %} shadow_round_img_{{ settings.enable_shadow_round_img }} t4s-header__{{ settings.header_design }} is-remove-unavai-{{ settings.variant_remove }} t4_compare_{{ settings.enable_compe }}{% if settings.type_qv == '1' %} t4s-sidebar-qv{% endif %} t4s-cart-count-{{ cart.item_count }} t4s-pr-ellipsis-{{ settings.enable_pr_ellipsis }}
{%- endcapture -%}
<html class="{{ class_html }} no-js" lang="{{ iso_code }}"{% if isRTL %} dir="rtl"{% endif %}>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, height=device-height, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="theme-color" content="{{ settings.body_bg }}">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
    
    {%- if settings.favicon != blank %}<link rel="shortcut icon" type="image/png" href="{{ settings.favicon | image_url: width: 32 }}">{% endif -%}
    {%- if settings.favicon_apple != blank %}<link rel="apple-touch-icon-precomposed" type="image/png" sizes="152x152" href="{{ settings.favicon_apple | image_url: width: 152 }}">{% endif -%}

    {%- if settings.font_source == '1' and settings.fnt_fm_sp1.system? == false or settings.fnt_fm_sp2.system? == false or settings.fnt_fm_sp3.system? == false -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endif -%}
    
    {%- if t_name != 'list-collections' -%}
    {%- capture seo_title -%}
      {%- if template == 'search' and search.performed == true -%}{{ 'search.general.heading' | t: count: search.results_count }}: {{ 'search.results_with_count_and_term' | t: terms: search.terms, count: search.results_count }}{%- elsif template == 'search.wishlist' %}{{ 'wishlist_page.meta' | t }}{%- elsif template == 'search.compare' %}{{ 'compare_page.meta' | t }}{%- else -%}{{ page_title }}{%- endif -%}
      {%- if current_tags -%}{%- assign meta_tags = current_tags | join: ', ' %} &ndash; {{ 'general.meta.tags' | t: tags: meta_tags -}}{%- endif -%}
      {%- if current_page != 1 %} &ndash; {{ 'general.meta.page' | t: page: current_page }}{%- endif -%}
      {%- assign escaped_page_title = page_title | escape -%}
      {%- unless escaped_page_title contains shop.name %} &ndash; {{ shop.name }}{%- endunless -%}
    {%- endcapture -%}
    <title>{{ seo_title | strip }}</title>
    <meta name="description" content="{{ page_description | default: shop.description | default: shop.name | escape }}">
    {%- else -%}
    <title>{{ 'list_collections.meta_title' | t | escape }}</title><meta name="description" content="{{ 'list_collections.meta_description' | t | escape }}">
    {%- endif -%}

    {%- render 'meta-tags', t_name: t_name -%}
    
    <script src="{{ 'lazysizes.min.js' | asset_url }}" async="async"></script>
    <script src="{{ 'global.min.js' | asset_url }}" defer="defer"></script>
    {{ content_for_header }}
    {%- render 'head_assets', t_name: t_name, isRTL: isRTL -%}
    
  </head>

  <body class="template-{{ request.page_type | handle }} {{ class_lazy }}"{% if body_img != blank %} data-bgset="{{ body_img | image_url: width: 1 }}" data-optimumx="2" data-sizes="auto"{% endif -%}>{% if isRTL %}<script type="text/javascript" id="t4s-flicker-fix">// Flicker fix.</script>{% endif -%}
    <a class="skip-to-content-link visually-hidden" href="#MainContent">{{ "accessibility.skip_to_text" | t }}</a>
    <div class="t4s-close-overlay t4s-op-0"></div>

    <div class="t4s-website-wrapper">
      {%- render 'header', t_name: t_name -%}

      <main id="MainContent" class="content-for-layout focus-none" role="main" tabindex="-1">
        {{ content_for_layout }}
      </main>

      <footer id="t4s-footer">
        {%- section 'footer' -%}
        {%- section 'bottom-bar' -%}
      </footer>
    </div>

      <ul hidden class="t4s-d-none">
        <li id="a11y-refresh-page-message">{{ 'accessibility.refresh_page' | t }}</li>
      </ul>
    {%- liquid
      render 'render_bottom'
      render 'structured-data'
    -%}
  </body>
</html>

{% else %}
{{ content_for_layout }}
{%- endunless -%}