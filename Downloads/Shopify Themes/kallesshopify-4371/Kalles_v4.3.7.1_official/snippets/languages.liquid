{%- liquid
  assign fl = ''
  assign current_locale = request.locale
  assign current_locale_iso_code = current_locale.iso_code
-%}
 
{%- unless is_fixed -%}
   
  {%- if linklists.languages-the4.links.size > 0 -%}
    {%- assign ck_iso_code = '#' | append: current_locale_iso_code -%}{%- assign links_cur = linklists.currency-the4.links | where: "url", ck_iso_code -%}
    <div data-locale-wrap class="t4s-top-bar__languages t4s-d-inline-block">
      <button data-dropdown-open data-position="bottom-end" data-id="dropdown_languages{{ sid }}"><span data-flagst4s="{{ sz }}" data-current class="t4s-d-inline-block {{ fl }} lazyloadt4s flagst4s-{{ current_locale_iso_code }}">{{ links_cur.first.title| default: current_locale_iso_code }}</span><svg class="t4s-ion-select-arrow " role="presentation" viewBox="0 0 19 12"><polyline fill="none" stroke="currentColor" points="17 2 9.5 10 2 2" fill-rule="evenodd" stroke-width="2" stroke-linecap="square"></polyline></svg></button>
      <div data-dropdown-wrapper class="t4s-dropdown__wrapper t4s-current-scrollbar {{ class_mb }}" id="dropdown_languages{{ sid }}">
        <div class="t4s-drop-arrow"></div>
        {%- for link_cur in linklists.languages-the4.links -%}{%- assign iso_code = link_cur.url | remove: '#' | remove: ' ' -%}
        <button type="button" data-flagst4s="{{ sz }}" data-locale-item data-dropdown-off class="t4s-lang-item {{ fl }} lazyloadt4s flagst4s-{{ iso_code }} t4s-text-uppercase {% if iso_code == current_locale_iso_code %} is--selected{% endif %}" data-iso="{{ iso_code }}"{% unless show_short_label %} data-language="{{ locale.endonym_name | escape }}"{% endunless %}>{% if show_short_label %}{{ iso_code | upcase }}{% else %}{{ link_cur.title }}{% endif %}</button>
        {%- endfor -%}
      </div>
    </div>
  {%- else -%}
    <div data-locale-wrap class="t4s-top-bar__languages t4s-d-inline-block">
      <button data-dropdown-open data-position="bottom-end" data-id="dropdown_languages{{ sid }}"><span data-flagst4s="{{ sz }}" data-current class="t4s-d-inline-block {{ fl }} lazyloadt4s flagst4s-{{ current_locale_iso_code }}">{{ current_locale.endonym_name }}</span><svg class="t4s-ion-select-arrow " role="presentation" viewBox="0 0 19 12"><polyline fill="none" stroke="currentColor" points="17 2 9.5 10 2 2" fill-rule="evenodd" stroke-width="2" stroke-linecap="square"></polyline></svg></button>
      <div data-dropdown-wrapper class="t4s-dropdown__wrapper t4s-current-scrollbar {{ class_mb }}" id="dropdown_languages{{ sid }}">
        <div class="t4s-drop-arrow"></div>
        {%- for locale in shop.published_locales -%}{%- assign iso_code = locale.iso_code -%}
        <button type="button" data-flagst4s="{{ sz }}" data-locale-item data-dropdown-off class="t4s-lang-item {{ fl }} lazyloadt4s flagst4s-{{ iso_code }} t4s-text-uppercase  {% if iso_code == current_locale_iso_code %} is--selected{% endif %}" data-iso="{{ iso_code }}"{% unless show_short_label %} data-language="{{ locale.endonym_name | escape }}"{% endunless %}>{% if show_short_label %}{{ iso_code | upcase }}{% else %}{{ locale.endonym_name }}{% endif %}</button>
        {%- endfor -%}
      </div>
    </div>
  {%- endif -%}
  
{%- else -%}
  
    {{ 't4s-currency-lang_drawer.css' | asset_url | stylesheet_tag }}
    <div data-locale-wrap data-lang-pos="{{ settings.lang_pos }}">
      <button data-drawer-options='{ "id":"#drawer-languages-t4s-fixed" }' class="t4s-btn-languages-sidebar"><span data-flagst4s="{{ sz }}" data-current class="t4s-d-inline-block {{ fl }} lazyloadt4s flagst4s-{{ current_locale_iso_code }}">{% if show_short_label %}{{ current_locale_iso_code | upcase }}{% else %}{{ current_locale.endonym_name }}{% endif %}</span><svg class="t4s-ion-select-arrow " role="presentation" viewBox="0 0 19 12"><polyline fill="none" stroke="currentColor" points="17 2 9.5 10 2 2" fill-rule="evenodd" stroke-width="2" stroke-linecap="square"></polyline></svg></button>
      <div id="drawer-languages-t4s-fixed" class="t4s-drawer t4s-drawer__right" aria-hidden="true">
        <div class="t4s-drawer__header">
          <span>{{ 'general.sidebar.language' | t }}</span>
          <button class="t4s-drawer__close" data-drawer-close aria-label="{{ 'general.sidebar.close' | t }}"><svg class="t4s-iconsvg-close" role="presentation" viewBox="0 0 16 14"><path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path></svg></button>
        </div>
        <div class="t4s-drawer__content">
           <div class="t4s-drawer__main t4s-current-scrollbar">
             {%- for locale in shop.published_locales -%}{%- assign iso_code = locale.iso_code -%}
             <button type="button" data-flagst4s="{{ sz }}" data-locale-item data-drawer-close class="t4s-lang-item {{ fl }} lazyloadt4s flagst4s-{{ iso_code }}{% if iso_code == current_locale_iso_code %} is--selected{% endif %}" data-iso="{{ iso_code }}">{% if show_short_label %}{{ iso_code | upcase }}{% else %}{{ locale.endonym_name }}{% endif %}</button>
             {%- endfor -%}
           </div>
        </div>
      </div>
    </div>

{%- endunless -%}