{%- comment -%}
t4s_group_btns
t4s_group_btns_split
{%- endcomment -%}

{%- if isSplit1 -%}
{%- case se_stts.h_icon -%}

  {%- when 'kalles' -%}
	  <svg xmlns="http://www.w3.org/2000/svg" class="t4s-d-none">
		  <symbol id="icon-h-search" viewBox="0 0 18 19" fill="none">
		    <path fill-rule="evenodd" clip-rule="evenodd" d="M11.03 11.68A5.784 5.784 0 112.85 3.5a5.784 5.784 0 018.18 8.18zm.26 1.12a6.78 6.78 0 11.72-.7l5.4 5.4a.5.5 0 11-.71.7l-5.41-5.4z" fill="currentColor"></path>
		  </symbol>
		  <symbol id="icon-h-account" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.2" fill="none" stroke-linecap="round" stroke-linejoin="round">
		  	<path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle>
		   </symbol>
		  <symbol id="icon-h-heart" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.2" fill="none" stroke-linecap="round" stroke-linejoin="round">
		  	<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
		   </symbol>
		  <symbol id="icon-h-cart" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.2" fill="none" stroke-linecap="round" stroke-linejoin="round">
		  <circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
		   </symbol>
		</svg>

  {%- when 'pe' -%}
	  <svg xmlns="http://www.w3.org/2000/svg" class="t4s-d-none">
		  <symbol id="icon-h-search" viewBox="0 0 32 32">
		    <path d="M28.591 27.273l-7.263-7.264c1.46-1.756 2.339-4.010 2.339-6.471 0-5.595-4.535-10.129-10.129-10.129-5.594 0-10.129 4.535-10.129 10.129 0 5.594 4.536 10.129 10.129 10.129 2.462 0 4.716-0.879 6.471-2.339l7.263 7.264 1.319-1.319zM4.475 13.538c0-4.997 4.065-9.063 9.063-9.063 4.997 0 9.063 4.066 9.063 9.063s-4.066 9.063-9.063 9.063c-4.998 0-9.063-4.066-9.063-9.063z" fill="currentColor"></path>
		  </symbol>
		  <symbol id="icon-h-account" viewBox="0 0 32 32">
		    <path d="M16 3.205c-7.067 0-12.795 5.728-12.795 12.795s5.728 12.795 12.795 12.795 12.795-5.728 12.795-12.795c0-7.067-5.728-12.795-12.795-12.795zM16 4.271c6.467 0 11.729 5.261 11.729 11.729 0 2.845-1.019 5.457-2.711 7.49-1.169-0.488-3.93-1.446-5.638-1.951-0.146-0.046-0.169-0.053-0.169-0.66 0-0.501 0.206-1.005 0.407-1.432 0.218-0.464 0.476-1.244 0.569-1.944 0.259-0.301 0.612-0.895 0.839-2.026 0.199-0.997 0.106-1.36-0.026-1.7-0.014-0.036-0.028-0.071-0.039-0.107-0.050-0.234 0.019-1.448 0.189-2.391 0.118-0.647-0.030-2.022-0.921-3.159-0.562-0.719-1.638-1.601-3.603-1.724l-1.078 0.001c-1.932 0.122-3.008 1.004-3.57 1.723-0.89 1.137-1.038 2.513-0.92 3.159 0.172 0.943 0.239 2.157 0.191 2.387-0.010 0.040-0.025 0.075-0.040 0.111-0.131 0.341-0.225 0.703-0.025 1.7 0.226 1.131 0.579 1.725 0.839 2.026 0.092 0.7 0.35 1.48 0.569 1.944 0.159 0.339 0.234 0.801 0.234 1.454 0 0.607-0.023 0.614-0.159 0.657-1.767 0.522-4.579 1.538-5.628 1.997-1.725-2.042-2.768-4.679-2.768-7.555 0-6.467 5.261-11.729 11.729-11.729zM7.811 24.386c1.201-0.49 3.594-1.344 5.167-1.808 0.914-0.288 0.914-1.058 0.914-1.677 0-0.513-0.035-1.269-0.335-1.908-0.206-0.438-0.442-1.189-0.494-1.776-0.011-0.137-0.076-0.265-0.18-0.355-0.151-0.132-0.458-0.616-0.654-1.593-0.155-0.773-0.089-0.942-0.026-1.106 0.027-0.070 0.053-0.139 0.074-0.216 0.128-0.468-0.015-2.005-0.17-2.858-0.068-0.371 0.018-1.424 0.711-2.311 0.622-0.795 1.563-1.238 2.764-1.315l1.011-0.001c1.233 0.078 2.174 0.521 2.797 1.316 0.694 0.887 0.778 1.94 0.71 2.312-0.154 0.852-0.298 2.39-0.17 2.857 0.022 0.078 0.047 0.147 0.074 0.217 0.064 0.163 0.129 0.333-0.025 1.106-0.196 0.977-0.504 1.461-0.655 1.593-0.103 0.091-0.168 0.218-0.18 0.355-0.051 0.588-0.286 1.338-0.492 1.776-0.236 0.502-0.508 1.171-0.508 1.886 0 0.619 0 1.389 0.924 1.68 1.505 0.445 3.91 1.271 5.18 1.77-2.121 2.1-5.035 3.4-8.248 3.4-3.183 0-6.073-1.277-8.188-3.342z" fill="currentColor"></path>
		  </symbol>
		  <symbol id="icon-h-heart" viewBox="0 0 32 32">
		    <path d="M21.886 5.115c3.521 0 6.376 2.855 6.376 6.376 0 1.809-0.754 3.439-1.964 4.6l-10.297 10.349-10.484-10.536c-1.1-1.146-1.778-2.699-1.778-4.413 0-3.522 2.855-6.376 6.376-6.376 2.652 0 4.925 1.62 5.886 3.924 0.961-2.304 3.234-3.924 5.886-3.924zM21.886 4.049c-2.345 0-4.499 1.089-5.886 2.884-1.386-1.795-3.54-2.884-5.886-2.884-4.104 0-7.442 3.339-7.442 7.442 0 1.928 0.737 3.758 2.075 5.152l11.253 11.309 11.053-11.108c1.46-1.402 2.275-3.308 2.275-5.352 0-4.104-3.339-7.442-7.442-7.442v0z" fill="currentColor"></path>
		  </symbol>
		  <symbol id="icon-h-cart" viewBox="0 0 32 32">
		    <path d="M3.205 3.205v25.59h25.59v-25.59h-25.59zM27.729 27.729h-23.457v-23.457h23.457v23.457z" fill="currentColor"></path>
          <path d="M9.068 13.334c0 3.828 3.104 6.931 6.931 6.931s6.93-3.102 6.93-6.931v-3.732h1.067v-1.066h-3.199v1.066h1.065v3.732c0 3.234-2.631 5.864-5.864 5.864-3.234 0-5.865-2.631-5.865-5.864v-3.732h1.067v-1.066h-3.199v1.066h1.065v3.732z" fill="currentColor"></path>
		  </symbol>
		</svg>
		
  {%- when 'drawn' -%}
	  <svg xmlns="http://www.w3.org/2000/svg" class="t4s-d-none">
		  <symbol id="icon-h-search" viewBox="0 0 20 20" stroke="currentColor">
		    <circle fill="none" stroke-width="2" stroke-miterlimit="10" cx="8.35" cy="8.35" r="6.5"></circle><path fill="none" stroke-width="2" stroke-miterlimit="10" d="M12.945 12.945l5.205 5.205"></path>
		  </symbol>
		  <symbol id="icon-h-account" viewBox="0 0 20 20" stroke="currentColor">
		    <path fill="none" stroke-width="2" stroke-miterlimit="10" d="M6 5.444C6 3.481 7.377 2 10 2c2.557 0 4 1.481 4 3.444S12.613 10 10 10c-2.512 0-4-2.592-4-4.556z"></path><path fill="none" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" d="M17.049 13.366s-1.22-.395-2.787-.761A7.056 7.056 0 0110 14c-1.623 0-3.028-.546-4.192-1.411-1.601.37-2.857.777-2.857.777-.523.17-.951.759-.951 1.309V17c0 .55.45 1 1 1h14c.55 0 1-.45 1-1v-2.325c0-.55-.428-1.139-.951-1.309z"></path>
		  </symbol>
		  <symbol id="icon-h-heart" viewBox="0 0 20 20" fill="currentColor">
		    <path d="M14.001 4c.802 0 1.556.311 2.122.876.565.564.877 1.315.877 2.113s-.311 1.548-.87 2.105l-6.158 6.087L3.876 9.1A2.964 2.964 0 013 6.989c0-.798.312-1.548.878-2.112A2.98 2.98 0 016 4c.802 0 1.556.311 2.122.876.142.142.382.411.388.417l1.491 1.665 1.49-1.666c.006-.007.245-.275.387-.417A2.988 2.988 0 0114.001 4m0-2a4.99 4.99 0 00-3.536 1.461c-.172.171-.465.499-.465.499s-.293-.328-.466-.5A4.987 4.987 0 006.001 2a4.99 4.99 0 00-3.536 1.461 4.98 4.98 0 00-.001 7.055L9.965 18l7.571-7.483a4.982 4.982 0 000-7.057A4.993 4.993 0 0014.001 2z"></path>
		  </symbol>
		  <symbol id="icon-h-cart" viewBox="0 0 329.998 329.998" stroke="currentColor" fill="currentColor">
		    <path d="M322.03 56.042A15 15 0 00309.999 50H78.98l-4.073-36.656C74.063 5.747 67.642 0 59.999 0h-40c-8.284 0-15 6.716-15 15s6.716 15 15 15h26.574l4.054 36.485c.012.127.026.253.042.379L65.09 196.656C65.934 204.253 72.356 210 79.999 210h191.104a15 15 0 0014.37-10.7l38.897-130a15.004 15.004 0 00-2.34-13.258zM259.934 180H93.425L82.313 80h207.54l-29.919 100zM109.999 239.998c-24.813 0-45 20.187-45 45 0 24.814 20.187 45 45 45s45-20.186 45-45c0-24.813-20.187-45-45-45zm0 60c-8.271 0-15-6.728-15-15 0-8.271 6.729-15 15-15s15 6.729 15 15c0 8.272-6.729 15-15 15zM228.999 239.998c-24.814 0-45 20.187-45 45 0 24.814 20.186 45 45 45s45-20.186 45-45c0-24.813-20.187-45-45-45zm0 60c-8.271 0-15-6.728-15-15 0-8.271 6.729-15 15-15s15 6.729 15 15c0 8.272-6.729 15-15 15z"></path>
		  </symbol>
		</svg>
		
  {%- else -%}
	  <svg xmlns="http://www.w3.org/2000/svg" class="t4s-d-none">
		  <symbol id="icon-h-search" viewBox="0 0 32 32" fill="currentColor">
		    <path d="M 19 3 C 13.488281 3 9 7.488281 9 13 C 9 15.394531 9.839844 17.589844 11.25 19.3125 L 3.28125 27.28125 L 4.71875 28.71875 L 12.6875 20.75 C 14.410156 22.160156 16.605469 23 19 23 C 24.511719 23 29 18.511719 29 13 C 29 7.488281 24.511719 3 19 3 Z M 19 5 C 23.429688 5 27 8.570313 27 13 C 27 17.429688 23.429688 21 19 21 C 14.570313 21 11 17.429688 11 13 C 11 8.570313 14.570313 5 19 5 Z"/>
		  </symbol>
		  <symbol id="icon-h-account" viewBox="0 0 32 32" fill="currentColor">
		  	<path xmlns="http://www.w3.org/2000/svg" d="M 16 5 C 12.144531 5 9 8.144531 9 12 C 9 14.410156 10.230469 16.550781 12.09375 17.8125 C 8.527344 19.34375 6 22.882813 6 27 L 8 27 C 8 22.570313 11.570313 19 16 19 C 20.429688 19 24 22.570313 24 27 L 26 27 C 26 22.882813 23.472656 19.34375 19.90625 17.8125 C 21.769531 16.550781 23 14.410156 23 12 C 23 8.144531 19.855469 5 16 5 Z M 16 7 C 18.773438 7 21 9.226563 21 12 C 21 14.773438 18.773438 17 16 17 C 13.226563 17 11 14.773438 11 12 C 11 9.226563 13.226563 7 16 7 Z"/>
		   </symbol>
		  <symbol id="icon-h-heart" viewBox="0 0 32 32" fill="currentColor">
		  	<path xmlns="http://www.w3.org/2000/svg" d="M 9.5 5 C 5.363281 5 2 8.402344 2 12.5 C 2 13.929688 2.648438 15.167969 3.25 16.0625 C 3.851563 16.957031 4.46875 17.53125 4.46875 17.53125 L 15.28125 28.375 L 16 29.09375 L 16.71875 28.375 L 27.53125 17.53125 C 27.53125 17.53125 30 15.355469 30 12.5 C 30 8.402344 26.636719 5 22.5 5 C 19.066406 5 16.855469 7.066406 16 7.9375 C 15.144531 7.066406 12.933594 5 9.5 5 Z M 9.5 7 C 12.488281 7 15.25 9.90625 15.25 9.90625 L 16 10.75 L 16.75 9.90625 C 16.75 9.90625 19.511719 7 22.5 7 C 25.542969 7 28 9.496094 28 12.5 C 28 14.042969 26.125 16.125 26.125 16.125 L 16 26.25 L 5.875 16.125 C 5.875 16.125 5.390625 15.660156 4.90625 14.9375 C 4.421875 14.214844 4 13.273438 4 12.5 C 4 9.496094 6.457031 7 9.5 7 Z"/>
		   </symbol>
		  <symbol id="icon-h-cart" viewBox="0 0 32 32" fill="currentColor">
		  	<path xmlns="http://www.w3.org/2000/svg" d="M 16 3 C 13.253906 3 11 5.253906 11 8 L 11 9 L 6.0625 9 L 6 9.9375 L 5 27.9375 L 4.9375 29 L 27.0625 29 L 27 27.9375 L 26 9.9375 L 25.9375 9 L 21 9 L 21 8 C 21 5.253906 18.746094 3 16 3 Z M 16 5 C 17.65625 5 19 6.34375 19 8 L 19 9 L 13 9 L 13 8 C 13 6.34375 14.34375 5 16 5 Z M 7.9375 11 L 11 11 L 11 14 L 13 14 L 13 11 L 19 11 L 19 14 L 21 14 L 21 11 L 24.0625 11 L 24.9375 27 L 7.0625 27 Z"/>
		   </symbol>
		</svg>
		
{%- endcase -%}
{%- endif -%}

{%- if isSplit1 -%}
		<div class="t4s-site-nav__icons t4s-use__{{ se_stts.h_icon }} is--hover{{ se_stts.hover_icon }} t4s-h-cart__design{{ se_stts.cart_des }} t4s-lh-1 t4s-d-inline-flex t4s-align-items-center">
			{%- if se_stts.show_search -%}
			<div class="t4s-site-nav__icon t4s-site-nav__search">
				<a class="t4s-pr" href="{{ routes.search_url }}" data-drawer-delay- data-drawer-options='{ "id":"#t4s-search-hidden" }'>
					<svg class="t4s-icon t4s-icon--search" aria-hidden="true" focusable="false" role="presentation"><use href="#icon-h-search"></use></svg>
				</a>
		   </div>
		   {%- endif -%}
		   	{%- if shop.customer_accounts_enabled and se_stts.show_acc -%}
				<div class="t4s-site-nav__icon t4s-site-nav__account t4s-pr t4s-d-none t4s-d-md-inline-block">
					<a class="t4s-pr" href="{{ routes.account_url }}"{% unless customer or request.page_type contains 'customers' or settings.login_side == false %} data-drawer-delay- data-drawer-options='{ "id":"#t4s-login-sidebar" }' {% endunless %}>
						<svg class="t4s-icon t4s-icon--account" aria-hidden="true" focusable="false" role="presentation"><use href="#icon-h-account"></use></svg>
					</a>
					{%- if customer -%}
						<ul class="t4s-my-account-split">
							<li><a class="t4s-d-block" href="{{ routes.account_url }}">{{ 'customer.account.dashboard' | t }}</a></li>
							<li><a class="t4s-d-block" href="{{ routes.account_addresses_url }}">{{ 'customer.menu.addresses' | t }}</a></li>
							<li><a class="t4s-d-block" href="{{ routes.account_logout_url }}" data-no-instant>{{ 'customer.account.log_out' | t }}</a></li>
						</ul>
						{%- style -%}
							.t4s-site-nav__account .t4s-my-account-split{
								position: absolute;
								top: 100%;
								z-index: 380;
								background-color: var(--t4s-body-background);
								box-shadow: 0 0 3px rgb(0 0 0 / 15%);
								list-style: none;
								left: -5px;
								width: 165px;
								padding: 10px 0;
								line-height: 1.4;
								margin-top: 12px;
								border-radius: 3px;
								transform: translateY(15px) translateZ(0);
								visibility: hidden;
								transition: opacity .2s,visibility .2s,transform .2s;
								opacity:0;
								text-align: end;
							}
							.t4s-site-nav__account .t4s-my-account-split::after{
								content: "";
								position: absolute;
								top: auto;
								right: 0;
								bottom: 100%;
								left: 0;
								width: auto;
								height: 12px;
							}
							.t4s-site-nav__account .t4s-my-account-split a{padding: 8px 18px;color:var(--text-color);}
							.rtl_true .t4s-site-nav__account .t4s-my-account-split{right:-5px;left:auto;}
							@media (-moz-touch-enabled: 0), (hover: hover) and (min-width: 1025px){
								.t4s-site-nav__account .t4s-my-account-split a:hover{color: var(--accent-color)!important;}
								.t4s-site-nav__account:hover .t4s-my-account-split{
									pointer-events: auto;
									visibility: visible;
									opacity: 1;
									transform: none;
								}
							}
						{%- endstyle -%}
				{%- endif -%}
				</div>
		   	{%- endif -%}
		</div>
{%- else -%}
		<div class="t4s-site-nav__icons t4s-use__{{ se_stts.h_icon }} t4s-h-cart__design{{ se_stts.cart_des }} t4s-lh-1 t4s-d-inline-flex t4s-align-items-center">
			{%- if se_stts.show_search -%}
			<div class="t4s-site-nav__icon t4s-site-nav__search t4s-d-lg-none">
				<a class="t4s-pr" href="{{ routes.search_url }}" data-drawer-delay- data-drawer-options='{ "id":"#t4s-search-hidden" }'>
					<svg class="t4s-icon t4s-icon--search" aria-hidden="true" focusable="false" role="presentation"><use href="#icon-h-search"></use></svg>
				</a>
		   </div>
		   {%- endif -%}
		   	{%- if shop.customer_accounts_enabled and se_stts.show_acc -%}
				<div class="t4s-site-nav__icon t4s-site-nav__account t4s-pr t4s-d-none t4s-d-md-inline-block t4s-d-lg-none">
					<a class="t4s-pr" href="{{ routes.account_url }}"{% unless customer or request.page_type contains 'customers' or settings.login_side == false %} data-drawer-delay- data-drawer-options='{ "id":"#t4s-login-sidebar" }' {% endunless %}>
						<svg class="t4s-icon t4s-icon--account" aria-hidden="true" focusable="false" role="presentation"><use href="#icon-h-account"></use></svg>
					</a>
					{%- if customer -%}
						<ul class="t4s-my-account">
							<li><a class="t4s-d-block" href="{{ routes.account_url }}">{{ 'customer.account.dashboard' | t }}</a></li>
							<li><a class="t4s-d-block" href="{{ routes.account_addresses_url }}">{{ 'customer.menu.addresses' | t }}</a></li>
							<li><a class="t4s-d-block" href="{{ routes.account_logout_url }}" data-no-instant>{{ 'customer.account.log_out' | t }}</a></li>
						</ul>
						{%- style -%}
							.t4s-site-nav__account .t4s-my-account{
								position: absolute;
								top: 100%;
								z-index: 380;
								background-color: var(--t4s-body-background);
								box-shadow: 0 0 3px rgb(0 0 0 / 15%);
								list-style: none;
								right: -5px;
								width: 165px;
								padding: 10px 0;
								line-height: 1.4;
								margin-top: 12px;
								border-radius: 3px;
								transform: translateY(15px) translateZ(0);
								visibility: hidden;
								transition: opacity .2s,visibility .2s,transform .2s;
								opacity:0;
								text-align: start;
							}
							.t4s-site-nav__account .t4s-my-account::after{
								content: "";
								position: absolute;
								top: auto;
								right: 0;
								bottom: 100%;
								left: 0;
								width: auto;
								height: 12px;
							}
							.t4s-site-nav__account .t4s-my-account a{padding: 8px 18px;color:var(--text-color)}
							.rtl_true .t4s-site-nav__account .t4s-my-account{left:-5px;right:auto;}
							@media (-moz-touch-enabled: 0), (hover: hover) and (min-width: 1025px){
								.t4s-site-nav__account .t4s-my-account a:hover{color: var(--h-text-color-hover)!important}
								.t4s-site-nav__account:hover .t4s-my-account{
									pointer-events: auto;
									visibility: visible;
									opacity: 1;
									transform: none;
								}
							}
						{%- endstyle -%}
					{%- endif -%}
			</div>
		   	{%- endif -%}
			{%- if se_stts.show_wis -%}
		   <div class="t4s-site-nav__icon t4s-site-nav__heart t4s-d-none t4s-d-md-inline-block">	
		      {%- if settings.wishlist_mode != '3' -%}		
			      <a data-link-wishlist class="t4s-pr" href="{{ routes.search_url }}/?view=wishlist">
					  <svg class="t4s-icon t4s-icon--heart" aria-hidden="true" focusable="false" role="presentation"><use href="#icon-h-heart"></use></svg>
					  <span data-count-wishlist class="t4s-pa t4s-op-0 t4s-ts-op t4s-count-box">0</span>
			      </a>
			   {%- else -%}
			      {%- capture the_snippet_fave_icon_menu %}{% render 'ssw-widget-faveicon-menu' with product.id %}{% endcapture -%}
			      {%- unless the_snippet_fave_icon_menu contains 'Liquid error' %}{{ the_snippet_fave_icon_menu }}{% endunless -%}
		      {%- endif -%}
		   </div>
		   {%- endif -%}

			{%- if se_stts.cart_des != '0' -%}
		   <div class="t4s-site-nav__icon t4s-site-nav__cart">	
				<a href="{{ routes.cart_url }}"{% if request.page_type != 'cart' and settings.cart_type != 'disable' %} data-drawer-delay- data-drawer-options='{ "id":"#t4s-mini_cart" }'{% endif %}>
					<span class="t4s-pr t4s-icon-cart__wrap">
					  <svg class="t4s-icon t4s-icon--account" aria-hidden="true" focusable="false" role="presentation"><use href="#icon-h-cart"></use></svg>
					  <span data-cart-count class="t4s-pa t4s-op-0 t4s-ts-op t4s-count-box">{{ cart.item_count }}</span>
					</span>
					<span class="t4s-h-cart-totals t4s-dn">
						<span class="t4s-h-cart__divider t4s-dn">/</span>
						<span data-cart-tt-price class="t4s-h-cart__total">{{ cart.total_price | money }}</span>
					</span>
		      </a>
		    </div>
		   {%- endif -%}

		</div>

		{%- if se_stts.cart_des != '0' -%}
		 <style>
			.t4s-h-cart__design3 .t4s-count-box {
			    width: 19px;
			    height: 19px;
			    line-height: 19px;
			    position: static;
			    display: inline-block;
			}
			.t4s-h-cart__design2 .t4s-site-nav__icon:last-child,
			.t4s-h-cart__design3 .t4s-site-nav__icon:last-child,
			.t4s-h-cart__design4 .t4s-site-nav__icon:last-child,
			.t4s-h-cart__design5 .t4s-site-nav__icon:last-child {
			    padding-right: 0;
			}
		 @media (min-width: 768px) {

		    	.t4s-h-cart__design2 .t4s-site-nav__cart a,
		    	.t4s-h-cart__design4 .t4s-site-nav__cart a,
		    	.t4s-h-cart__design5 .t4s-site-nav__cart a {
			      display: flex;
				   align-items: center;
				   justify-content: center;
		    	}
		    	.t4s-h-cart__design2 .t4s-h-cart-totals,
		    	.t4s-h-cart__design4 .t4s-h-cart-totals {
				    display: block;
				    margin-left: 15px;
				}
				.t4s-h-cart__design4 .t4s-site-nav__cart .t4s-count-box,
				.t4s-h-cart__design5 .t4s-site-nav__cart .t4s-count-box {
					display: none
				}
				.t4s-h-cart__design4 .t4s-h-cart-totals {
					margin-left: 7px;
				}
				.t4s-h-cart__design5 .t4s-h-cart__divider {
				    display: inline-block;
				}
				.t4s-h-cart__design5 .t4s-h-cart-totals {
				    display: block;
				    margin-left: 7px;
				}
				
				.t4s-h-cart__design1 .t4s-site-nav__icon.t4s-site-nav__btnMenu {
				    padding-right: 0;
				}
		 }
		 </style>
		{%- endif -%}
{%- endif -%}