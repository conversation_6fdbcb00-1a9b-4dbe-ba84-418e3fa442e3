{%- assign image = article.image -%} 
<div class="t4s-row t4s-space-item-inner"> 
	<div class="t4s-col-item t4s-col t4s-widget_img_ar {{ image_ratio }} {{ image_position }} {{ image_size }}">
		{%- if image != blank -%}
			<a class="t4s-d-block t4s-pr t4s-oh t4s_ratio" href="{{ article.url }}" {{ imgatt }}style="--aspect-ratioapt: {{ image.aspect_ratio | default: 1 }}">
				<img class="lazyloadt4s" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
				<span class="lazyloadt4s-loader"></span>
			</a>
		{%- endif -%}
	</div>
	<div class="t4s-col-item t4s-col t4s-widget_if_ar">
	   <a class="t4s-article-title t4s-d-block" href="{{ article.url }}">{{ article.title }}</a>
	   {{ article.published_at | time_tag: format: date }}
	</div>
</div> 
