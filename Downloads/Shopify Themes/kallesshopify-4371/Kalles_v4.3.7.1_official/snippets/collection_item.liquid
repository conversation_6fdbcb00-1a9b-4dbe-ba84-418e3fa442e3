{%- liquid
  assign collection = collections[bk_stts.collection]
  assign block_title = bk_stts.title | default: collection.title
  assign image = bk_stts.image | default: collection.image
  assign icon = bk_stts.icon
  assign title = bk_stts.collection_title | default: collection.title
  assign collection_link = bk_stts.collection_link | default: collection.url
 -%}
<div class="t4s-cat-content t4s-source-{{ source }} t4s-eff t4s-eff-{{ b_effect }} t4s-eff-img-{{ img_effect }} t4s-text-center t4s-pr t4s-oh" timeline hdt-reveal="{% if section.index < 3 %}fade-in{% else %}slide-in{% endif %}">
  <div class="t4s-coll-img t4s-pr" data-cacl-slide>
    {% if collection_des == "11" %}
      <span class="t4s-count">{{ collection.all_products_count | default: 0 }}</span> 
    {% endif %}
    <a href="{{ collection_link }}" target="{{ open_link }}" class="t4s-full-width-link"></a>
    <div class="t4s_cat_item_link t4s-img-wrap t4s-d-block" >
      {% if source == "image" %}
        <div class="t4s_ratio t4s-bg-11" style="--aspect-ratioapt: {{ image.aspect_ratio | default: 1.2 }}; {%- if image != blank -%} background: url({{ image | image_url: width: 1 }}); {% endif %}" >
          {%- if image != blank -%}
            <img {% if image.presentation.focal_point != '50.0% 50.0%' %} style="object-position: {{ image.presentation.focal_point }}"{% endif %} class="lazyloadt4s t4s-lz--fadeIn t4s-obj-eff" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
          {%- else -%}
            {{ 'collection-' | append: current | placeholder_svg_tag: 't4s-placeholder-svg t4s-obj-eff' }}  
          {%- endif -%}
        </div>
      {% elsif source == "icon" %}
        <div class="t4s-coll-icon"><i class="{{ icon }} t4s-obj-eff"></i></div>
      {% else %}
        <div class="t4s-coll-icon-svg">{{ bk_stts.custom_svg }}</div>
      {% endif %}
    </div>
  </div>
  {%- if title != blank -%}
    <div class="t4s-cate-wrapper">
        <a class="t4s-cat-title" href="{{ collection_link }}" target="{{ open_link }}">
          <span class="t4s-text">{{ title }}</span> {% if collection_des == "12" %} <span class="t4s-count">{{ collection.all_products_count | default: 0 }}</span> {% endif %}
        </a>
      {%- if subtitle != blank -%}
        <a class="t4s-cat-subtitle" href="{{ collection_link }}" target="{{ open_link }}">
          <span class="t4s-count">{{ collection.all_products_count | default: 0 }}</span> <span class="t4s-text">{{ subtitle }}</span>
        </a>
      {%- endif -%}
    </div>
  {%- endif -%}
</div>