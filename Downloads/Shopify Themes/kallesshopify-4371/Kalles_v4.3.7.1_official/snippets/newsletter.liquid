{%- comment -%}
    Renders a form newsletter

    Accepts:
    - form_id: {String} Form's id attribute for accessibility purpose (required)
    - layout: '1', '2' (optional)
    - html:            (optional)
    - margin:          (optional)
    - class:           (optional)
    - styleInline:     (optional)
    - buttonIcon:     (optional)
    
    Usage:
    {%- render 'newsletter', form_id: formId, layout: -%}
{%- endcomment -%}

{%- liquid
  unless form_id
    assign form_id = 'newsletter'
  endunless
  unless html
    assign html = 'p'
  endunless
  unless buttonIcon
    assign buttonIcon = false
  endunless
  assign newl_des = bk_stts.newl_des
  if newl_des == '11' or newl_des == '13'
    assign buttonIcon = true
  endif
  assign checkbox_mail = settings.checkbox_mail
  if layout == '1'
    assign class_email = 't4s-col-md t4s-col-12'
    assign class_btn = 't4s-col-md-auto t4s-col-12'
    assign class_ip = 't4s-text-center t4s-text-md-start'
  else
    assign class_email = 't4s-col'
    assign class_btn = 't4s-col-auto' 
    assign class_ip = 't4s-text-center t4s-text-md-start'
  endif
-%}

{%- capture conditions_mail -%}
  {%- if checkbox_mail -%}
    <div class="t4s-clearfix"></div>
    {%- assign page_url = settings.link_mail -%}
    <{{ html }} class="t4s-agree__checkbox t4s-pr t4s-d-inline-block {{ margin }}">
      <input type="checkbox" data-agreeMail-checkbox id="t4s-agree_{{ form_id }}" name="t4s-agree_{{ form_id }}" required="required">
      <label for="t4s-agree_{{ form_id }}">{% if page_url != blank %}{{ 'sections.agree_mail.link_conditions_html' | t: link: page_url }}{% else %}{{ 'sections.agree_mail.link_conditions_emty' | t }}{% endif %}</label>
      <svg class="t4s-dn t4s-icon_checked" viewBox="0 0 24 24"><path d="M9 20l-7-7 3-3 4 4L19 4l3 3z"></path></svg>
    </{{ html }}>
  {%- endif -%}
{%- endcapture -%}

{%- capture btn_icon -%}
  {% if newl_des == '10' %}
    <svg xmlns="http://www.w3.org/2000/svg" version="1.0" width="24px" height="24px" viewBox="0 0 300.000000 300.000000" preserveAspectRatio="xMidYMid meet">
    <metadata>
    Created by potrace 1.10, written by Peter Selinger 2001-2011
    </metadata>
    <g transform="translate(0.000000,300.000000) scale(0.100000,-0.100000)" fill="currentColor" stroke="none">
    <path d="M1507 2160 c-819 -460 -1488 -841 -1487 -846 1 -10 929 -356 940 -352 4 2 321 297 705 657 383 359 710 665 727 679 17 15 -19 -41 -80 -123 -61 -83 -311 -420 -556 -750 l-445 -600 -1 -407 c0 -225 3 -408 8 -407 4 0 125 139 270 308 l262 308 323 -119 c177 -66 330 -123 339 -125 12 -4 19 2 23 18 9 39 464 2593 462 2595 -1 1 -672 -375 -1490 -836z"/>
    </g>
    </svg>
  {% endif %}

{%- endcapture -%}

{%- capture icon_Html -%}
  {%- if buttonIcon -%}
    {%- if newl_des == '13' -%}
    x
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M11.4533 2.19455L17.7267 6.27222C17.9483 6.41631 18.0592 6.48835 18.1395 6.58445C18.2106 6.66952 18.264 6.76791 18.2965 6.87387C18.3334 6.99357 18.3334 7.12577 18.3334 7.39015V13.4999C18.3334 14.9 18.3334 15.6001 18.0609 16.1349C17.8212 16.6053 17.4387 16.9877 16.9683 17.2274C16.4336 17.4999 15.7335 17.4999 14.3334 17.4999H5.66669C4.26656 17.4999 3.56649 17.4999 3.03171 17.2274C2.56131 16.9877 2.17885 16.6053 1.93917 16.1349C1.66669 15.6001 1.66669 14.9 1.66669 13.4999V7.39015C1.66669 7.12577 1.66669 6.99357 1.7035 6.87387C1.73608 6.76791 1.78948 6.66952 1.86056 6.58445C1.94086 6.48835 2.0517 6.41631 2.27337 6.27222L8.54672 2.19455M11.4533 2.19455C10.9273 1.85262 10.6643 1.68166 10.3809 1.61514C10.1304 1.55635 9.86966 1.55635 9.61917 1.61514C9.33577 1.68166 9.07275 1.85262 8.54672 2.19455M11.4533 2.19455L16.6135 5.54864C17.1867 5.92123 17.4733 6.10752 17.5725 6.34377C17.6593 6.55023 17.6593 6.78291 17.5725 6.98937C17.4733 7.22562 17.1867 7.41191 16.6135 7.78449L11.4533 11.1386C10.9273 11.4805 10.6643 11.6515 10.3809 11.718C10.1304 11.7768 9.86966 11.7768 9.61917 11.718C9.33577 11.6515 9.07275 11.4805 8.54672 11.1386L3.38657 7.78449C2.81336 7.41191 2.52676 7.22562 2.4275 6.98937C2.34075 6.78291 2.34075 6.55023 2.4275 6.34377C2.52676 6.10752 2.81336 5.92123 3.38657 5.54864L8.54672 2.19455M17.9167 15.8332L12.381 10.8332M7.61904 10.8332L2.08335 15.8332" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- elsif newl_des != '10' -%}
      <svg viewBox="0 0 32 32" width="18" aria-hidden="true" focusable="false" class="t4s-newsletter__icon-arrow">
        <path d="M 18.71875 6.78125 L 17.28125 8.21875 L 24.0625 15 L 4 15 L 4 17 L 24.0625 17 L 17.28125 23.78125 L 18.71875 25.21875 L 27.21875 16.71875 L 27.90625 16 L 27.21875 15.28125 Z" fill="currentColor"/>
      </svg>
    {%- endif -%}
  {%- endif -%}
{%- endcapture -%}

{%- case settings.platform_email -%}
  {%- when '3' -%}
    <form data-t4s-klaviyo-form data-form-mail-agree id="t4s-form-{{ form_id }}" class="t4s-pr t4s-z-100 t4s-newsletter__form is--klaviyo" action="//manage.kmail-lists.com/subscriptions/subscribe" data-ajax-submit="//manage.kmail-lists.com/ajax/subscriptions/subscribe" method="GET"{% if settings.ajax_klaviyo and settings.klaviyo_list_id != blank %} data-t4s-klaviyo-ajax{% endif %}>
      <input type="hidden" name="g" value="{{ settings.klaviyo_list_id }}">
      <div class="t4s-newsletter__fields">
        <div class="t4s-newsletter__inner t4s-row t4s-g-0 t4s-pr t4s-oh {{ class }}">
          {%- if newl_des == '15' -%}
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" class="t4s-email-icon">
            <path d="M17 21.2524H7C3.35 21.2524 1.25 19.1524 1.25 15.5024V8.50244C1.25 4.85244 3.35 2.75244 7 2.75244H17C20.65 2.75244 22.75 4.85244 22.75 8.50244V15.5024C22.75 19.1524 20.65 21.2524 17 21.2524ZM7 4.25244C4.14 4.25244 2.75 5.64244 2.75 8.50244V15.5024C2.75 18.3624 4.14 19.7524 7 19.7524H17C19.86 19.7524 21.25 18.3624 21.25 15.5024V8.50244C21.25 5.64244 19.86 4.25244 17 4.25244H7Z" fill="currentColor"/>
            <path d="M11.9998 12.8724C11.1598 12.8724 10.3098 12.6124 9.65978 12.0824L6.52978 9.58241C6.20978 9.32241 6.14978 8.85241 6.40978 8.53241C6.66978 8.21241 7.13978 8.15241 7.45978 8.41241L10.5898 10.9124C11.3498 11.5224 12.6398 11.5224 13.3998 10.9124L16.5298 8.41241C16.8498 8.15241 17.3298 8.20241 17.5798 8.53241C17.8398 8.85241 17.7898 9.33241 17.4598 9.58241L14.3298 12.0824C13.6898 12.6124 12.8398 12.8724 11.9998 12.8724Z" fill="currentColor"/>
            </svg>
          {% endif %}
          <div class="{{ class_email }} t4s-col-item is--col-email"><input type="email" name="email" placeholder="{{ 'sections.newsletter_form.email_placeholder' | t }}"  value="{% if customer %}{{ customer.email }}{% endif %}" class="{{ class_ip }} t4s-newsletter__email" required="required"></div>
          <div class="{{ class_btn }} t4s-col-item is--col-btn">
            <button data-t4s-klaviyo-submit data-agreeMail-btn type="submit" class="t4s-w-100 t4s-newsletter__submit t4s-truncate t4s-btn-loading__svg">
              <span class="t4s-newsletter__text">{{ 'sections.newsletter_form.submit' | t }}{{ btn_icon }}{{ icon_Html }}</span>
              <span class="t4s-loading__spinner t4s-dn">
                <svg width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
              </span>
            </button>
          </div>
        </div>
        {{- conditions_mail -}}
      </div>
      <div class="t4s-newsletter__response klaviyo_messages">
        <div class="t4s-newsletter__success success_message t4s-dn" style="display:none"><svg width="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M443.3 100.7C449.6 106.9 449.6 117.1 443.3 123.3L171.3 395.3C165.1 401.6 154.9 401.6 148.7 395.3L4.686 251.3C-1.562 245.1-1.562 234.9 4.686 228.7C10.93 222.4 21.06 222.4 27.31 228.7L160 361.4L420.7 100.7C426.9 94.44 437.1 94.44 443.3 100.7H443.3z"/></svg>{{ 'sections.newsletter_form.confirmation' | t }}</div>
        <div class="t4s-newsletter__error error_message t4s-dn" style="display:none"></div>
      </div>
    </form>

  {%- when '4' -%}
    <form id="t4s-form-{{ form_id }}" data-form-mail-agree role="form" action="{%- if settings.ajax_mailChimp -%}{{ settings.action_mailchimp | replace: '/post?u', '/post-json?u' }}{%- else -%}{{ settings.action_mailchimp }}{%- endif -%}" class="t4s-pr t4s-z-100 t4s-newsletter__form is--mailChimp" method="post"{%- if settings.ajax_mailChimp and settings.action_mailchimp != blank %} data-t4s-mailChimp-ajax{%- endif -%}>
      <div class="t4s-newsletter__fields">
        <div class="t4s-newsletter__inner t4s-row t4s-g-0 t4s-pr t4s-oh {{ class }}">
          {%- if newl_des == '15' -%}
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" class="t4s-email-icon">
            <path d="M17 21.2524H7C3.35 21.2524 1.25 19.1524 1.25 15.5024V8.50244C1.25 4.85244 3.35 2.75244 7 2.75244H17C20.65 2.75244 22.75 4.85244 22.75 8.50244V15.5024C22.75 19.1524 20.65 21.2524 17 21.2524ZM7 4.25244C4.14 4.25244 2.75 5.64244 2.75 8.50244V15.5024C2.75 18.3624 4.14 19.7524 7 19.7524H17C19.86 19.7524 21.25 18.3624 21.25 15.5024V8.50244C21.25 5.64244 19.86 4.25244 17 4.25244H7Z" fill="currentColor"/>
            <path d="M11.9998 12.8724C11.1598 12.8724 10.3098 12.6124 9.65978 12.0824L6.52978 9.58241C6.20978 9.32241 6.14978 8.85241 6.40978 8.53241C6.66978 8.21241 7.13978 8.15241 7.45978 8.41241L10.5898 10.9124C11.3498 11.5224 12.6398 11.5224 13.3998 10.9124L16.5298 8.41241C16.8498 8.15241 17.3298 8.20241 17.5798 8.53241C17.8398 8.85241 17.7898 9.33241 17.4598 9.58241L14.3298 12.0824C13.6898 12.6124 12.8398 12.8724 11.9998 12.8724Z" fill="currentColor"/>
            </svg>
          {% endif %}
          <div class="{{ class_email }} t4s-col-item is--col-email"><input type="email" name="EMAIL" placeholder="{{ 'sections.newsletter_form.email_placeholder' | t }}"  value="{% if customer %}{{ customer.email }}{% endif %}" class="{{ class_ip }} t4s-newsletter__email" required="required"></div>
          <div class="{{ class_btn }} t4s-col-item is--col-btn">
            <button data-t4s-mailChimp-submit data-agreeMail-btn type="submit" class="t4s-w-100 t4s-newsletter__submit t4s-truncate t4s-btn-loading__svg">
              <span class="t4s-newsletter__text">{{ 'sections.newsletter_form.submit' | t }}{{ btn_icon }}{{ icon_Html }}</span>
              <span class="t4s-loading__spinner t4s-dn">
                <svg width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
              </span>
            </button>
          </div>
        </div>
        {{- conditions_mail -}}
      </div>
      
    </form>

  {%- else -%}
    {%- assign form_id = 't4s-form-' | append: form_id -%}
    {%- if bk_stts_code and bk_stts_code != blank -%}
      {%- assign form_id = form_id | append: "-code" -%}
    {%- endif -%}
    {%- assign sub_cribe = 'sections.newsletter_form.submit' | t -%}
    {%- form 'customer', class: 't4s-pr t4s-z-100 t4s-newsletter__form', id: form_id, data-form-mail-agree: '' -%}
      <input type="hidden" name="contact[tags]" value="newsletter">
      <div class="t4s-newsletter__fields">
        <div class="t4s-newsletter__inner t4s-row t4s-g-0 t4s-pr t4s-oh {{ class }}">
          {%- if newl_des == '15' -%}
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" class="t4s-email-icon">
            <path d="M17 21.2524H7C3.35 21.2524 1.25 19.1524 1.25 15.5024V8.50244C1.25 4.85244 3.35 2.75244 7 2.75244H17C20.65 2.75244 22.75 4.85244 22.75 8.50244V15.5024C22.75 19.1524 20.65 21.2524 17 21.2524ZM7 4.25244C4.14 4.25244 2.75 5.64244 2.75 8.50244V15.5024C2.75 18.3624 4.14 19.7524 7 19.7524H17C19.86 19.7524 21.25 18.3624 21.25 15.5024V8.50244C21.25 5.64244 19.86 4.25244 17 4.25244H7Z" fill="currentColor"/>
            <path d="M11.9998 12.8724C11.1598 12.8724 10.3098 12.6124 9.65978 12.0824L6.52978 9.58241C6.20978 9.32241 6.14978 8.85241 6.40978 8.53241C6.66978 8.21241 7.13978 8.15241 7.45978 8.41241L10.5898 10.9124C11.3498 11.5224 12.6398 11.5224 13.3998 10.9124L16.5298 8.41241C16.8498 8.15241 17.3298 8.20241 17.5798 8.53241C17.8398 8.85241 17.7898 9.33241 17.4598 9.58241L14.3298 12.0824C13.6898 12.6124 12.8398 12.8724 11.9998 12.8724Z" fill="currentColor"/>
            </svg>
          {% endif %}
          <div class="{{ class_email }} t4s-col-item is--col-email"><input type="email" name="contact[email]" placeholder="{{ 'sections.newsletter_form.email_placeholder' | t }}"  value="{% if customer %}{{ customer.email }}{% endif %}" class="{{ class_ip }} t4s-newsletter__email" required="required"></div>
       
            <div class="{{ class_btn }} t4s-col-item is--col-btn" style="padding: 0px">
              <button data-coupon="{{ bk_stts_code | base64_encode }}" data-agreeMail-btn type="submit" class="t4s-w-100 t4s-newsletter__submit t4s-truncate t4s-btn-loading__svg">
                <span class="t4s-newsletter__text">{{ bk_label_button | default: sub_cribe }}{{ btn_icon }}{{ icon_Html }}</span>
                <span class="t4s-loading__spinner t4s-dn">
                  <svg width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
                </span>
              </button>
            </div>
        </div>
        {{- conditions_mail -}}
      </div>
      
     

      {%- if bk_stts_code != blank -%}    
        <script>
          let recent_form = localStorage.getItem("t4s-recentform") || "";
          let checkCodeForm = false;

          if(location.href.indexOf('customer_posted=true#') > -1 && location.href.indexOf("-code") > -1) {
            checkCodeForm = true;

          }else if(location.href.indexOf('customer_posted=true#') > -1 && recent_form.indexOf("-code") > -1) {
            checkCodeForm = true;

          }else if(location.href.indexOf('form_type=customer#') > -1 && location.href.indexOf("-code") > -1) {
            checkCodeForm = true;

          }else if(location.href.indexOf('form_type=customer#') > -1 && recent_form.indexOf("-code") > -1) {
            checkCodeForm = true;
          }

          if (checkCodeForm) {
            T4SThemeSP.Notices(T4Sstrings.frm_newsletter_popup_success_code.replace("{% raw %}{{coupon}}{% endraw %}", atob({{ bk_stts_code | base64_encode | json }})),'success');
            copDiscount({{ bk_stts_code | base64_encode | json }});
            document.querySelector("body").dispatchEvent(new CustomEvent('mail.subscribe.success'));
            localStorage.removeItem("t4s-recentform");
          }
          document.getElementById({{ form_id | json}}).addEventListener('submit', function(event) {
            let code = this.querySelector("[data-coupon]").dataset.coupon; 
            event.preventDefault();
            copDiscount(code)
          });
          function copDiscount(code) {
            code = atob(code);
            navigator.clipboard.writeText(code)
            .then(function() {
              document.querySelector('.t4s-noti-get-code').style.display = "block";              
              form.submit();      
            })
            .catch(function(err) {
              console.error(err);
            });
          }
        </script>
      {%- endif -%}
      
      <div data-new-response-form class="t4s-newsletter__response">
        {%- if form.posted_successfully? -%}
        <div class="t4s-newsletter__success"><svg width="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M443.3 100.7C449.6 106.9 449.6 117.1 443.3 123.3L171.3 395.3C165.1 401.6 154.9 401.6 148.7 395.3L4.686 251.3C-1.562 245.1-1.562 234.9 4.686 228.7C10.93 222.4 21.06 222.4 27.31 228.7L160 361.4L420.7 100.7C426.9 94.44 437.1 94.44 443.3 100.7H443.3z"/></svg>{{ 'sections.newsletter_form.confirmation' | t }}</div>
        {%- elsif form.errors -%}
        <div class="t4s-newsletter__error">{{ form.errors | default_errors }}</div>{%- endif -%}
      </div>
        
    {%- endform -%}
     
{%- endcase -%}