{%- if bk_stts.top_heading != blank or  bk_stts.top_subheading != blank -%}
	{{ 'top-head.css' | asset_url | stylesheet_tag }}
	<div timeline hdt-reveal="slide-in" class="t4s-top-heading t4s_des_title_{{ bk_stts.design_heading }} {{ bk_stts.heading_align }}" style="--tophead_mb: {{ bk_stts.tophead_mb }}px;">
		{%- if bk_stts.top_heading != blank -%}<h3 class="t4s-section-title t4s-title"><span>{{ bk_stts.top_heading }}</span></h3>{%- endif -%}
		{%- if bk_stts.design_heading == "13" -%}
			<span class="heading-char t4s-d-block"><svg version="1.0" xmlns="http://www.w3.org/2000/svg" width="82" height="30" viewBox="0 0 82.000000 9.000000" preserveAspectRatio="xMidYMid meet"><g transform="translate(0.000000,9.000000) scale(0.050000,-0.050000)" fill="currentColor" stroke="none"> <path d="M20 161 c0 -10 17 -24 37 -30 21 -7 54 -30 73 -52 50 -54 142 -50 214 11 32 28 75 50 94 50 19 0 56 -22 82 -50 62 -66 157 -66 236 0 75 63 106 63 180 0 76 -64 152 -64 228 0 75 63 117 63 176 0 66 -70 160 -67 245 8 82 74 59 105 -26 34 -77 -65 -113 -65 -199 -2 -86 63 -141 63 -216 0 -75 -63 -113 -63 -188 0 -32 27 -82 50 -110 50 -27 0 -77 -22 -110 -50 -74 -63 -111 -63 -196 0 -37 28 -88 50 -112 50 -25 0 -72 -22 -104 -50 -33 -27 -75 -50 -94 -50 -19 0 -61 23 -94 50 -60 50 -116 66 -116 31z"/></g></svg></span>
		{% endif %}
		{%- if bk_stts.design_heading == "6" -%}
			<span class="t4s-cbl">
				{%- if bk_stts.icon_heading != blank -%}
					<i class="{{ bk_stts.icon_heading }}"></i>
				{%- else -%}
					<span></span>
				{%- endif -%}
				</span>
		{%- endif -%}
		{%- if bk_stts.top_subheading != blank -%}<span class="t4s-section-des t4s-subtitle">{{ bk_stts.top_subheading }}</span>{%- endif -%}
	</div>
{%- endif -%}