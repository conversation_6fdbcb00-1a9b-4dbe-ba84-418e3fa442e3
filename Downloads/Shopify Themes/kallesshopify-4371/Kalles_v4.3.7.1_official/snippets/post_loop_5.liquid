{%- assign image = post.image -%}
<article class="t4s-col-item t4s-post t4s-post-item t4s-post-des-5">
	<div class="t4s-post-inner" timeline hdt-reveal="slide-in">
		{%- if image != blank -%}
		<div class="t4s-post-thumb">	
			<a class="t4s-eff t4s-eff-{{ b_effect }} t4s-eff-img-{{ img_effect }} t4s-d-block" href="{{ post.url }}">
				<div class="t4s_ratio t4s-bg-11" style="background: url({{ image | image_url: width: 1 }});--aspect-ratioapt: {{ image.aspect_ratio | default: 2 }}"  data-cacl-slide>
					<img class="lazyloadt4s t4s-lz--fadeIn t4s-obj-eff" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
				</div>
			</a>
			{% if show_cate %}
				{%- assign post_tags = post.tags | join: ';' | split: ';' -%}
				{%- assign tag_category = post_tags | where: 'category_' | first -%}
				<div class="t4s-categories">
					{%- if tag_category != blank -%} 
						<a href="{{ blog_url }}/tagged/{{ tag_category | handle }}" class="t4s-post-text-color">{{ tag_category | remove : 'category_' | strip }}</a> 
					{%- else -%}
						<a href="{{ blog_url }}" class="t4s-post-text-color">{{ blog.title }}</a> 
					{%- endif -%}
				</div> 
			{% endif %}
		</div>
		{%- endif -%}
		<div class="t4s-post-info">
			{% if show_tags %}
				<div class="t4s-tags">
					{%- for tag in post.tags limit: 3 -%} 
						<a href="{{ blog_url }}/tagged/{{ tag | handle }}" class="t4s-post-text-color">{{ tag | remove : 'category_' | strip }}</a> {%- unless forloop.last -%} , {% endunless -%}
					{%- endfor -%} 
				</div>
			{% endif %}
			<div class="t4s-post-metas">
	            {% if show_au %}
		            <span class="t4s-post-author"><span class="t4s-post-author__text t4s-dib">{{ by_txt }}</span>
		              <span class="t4s-author-name">{{ post.author | replace_first: 'ad clnt', 'admin' }}</span>
		            </span>
		        {% endif %}
		        {% if show_dt %}
		             <span class="t4s-post-time">
			            	{%- if date contains "%" -%}
			            		{{ post.published_at | time_tag: date }}
			            	{%- else -%}	
			            	<span><span class="t4s-post-time__text t4s-dib">{{ on_txt }}</span> {{ post.published_at | time_tag: format: date }}</span>
			            	{%- endif -%}
				        </span>
		        {% endif %}
		        {% if show_cm %}
		        	<span class="t4s-post-comment">{{ 'blogs.comments.with_count_html' | t: count: post.comments_count }}</span>
		        {% endif %}
		    </div>
			<h3 class="t4s-post-title"><a href="{{ post.url }}" class="t4s-post-text-color">{{ post.title }}</a></h3>
			{% if show_cnt %}
				<div class="t4s-post-content t4s-rte">{%- if post.excerpt.size > 0 -%}{{ post.excerpt }}{%- else -%}{{ post.content | strip_html | truncate: 118 }}{%- endif -%}</div>
			{% endif %}
			{% if show_rm %}
				<a href="{{ post.url }}" class="t4s-post-readmore">{{ readmore_txt }} {% if show_irm %}
              <svg width="24" height="24" viewBox="0 0 24 24" fill="transparent" xmlns="http://www.w3.org/2000/svg">
				<path d="M1 12H15" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M12 8L16 12L12 16" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
              {% endif %}
				</a>
			{% endif %}
		</div>	
	</div>
</article>