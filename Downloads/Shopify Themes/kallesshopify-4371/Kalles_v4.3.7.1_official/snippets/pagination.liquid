{%- comment -%}
    Renders a set of links for paginated results. Must be used within paginate tags.

    Usage:
    {% paginate results by 2 %}
      {% render 'pagination', paginate: paginate, anchor: '#yourID' %}
    {% endpaginate %}

    Accepts:
    - paginate: {Object}
    - anchor: {String} (optional) This can be added so that on page reload it takes you to wherever you've placed your anchor tag.
{%- endcomment -%}

{%- if paginate.parts.size > 0 -%}

  <link rel="stylesheet" href="{{ 'pagination.css' | asset_url }}" media="print" onload="this.media='all'">
  <noscript>{{ 'pagination.css' | asset_url | stylesheet_tag }}</noscript>

  <div class="t4s-pagination-wrapper t4s-w-100">
    <nav class="t4s-pagination" role="navigation" aria-label="{{ 'general.pagination.label' | t }}">
      <ul data-ntjax-wrap{{ ajax }} class="t4s-pagination__list list-unstyled" role="list">
      {%- if paginate.previous -%}
        <li>
          <a href="{{ paginate.previous.url }}{{ anchor }}" class="t4s-pagination__item pagination__item--next pagination__item-arrow" aria-label="{{ 'general.pagination.previous' | t }}">
            {{ 'general.pagination.previous' | t }}
          </a>
        </li>
      {%- endif -%}

      {%- for part in paginate.parts -%}
        <li>
          {%- if part.is_link -%}
            <a href="{{ part.url }}{{ anchor }}" class="t4s-pagination__item link" aria-label="{{ 'general.pagination.page' | t: number: part.title }}">{{ part.title }}</a>
          {%- else -%}
            {%- if part.title == paginate.current_page -%}
              <span class="t4s-pagination__item pagination__item--current" aria-current="page" aria-label="{{ 'general.pagination.page' | t: number: part.title }}">{{ part.title }}</span>
            {%- else -%}
              <span class="t4s-pagination__item">{{ part.title }}</span>
            {%- endif -%}
          {%- endif -%}
        </li>
      {%- endfor -%}

      {%- if paginate.next -%}
        <li>
          <a href="{{ paginate.next.url }}{{ anchor }}" class="t4s-pagination__item pagination__item--prev pagination__item-arrow" aria-label="{{ 'general.pagination.next' | t }}">
            {{ 'general.pagination.next' | t }}
          </a>
        </li>
      {%- endif -%}
      </ul>
    </nav>
  </div>
  
{%- endif -%}
