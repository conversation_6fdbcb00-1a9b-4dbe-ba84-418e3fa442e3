{%- if se_stts.top_heading != blank or se_stts.top_subheading != blank -%}
	{{ 'top-head.css' | asset_url | stylesheet_tag }}
	<div timeline hdt-reveal="slide-in" class="t4s-top-heading t4s_des_title_{{ se_stts.design_heading }} {{ se_stts.heading_align }} {% if se_stts.head_btn_url != blank and se_stts.head_btn_label != blank %} t4s-heading-has-btn {%- endif -%}" style="--heading-height: {{ heading_height }}px;--tophead_mb: {{ se_stts.tophead_mb }}px;--tophead_mt: {{ se_stts.tophead_mt }}px;">
		{%- if se_stts.top_heading != blank -%}
			<div class="heading-testimonials-star ">
				{% if se_stts.design_heading == "30" %}
					<span class="heading-star t4s-d-block"><svg width="41" height="37" viewBox="0 0 41 37" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 28.2154L29.1905 26.0629L32.8214 37L20.5 28.2154ZM40.5 14.0786H25.2024L20.5 0L15.7976 14.0786H0.5L12.881 22.805L8.17857 36.8836L20.5595 28.1572L28.1786 22.805L40.5 14.0786Z" fill="#219653"/></svg></span>
				{% endif %}
				<h3 class="t4s-section-title t4s-title"><span>{{ se_stts.top_heading }}</span></h3>
			</div>
		{%- endif -%}
		{%- if se_stts.design_heading == "13" -%}
			<span class="heading-char t4s-d-block"><svg version="1.0" xmlns="http://www.w3.org/2000/svg" width="82" height="30" viewBox="0 0 82.000000 9.000000" preserveAspectRatio="xMidYMid meet"><g transform="translate(0.000000,9.000000) scale(0.050000,-0.050000)" fill="currentColor" stroke="none"> <path d="M20 161 c0 -10 17 -24 37 -30 21 -7 54 -30 73 -52 50 -54 142 -50 214 11 32 28 75 50 94 50 19 0 56 -22 82 -50 62 -66 157 -66 236 0 75 63 106 63 180 0 76 -64 152 -64 228 0 75 63 117 63 176 0 66 -70 160 -67 245 8 82 74 59 105 -26 34 -77 -65 -113 -65 -199 -2 -86 63 -141 63 -216 0 -75 -63 -113 -63 -188 0 -32 27 -82 50 -110 50 -27 0 -77 -22 -110 -50 -74 -63 -111 -63 -196 0 -37 28 -88 50 -112 50 -25 0 -72 -22 -104 -50 -33 -27 -75 -50 -94 -50 -19 0 -61 23 -94 50 -60 50 -116 66 -116 31z"/></g></svg></span>
		{%- endif -%}
		{%- if se_stts.design_heading == "16" -%}
			<span class="t4s-heading-icon__line-wave t4s-d-block"><svg xmlns="http://www.w3.org/2000/svg" width="180" height="10" viewBox="0 0 180 10" fill="none"><path d="M1.5 7.75903L5.47566 3.35661C6.73073 1.96681 8.91213 1.96681 10.1672 3.3566L11.7971 5.16146C13.0522 6.55125 15.2336 6.55125 16.4886 5.16146L18.1185 3.35661C19.3736 1.96681 21.555 1.96681 22.8101 3.3566L24.4399 5.16146C25.695 6.55125 27.8764 6.55125 29.1315 5.16146L30.7614 3.35661C32.0164 1.96681 34.1978 1.96681 35.4529 3.3566L37.0828 5.16146C38.3379 6.55125 40.5193 6.55125 41.7743 5.16146L43.4042 3.35661C44.6593 1.96681 46.8407 1.96681 48.0958 3.3566L49.7257 5.16146C50.9807 6.55125 53.1621 6.55125 54.4172 5.16146L56.0471 3.35661C57.3022 1.96681 59.4836 1.96681 60.7386 3.3566L62.3685 5.16146C63.6236 6.55125 65.805 6.55125 67.0601 5.16146L68.6899 3.35661C69.945 1.96681 72.1264 1.96681 73.3815 3.3566L75.0114 5.16146C76.2664 6.55125 78.4478 6.55125 79.7029 5.16146L81.3328 3.35661C82.5879 1.96681 84.7693 1.96681 86.0243 3.3566L87.6542 5.16146C88.9093 6.55125 91.0907 6.55125 92.3458 5.16146L93.9757 3.35661C95.2307 1.96681 97.4121 1.96681 98.6672 3.3566L100.297 5.16146C101.552 6.55125 103.734 6.55125 104.989 5.16146L106.619 3.35661C107.874 1.96681 110.055 1.96681 111.31 3.3566L112.94 5.16146C114.195 6.55125 116.376 6.55125 117.631 5.16146L119.261 3.35661C120.516 1.96681 122.698 1.96681 123.953 3.3566L125.583 5.16146C126.838 6.55125 129.019 6.55125 130.274 5.16146L131.904 3.35661C133.159 1.96681 135.341 1.96681 136.596 3.3566L138.226 5.16146C139.481 6.55125 141.662 6.55125 142.917 5.16146L144.547 3.35661C145.802 1.96681 147.984 1.96681 149.239 3.3566L150.869 5.16146C152.124 6.55125 154.305 6.55125 155.56 5.16146L157.19 3.35661C158.445 1.96681 160.626 1.96681 161.881 3.3566L163.511 5.16146C164.766 6.55125 166.948 6.55125 168.203 5.16146L169.833 3.35661C171.088 1.96681 173.269 1.96681 174.524 3.3566L178.5 7.75903" stroke="var(--accent-color)" stroke-width="4"/></svg></span>
		{%- endif -%}
		{%- if se_stts.design_heading == "6" -%}
			<span class="t4s-cbl">
			{%- if se_stts.icon_heading != blank -%}
	            <i class="{{ se_stts.icon_heading }}"></i>
	        {%- else -%}
	        	<span></span>
	        {%- endif -%}
	        </span>
		{%- endif -%}
		{%- if se_stts.top_subheading != blank -%}<span class="t4s-section-des t4s-subtitle">{{ se_stts.top_subheading }}</span>{%- endif -%}
		{%- if se_stts.head_btn_url != blank or se_stts.head_slide_btn -%}
        <div class="t4s-head-btn">
          {% if se_stts.head_btn_url != blank and se_stts.head_btn_label != blank %}
            {{ 'button-style.css' | asset_url | stylesheet_tag }}
            <a class="t4s-btn t4s-btn-base t4s-btn-style-{{ se_stts.head_btn_style }} t4s-btn-size-{{ se_stts.head_btn_size }} t4s-btn-icon-{{ se_stts.head_btn_icon }} t4s-btn-color-{{ se_stts.head_btn_cl }} t4s-btn-rounded-{{ se_stts.head_btn_rounded }} {% if se_stts.head_btn_style == 'default' or se_stts.head_btn_style == 'outline' %}t4s-btn-effect-{{ se_stts.head_btn_effect }}{% endif %}" href="{{ se_stts.head_btn_url }}">{{ se_stts.head_btn_label }}
              {%- if se_stts.head_btn_icon -%}<svg class="t4s-btn-icon" width="14"><use xlink:href="#t4s-icon-btn"></use></svg>{%- endif -%}
            </a>
						{%- endif -%}
        </div>
      {%- endif -%}
	</div>
{%- endif -%}
