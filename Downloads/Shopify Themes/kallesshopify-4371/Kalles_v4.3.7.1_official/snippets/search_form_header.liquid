{%- style -%}
  .t4s-search-header__form {
    padding: 0;
    min-width: 260px;
    border: 1px solid rgba(var(--h-text-color-rgb), 0.15);
    border-radius: var(--t4s-other-radius);
    background-color: var(--primary-form-color);
    color: var(--secondary-form-color);
  }
  .t4s-search-header__input {
      padding-top:0;
      padding-inline-end:50px;
      padding-bottom:0;
      padding-inline-start:15px;
      height: 40px;
      border: 0;
      width: 100%;
      line-height: 18px;
      background-color: var(--primary-form-color);
      color: var(--secondary-form-color);
      border-radius: var(--t4s-other-radius);
      font-size: 13px;
  }
  .t4s-search-header__input:focus::placeholder{color: transparent;}
  .t4s-search-header__submit {
    position: absolute;
    top: 0;
    right: 2px;
    bottom: 0;
    padding: 0;
    width: 40px;
    border: none;
    background-color: transparent !important;
    color: var(--secondary-form-color);
    font-size: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .rtl_true .t4s-search-header__submit{left:2px;right:auto}
  .t4s-search-header__submit svg.t4s-icon {
    color: var(--secondary-form-color);
    width: 18px;
    height: 18px;
  }
  .t4s-search-header__type select {
    border: 0;
    max-width: 138px;
    padding: 0 30px 0 15px;
    appearance: none;
    font-size: 13px;
    display: inline-block;
    background-color: transparent;
    box-shadow: none;
    color: var(--secondary-form-color);
    border-radius: var(--btn-radius);
  }
  .t4s-search-header__type .t4s-icon-select-arrow { color: var(--secondary-form-color); }
  .t4s-search-header_border {
    height: 18px;
    background-color: var(--secondary-form-color);
    width: 1.5px;
  }
  .t4s-frm-search__results {
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    z-index: 1000;
    width: auto;
    height: auto;
    background-color: var(--t4s-body-background);
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all .1s ease-in-out;
    max-width: 1170px;
    margin: 0 auto;
    margin-top:15px;
    box-shadow: 0 1px 5px 2px rgba(var(--border-color-rgb),.3);
  }
  .calc-pos-submenu .t4s-search-header__form-wrap:hover .t4s-frm-search__results {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }
  .t4s-frm-search__content { 
    height:auto;
    overflow: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    padding: 20px;
    line-height: {{ settings.bd_lheight }}
  }
  .t4s-frm-search__content .t4s-widget_img_pr {
    min-width: 95px;
    max-width: 95px;
    max-height: 120px;
  }
  .t4s-frm-search__content .t4s-widget_img_pr>a {
    height: 100%;
  }
  .t4s-frm-search__content .t4s-widget_img_pr img {
    object-fit: contain;
    max-height: 120px;
  }
  .t4s-frm-search__content .t4s-row.t4s-widget__pr {
    --ts-gutter-x: 20px;flex-wrap: nowrap;
  }
  .t4s-frm-search__content .t4s-widget__pr .t4s-widget__pr-title {
    font-weight: 500;
    line-height: 1.25;
    font-size: 14px;
    color: var(--secondary-color);
  }
  .t4s-frm-search__content .t4s-widget__pr-price {
    font-size: 14px;
    color: var(--secondary-price-color);
  }
  .t4s-frm-search__content .t4s-widget__pr-price ins {
    color: var(--sale-price-color);
    margin-inline-start: 5px;
    display: inline-block;
  }
  .t4s-frm-search__content .t4s-widget__pr .t4s-widget__pr-price {
      margin-top: 1.5px;
  }
  .t4s-frm-search__content .t4s-widget__pr .t4s-widget__pr-price .t4s-badge-price{
    color: var(--sale-badge-color);
    background-color: var(--sale-badge-background);
    display: inline-block;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 10px;
    margin: 5px;
    line-height: 17.5px;
  }
  .t4s-search-header__form-wrap .t4s-mini-search__viewAll {
    padding: 12px 20px;
    border-top: 1px solid rgba(var(--border-color-rgb),.35);
    box-shadow: 0 0 10px 0 rgba(var(--border-color-rgb),.35);
    line-height:{{ settings.bd_lheight }};
  }
  .t4s-frm-search__content .t4s-widget__pr .t4s-widget__pr-title:hover,
  .t4s-search-header__form-wrap .t4s-mini-search__viewAll:hover {
      color: var(--accent-color);
  }
  .t4s-frm-search__content .t4s-badge-price {
    color: var(--sale-badge-color);
    background-color: var(--sale-badge-background);
    display: inline-block;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 10px;
    margin: 5px;
    line-height: 17.5px;
  }
{%- endstyle -%}
<div data-predictive-search data-sid="search-hidden" class="t4s-search-header__form-wrap t4s-search-header t4s-pr t4s-d-none t4s-d-lg-block">
  {%- liquid
    assign collection = collections[settings.search_prs_suggest]
    assign limit = 5 
    assign show_search_suggest = settings.show_search_suggest
    if shop.types.size < 3
      assign shop_types = shop.types | join: ' ' | remove: ' '
    else
      assign shop_types = 'type_the4'
    endif 
  -%}
  <form data-frm-search action="{{ routes.search_url }}" method="get" class="t4s-search-header__form t4s-row t4s-g-0 t4s-align-items-center t4s-pr" role="search">
    {%- if settings.filter_type_search and shop_types != blank -%}
      <div data-cat-search class="t4s-search-header__type t4s-pr t4s-oh t4s-col-auto t4s-col-item">
        <select data-name="product_type" class="t4s-truncate">
          <option value="*">{{ 'search.general.all_categories' | t }}</option>
          {%- for product_type in shop.types -%}{%- if product_type == blank %}{% continue -%}{% endif -%}<option value="{{ product_type }}">{{ product_type }}</option>{%- endfor -%}
        </select>
        {%- comment %}<svg class="t4s-icon-select-arrow t4s-pe-none" role="presentation" width="10" height="10" viewBox="0 0 19 12"><use xlink:href="#t4s-select-arrow"></use></svg>{% endcomment -%}
      </div>
      <div class="t4s-search-header_border t4s-col-auto t4s-col-item"></div>
    {%- endif -%}
    <div class="t4s-search-header__main t4s-pr t4s-oh t4s-d-flex t4s-col t4s-col-item">
       <input type="hidden" name="resources[options][fields]" value="title,product_type,variants.title,vendor,variants.sku,tag">
      <input data-input-search class="t4s-search-header__input t4s-input__currentcolor" autocomplete="off" type="text" name="q" placeholder="{{ 'search.general.placeholder_products' | t }}">
      <button class="t4s-search-header__submit{% if settings.ajax_search %} t4s-pe-none{% endif %}" type="submit"><svg class="t4s-icon t4s-icon--search" aria-hidden="true" focusable="false" role="presentation"><use href="#icon-h-search"></use></svg></button>
    </div>
  </form>
  <div class="t4s-search-h-break t4s-pa t4s-w-100" style="height:20px;"></div>
  <div class="t4s-pr">
    <div class="t4s-pa t4s-frm-search__results t4s-text-start">
      <div data-skeleton-search class="t4s-skeleton_wrap t4s-dn">
        {%- for i in (1..4) -%}
          <div class="t4s-row t4s-space-item-inner">
            <div class="t4s-col-auto t4s-col-item t4s-widget_img_pr"><div class="t4s-skeleton_img"></div></div>
            <div class="t4s-col t4s-col-item t4s-widget_if_pr"><div class="t4s-skeleton_txt1"></div><div class="t4s-skeleton_txt2"></div></div>
          </div>
        {%- endfor -%}
      </div>
      <div data-results-search class="t4s-frm-search__content t4s_ratioadapt t4s-current-scrollbar"{% if collection == blank or show_search_suggest == false %} style="display: none;"{% endif %}>
        {%- if collection != blank and show_search_suggest -%}
          {%- for product in collection.products limit: limit -%}
            {%- render 'pr-sidebar-loop', imgatt: "", product: product, pr_url: product.url, placeholder_img: placeholder_img, price_varies_style: price_varies_style -%}
          {%- endfor -%}
        {%- endif -%}
      </div>
      {%- if collection != blank and show_search_suggest -%}
          {%- if collection.all_products_count > limit -%}
            <div data-viewAll-search>
              <a href="{{ collection.url }}" class="t4s-mini-search__viewAll t4s-d-block">{{ 'search.pagination.view_all' | t }} <svg width="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M 18.71875 6.78125 L 17.28125 8.21875 L 24.0625 15 L 4 15 L 4 17 L 24.0625 17 L 17.28125 23.78125 L 18.71875 25.21875 L 27.21875 16.71875 L 27.90625 16 L 27.21875 15.28125 Z"/></svg></a>
            </div>
          {%- endif -%}
      {%- else -%}
        <div data-viewAll-search></div>
      {%- endif -%}
    </div>
  </div>
</div>