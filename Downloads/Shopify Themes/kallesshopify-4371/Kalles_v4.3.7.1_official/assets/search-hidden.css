#t4s-search-hidden .t4s-drawer__header{
    padding: 5px 0 5px 20px;
}
#t4s-search-hidden .t4s-drawer__header span{font-weight: 500;}

#t4s-search-hidden .t4s-mini-search__frm{
    padding: 30px 20px;
    border-bottom: 1px solid rgba(var(--border-color-rgb),.8);
}

#t4s-search-hidden .t4s-drawer__close{transition: .3s;}
#t4s-search-hidden .t4s-drawer__close:hover{
    background-color: transparent;
    transform: rotate(180deg);
}

#t4s-search-hidden .t4s-mini-search__cat{margin-bottom: 20px;}

#t4s-search-hidden input:not([type=submit]):not([type=checkbox]),
#t4s-search-hidden select,
#t4s-search-hidden textarea {
    border: 1px solid var(--border-color);
    height: 40px;
    font-size: 13px;
    outline: 0;
    color: var(--text-color);
    border-radius: 0;
    max-width: 100%;
    width: 100%;
}
#t4s-search-hidden input:not([type=submit]):not([type=checkbox]),
#t4s-search-hidden textarea {
    padding: 0 15px;
}
#t4s-search-hidden select{
    vertical-align: middle;
    font-size: 14px;
    transition: border-color 0.5s ease 0s;
    color: var(--secondary-color);
    border-radius: var(--btn-radius);
}
#t4s-search-hidden input.t4s-mini-search__input:not([type=submit]):not([type=checkbox]) {
    padding: 0 50px 0 20px;
    line-height: 18px;
    color: var(--secondary-color);
    border-radius: var(--btn-radius);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}
.t4s-mini-search__submit{
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    padding: 0;
    width: 50px;
    border: none;
    background-color: transparent;
    color: var(--secondary-color);
    font-size: 15px;
    font-weight: 600;
    min-height: 40px;
    border-radius: var(--btn-radius);
    transition: .3s;    
    display: inline-flex;
    justify-content: center;
    align-items: center;
}
.t4s-mini-search__submit:hover {
    background-color: transparent;
    color: var(--secondary-color);
}

.t4s-mini-search__keys{
    font-size: 12px;
    margin-top: 10px;
}
.t4s-mini-search__label{
    color: var(--secondary-color);
    margin-right: 5px;
}
.t4s-mini-search__listKey{
    padding: 0px;
    margin: 0;
}
.t4s-mini-search__listKey li a{
    font-weight: 400;
    color: var(--text-color);
}
.t4s-mini-search__listKey li a:hover{color: var(--accent-color);}

.t4s-mini-search__title{
    font-weight: 600;
    color: var(--secondary-color);
    padding: 10px 20px;
    border-bottom: 1px solid rgba(var(--border-color-rgb),.8);
    box-shadow: 0 3px 10px 0 rgb(129 129 129 / 20%);
}
#t4s-search-hidden div[data-viewall-search]{
    padding: 12px 20px;
    border-top: 1px solid rgba(var(--border-color-rgb),.7);
    box-shadow: 0 0 10px 0 rgba(var(--border-color-rgb),.7);
}
.t4s-mini-search__viewAll{
    font-size: 14px;
    font-weight: 600;
    transition: .3s;
    color: var(--secondary-color);
}
.t4s-mini-search__viewAll svg{width: 18px;height: auto;fill: currentColor;position: relative;top: 3px; transition: -webkit-transform .2s ease-in-out,transform .2s ease-in-out}
.t4s-mini-search__viewAll:hover>svg{
    -webkit-transform: translateX(0.25rem);
    transform: translateX(0.25rem);
}
.t4s-mini-search__content{padding: 20px;}

#t4s-search-hidden .t4s-widget_img_pr{padding-right: 0;min-width: 95px; max-width: 95px; max-height: 120px;}
#t4s-search-hidden .t4s-widget_img_pr>a{height: 100%;}
#t4s-search-hidden .t4s-widget_img_pr img {
  max-height: 120px;
  object-fit: contain;
}
#t4s-search-hidden .t4s-widget_if_pr{padding-left: 10px!important;color: var(--text-color);}
#t4s-search-hidden .t4s-space-item-inner:not(:last-child),
.t4s-frm-search__results .t4s-space-item-inner:not(:last-child){margin-bottom: 10px;padding-bottom: 10px;}
#t4s-search-hidden .t4s-product-title{
    color: var(--secondary-color);
    font-weight: 500;
    line-height: 24px;
}
#t4s-search-hidden .t4s-product-title:hover{color: var(--accent-color);}
.t4s-skeleton_img{
    background: rgb(225, 227, 228);
    padding-bottom: 100%;
    width: 80px;
}
.t4s-skeleton_txt1, .t4s-skeleton_txt2 {
    height: 10px;
    width: 100%;
    background: rgb(225, 227, 228);
    margin-bottom: 8px;
}
.t4s-skeleton_txt2
{
    width: 38%;
    margin-bottom: 0px;
}
.t4s-skeleton_wrap{
    padding: 20px;
    animation: 0.45s linear 0s infinite alternate none running skeletonAnimation;
    will-change: opacity;
}
@-webkit-keyframes skeletonAnimation { 
    0% { opacity: 0.45; }
    100% { opacity: 0.9; }
}
@keyframes skeletonAnimation { 
    0% { opacity: 0.45; }
    100% { opacity: 0.9; }
}

.t4s-mini-search-suggest {
    margin-inline-end: 7px;
}
.t4s-mini-search-suggest mark {
    padding: 0;
    background: transparent;
}