!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIT4sCore={})}(this,(function(e){"use strict";function t(e){return e.split("-")[0]}function n(e){return e.split("-")[1]}function i(e){return["top","bottom"].includes(t(e))?"x":"y"}function o(e){return"y"===e?"height":"width"}function a(e,a,s){let{reference:r,floating:l}=e;const c=r.x+r.width/2-l.width/2,d=r.y+r.height/2-l.height/2,u=i(a),p=o(u),m=r[p]/2-l[p]/2,f="x"===u;let h;switch(t(a)){case"top":h={x:c,y:r.y-l.height};break;case"bottom":h={x:c,y:r.y+r.height};break;case"right":h={x:r.x+r.width,y:d};break;case"left":h={x:r.x-l.width,y:d};break;default:h={x:r.x,y:r.y}}switch(n(a)){case"start":h[u]-=m*(s&&f?-1:1);break;case"end":h[u]+=m*(s&&f?-1:1)}return h}function s(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function r(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}async function l(e,t){var n;void 0===t&&(t={});const{x:i,y:o,platform:a,rects:l,elements:c,strategy:d}=e,{boundary:u="clippingAncestors",rootBoundary:p="viewport",elementContext:m="floating",altBoundary:f=!1,padding:h=0}=t,g=s(h),v=c[f?"floating"===m?"reference":"floating":m],T=r(await a.getClippingRect({element:null==(n=await(null==a.isElement?void 0:a.isElement(v)))||n?v:v.contextElement||await(null==a.getDocumentElement?void 0:a.getDocumentElement(c.floating)),boundary:u,rootBoundary:p})),y=r(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({rect:"floating"===m?{...l.floating,x:i,y:o}:l.reference,offsetParent:await(null==a.getOffsetParent?void 0:a.getOffsetParent(c.floating)),strategy:d}):l[m]);return{top:T.top-y.top+g.top,bottom:y.bottom-T.bottom+g.bottom,left:T.left-y.left+g.left,right:y.right-T.right+g.right}}const c=Math.min,d=Math.max;function u(e,t,n){return d(e,c(t,n))}const p={left:"right",right:"left",bottom:"top",top:"bottom"};function m(e){return e.replace(/left|right|bottom|top/g,(e=>p[e]))}function f(e,t,a){void 0===a&&(a=!1);const s=n(e),r=i(e),l=o(r);let c="x"===r?s===(a?"end":"start")?"right":"left":"start"===s?"bottom":"top";return t.reference[l]>t.floating[l]&&(c=m(c)),{main:c,cross:m(c)}}const h={start:"end",end:"start"};function g(e){return e.replace(/start|end/g,(e=>h[e]))}const v=["top","right","bottom","left"],T=v.reduce(((e,t)=>e.concat(t,t+"-start",t+"-end")),[]);function y(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function S(e){return v.some((t=>e[t]>=0))}function w(e){return"x"===e?"y":"x"}e.arrow=e=>({name:"arrow",options:e,async fn(t){const{element:n,padding:a=0}=null!=e?e:{},{x:r,y:l,placement:c,rects:d,platform:p}=t;if(null==n)return{};const m=s(a),f={x:r,y:l},h=i(c),g=o(h),v=await p.getDimensions(n),T="y"===h?"top":"left",y="y"===h?"bottom":"right",S=d.reference[g]+d.reference[h]-f[h]-d.floating[g],w=f[h]-d.reference[h],b=await(null==p.getOffsetParent?void 0:p.getOffsetParent(n)),x=b?"y"===h?b.clientHeight||0:b.clientWidth||0:0,C=S/2-w/2,_=m[T],P=x-v[g]-m[y],k=x/2-v[g]/2+C,I=u(_,k,P);return{data:{[h]:I,centerOffset:k-I}}}}),e.autoPlacement=function(e){return void 0===e&&(e={}),{name:"autoPlacement",options:e,async fn(i){var o,a,s,r,c;const{x:d,y:u,rects:p,middlewareData:m,placement:h,platform:v,elements:y}=i,{alignment:S=null,allowedPlacements:w=T,autoAlignment:b=!0,...x}=e,C=function(e,i,o){return(e?[...o.filter((t=>n(t)===e)),...o.filter((t=>n(t)!==e))]:o.filter((e=>t(e)===e))).filter((t=>!e||n(t)===e||!!i&&g(t)!==t))}(S,b,w),_=await l(i,x),P=null!=(o=null==(a=m.autoPlacement)?void 0:a.index)?o:0,k=C[P],{main:I,cross:D}=f(k,p,await(null==v.isRTL?void 0:v.isRTL(y.floating)));if(h!==k)return{x:d,y:u,reset:{skip:!1,placement:C[0]}};const A=[_[t(k)],_[I],_[D]],M=[...null!=(s=null==(r=m.autoPlacement)?void 0:r.overflows)?s:[],{placement:k,overflows:A}],E=C[P+1];if(E)return{data:{index:P+1,overflows:M},reset:{skip:!1,placement:E}};const R=M.slice().sort(((e,t)=>e.overflows[0]-t.overflows[0])),O=null==(c=R.find((e=>{let{overflows:t}=e;return t.every((e=>e<=0))})))?void 0:c.placement;return{reset:{placement:null!=O?O:R[0].placement}}}}},e.computePosition=async(e,t,n)=>{const{placement:i="bottom",strategy:o="absolute",middleware:s=[],platform:r}=n,l=await(null==r.isRTL?void 0:r.isRTL(t));let c=await r.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:u}=a(c,i,l),p=i,m={};const f=new Set;for(let n=0;n<s.length;n++){const{name:h,fn:g}=s[n];if(f.has(h))continue;const{x:v,y:T,data:y,reset:S}=await g({x:d,y:u,initialPlacement:i,placement:p,strategy:o,middlewareData:m,rects:c,platform:r,elements:{reference:e,floating:t}});d=null!=v?v:d,u=null!=T?T:u,m={...m,[h]:{...m[h],...y}},S&&("object"==typeof S&&(S.placement&&(p=S.placement),S.rects&&(c=!0===S.rects?await r.getElementRects({reference:e,floating:t,strategy:o}):S.rects),({x:d,y:u}=a(c,p,l)),!1!==S.skip&&f.add(h)),n=-1)}return{x:d,y:u,placement:p,strategy:o,middlewareData:m}},e.detectOverflow=l,e.flip=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(n){var i;const{placement:o,middlewareData:a,rects:s,initialPlacement:r,platform:c,elements:d}=n,{mainAxis:u=!0,crossAxis:p=!0,fallbackPlacements:h,fallbackStrategy:v="bestFit",flipAlignment:T=!0,...y}=e,S=t(o),w=[r,...h||(S!==r&&T?function(e){const t=m(e);return[g(e),t,g(t)]}(r):[m(r)])],b=await l(n,y),x=[];let C=(null==(i=a.flip)?void 0:i.overflows)||[];if(u&&x.push(b[S]),p){const{main:e,cross:t}=f(o,s,await(null==c.isRTL?void 0:c.isRTL(d.floating)));x.push(b[e],b[t])}if(C=[...C,{placement:o,overflows:x}],!x.every((e=>e<=0))){var _,P;const e=(null!=(_=null==(P=a.flip)?void 0:P.index)?_:0)+1,t=w[e];if(t)return{data:{index:e,overflows:C},reset:{skip:!1,placement:t}};let n="bottom";switch(v){case"bestFit":{var k;const e=null==(k=C.slice().sort(((e,t)=>e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)-t.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)))[0])?void 0:k.placement;e&&(n=e);break}case"initialPlacement":n=r}return{reset:{placement:n}}}return{}}}},e.hide=function(e){let{strategy:t="referenceHidden",...n}=void 0===e?{}:e;return{name:"hide",async fn(e){const{rects:i}=e;switch(t){case"referenceHidden":{const t=y(await l(e,{...n,elementContext:"reference"}),i.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:S(t)}}}case"escaped":{const t=y(await l(e,{...n,altBoundary:!0}),i.floating);return{data:{escapedOffsets:t,escaped:S(t)}}}default:return{}}}}},e.inline=function(e){return void 0===e&&(e={}),{name:"inline",options:e,async fn(n){var o;const{placement:a,elements:l,rects:u,platform:p,strategy:m}=n,{padding:f=2,x:h,y:g}=e,v=r(p.convertOffsetParentRelativeRectToViewportRelativeRect?await p.convertOffsetParentRelativeRectToViewportRelativeRect({rect:u.reference,offsetParent:await(null==p.getOffsetParent?void 0:p.getOffsetParent(l.floating)),strategy:m}):u.reference),T=null!=(o=await(null==p.getClientRects?void 0:p.getClientRects(l.reference)))?o:[],y=s(f);return{reset:{rects:await p.getElementRects({reference:{getBoundingClientRect:function(){var e;if(2===T.length&&T[0].left>T[1].right&&null!=h&&null!=g)return null!=(e=T.find((e=>h>e.left-y.left&&h<e.right+y.right&&g>e.top-y.top&&g<e.bottom+y.bottom)))?e:v;if(T.length>=2){if("x"===i(a)){const e=T[0],n=T[T.length-1],i="top"===t(a),o=e.top,s=n.bottom,r=i?e.left:n.left,l=i?e.right:n.right;return{top:o,bottom:s,left:r,right:l,width:l-r,height:s-o,x:r,y:o}}const e="left"===t(a),n=d(...T.map((e=>e.right))),o=c(...T.map((e=>e.left))),s=T.filter((t=>e?t.left===o:t.right===n)),r=s[0].top,l=s[s.length-1].bottom;return{top:r,bottom:l,left:o,right:n,width:n-o,height:l-r,x:o,y:r}}return v}},floating:l.floating,strategy:m})}}}}},e.limitShift=function(e){return void 0===e&&(e={}),{options:e,fn(n){const{x:o,y:a,placement:s,rects:r,middlewareData:l}=n,{offset:c=0,mainAxis:d=!0,crossAxis:u=!0}=e,p={x:o,y:a},m=i(s),f=w(m);let h=p[m],g=p[f];const v="function"==typeof c?c({...r,placement:s}):c,T="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(d){const e="y"===m?"height":"width",t=r.reference[m]-r.floating[e]+T.mainAxis,n=r.reference[m]+r.reference[e]-T.mainAxis;h<t?h=t:h>n&&(h=n)}if(u){var y,S,b,x;const e="y"===m?"width":"height",n=["top","left"].includes(t(s)),i=r.reference[f]-r.floating[e]+(n&&null!=(y=null==(S=l.offset)?void 0:S[f])?y:0)+(n?0:T.crossAxis),o=r.reference[f]+r.reference[e]+(n?0:null!=(b=null==(x=l.offset)?void 0:x[f])?b:0)-(n?T.crossAxis:0);g<i?g=i:g>o&&(g=o)}return{[m]:h,[f]:g}}}},e.offset=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(o){const{x:a,y:s,placement:r,rects:l,platform:c,elements:d}=o,u=function(e,o,a,s){void 0===s&&(s=!1);const r=t(e),l=n(e),c="x"===i(e),d=["left","top"].includes(r)?-1:1;let u=1;"end"===l&&(u=-1),s&&c&&(u*=-1);const p="function"==typeof a?a({...o,placement:e}):a,{mainAxis:m,crossAxis:f}="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};return c?{x:f*u,y:m*d}:{x:m*d,y:f*u}}(r,l,e,await(null==c.isRTL?void 0:c.isRTL(d.floating)));return{x:a+u.x,y:s+u.y,data:u}}}},e.rectToClientRect=r,e.shift=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(n){const{x:o,y:a,placement:s}=n,{mainAxis:r=!0,crossAxis:c=!1,limiter:d={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...p}=e,m={x:o,y:a},f=await l(n,p),h=i(t(s)),g=w(h);let v=m[h],T=m[g];if(r){const e="y"===h?"bottom":"right";v=u(v+f["y"===h?"top":"left"],v,v-f[e])}if(c){const e="y"===g?"bottom":"right";T=u(T+f["y"===g?"top":"left"],T,T-f[e])}const y=d.fn({...n,[h]:v,[g]:T});return{...y,data:{x:y.x-o,y:y.y-a}}}}},e.size=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(i){const{placement:o,rects:a,platform:s,elements:r}=i,{apply:c,...u}=e,p=await l(i,u),m=t(o),f=n(o);let h,g;"top"===m||"bottom"===m?(h=m,g=f===(await(null==s.isRTL?void 0:s.isRTL(r.floating))?"start":"end")?"left":"right"):(g=m,h="end"===f?"top":"bottom");const v=d(p.left,0),T=d(p.right,0),y=d(p.top,0),S=d(p.bottom,0),w={height:a.floating.height-(["left","right"].includes(o)?2*(0!==y||0!==S?y+S:d(p.top,p.bottom)):p[h]),width:a.floating.width-(["top","bottom"].includes(o)?2*(0!==v||0!==T?v+T:d(p.left,p.right)):p[g])};return null==c||c({...w,...a}),{reset:{rects:!0}}}}},Object.defineProperty(e,"__esModule",{value:!0})})),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@floating-ui/core")):"function"==typeof define&&define.amd?define(["exports","@floating-ui/core"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIT4sDOM={},e.FloatingUIT4sCore)}(this,(function(e,t){"use strict";function n(e){return"[object Window]"===(null==e?void 0:e.toString())}function i(e){if(null==e)return window;if(!n(e)){const t=e.ownerDocument;return t&&t.defaultView||window}return e}function o(e){return i(e).getComputedStyle(e)}function a(e){return n(e)?"":e?(e.nodeName||"").toLowerCase():""}function s(e){return e instanceof i(e).HTMLElement}function r(e){return e instanceof i(e).Element}function l(e){return e instanceof i(e).ShadowRoot||e instanceof ShadowRoot}function c(e){const{overflow:t,overflowX:n,overflowY:i}=o(e);return/auto|scroll|overlay|hidden/.test(t+i+n)}function d(e){return["table","td","th"].includes(a(e))}function u(e){const t=navigator.userAgent.toLowerCase().includes("firefox"),n=o(e);return"none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||["transform","perspective"].includes(n.willChange)||t&&"filter"===n.willChange||t&&!!n.filter&&"none"!==n.filter}const p=Math.min,m=Math.max,f=Math.round;function h(e,t){void 0===t&&(t=!1);const n=e.getBoundingClientRect();let i=1,o=1;return t&&s(e)&&(i=e.offsetWidth>0&&f(n.width)/e.offsetWidth||1,o=e.offsetHeight>0&&f(n.height)/e.offsetHeight||1),{width:n.width/i,height:n.height/o,top:n.top/o,right:n.right/i,bottom:n.bottom/o,left:n.left/i,x:n.left/i,y:n.top/o}}function g(e){return(t=e,(t instanceof i(t).Node?e.ownerDocument:e.document)||window.document).documentElement;var t}function v(e){return n(e)?{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}:{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function T(e){return h(g(e)).left+v(e).scrollLeft}function y(e,t,n){const i=s(t),o=g(t),r=h(e,i&&function(e){const t=h(e);return f(t.width)!==e.offsetWidth||f(t.height)!==e.offsetHeight}(t));let l={scrollLeft:0,scrollTop:0};const d={x:0,y:0};if(i||!i&&"fixed"!==n)if(("body"!==a(t)||c(o))&&(l=v(t)),s(t)){const e=h(t,!0);d.x=e.x+t.clientLeft,d.y=e.y+t.clientTop}else o&&(d.x=T(o));return{x:r.left+l.scrollLeft-d.x,y:r.top+l.scrollTop-d.y,width:r.width,height:r.height}}function S(e){return"html"===a(e)?e:e.assignedSlot||e.parentNode||(l(e)?e.host:null)||g(e)}function w(e){return s(e)&&"fixed"!==getComputedStyle(e).position?e.offsetParent:null}function b(e){const t=i(e);let n=w(e);for(;n&&d(n)&&"static"===getComputedStyle(n).position;)n=w(n);return n&&("html"===a(n)||"body"===a(n)&&"static"===getComputedStyle(n).position&&!u(n))?t:n||function(e){let t=S(e);for(l(t)&&(t=t.host);s(t)&&!["html","body"].includes(a(t));){if(u(t))return t;t=t.parentNode}return null}(e)||t}function x(e){if(s(e))return{width:e.offsetWidth,height:e.offsetHeight};const t=h(e);return{width:t.width,height:t.height}}function C(e,t){var n;void 0===t&&(t=[]);const o=function e(t){return["html","body","#document"].includes(a(t))?t.ownerDocument.body:s(t)&&c(t)?t:e(S(t))}(e),r=o===(null==(n=e.ownerDocument)?void 0:n.body),l=i(o),d=r?[l].concat(l.visualViewport||[],c(o)?o:[]):o,u=t.concat(d);return r?u:u.concat(C(S(d)))}function _(e,n){return"viewport"===n?t.rectToClientRect(function(e){const t=i(e),n=g(e),o=t.visualViewport;let a=n.clientWidth,s=n.clientHeight,r=0,l=0;return o&&(a=o.width,s=o.height,Math.abs(t.innerWidth/o.scale-o.width)<.01&&(r=o.offsetLeft,l=o.offsetTop)),{width:a,height:s,x:r,y:l}}(e)):r(n)?function(e){const t=h(e),n=t.top+e.clientTop,i=t.left+e.clientLeft;return{top:n,left:i,x:i,y:n,right:i+e.clientWidth,bottom:n+e.clientHeight,width:e.clientWidth,height:e.clientHeight}}(n):t.rectToClientRect(function(e){var t;const n=g(e),i=v(e),a=null==(t=e.ownerDocument)?void 0:t.body,s=m(n.scrollWidth,n.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),r=m(n.scrollHeight,n.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0);let l=-i.scrollLeft+T(e);const c=-i.scrollTop;return"rtl"===o(a||n).direction&&(l+=m(n.clientWidth,a?a.clientWidth:0)-s),{width:s,height:r,x:l,y:c}}(g(e)))}function P(e){const t=C(S(e)),n=["absolute","fixed"].includes(o(e).position)&&s(e)?b(e):e;return r(n)?t.filter((e=>r(e)&&function(e,t){const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&l(n)){let n=t;do{if(n&&e===n)return!0;n=n.parentNode||n.host}while(n)}return!1}(e,n)&&"body"!==a(e))):[]}const k={getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:i}=e;const o=[..."clippingAncestors"===n?P(t):[].concat(n),i],a=o[0],s=o.reduce(((e,n)=>{const i=_(t,n);return e.top=m(i.top,e.top),e.right=p(i.right,e.right),e.bottom=p(i.bottom,e.bottom),e.left=m(i.left,e.left),e}),_(t,a));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{rect:t,offsetParent:n,strategy:i}=e;const o=s(n),r=g(n);if(n===r)return t;let l={scrollLeft:0,scrollTop:0};const d={x:0,y:0};if((o||!o&&"fixed"!==i)&&(("body"!==a(n)||c(r))&&(l=v(n)),s(n))){const e=h(n,!0);d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}return{...t,x:t.x-l.scrollLeft+d.x,y:t.y-l.scrollTop+d.y}},isElement:r,getDimensions:x,getOffsetParent:b,getDocumentElement:g,getElementRects:e=>{let{reference:t,floating:n,strategy:i}=e;return{reference:y(t,b(n),i),floating:{...x(n),x:0,y:0}}},getClientRects:e=>Array.from(e.getClientRects()),isRTL:e=>"rtl"===o(e).direction};Object.defineProperty(e,"arrow",{enumerable:!0,get:function(){return t.arrow}}),Object.defineProperty(e,"autoPlacement",{enumerable:!0,get:function(){return t.autoPlacement}}),Object.defineProperty(e,"detectOverflow",{enumerable:!0,get:function(){return t.detectOverflow}}),Object.defineProperty(e,"flip",{enumerable:!0,get:function(){return t.flip}}),Object.defineProperty(e,"hide",{enumerable:!0,get:function(){return t.hide}}),Object.defineProperty(e,"inline",{enumerable:!0,get:function(){return t.inline}}),Object.defineProperty(e,"limitShift",{enumerable:!0,get:function(){return t.limitShift}}),Object.defineProperty(e,"offset",{enumerable:!0,get:function(){return t.offset}}),Object.defineProperty(e,"shift",{enumerable:!0,get:function(){return t.shift}}),Object.defineProperty(e,"size",{enumerable:!0,get:function(){return t.size}}),e.autoUpdate=function(e,t,n,i){void 0===i&&(i={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:s=!0,animationFrame:l=!1}=i;let c=!1;const d=o&&!l,u=a&&!l,p=s&&!l,m=d||u?[...r(e)?C(e):[],...C(t)]:[];m.forEach((e=>{d&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)}));let f,g=null;p&&(g=new ResizeObserver(n),r(e)&&g.observe(e),g.observe(t));let v=l?h(e):null;return l&&function t(){if(c)return;const i=h(e);!v||i.x===v.x&&i.y===v.y&&i.width===v.width&&i.height===v.height||n(),v=i,f=requestAnimationFrame(t)}(),()=>{var e;c=!0,m.forEach((e=>{d&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)})),null==(e=g)||e.disconnect(),g=null,l&&cancelAnimationFrame(f)}},e.computePosition=(e,n,i)=>t.computePosition(e,n,{platform:k,...i}),e.getOverflowAncestors=C,Object.defineProperty(e,"__esModule",{value:!0})})),function(e){"use strict";"function"==typeof define&&define.amd?define(["jQuery_T4NT"],e):"object"==typeof module&&module.exports?module.exports=e(require("jQuery_T4NT")):jQuery_T4NT&&!jQuery_T4NT.fn.hoverIntent&&e(jQuery_T4NT)}((function(e){"use strict";function t(e){i=e.pageX,o=e.pageY}function n(e){return"function"==typeof e}var i,o,a={interval:100,sensitivity:6,timeout:0},s=0,r=function(e,n,a,s){if(Math.sqrt((a.pX-i)*(a.pX-i)+(a.pY-o)*(a.pY-o))<s.sensitivity)return n.off(a.event,t),delete a.timeoutId,a.isActive=!0,e.pageX=i,e.pageY=o,delete a.pX,delete a.pY,s.over.apply(n[0],[e]);a.pX=i,a.pY=o,a.timeoutId=setTimeout((function(){r(e,n,a,s)}),s.interval)};e.fn.hoverIntent=function(i,o,l){var c=s++,d=e.extend({},a);function u(n){var i=e.extend({},n),o=e(this),a=o.data("hoverIntent");a||o.data("hoverIntent",a={});var s=a[c];s||(a[c]=s={id:c}),s.timeoutId&&(s.timeoutId=clearTimeout(s.timeoutId));var l=s.event="mousemove.hoverIntent.hoverIntent"+c;if("mouseenter"===n.type){if(s.isActive)return;s.pX=i.pageX,s.pY=i.pageY,o.off(l,t).on(l,t),s.timeoutId=setTimeout((function(){r(i,o,s,d)}),d.interval)}else{if(!s.isActive)return;o.off(l,t),s.timeoutId=setTimeout((function(){!function(e,t,n,i){var o=t.data("hoverIntent");o&&delete o[n.id],i.apply(t[0],[e])}(i,o,s,d.out)}),d.timeout)}}return e.isPlainObject(i)?n((d=e.extend(d,i)).out)||(d.out=d.over):d=n(o)?e.extend(d,{over:i,out:o,selector:l}):e.extend(d,{over:i,out:i,selector:o}),this.on({"mouseenter.hoverIntent":u,"mouseleave.hoverIntent":u},d.selector)}})),function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipe=t()}(this,(function(){"use strict";return function(e,t,n,i){var o={features:null,bind:function(e,t,n,i){var o=(i?"remove":"add")+"EventListener";t=t.split(" ");for(var a=0;a<t.length;a++)t[a]&&e[o](t[a],n,!1)},isArray:function(e){return e instanceof Array},createEl:function(e,t){var n=document.createElement(t||"div");return e&&(n.className=e),n},getScrollY:function(){var e=window.pageYOffset;return void 0!==e?e:document.documentElement.scrollTop},unbind:function(e,t,n){o.bind(e,t,n,!0)},removeClass:function(e,t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(n," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")},addClass:function(e,t){o.hasClass(e,t)||(e.className+=(e.className?" ":"")+t)},hasClass:function(e,t){return e.className&&new RegExp("(^|\\s)"+t+"(\\s|$)").test(e.className)},getChildByClass:function(e,t){for(var n=e.firstChild;n;){if(o.hasClass(n,t))return n;n=n.nextSibling}},arraySearch:function(e,t,n){for(var i=e.length;i--;)if(e[i][n]===t)return i;return-1},extend:function(e,t,n){for(var i in t)if(t.hasOwnProperty(i)){if(n&&e.hasOwnProperty(i))continue;e[i]=t[i]}},easing:{sine:{out:function(e){return Math.sin(e*(Math.PI/2))},inOut:function(e){return-(Math.cos(Math.PI*e)-1)/2}},cubic:{out:function(e){return--e*e*e+1}}},detectFeatures:function(){if(o.features)return o.features;var e=o.createEl().style,t="",n={};if(n.oldIE=document.all&&!document.addEventListener,n.touch="ontouchstart"in window,window.requestAnimationFrame&&(n.raf=window.requestAnimationFrame,n.caf=window.cancelAnimationFrame),n.pointerEvent=!!window.PointerEvent||navigator.msPointerEnabled,!n.pointerEvent){var i=navigator.userAgent;if(/iP(hone|od)/.test(navigator.platform)){var a=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);a&&a.length>0&&((a=parseInt(a[1],10))>=1&&a<8&&(n.isOldIOSPhone=!0))}var s=i.match(/Android\s([0-9\.]*)/),r=s?s[1]:0;(r=parseFloat(r))>=1&&(r<4.4&&(n.isOldAndroid=!0),n.androidVersion=r),n.isMobileOpera=/opera mini|opera mobi/i.test(i)}for(var l,c,d=["transform","perspective","animationName"],u=["","webkit","Moz","ms","O"],p=0;p<4;p++){t=u[p];for(var m=0;m<3;m++)l=d[m],c=t+(t?l.charAt(0).toUpperCase()+l.slice(1):l),!n[l]&&c in e&&(n[l]=c);t&&!n.raf&&(t=t.toLowerCase(),n.raf=window[t+"RequestAnimationFrame"],n.raf&&(n.caf=window[t+"CancelAnimationFrame"]||window[t+"CancelRequestAnimationFrame"]))}if(!n.raf){var f=0;n.raf=function(e){var t=(new Date).getTime(),n=Math.max(0,16-(t-f)),i=window.setTimeout((function(){e(t+n)}),n);return f=t+n,i},n.caf=function(e){clearTimeout(e)}}return n.svg=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,o.features=n,n}};o.detectFeatures(),o.features.oldIE&&(o.bind=function(e,t,n,i){t=t.split(" ");for(var o,a=(i?"detach":"attach")+"Event",s=function(){n.handleEvent.call(n)},r=0;r<t.length;r++)if(o=t[r])if("object"==typeof n&&n.handleEvent){if(i){if(!n["oldIE"+o])return!1}else n["oldIE"+o]=s;e[a]("on"+o,n["oldIE"+o])}else e[a]("on"+o,n)});var a=this,s={allowPanToNext:!0,spacing:.12,bgOpacity:1,mouseUsed:!1,loop:!0,pinchToClose:!0,closeOnScroll:!0,closeOnVerticalDrag:!0,verticalDragRange:.75,hideAnimationDuration:333,showAnimationDuration:333,showHideOpacity:!1,focus:!0,escKey:!0,arrowKeys:!0,mainScrollEndFriction:.35,panEndFriction:.35,isClickableElement:function(e){return"A"===e.tagName},getDoubleTapZoom:function(e,t){return e||t.initialZoomLevel<.7?1:1.33},maxSpreadZoom:1.33,modal:!0,scaleMode:"fit"};o.extend(s,i);var r,l,c,d,u,p,m,f,h,g,v,T,y,S,w,b,x,C,_,P,k,I,D,A,M,E,R,O,N,$,L,F,U,B,H,W,z,j,G,q,Z,K,V,Y,X,J,Q,ee,te,ne,ie,oe,ae,se,re,le,ce={x:0,y:0},de={x:0,y:0},ue={x:0,y:0},pe={},me=0,fe={},he={x:0,y:0},ge=0,ve=!0,Te=[],ye={},Se=!1,we=function(e,t){o.extend(a,t.publicMethods),Te.push(e)},be=function(e){var t=Ht();return e>t-1?e-t:e<0?t+e:e},xe={},Ce=function(e,t){return xe[e]||(xe[e]=[]),xe[e].push(t)},_e=function(e){var t=xe[e];if(t){var n=Array.prototype.slice.call(arguments);n.shift();for(var i=0;i<t.length;i++)t[i].apply(a,n)}},Pe=function(){return(new Date).getTime()},ke=function(e){se=e,a.bg.style.opacity=e*s.bgOpacity},Ie=function(e,t,n,i,o){(!Se||o&&o!==a.currItem)&&(i/=o?o.fitRatio:a.currItem.fitRatio),e[I]=T+t+"px, "+n+"px"+y+" scale("+i+")"},De=function(e){te&&(e&&(g>a.currItem.fitRatio?Se||(Yt(a.currItem,!1,!0),Se=!0):Se&&(Yt(a.currItem),Se=!1)),Ie(te,ue.x,ue.y,g))},Ae=function(e){e.container&&Ie(e.container.style,e.initialPosition.x,e.initialPosition.y,e.initialZoomLevel,e)},Me=function(e,t){t[I]=T+e+"px, 0px"+y},Ee=function(e,t){if(!s.loop&&t){var n=d+(he.x*me-e)/he.x,i=Math.round(e-dt.x);(n<0&&i>0||n>=Ht()-1&&i<0)&&(e=dt.x+i*s.mainScrollEndFriction)}dt.x=e,Me(e,u)},Re=function(e,t){var n=ut[e]-fe[e];return de[e]+ce[e]+n-n*(t/v)},Oe=function(e,t){e.x=t.x,e.y=t.y,t.id&&(e.id=t.id)},Ne=function(e){e.x=Math.round(e.x),e.y=Math.round(e.y)},$e=null,Le=function(){$e&&(o.unbind(document,"mousemove",Le),o.addClass(e,"pswp--has_mouse"),s.mouseUsed=!0,_e("mouseUsed")),$e=setTimeout((function(){$e=null}),100)},Fe=function(e,t){var n=qt(a.currItem,pe,e);return t&&(ee=n),n},Ue=function(e){return e||(e=a.currItem),e.initialZoomLevel},Be=function(e){return e||(e=a.currItem),e.w>0?s.maxSpreadZoom:1},He=function(e,t,n,i){return i===a.currItem.initialZoomLevel?(n[e]=a.currItem.initialPosition[e],!0):(n[e]=Re(e,i),n[e]>t.min[e]?(n[e]=t.min[e],!0):n[e]<t.max[e]&&(n[e]=t.max[e],!0))},We=function(e){var t="";s.escKey&&27===e.keyCode?t="close":s.arrowKeys&&(37===e.keyCode?t="prev":39===e.keyCode&&(t="next")),t&&(e.ctrlKey||e.altKey||e.shiftKey||e.metaKey||(e.preventDefault?e.preventDefault():e.returnValue=!1,a[t]()))},ze=function(e){e&&(K||Z||ne||z)&&(e.preventDefault(),e.stopPropagation())},je=function(){a.setScrollOffset(0,o.getScrollY())},Ge={},qe=0,Ze=function(e){Ge[e]&&(Ge[e].raf&&E(Ge[e].raf),qe--,delete Ge[e])},Ke=function(e){Ge[e]&&Ze(e),Ge[e]||(qe++,Ge[e]={})},Ve=function(){for(var e in Ge)Ge.hasOwnProperty(e)&&Ze(e)},Ye=function(e,t,n,i,o,a,s){var r,l=Pe();Ke(e);var c=function(){if(Ge[e]){if((r=Pe()-l)>=i)return Ze(e),a(n),void(s&&s());a((n-t)*o(r/i)+t),Ge[e].raf=M(c)}};c()},Xe={shout:_e,listen:Ce,viewportSize:pe,options:s,isMainScrollAnimating:function(){return ne},getZoomLevel:function(){return g},getCurrentIndex:function(){return d},isDragging:function(){return G},isZooming:function(){return J},setScrollOffset:function(e,t){fe.x=e,$=fe.y=t,_e("updateScrollOffset",fe)},applyZoomPan:function(e,t,n,i){ue.x=t,ue.y=n,g=e,De(i)},init:function(){if(!r&&!l){var n;a.framework=o,a.template=e,a.bg=o.getChildByClass(e,"pswp__bg"),R=e.className,r=!0,L=o.detectFeatures(),M=L.raf,E=L.caf,I=L.transform,N=L.oldIE,a.scrollWrap=o.getChildByClass(e,"pswp__scroll-wrap"),a.container=o.getChildByClass(a.scrollWrap,"pswp__container"),u=a.container.style,a.itemHolders=b=[{el:a.container.children[0],wrap:0,index:-1},{el:a.container.children[1],wrap:0,index:-1},{el:a.container.children[2],wrap:0,index:-1}],b[0].el.style.display=b[2].el.style.display="none",function(){if(I){var t=L.perspective&&!A;return T="translate"+(t?"3d(":"("),void(y=L.perspective?", 0px)":")")}I="left",o.addClass(e,"pswp--ie"),Me=function(e,t){t.left=e+"px"},Ae=function(e){var t=e.fitRatio>1?1:e.fitRatio,n=e.container.style,i=t*e.w,o=t*e.h;n.width=i+"px",n.height=o+"px",n.left=e.initialPosition.x+"px",n.top=e.initialPosition.y+"px"},De=function(){if(te){var e=te,t=a.currItem,n=t.fitRatio>1?1:t.fitRatio,i=n*t.w,o=n*t.h;e.width=i+"px",e.height=o+"px",e.left=ue.x+"px",e.top=ue.y+"px"}}}(),h={resize:a.updateSize,orientationchange:function(){clearTimeout(F),F=setTimeout((function(){pe.x!==a.scrollWrap.clientWidth&&a.updateSize()}),500)},scroll:je,keydown:We,click:ze};var i=L.isOldIOSPhone||L.isOldAndroid||L.isMobileOpera;for(L.animationName&&L.transform&&!i||(s.showAnimationDuration=s.hideAnimationDuration=0),n=0;n<Te.length;n++)a["init"+Te[n]]();if(t)(a.ui=new t(a,o)).init();_e("firstUpdate"),d=d||s.index||0,(isNaN(d)||d<0||d>=Ht())&&(d=0),a.currItem=Bt(d),(L.isOldIOSPhone||L.isOldAndroid)&&(ve=!1),e.setAttribute("aria-hidden","false"),s.modal&&(ve?e.style.position="fixed":(e.style.position="absolute",e.style.top=o.getScrollY()+"px")),void 0===$&&(_e("initialLayout"),$=O=o.getScrollY());var c="pswp--open ";for(s.mainClass&&(c+=s.mainClass+" "),s.showHideOpacity&&(c+="pswp--animate_opacity "),c+=A?"pswp--touch":"pswp--notouch",c+=L.animationName?" pswp--css_animation":"",c+=L.svg?" pswp--svg":"",o.addClass(e,c),a.updateSize(),p=-1,ge=null,n=0;n<3;n++)Me((n+p)*he.x,b[n].el.style);N||o.bind(a.scrollWrap,f,a),Ce("initialZoomInEnd",(function(){a.setContent(b[0],d-1),a.setContent(b[2],d+1),b[0].el.style.display=b[2].el.style.display="block",s.focus&&e.focus(),o.bind(document,"keydown",a),L.transform&&o.bind(a.scrollWrap,"click",a),s.mouseUsed||o.bind(document,"mousemove",Le),o.bind(window,"resize scroll orientationchange",a),_e("bindEvents")})),a.setContent(b[1],d),a.updateCurrItem(),_e("afterInit"),ve||(S=setInterval((function(){qe||G||J||g!==a.currItem.initialZoomLevel||a.updateSize()}),1e3)),o.addClass(e,"pswp--visible")}},close:function(){r&&(r=!1,l=!0,_e("close"),o.unbind(window,"resize scroll orientationchange",a),o.unbind(window,"scroll",h.scroll),o.unbind(document,"keydown",a),o.unbind(document,"mousemove",Le),L.transform&&o.unbind(a.scrollWrap,"click",a),G&&o.unbind(window,m,a),clearTimeout(F),_e("unbindEvents"),Wt(a.currItem,null,!0,a.destroy))},destroy:function(){_e("destroy"),$t&&clearTimeout($t),e.setAttribute("aria-hidden","true"),e.className=R,S&&clearInterval(S),o.unbind(a.scrollWrap,f,a),o.unbind(window,"scroll",a),ft(),Ve(),xe=null},panTo:function(e,t,n){n||(e>ee.min.x?e=ee.min.x:e<ee.max.x&&(e=ee.max.x),t>ee.min.y?t=ee.min.y:t<ee.max.y&&(t=ee.max.y)),ue.x=e,ue.y=t,De()},handleEvent:function(e){e=e||window.event,h[e.type]&&h[e.type](e)},goTo:function(e){var t=(e=be(e))-d;ge=t,d=e,a.currItem=Bt(d),me-=t,Ee(he.x*me),Ve(),ne=!1,a.updateCurrItem()},next:function(){a.goTo(d+1)},prev:function(){a.goTo(d-1)},updateCurrZoomItem:function(e){if(e&&_e("beforeChange",0),b[1].el.children.length){var t=b[1].el.children[0];te=o.hasClass(t,"pswp__zoom-wrap")?t.style:null}else te=null;ee=a.currItem.bounds,v=g=a.currItem.initialZoomLevel,ue.x=ee.center.x,ue.y=ee.center.y,e&&_e("afterChange")},invalidateCurrItems:function(){w=!0;for(var e=0;e<3;e++)b[e].item&&(b[e].item.needsUpdate=!0)},updateCurrItem:function(e){if(0!==ge){var t,n=Math.abs(ge);if(!(e&&n<2)){a.currItem=Bt(d),Se=!1,_e("beforeChange",ge),n>=3&&(p+=ge+(ge>0?-3:3),n=3);for(var i=0;i<n;i++)ge>0?(t=b.shift(),b[2]=t,p++,Me((p+2)*he.x,t.el.style),a.setContent(t,d-n+i+1+1)):(t=b.pop(),b.unshift(t),p--,Me(p*he.x,t.el.style),a.setContent(t,d+n-i-1-1));if(te&&1===Math.abs(ge)){var o=Bt(x);o.initialZoomLevel!==g&&(qt(o,pe),Yt(o),Ae(o))}ge=0,a.updateCurrZoomItem(),x=d,_e("afterChange")}}},updateSize:function(t){if(!ve&&s.modal){var n=o.getScrollY();if($!==n&&(e.style.top=n+"px",$=n),!t&&ye.x===window.innerWidth&&ye.y===window.innerHeight)return;ye.x=window.innerWidth,ye.y=window.innerHeight,e.style.height=ye.y+"px"}if(pe.x=a.scrollWrap.clientWidth,pe.y=a.scrollWrap.clientHeight,je(),he.x=pe.x+Math.round(pe.x*s.spacing),he.y=pe.y,Ee(he.x*me),_e("beforeResize"),void 0!==p){for(var i,r,l,c=0;c<3;c++)i=b[c],Me((c+p)*he.x,i.el.style),l=d+c-1,s.loop&&Ht()>2&&(l=be(l)),(r=Bt(l))&&(w||r.needsUpdate||!r.bounds)?(a.cleanSlide(r),a.setContent(i,l),1===c&&(a.currItem=r,a.updateCurrZoomItem(!0)),r.needsUpdate=!1):-1===i.index&&l>=0&&a.setContent(i,l),r&&r.container&&(qt(r,pe),Yt(r),Ae(r));w=!1}v=g=a.currItem.initialZoomLevel,(ee=a.currItem.bounds)&&(ue.x=ee.center.x,ue.y=ee.center.y,De(!0)),_e("resize")},zoomTo:function(e,t,n,i,a){t&&(v=g,ut.x=Math.abs(t.x)-ue.x,ut.y=Math.abs(t.y)-ue.y,Oe(de,ue));var s=Fe(e,!1),r={};He("x",s,r,e),He("y",s,r,e);var l=g,c=ue.x,d=ue.y;Ne(r);var u=function(t){1===t?(g=e,ue.x=r.x,ue.y=r.y):(g=(e-l)*t+l,ue.x=(r.x-c)*t+c,ue.y=(r.y-d)*t+d),a&&a(t),De(1===t)};n?Ye("customZoomTo",0,1,n,i||o.easing.sine.inOut,u):u(1)}},Je={},Qe={},et={},tt={},nt={},it=[],ot={},at=[],st={},rt=0,lt={x:0,y:0},ct=0,dt={x:0,y:0},ut={x:0,y:0},pt={x:0,y:0},mt=function(e,t){return st.x=Math.abs(e.x-t.x),st.y=Math.abs(e.y-t.y),Math.sqrt(st.x*st.x+st.y*st.y)},ft=function(){V&&(E(V),V=null)},ht=function(){G&&(V=M(ht),Dt())},gt=function(e,t){return!(!e||e===document)&&!(e.getAttribute("class")&&e.getAttribute("class").indexOf("pswp__scroll-wrap")>-1)&&(t(e)?e:gt(e.parentNode,t))},vt={},Tt=function(e,t){return vt.prevent=!gt(e.target,s.isClickableElement),_e("preventDragEvent",e,t,vt),vt.prevent},yt=function(e,t){return t.x=e.pageX,t.y=e.pageY,t.id=e.identifier,t},St=function(e,t,n){n.x=.5*(e.x+t.x),n.y=.5*(e.y+t.y)},wt=function(){var e=ue.y-a.currItem.initialPosition.y;return 1-Math.abs(e/(pe.y/2))},bt={},xt={},Ct=[],_t=function(e){for(;Ct.length>0;)Ct.pop();return D?(le=0,it.forEach((function(e){0===le?Ct[0]=e:1===le&&(Ct[1]=e),le++}))):e.type.indexOf("touch")>-1?e.touches&&e.touches.length>0&&(Ct[0]=yt(e.touches[0],bt),e.touches.length>1&&(Ct[1]=yt(e.touches[1],xt))):(bt.x=e.pageX,bt.y=e.pageY,bt.id="",Ct[0]=bt),Ct},Pt=function(e,t){var n,i,o,r,l=ue[e]+t[e],c=t[e]>0,d=dt.x+t.x,u=dt.x-ot.x;return n=l>ee.min[e]||l<ee.max[e]?s.panEndFriction:1,l=ue[e]+t[e]*n,!s.allowPanToNext&&g!==a.currItem.initialZoomLevel||(te?"h"!==ie||"x"!==e||Z||(c?(l>ee.min[e]&&(n=s.panEndFriction,ee.min[e]-l,i=ee.min[e]-de[e]),(i<=0||u<0)&&Ht()>1?(r=d,u<0&&d>ot.x&&(r=ot.x)):ee.min.x!==ee.max.x&&(o=l)):(l<ee.max[e]&&(n=s.panEndFriction,l-ee.max[e],i=de[e]-ee.max[e]),(i<=0||u>0)&&Ht()>1?(r=d,u>0&&d<ot.x&&(r=ot.x)):ee.min.x!==ee.max.x&&(o=l))):r=d,"x"!==e)?void(ne||Y||g>a.currItem.fitRatio&&(ue[e]+=t[e]*n)):(void 0!==r&&(Ee(r,!0),Y=r!==ot.x),ee.min.x!==ee.max.x&&(void 0!==o?ue.x=o:Y||(ue.x+=t.x*n)),void 0!==r)},kt=function(e){if(!("mousedown"===e.type&&e.button>0)){if(Ut)return void e.preventDefault();if(!j||"mousedown"!==e.type){if(Tt(e,!0)&&e.preventDefault(),_e("pointerDown"),D){var t=o.arraySearch(it,e.pointerId,"id");t<0&&(t=it.length),it[t]={x:e.pageX,y:e.pageY,id:e.pointerId}}var n=_t(e),i=n.length;X=null,Ve(),G&&1!==i||(G=oe=!0,o.bind(window,m,a),W=re=ae=z=Y=K=q=Z=!1,ie=null,_e("firstTouchStart",n),Oe(de,ue),ce.x=ce.y=0,Oe(tt,n[0]),Oe(nt,tt),ot.x=he.x*me,at=[{x:tt.x,y:tt.y}],B=U=Pe(),Fe(g,!0),ft(),ht()),!J&&i>1&&!ne&&!Y&&(v=g,Z=!1,J=q=!0,ce.y=ce.x=0,Oe(de,ue),Oe(Je,n[0]),Oe(Qe,n[1]),St(Je,Qe,pt),ut.x=Math.abs(pt.x)-ue.x,ut.y=Math.abs(pt.y)-ue.y,Q=mt(Je,Qe))}}},It=function(e){if(e.preventDefault(),D){var t=o.arraySearch(it,e.pointerId,"id");if(t>-1){var n=it[t];n.x=e.pageX,n.y=e.pageY}}if(G){var i=_t(e);if(ie||K||J)X=i;else if(dt.x!==he.x*me)ie="h";else{var a=Math.abs(i[0].x-tt.x)-Math.abs(i[0].y-tt.y);Math.abs(a)>=10&&(ie=a>0?"h":"v",X=i)}}},Dt=function(){if(X){var e=X.length;if(0!==e)if(Oe(Je,X[0]),et.x=Je.x-tt.x,et.y=Je.y-tt.y,J&&e>1){if(tt.x=Je.x,tt.y=Je.y,!et.x&&!et.y&&function(e,t){return e.x===t.x&&e.y===t.y}(X[1],Qe))return;Oe(Qe,X[1]),Z||(Z=!0,_e("zoomGestureStarted"));var t=mt(Je,Qe),n=Ot(t);n>a.currItem.initialZoomLevel+a.currItem.initialZoomLevel/15&&(re=!0);var i=1,o=Ue(),r=Be();if(n<o)if(s.pinchToClose&&!re&&v<=a.currItem.initialZoomLevel){var l=1-(o-n)/(o/1.2);ke(l),_e("onPinchClose",l),ae=!0}else(i=(o-n)/o)>1&&(i=1),n=o-i*(o/3);else n>r&&((i=(n-r)/(6*o))>1&&(i=1),n=r+i*o);i<0&&(i=0),t,St(Je,Qe,lt),ce.x+=lt.x-pt.x,ce.y+=lt.y-pt.y,Oe(pt,lt),ue.x=Re("x",n),ue.y=Re("y",n),W=n>g,g=n,De()}else{if(!ie)return;if(oe&&(oe=!1,Math.abs(et.x)>=10&&(et.x-=X[0].x-nt.x),Math.abs(et.y)>=10&&(et.y-=X[0].y-nt.y)),tt.x=Je.x,tt.y=Je.y,0===et.x&&0===et.y)return;if("v"===ie&&s.closeOnVerticalDrag&&"fit"===s.scaleMode&&g===a.currItem.initialZoomLevel){ce.y+=et.y,ue.y+=et.y;var c=wt();return z=!0,_e("onVerticalDrag",c),ke(c),void De()}(function(e,t,n){if(e-B>50){var i=at.length>2?at.shift():{};i.x=t,i.y=n,at.push(i),B=e}})(Pe(),Je.x,Je.y),K=!0,ee=a.currItem.bounds,Pt("x",et)||(Pt("y",et),Ne(ue),De())}}},At=function(e){if(L.isOldAndroid){if(j&&"mouseup"===e.type)return;e.type.indexOf("touch")>-1&&(clearTimeout(j),j=setTimeout((function(){j=0}),600))}var t;if(_e("pointerUp"),Tt(e,!1)&&e.preventDefault(),D){var n=o.arraySearch(it,e.pointerId,"id");if(n>-1)if(t=it.splice(n,1)[0],navigator.msPointerEnabled){t.type={4:"mouse",2:"touch",3:"pen"}[e.pointerType],t.type||(t.type=e.pointerType||"mouse")}else t.type=e.pointerType||"mouse"}var i,r=_t(e),l=r.length;if("mouseup"===e.type&&(l=0),2===l)return X=null,!0;1===l&&Oe(nt,r[0]),0!==l||ie||ne||(t||("mouseup"===e.type?t={x:e.pageX,y:e.pageY,type:"mouse"}:e.changedTouches&&e.changedTouches[0]&&(t={x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY,type:"touch"})),_e("touchRelease",e,t));var c=-1;if(0===l&&(G=!1,o.unbind(window,m,a),ft(),J?c=0:-1!==ct&&(c=Pe()-ct)),ct=1===l?Pe():-1,i=-1!==c&&c<150?"zoom":"swipe",J&&l<2&&(J=!1,1===l&&(i="zoomPointerUp"),_e("zoomGestureEnded")),X=null,K||Z||ne||z)if(Ve(),H||(H=Mt()),H.calculateSwipeSpeed("x"),z){if(wt()<s.verticalDragRange)a.close();else{var d=ue.y,u=se;Ye("verticalDrag",0,1,300,o.easing.cubic.out,(function(e){ue.y=(a.currItem.initialPosition.y-d)*e+d,ke((1-u)*e+u),De()})),_e("onVerticalDrag",1)}}else{if((Y||ne)&&0===l){if(Rt(i,H))return;i="zoomPointerUp"}if(!ne)return"swipe"!==i?void Nt():void(!Y&&g>a.currItem.fitRatio&&Et(H))}},Mt=function(){var e,t,n={lastFlickOffset:{},lastFlickDist:{},lastFlickSpeed:{},slowDownRatio:{},slowDownRatioReverse:{},speedDecelerationRatio:{},speedDecelerationRatioAbs:{},distanceOffset:{},backAnimDestination:{},backAnimStarted:{},calculateSwipeSpeed:function(i){at.length>1?(e=Pe()-B+50,t=at[at.length-2][i]):(e=Pe()-U,t=nt[i]),n.lastFlickOffset[i]=tt[i]-t,n.lastFlickDist[i]=Math.abs(n.lastFlickOffset[i]),n.lastFlickDist[i]>20?n.lastFlickSpeed[i]=n.lastFlickOffset[i]/e:n.lastFlickSpeed[i]=0,Math.abs(n.lastFlickSpeed[i])<.1&&(n.lastFlickSpeed[i]=0),n.slowDownRatio[i]=.95,n.slowDownRatioReverse[i]=1-n.slowDownRatio[i],n.speedDecelerationRatio[i]=1},calculateOverBoundsAnimOffset:function(e,t){n.backAnimStarted[e]||(ue[e]>ee.min[e]?n.backAnimDestination[e]=ee.min[e]:ue[e]<ee.max[e]&&(n.backAnimDestination[e]=ee.max[e]),void 0!==n.backAnimDestination[e]&&(n.slowDownRatio[e]=.7,n.slowDownRatioReverse[e]=1-n.slowDownRatio[e],n.speedDecelerationRatioAbs[e]<.05&&(n.lastFlickSpeed[e]=0,n.backAnimStarted[e]=!0,Ye("bounceZoomPan"+e,ue[e],n.backAnimDestination[e],t||300,o.easing.sine.out,(function(t){ue[e]=t,De()})))))},calculateAnimOffset:function(e){n.backAnimStarted[e]||(n.speedDecelerationRatio[e]=n.speedDecelerationRatio[e]*(n.slowDownRatio[e]+n.slowDownRatioReverse[e]-n.slowDownRatioReverse[e]*n.timeDiff/10),n.speedDecelerationRatioAbs[e]=Math.abs(n.lastFlickSpeed[e]*n.speedDecelerationRatio[e]),n.distanceOffset[e]=n.lastFlickSpeed[e]*n.speedDecelerationRatio[e]*n.timeDiff,ue[e]+=n.distanceOffset[e])},panAnimLoop:function(){if(Ge.zoomPan&&(Ge.zoomPan.raf=M(n.panAnimLoop),n.now=Pe(),n.timeDiff=n.now-n.lastNow,n.lastNow=n.now,n.calculateAnimOffset("x"),n.calculateAnimOffset("y"),De(),n.calculateOverBoundsAnimOffset("x"),n.calculateOverBoundsAnimOffset("y"),n.speedDecelerationRatioAbs.x<.05&&n.speedDecelerationRatioAbs.y<.05))return ue.x=Math.round(ue.x),ue.y=Math.round(ue.y),De(),void Ze("zoomPan")}};return n},Et=function(e){return e.calculateSwipeSpeed("y"),ee=a.currItem.bounds,e.backAnimDestination={},e.backAnimStarted={},Math.abs(e.lastFlickSpeed.x)<=.05&&Math.abs(e.lastFlickSpeed.y)<=.05?(e.speedDecelerationRatioAbs.x=e.speedDecelerationRatioAbs.y=0,e.calculateOverBoundsAnimOffset("x"),e.calculateOverBoundsAnimOffset("y"),!0):(Ke("zoomPan"),e.lastNow=Pe(),void e.panAnimLoop())},Rt=function(e,t){var n,i,r;if(ne||(rt=d),"swipe"===e){var l=tt.x-nt.x,c=t.lastFlickDist.x<10;l>30&&(c||t.lastFlickOffset.x>20)?i=-1:l<-30&&(c||t.lastFlickOffset.x<-20)&&(i=1)}i&&((d+=i)<0?(d=s.loop?Ht()-1:0,r=!0):d>=Ht()&&(d=s.loop?0:Ht()-1,r=!0),r&&!s.loop||(ge+=i,me-=i,n=!0));var u,p=he.x*me,m=Math.abs(p-dt.x);return n||p>dt.x==t.lastFlickSpeed.x>0?(u=Math.abs(t.lastFlickSpeed.x)>0?m/Math.abs(t.lastFlickSpeed.x):333,u=Math.min(u,400),u=Math.max(u,250)):u=333,rt===d&&(n=!1),ne=!0,_e("mainScrollAnimStart"),Ye("mainScroll",dt.x,p,u,o.easing.cubic.out,Ee,(function(){Ve(),ne=!1,rt=-1,(n||rt!==d)&&a.updateCurrItem(),_e("mainScrollAnimComplete")})),n&&a.updateCurrItem(!0),n},Ot=function(e){return 1/Q*e*v},Nt=function(){var e=g,t=Ue(),n=Be();g<t?e=t:g>n&&(e=n);var i,s=se;return ae&&!W&&!re&&g<t?(a.close(),!0):(ae&&(i=function(e){ke((1-s)*e+s)}),a.zoomTo(e,0,200,o.easing.cubic.out,i),!0)};we("Gestures",{publicMethods:{initGestures:function(){var e=function(e,t,n,i,o){C=e+t,_=e+n,P=e+i,k=o?e+o:""};(D=L.pointerEvent)&&L.touch&&(L.touch=!1),D?navigator.msPointerEnabled?e("MSPointer","Down","Move","Up","Cancel"):e("pointer","down","move","up","cancel"):L.touch?(e("touch","start","move","end","cancel"),A=!0):e("mouse","down","move","up"),m=_+" "+P+" "+k,f=C,D&&!A&&(A=navigator.maxTouchPoints>1||navigator.msMaxTouchPoints>1),a.likelyTouchDevice=A,h[C]=kt,h[_]=It,h[P]=At,k&&(h[k]=h[P]),L.touch&&(f+=" mousedown",m+=" mousemove mouseup",h.mousedown=h[C],h.mousemove=h[_],h.mouseup=h[P]),A||(s.allowPanToNext=!1)}}});var $t,Lt,Ft,Ut,Bt,Ht,Wt=function(t,n,i,r){var l;$t&&clearTimeout($t),Ut=!0,Ft=!0,t.initialLayout?(l=t.initialLayout,t.initialLayout=null):l=s.getThumbBoundsFn&&s.getThumbBoundsFn(d);var u=i?s.hideAnimationDuration:s.showAnimationDuration,p=function(){Ze("initialZoom"),i?(a.template.removeAttribute("style"),a.bg.removeAttribute("style")):(ke(1),n&&(n.style.display="block"),o.addClass(e,"pswp--animated-in"),_e("initialZoom"+(i?"OutEnd":"InEnd"))),r&&r(),Ut=!1};if(!u||!l||void 0===l.x)return _e("initialZoom"+(i?"Out":"In")),g=t.initialZoomLevel,Oe(ue,t.initialPosition),De(),e.style.opacity=i?0:1,ke(1),void(u?setTimeout((function(){p()}),u):p());!function(){var n=c,r=!a.currItem.src||a.currItem.loadError||s.showHideOpacity;t.miniImg&&(t.miniImg.style.webkitBackfaceVisibility="hidden"),i||(g=l.w/t.w,ue.x=l.x,ue.y=l.y-O,a[r?"template":"bg"].style.opacity=.001,De()),Ke("initialZoom"),i&&!n&&o.removeClass(e,"pswp--animated-in"),r&&(i?o[(n?"remove":"add")+"Class"](e,"pswp--animate_opacity"):setTimeout((function(){o.addClass(e,"pswp--animate_opacity")}),30)),$t=setTimeout((function(){if(_e("initialZoom"+(i?"Out":"In")),i){var a=l.w/t.w,s={x:ue.x,y:ue.y},c=g,d=se,m=function(t){1===t?(g=a,ue.x=l.x,ue.y=l.y-$):(g=(a-c)*t+c,ue.x=(l.x-s.x)*t+s.x,ue.y=(l.y-$-s.y)*t+s.y),De(),r?e.style.opacity=1-t:ke(d-t*d)};n?Ye("initialZoom",0,1,u,o.easing.cubic.out,m,p):(m(1),$t=setTimeout(p,u+20))}else g=t.initialZoomLevel,Oe(ue,t.initialPosition),De(),ke(1),r?e.style.opacity=1:ke(1),$t=setTimeout(p,u+20)}),i?25:90)}()},zt={},jt=[],Gt={index:0,errorMsg:'<div class="pswp__error-msg"><a href="%url%" target="_blank">The image</a> could not be loaded.</div>',forceProgressiveLoading:!1,preload:[1,1],getNumItemsFn:function(){return Lt.length}},qt=function(e,t,n){if(e.src&&!e.loadError){var i=!n;if(i&&(e.vGap||(e.vGap={top:0,bottom:0}),_e("parseVerticalMargin",e)),zt.x=t.x,zt.y=t.y-e.vGap.top-e.vGap.bottom,i){var o=zt.x/e.w,a=zt.y/e.h;e.fitRatio=o<a?o:a;var r=s.scaleMode;"orig"===r?n=1:"fit"===r&&(n=e.fitRatio),n>1&&(n=1),e.initialZoomLevel=n,e.bounds||(e.bounds={center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}})}if(!n)return;return function(e,t,n){var i=e.bounds;i.center.x=Math.round((zt.x-t)/2),i.center.y=Math.round((zt.y-n)/2)+e.vGap.top,i.max.x=t>zt.x?Math.round(zt.x-t):i.center.x,i.max.y=n>zt.y?Math.round(zt.y-n)+e.vGap.top:i.center.y,i.min.x=t>zt.x?0:i.center.x,i.min.y=n>zt.y?e.vGap.top:i.center.y}(e,e.w*n,e.h*n),i&&n===e.initialZoomLevel&&(e.initialPosition=e.bounds.center),e.bounds}return e.w=e.h=0,e.initialZoomLevel=e.fitRatio=1,e.bounds={center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}},e.initialPosition=e.bounds.center,e.bounds},Zt=function(e,t,n,i,o,s){t.loadError||i&&(t.imageAppended=!0,Yt(t,i,t===a.currItem&&Se),n.appendChild(i),s&&setTimeout((function(){t&&t.loaded&&t.placeholder&&(t.placeholder.style.display="none",t.placeholder=null)}),500))},Kt=function(e){e.loading=!0,e.loaded=!1;var t=e.img=o.createEl("pswp__img","img"),n=function(){e.loading=!1,e.loaded=!0,e.loadComplete?e.loadComplete(e):e.img=null,t.onload=t.onerror=null,t=null};return t.onload=n,t.onerror=function(){e.loadError=!0,n()},t.src=e.src,t},Vt=function(e,t){if(e.src&&e.loadError&&e.container)return t&&(e.container.innerHTML=""),e.container.innerHTML=s.errorMsg.replace("%url%",e.src),!0},Yt=function(e,t,n){if(e.src){t||(t=e.container.lastChild);var i=n?e.w:Math.round(e.w*e.fitRatio),o=n?e.h:Math.round(e.h*e.fitRatio);e.placeholder&&!e.loaded&&(e.placeholder.style.width=i+"px",e.placeholder.style.height=o+"px"),t.style.width=i+"px",t.style.height=o+"px"}},Xt=function(){if(jt.length){for(var e,t=0;t<jt.length;t++)(e=jt[t]).holder.index===e.index&&Zt(e.index,e.item,e.baseDiv,e.img,0,e.clearPlaceholder);jt=[]}};we("Controller",{publicMethods:{lazyLoadItem:function(e){e=be(e);var t=Bt(e);t&&(!t.loaded&&!t.loading||w)&&(_e("gettingData",e,t),t.src&&Kt(t))},initController:function(){o.extend(s,Gt,!0),a.items=Lt=n,Bt=a.getItemAt,Ht=s.getNumItemsFn,s.loop,Ht()<3&&(s.loop=!1),Ce("beforeChange",(function(e){var t,n=s.preload,i=null===e||e>=0,o=Math.min(n[0],Ht()),r=Math.min(n[1],Ht());for(t=1;t<=(i?r:o);t++)a.lazyLoadItem(d+t);for(t=1;t<=(i?o:r);t++)a.lazyLoadItem(d-t)})),Ce("initialLayout",(function(){a.currItem.initialLayout=s.getThumbBoundsFn&&s.getThumbBoundsFn(d)})),Ce("mainScrollAnimComplete",Xt),Ce("initialZoomInEnd",Xt),Ce("destroy",(function(){for(var e,t=0;t<Lt.length;t++)(e=Lt[t]).container&&(e.container=null),e.placeholder&&(e.placeholder=null),e.img&&(e.img=null),e.preloader&&(e.preloader=null),e.loadError&&(e.loaded=e.loadError=!1);jt=null}))},getItemAt:function(e){return e>=0&&void 0!==Lt[e]&&Lt[e]},allowProgressiveImg:function(){return s.forceProgressiveLoading||!A||s.mouseUsed||screen.width>1200},setContent:function(e,t){s.loop&&(t=be(t));var n=a.getItemAt(e.index);n&&(n.container=null);var i,l=a.getItemAt(t);if(l){_e("gettingData",t,l),e.index=t,e.item=l;var c=l.container=o.createEl("pswp__zoom-wrap");if(!l.src&&l.html&&(l.html.tagName?c.appendChild(l.html):c.innerHTML=l.html),Vt(l),qt(l,pe),!l.src||l.loadError||l.loaded)l.src&&!l.loadError&&((i=o.createEl("pswp__img","img")).style.opacity=1,i.src=l.src,Yt(l,i),Zt(0,l,c,i));else{if(l.loadComplete=function(n){if(r){if(e&&e.index===t){if(Vt(n,!0))return n.loadComplete=n.img=null,qt(n,pe),Ae(n),void(e.index===d&&a.updateCurrZoomItem());n.imageAppended?!Ut&&n.placeholder&&(n.placeholder.style.display="none",n.placeholder=null):L.transform&&(ne||Ut)?jt.push({item:n,baseDiv:c,img:n.img,index:t,holder:e,clearPlaceholder:!0}):Zt(0,n,c,n.img,0,!0)}n.loadComplete=null,n.img=null,_e("imageLoadComplete",t,n)}},o.features.transform){var u="pswp__img pswp__img--placeholder";u+=l.msrc?"":" pswp__img--placeholder--blank";var p=o.createEl(u,l.msrc?"img":"");l.msrc&&(p.src=l.msrc),Yt(l,p),c.appendChild(p),l.placeholder=p}l.loading||Kt(l),a.allowProgressiveImg()&&(!Ft&&L.transform?jt.push({item:l,baseDiv:c,img:l.img,index:t,holder:e}):Zt(0,l,c,l.img,0,!0))}Ft||t!==d?Ae(l):(te=c.style,Wt(l,i||l.img)),e.el.innerHTML="",e.el.appendChild(c)}else e.el.innerHTML=""},cleanSlide:function(e){e.img&&(e.img.onload=e.img.onerror=null),e.loaded=e.loading=e.img=e.imageAppended=!1}}});var Jt,Qt,en={},tn=function(e,t,n){var i=document.createEvent("CustomEvent"),o={origEvent:e,target:e.target,releasePoint:t,pointerType:n||"touch"};i.initCustomEvent("pswpTap",!0,!0,o),e.target.dispatchEvent(i)};we("Tap",{publicMethods:{initTap:function(){Ce("firstTouchStart",a.onTapStart),Ce("touchRelease",a.onTapRelease),Ce("destroy",(function(){en={},Jt=null}))},onTapStart:function(e){e.length>1&&(clearTimeout(Jt),Jt=null)},onTapRelease:function(e,t){if(t&&!K&&!q&&!qe){var n=t;if(Jt&&(clearTimeout(Jt),Jt=null,function(e,t){return Math.abs(e.x-t.x)<25&&Math.abs(e.y-t.y)<25}(n,en)))return void _e("doubleTap",n);if("mouse"===t.type)return void tn(e,t,"mouse");if("BUTTON"===e.target.tagName.toUpperCase()||o.hasClass(e.target,"pswp__single-tap"))return void tn(e,t);Oe(en,n),Jt=setTimeout((function(){tn(e,t),Jt=null}),300)}}}}),we("DesktopZoom",{publicMethods:{initDesktopZoom:function(){N||(A?Ce("mouseUsed",(function(){a.setupDesktopZoom()})):a.setupDesktopZoom(!0))},setupDesktopZoom:function(t){Qt={};var n="wheel mousewheel DOMMouseScroll";Ce("bindEvents",(function(){o.bind(e,n,a.handleMouseWheel)})),Ce("unbindEvents",(function(){Qt&&o.unbind(e,n,a.handleMouseWheel)})),a.mouseZoomedIn=!1;var i,s=function(){a.mouseZoomedIn&&(o.removeClass(e,"pswp--zoomed-in"),a.mouseZoomedIn=!1),g<1?o.addClass(e,"pswp--zoom-allowed"):o.removeClass(e,"pswp--zoom-allowed"),r()},r=function(){i&&(o.removeClass(e,"pswp--dragging"),i=!1)};Ce("resize",s),Ce("afterChange",s),Ce("pointerDown",(function(){a.mouseZoomedIn&&(i=!0,o.addClass(e,"pswp--dragging"))})),Ce("pointerUp",r),t||s()},handleMouseWheel:function(e){if(g<=a.currItem.fitRatio)return s.modal&&(!s.closeOnScroll||qe||G?e.preventDefault():I&&Math.abs(e.deltaY)>2&&(c=!0,a.close())),!0;if(e.stopPropagation(),Qt.x=0,"deltaX"in e)1===e.deltaMode?(Qt.x=18*e.deltaX,Qt.y=18*e.deltaY):(Qt.x=e.deltaX,Qt.y=e.deltaY);else if("wheelDelta"in e)e.wheelDeltaX&&(Qt.x=-.16*e.wheelDeltaX),e.wheelDeltaY?Qt.y=-.16*e.wheelDeltaY:Qt.y=-.16*e.wheelDelta;else{if(!("detail"in e))return;Qt.y=e.detail}Fe(g,!0);var t=ue.x-Qt.x,n=ue.y-Qt.y;(s.modal||t<=ee.min.x&&t>=ee.max.x&&n<=ee.min.y&&n>=ee.max.y)&&e.preventDefault(),a.panTo(t,n)},toggleDesktopZoom:function(t){t=t||{x:pe.x/2+fe.x,y:pe.y/2+fe.y};var n=s.getDoubleTapZoom(!0,a.currItem),i=g===n;a.mouseZoomedIn=!i,a.zoomTo(i?a.currItem.initialZoomLevel:n,t,333),o[(i?"remove":"add")+"Class"](e,"pswp--zoomed-in")}}});var nn,on,an,sn,rn,ln,cn,dn,un,pn,mn,fn,hn={history:!0,galleryUID:1},gn=function(){return mn.hash.substring(1)},vn=function(){nn&&clearTimeout(nn),an&&clearTimeout(an)},Tn=function(){var e=gn(),t={};if(e.length<5)return t;var n,i=e.split("&");for(n=0;n<i.length;n++)if(i[n]){var o=i[n].split("=");o.length<2||(t[o[0]]=o[1])}if(s.galleryPIDs){var a=t.pid;for(t.pid=0,n=0;n<Lt.length;n++)if(Lt[n].pid===a){t.pid=n;break}}else t.pid=parseInt(t.pid,10)-1;return t.pid<0&&(t.pid=0),t},yn=function(){if(an&&clearTimeout(an),qe||G)an=setTimeout(yn,500);else{sn?clearTimeout(on):sn=!0;var e=d+1,t=Bt(d);t.hasOwnProperty("pid")&&(e=t.pid);var n=cn+"&gid="+s.galleryUID+"&pid="+e;dn||-1===mn.hash.indexOf(n)&&(pn=!0);var i=mn.href.split("#")[0]+"#"+n;fn?"#"+n!==window.location.hash&&history[dn?"replaceState":"pushState"]("",document.title,i):dn?mn.replace(i):mn.hash=n,dn=!0,on=setTimeout((function(){sn=!1}),60)}};we("History",{publicMethods:{initHistory:function(){if(o.extend(s,hn,!0),s.history){mn=window.location,pn=!1,un=!1,dn=!1,cn=gn(),fn="pushState"in history,cn.indexOf("gid=")>-1&&(cn=(cn=cn.split("&gid=")[0]).split("?gid=")[0]),Ce("afterChange",a.updateURL),Ce("unbindEvents",(function(){o.unbind(window,"hashchange",a.onHashChange)}));var e=function(){ln=!0,un||(pn?history.back():cn?mn.hash=cn:fn?history.pushState("",document.title,mn.pathname+mn.search):mn.hash=""),vn()};Ce("unbindEvents",(function(){c&&e()})),Ce("destroy",(function(){ln||e()})),Ce("firstUpdate",(function(){d=Tn().pid}));var t=cn.indexOf("pid=");t>-1&&("&"===(cn=cn.substring(0,t)).slice(-1)&&(cn=cn.slice(0,-1))),setTimeout((function(){r&&o.bind(window,"hashchange",a.onHashChange)}),40)}},onHashChange:function(){return gn()===cn?(un=!0,void a.close()):void(sn||(rn=!0,a.goTo(Tn().pid),rn=!1))},updateURL:function(){vn(),rn||(dn?nn=setTimeout(yn,800):yn())}}}),o.extend(a,Xe)}})),function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipeUI_Default=t()}(this,(function(){"use strict";return function(e,t){var n,i,o,a,s,r,l,c,d,u,p,m,f,h,g,v,T,y,S=this,w=!1,b=!0,x=!0,C={barsSize:{top:44,bottom:"auto"},closeElClasses:["item","caption","zoom-wrap","ui","top-bar"],timeToIdle:4e3,timeToIdleOutside:1e3,loadingIndicatorDelay:1e3,addCaptionHTMLFn:function(e,t){return e.title?(t.children[0].innerHTML=e.title,!0):(t.children[0].innerHTML="",!1)},closeEl:!0,captionEl:!0,fullscreenEl:!0,zoomEl:!0,shareEl:!0,counterEl:!0,arrowEl:!0,preloaderEl:!0,tapToClose:!1,tapToToggleControls:!0,clickToCloseNonZoomable:!0,shareButtons:[{id:"facebook",label:"Share on Facebook",url:"https://www.facebook.com/sharer/sharer.php?u={{url}}"},{id:"twitter",label:"Tweet",url:"https://twitter.com/intent/tweet?text={{text}}&url={{url}}"},{id:"pinterest",label:"Pin it",url:"http://www.pinterest.com/pin/create/button/?url={{url}}&media={{image_url}}&description={{text}}"},{id:"download",label:"Download image",url:"{{raw_image_url}}",download:!0}],getImageURLForShare:function(){return e.currItem.src||""},getPageURLForShare:function(){return window.location.href},getTextForShare:function(){return e.currItem.title||""},indexIndicatorSep:" / ",fitControlsWidth:1200},_=function(e){if(v)return!0;e=e||window.event,g.timeToIdle&&g.mouseUsed&&!d&&N();for(var n,i,o=(e.target||e.srcElement).getAttribute("class")||"",a=0;a<U.length;a++)(n=U[a]).onTap&&o.indexOf("pswp__"+n.name)>-1&&(n.onTap(),i=!0);if(i){e.stopPropagation&&e.stopPropagation(),v=!0;var s=t.features.isOldAndroid?600:30;setTimeout((function(){v=!1}),s)}},P=function(){return!e.likelyTouchDevice||g.mouseUsed||screen.width>g.fitControlsWidth},k=function(e,n,i){t[(i?"add":"remove")+"Class"](e,"pswp__"+n)},I=function(){var e=1===g.getNumItemsFn();e!==h&&(k(i,"ui--one-slide",e),h=e)},D=function(){k(l,"share-modal--hidden",x)},A=function(){return(x=!x)?(t.removeClass(l,"pswp__share-modal--fade-in"),setTimeout((function(){x&&D()}),300)):(D(),setTimeout((function(){x||t.addClass(l,"pswp__share-modal--fade-in")}),30)),x||E(),!1},M=function(t){var n=(t=t||window.event).target||t.srcElement;return e.shout("shareLinkClick",t,n),!(!n.href||!n.hasAttribute("download")&&(window.open(n.href,"pswp_share","scrollbars=yes,resizable=yes,toolbar=no,location=yes,width=550,height=420,top=100,left="+(window.screen?Math.round(screen.width/2-275):100)),x||A(),1))},E=function(){for(var e,t,n,i,o="",a=0;a<g.shareButtons.length;a++)e=g.shareButtons[a],t=g.getImageURLForShare(e),n=g.getPageURLForShare(e),i=g.getTextForShare(e),o+='<a href="'+e.url.replace("{{url}}",encodeURIComponent(n)).replace("{{image_url}}",encodeURIComponent(t)).replace("{{raw_image_url}}",t).replace("{{text}}",encodeURIComponent(i))+'" target="_blank" class="pswp__share--'+e.id+'"'+(e.download?"download":"")+">"+e.label+"</a>",g.parseShareButtonOut&&(o=g.parseShareButtonOut(e,o));l.children[0].innerHTML=o,l.children[0].onclick=M},R=function(e){for(var n=0;n<g.closeElClasses.length;n++)if(t.hasClass(e,"pswp__"+g.closeElClasses[n]))return!0},O=0,N=function(){clearTimeout(y),O=0,d&&S.setIdle(!1)},$=function(e){var t=(e=e||window.event).relatedTarget||e.toElement;t&&"HTML"!==t.nodeName||(clearTimeout(y),y=setTimeout((function(){S.setIdle(!0)}),g.timeToIdleOutside))},L=function(e){m!==e&&(k(p,"preloader--active",!e),m=e)},F=function(e){var n=e.vGap;if(P()){var s=g.barsSize;if(g.captionEl&&"auto"===s.bottom)if(a||((a=t.createEl("pswp__caption pswp__caption--fake")).appendChild(t.createEl("pswp__caption__center")),i.insertBefore(a,o),t.addClass(i,"pswp__ui--fit")),g.addCaptionHTMLFn(e,a,!0)){var r=a.clientHeight;n.bottom=parseInt(r,10)||44}else n.bottom=s.top;else n.bottom="auto"===s.bottom?0:s.bottom;n.top=s.top}else n.top=n.bottom=0},U=[{name:"caption",option:"captionEl",onInit:function(e){o=e}},{name:"share-modal",option:"shareEl",onInit:function(e){l=e},onTap:function(){A()}},{name:"button--share",option:"shareEl",onInit:function(e){r=e},onTap:function(){A()}},{name:"button--zoom",option:"zoomEl",onTap:e.toggleDesktopZoom},{name:"counter",option:"counterEl",onInit:function(e){s=e}},{name:"button--close",option:"closeEl",onTap:e.close},{name:"button--arrow--left",option:"arrowEl",onTap:e.prev},{name:"button--arrow--right",option:"arrowEl",onTap:e.next},{name:"button--fs",option:"fullscreenEl",onTap:function(){n.isFullscreen()?n.exit():n.enter()}},{name:"preloader",option:"preloaderEl",onInit:function(e){p=e}}];S.init=function(){t.extend(e.options,C,!0),g=e.options,i=t.getChildByClass(e.scrollWrap,"pswp__ui"),u=e.listen,function(){var e;u("onVerticalDrag",(function(e){b&&e<.95?S.hideControls():!b&&e>=.95&&S.showControls()})),u("onPinchClose",(function(t){b&&t<.9?(S.hideControls(),e=!0):e&&!b&&t>.9&&S.showControls()})),u("zoomGestureEnded",(function(){(e=!1)&&!b&&S.showControls()}))}(),u("beforeChange",S.update),u("doubleTap",(function(t){var n=e.currItem.initialZoomLevel;e.getZoomLevel()!==n?e.zoomTo(n,t,333):e.zoomTo(g.getDoubleTapZoom(!1,e.currItem),t,333)})),u("preventDragEvent",(function(e,t,n){var i=e.target||e.srcElement;i&&i.getAttribute("class")&&e.type.indexOf("mouse")>-1&&(i.getAttribute("class").indexOf("__caption")>0||/(SMALL|STRONG|EM)/i.test(i.tagName))&&(n.prevent=!1)})),u("bindEvents",(function(){t.bind(i,"pswpTap click",_),t.bind(e.scrollWrap,"pswpTap",S.onGlobalTap),e.likelyTouchDevice||t.bind(e.scrollWrap,"mouseover",S.onMouseOver)})),u("unbindEvents",(function(){x||A(),T&&clearInterval(T),t.unbind(document,"mouseout",$),t.unbind(document,"mousemove",N),t.unbind(i,"pswpTap click",_),t.unbind(e.scrollWrap,"pswpTap",S.onGlobalTap),t.unbind(e.scrollWrap,"mouseover",S.onMouseOver),n&&(t.unbind(document,n.eventK,S.updateFullscreen),n.isFullscreen()&&(g.hideAnimationDuration=0,n.exit()),n=null)})),u("destroy",(function(){g.captionEl&&(a&&i.removeChild(a),t.removeClass(o,"pswp__caption--empty")),l&&(l.children[0].onclick=null),t.removeClass(i,"pswp__ui--over-close"),t.addClass(i,"pswp__ui--hidden"),S.setIdle(!1)})),g.showAnimationDuration||t.removeClass(i,"pswp__ui--hidden"),u("initialZoomIn",(function(){g.showAnimationDuration&&t.removeClass(i,"pswp__ui--hidden")})),u("initialZoomOut",(function(){t.addClass(i,"pswp__ui--hidden")})),u("parseVerticalMargin",F),function(){var e,n,o,a=function(i){if(i)for(var a=i.length,s=0;s<a;s++){e=i[s],n=e.className;for(var r=0;r<U.length;r++)o=U[r],n.indexOf("pswp__"+o.name)>-1&&(g[o.option]?(t.removeClass(e,"pswp__element--disabled"),o.onInit&&o.onInit(e)):t.addClass(e,"pswp__element--disabled"))}};a(i.children);var s=t.getChildByClass(i,"pswp__top-bar");s&&a(s.children)}(),g.shareEl&&r&&l&&(x=!0),I(),g.timeToIdle&&u("mouseUsed",(function(){t.bind(document,"mousemove",N),t.bind(document,"mouseout",$),T=setInterval((function(){2==++O&&S.setIdle(!0)}),g.timeToIdle/2)})),g.fullscreenEl&&!t.features.isOldAndroid&&(n||(n=S.getFullscreenAPI()),n?(t.bind(document,n.eventK,S.updateFullscreen),S.updateFullscreen(),t.addClass(e.template,"pswp--supports-fs")):t.removeClass(e.template,"pswp--supports-fs")),g.preloaderEl&&(L(!0),u("beforeChange",(function(){clearTimeout(f),f=setTimeout((function(){e.currItem&&e.currItem.loading?(!e.allowProgressiveImg()||e.currItem.img&&!e.currItem.img.naturalWidth)&&L(!1):L(!0)}),g.loadingIndicatorDelay)})),u("imageLoadComplete",(function(t,n){e.currItem===n&&L(!0)})))},S.setIdle=function(e){d=e,k(i,"ui--idle",e)},S.update=function(){b&&e.currItem?(S.updateIndexIndicator(),g.captionEl&&(g.addCaptionHTMLFn(e.currItem,o),k(o,"caption--empty",!e.currItem.title)),w=!0):w=!1,x||A(),I()},S.updateFullscreen=function(i){i&&setTimeout((function(){e.setScrollOffset(0,t.getScrollY())}),50),t[(n.isFullscreen()?"add":"remove")+"Class"](e.template,"pswp--fs")},S.updateIndexIndicator=function(){g.counterEl&&(s.innerHTML=e.getCurrentIndex()+1+g.indexIndicatorSep+g.getNumItemsFn())},S.onGlobalTap=function(n){var i=(n=n||window.event).target||n.srcElement;if(!v)if(n.detail&&"mouse"===n.detail.pointerType){if(R(i))return void e.close();t.hasClass(i,"pswp__img")&&(1===e.getZoomLevel()&&e.getZoomLevel()<=e.currItem.fitRatio?g.clickToCloseNonZoomable&&e.close():e.toggleDesktopZoom(n.detail.releasePoint))}else if(g.tapToToggleControls&&(b?S.hideControls():S.showControls()),g.tapToClose&&(t.hasClass(i,"pswp__img")||R(i)))return void e.close()},S.onMouseOver=function(e){var t=(e=e||window.event).target||e.srcElement;k(i,"ui--over-close",R(t))},S.hideControls=function(){t.addClass(i,"pswp__ui--hidden"),b=!1},S.showControls=function(){b=!0,w||S.update(),t.removeClass(i,"pswp__ui--hidden")},S.supportsFullscreen=function(){var e=document;return!!(e.exitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen||e.msExitFullscreen)},S.getFullscreenAPI=function(){var t,n=document.documentElement,i="fullscreenchange";return n.requestFullscreen?t={enterK:"requestFullscreen",exitK:"exitFullscreen",elementK:"fullscreenElement",eventK:i}:n.mozRequestFullScreen?t={enterK:"mozRequestFullScreen",exitK:"mozCancelFullScreen",elementK:"mozFullScreenElement",eventK:"moz"+i}:n.webkitRequestFullscreen?t={enterK:"webkitRequestFullscreen",exitK:"webkitExitFullscreen",elementK:"webkitFullscreenElement",eventK:"webkit"+i}:n.msRequestFullscreen&&(t={enterK:"msRequestFullscreen",exitK:"msExitFullscreen",elementK:"msFullscreenElement",eventK:"MSFullscreenChange"}),t&&(t.enter=function(){return c=g.closeOnScroll,g.closeOnScroll=!1,"webkitRequestFullscreen"!==this.enterK?e.template[this.enterK]():void e.template[this.enterK](Element.ALLOW_KEYBOARD_INPUT)},t.exit=function(){return g.closeOnScroll=c,document[this.exitK]()},t.isFullscreen=function(){return document[this.elementK]}),t}}})),function(e){"use strict";var t,n,i,o,a,s=e(window),r=e(document),l=s.width(),c=e("html"),d=e("body"),u=l<768,p=window.T4Sstrings,m=e(".t4s-close-overlay"),f="t4s-lock-scroll",h="[data-t4s-scroll-me]",g=T4Srequest.page_type,v=T4Sroutes.search_url,T=T4Sconfigs.platform_email,y=T4Sconfigs.enableConfetti,S=T4SThemeSP.cacheNameFirst,w="change:ajaxCart",b="top-start, top, top-end, left-start, left, left-end, right-start, right, right-end, bottom-start, bottom, bottom-end".split(", "),x={left:"right","left-start":"right-start","left-end":"right-end",right:"left","right-start":"left-start","right-end":"left-end"},C=function(e){return isThemeRTL&&x[e]||e};function _(e,t){return JSON.parse(e||t||"{}")}T4SThemeSP.Tooltip=function(){var t='<div class="t4s-tooltip" id="id_nt_tt" role="tooltip"><div class="t4s-tt-arrow"></div><div class="t4s-tooltip-inner">nt_txt_tt</div></div>',n="data-original-title",i="is--show",o=function(e){var t=e.find(".t4s-text-pr").text()||e.text();return e.attr("title")&&"string"!=typeof e.attr(n)&&(t=e.attr("title"),e.attr(n,e.attr("title")||"").attr("title","")),e.attr("data-t4s-tooltip")?t=e.attr("data-t4s-tooltip"):e.attr(n)&&(t=e.attr(n)),t};function a(n,o,a){var s=e(n),r=function(e){do{e+=~~(1e6*Math.random())}while(document.getElementById(e));return e}("tooltipt4s");s.attr("aria-describedby",r),function(e,n){T4SThemeSP.$appendComponent.after(t.replace("nt_txt_tt",e).replace("id_nt_tt",n))}(a,r);var l=e("#"+r);!function(e,t,n,i){fastdomT4s.mutate((function(){FloatingUIT4sDOM.computePosition(e,t,{placement:i,middleware:[FloatingUIT4sDOM.offset(6),FloatingUIT4sDOM.flip({fallbackPlacements:["top","bottom"]}),FloatingUIT4sDOM.shift({padding:5}),FloatingUIT4sDOM.arrow({element:n})]}).then((({x:e,y:i,placement:o,middlewareData:a})=>{Object.assign(t.style,{top:"0",left:"0",transform:`translate3d(${Math.round(e)}px,${Math.round(i)}px,0)`});const{x:s,y:r}=a.arrow,l={top:"bottom",right:"left",bottom:"top",left:"right"}[o.split("-")[0]];Object.assign(n.style,{left:null!=s?`${s}px`:"",top:null!=r?`${r}px`:"",right:"",bottom:"",[l]:"-4px"})}))}))}(n,l[0],l.find(".t4s-tt-arrow")[0],o),l.addClass(i)}function s(t){var n=e("#"+e(t).attr("aria-describedby"));n.removeClass(i),n.remove()}return d.on("t4s:hideTooltip",(function(){e(".t4s-tooltip.is--show").remove()})),function(t){if(!T4SThemeSP.isTouch){var n=e("[data-tooltip]:not(.t4s-tooltip-actived)");0!=n.length&&n.hoverIntent({sensitivity:6,interval:80,timeout:100,over:function(t){let n=e(this),i=n.attr("data-tooltip")||"nt94";b.indexOf(i)<0||(a(this,C(i),o(n)),n.on("updateTooltip",(function(){s(this),a(this,C(i),o(n))})),n.on("destroyTooltip",(function(){s(this)})))},out:function(t){var n=e(this).attr("data-tooltip")||"nt94";b.indexOf(n)<0||(s(this),e(this).off("updateTooltip").off("destroyTooltip"))}}).addClass("t4s-tooltip-actived")}}}(),T4SThemeSP.LookBook=function(){var t={loading:"is--loading",loaded:"is--loaded",clicked:"is--clicked",selected:"is--selected",opened:"is--opened",preload:"is--preLoaded",visible:"is--visible is--pindop"},n=[],i=!1;function o(o,a,s=!1){if(o.hasClass(t.loaded))s&&l(o,a);else{var r=o.data("sid"),c=n[a+r];if(!o.is("[data-is-pr]"))return o.addClass(t.loaded),c=e("#tem"+a).html(),T4SThemeSP.$appendComponent.after(c.replace('id=""','id="'+a+'"')),void(s&&l(o,a));c?(T4SThemeSP.$appendComponent.after(c),o.addClass(t.loaded),T4SThemeSP.ProductItem.init(),T4SThemeSP.Tooltip(),s&&l(o,a)):(o.addClass(t.loading),fetch(o.data("href")+"/?section_id="+r).then((function(e){return e.text()})).then((function(e){e=(e=e.split("[t4splitlz]")[1]).replace("id_nt_t4s",a),T4SThemeSP.$appendComponent.after(e),o.removeClass(t.loading).addClass(t.loaded),T4SThemeSP.ProductItem.init(),T4SThemeSP.Tooltip(),s&&l(o,a),i&&(n[a+r]=e)})).catch((function(e){o.removeClass(t.loading),console.log(e)})))}}function a(){u&&(d.removeClass(f),T4SThemeSP.Helpers.disableBodyScroll(!1,h)),e("[data-pin-close],[data-pin-popup]."+t.clicked).off("click.closelb"),r.off("click.closelb").off("keyup.closelb"),e("[data-pin-wrapper]."+t.opened).removeClass(t.opened),e("[data-pin-popup]."+t.clicked).removeClass(t.clicked),d.hasClass("is--opend-drawer")?m.removeClass("is--pindop"):m.removeClass(t.visible)}function l(n,i,o="lb"){!function(n){u&&(d.addClass(f),T4SThemeSP.Helpers.disableBodyScroll(!0,h));let i=e("#"+n);i.addClass(t.opened),i.hasClass("is-style-mb--false")||m.addClass(t.visible)}(i);var l=e("#"+i),c=l[0],g=l.find(".t4s-"+o+"-arrow"),v=n.data("position")||"top";(s.width()>767||l.hasClass("is-style-mb--false"))&&function(e,t,n,i){fastdomT4s.mutate((function(){FloatingUIT4sDOM.computePosition(e,t,{placement:i,middleware:[FloatingUIT4sDOM.offset(12),FloatingUIT4sDOM.flip({fallbackPlacements:["top","bottom"]}),FloatingUIT4sDOM.shift({padding:0}),FloatingUIT4sDOM.arrow({element:n})]}).then((({x:e,y:i,placement:o,middlewareData:a})=>{Object.assign(t.style,{top:"0",left:"0",transform:`translate3d(${Math.round(e)}px,${Math.round(i)}px,0)`});const{x:s,y:r}=a.arrow,l={top:"bottom",right:"left",bottom:"top",left:"right"}[o.split("-")[0]];Object.assign(n.style,{left:null!=s?`${s}px`:"",top:null!=r?`${r}px`:"",right:"",bottom:"",[l]:"-6px"})}))}))}(n[0],c,g[0],C(v)),"lb"==o?(r.on("click.closelb",(function(t){var n=e(t.target);n.closest("[data-pin-wrapper]").length>0||n.is("[data-pin-popup]")||a()})),e(`#${i} [data-pin-close], [data-pin-popup].${t.clicked}`).on("click.closelb",(function(e){e.preventDefault(),e.stopPropagation(),a()})),r.on("keyup.closelb",(function(e){27===e.keyCode&&a()}))):(r.on("click.closeDrop",(function(t){var n=e(t.target);n.closest("[data-dropdown-wrapper]").length>0||n.is("[data-dropdown-open]")||n.closest("[data-dropdown-open]").length>0||p()})),e(`#${i} [data-dropdown-close], [data-dropdown-open].${t.clicked}`).on("click.closeDrop",(function(e){e.preventDefault(),e.stopPropagation(),p()})),r.on("keyup.closeDrop",(function(e){27===e.keyCode&&p()})))}function c(){r.on("click","[data-pin-popup]:not(."+t.clicked+")",(function(n){n.preventDefault();var i=e(this),s=i.data("bid");a(),i.addClass(t.clicked),o(i,s,!0)})),e("[data-pin-popup][data-is-pr]:not(."+t.clicked+"):not(."+t.opened+")").on("mouseenter.pin",(function(n){var i=e(this),a=i.data("bid");i.addClass(t.preload),o(i,a),i.off("touchstart.pin mouseenter.pin")}))}function p(){d.removeClass(f),T4SThemeSP.Helpers.disableBodyScroll(!1,h),e("[data-dropdown-close],[data-dropdown-open]."+t.clicked).off("click.closeDrop"),r.off("click.closeDrop").off("keyup.closeDrop"),e("[data-dropdown-wrapper]."+t.opened).removeClass(t.opened),e("[data-dropdown-open]."+t.clicked).removeClass(t.clicked),d.hasClass("is--opend-drawer")?m.removeClass("is--pindop"):m.removeClass(t.visible)}function g(){r.on("click","[data-dropdown-open]:not(."+t.clicked+")",(function(n){n.preventDefault();var i=e(this),o=i.data("id");p(),i.addClass(t.clicked),l(i,o,"drop"),function(n){e("#"+n).on("click.dopText","[data-dropdown-item]",(function(i){i.preventDefault();var o=e(this);o.parents().find("."+t.selected).removeClass(t.selected),o.addClass(t.selected),e('[data-dropdown-open][data-id="'+n+'"]>span:not([data-not-change-txt])').text(o.text()),e("#"+n).off("click.dopText"),o.trigger("change:drop",[o,o.data("value")]),p()}))}(o)})),r.on("click","[data-dropdown-off]",(function(t){var n=e(this);e(this).closest("[data-dropdown-wrapper]").find("[data-dropdown-open]>span").text(n.text()),p()})),r.on("dropdown:t4s:close",(function(e){p()}))}return function(){c(),g()}}(),T4SThemeSP.Hover=function(){var t=e("[data-hover-t4s]");if(!(0==t.length||l<1025)){var n="is--hover";t.each((function(t,i){var o=this,a=e(o);o.ishasGroup=o.hasAttribute("data-has-group"),a.hoverIntent({sensitivity:3,interval:a.data("interval")||35,timeout:a.data("timeout")||150,over:function(e){o.ishasGroup?(a.siblings().removeClass(n),a.addClass(n)):a.addClass(n)},out:function(){o.ishasGroup||a.removeClass(n)}})}))}},T4SThemeSP.ProductItem=function(){var t=e("#btns_pr_temp").html(),n="data-tooltip",i=n+'="',o="id_nt_94",a="handle_nt_94",s=T4Sconfigs,l=T4SProductStrings,u=T4Sconfigs.img2,p=s.nowTimestamp,m=s.new_day_int,f=s.show_img,h=s.enable_quickshop,g=s.use_sale_badge,v=s.label_sale_style,T=s.use_preorder_badge,y=s.use_new_badge,S=s.use_soldout_badge,w=s.use_custom_badge,b=s.swatch_limit,x=s.swatch_click,C=s.swatch_num,P=2,k=l.preOrder,I=l.readMore,D=(l.soldOut,l.selectOption),A=l.quickShop,M=l.preView,E=".t4s-text-pr",R=".t4s-svg-pr-icon use",O="data-color-options",N=".t4s-product-sizes",$="is--loading",L="is-t4s--opended",F="is-t4s--calced",U="is-t4s--limit",B="[data-t4s-resizeobserver]",H=s.sw_item_style,W=l.swatch_limit,z=l.swatch_limit_less,j=s.show_qty,G='.t4s-pr-color__item:not(.is-swatch--current):not([data-img="none"]):not(.is--colors-more)',q=s.pr_curent,Z=T4Sconfigs.app_review,K={sale:l.badgeSale,new:l.badgeNew,preOrder:l.badgepreOrder,soldout:l.badgeSoldout,SavePercent:l.badgeSavePercent},V="initProducts__enabled";function Y(t){if(b){var n=t?"["+O+"]":"["+O+"]:not(."+F+")";C?fastdomT4s.measure((function(){e(n).each((function(t){var n=e(this),i=n.find(".t4s-pr-color__item").length-1,o=i-C;fastdomT4s.mutate((function(){n.addClass(F).removeClass(U),n.find(".is-color--limit").removeClass("is-color--limit"),o>0&&(n.addClass(U),n.find(".t4s-pr-color__item").eq(C-1).addClass("is-color--limit"),n.attr("data-limit",i).attr("style",'--text : "+'+o+'";--text2 : "-'+o+'"'))}))}))})):fastdomT4s.measure((function(){e(n).each((function(t){var n=e(this),i=n.find(".t4s-pr-color__item"),o=i.outerWidth(!0),a=i.length-1,s=Math.floor(n.outerWidth()/o),r=a-s;fastdomT4s.mutate((function(){n.addClass(F).removeClass(U),n.find(".is-color--limit").removeClass("is-color--limit"),r>0&&r!=a&&(r+=1,n.addClass(U),n.find(".t4s-pr-color__item").eq(s-2).addClass("is-color--limit"),n.attr("data-limit",s).attr("style",'--text : "+'+r+'";--text2 : "-'+r+'"'))}))}))}))}}function X(t){var n=t.closest("[data-product-options]"),i=n.data("product-options");if("0"==t.data("imgid")){var o,a,s=t.data("img"),r=(t.data("ratio"),t.data("vid")),l=n.find("[data-pr-img]"),c=n.find("[data-pr-href]"),d=c.attr("href");if(t.closest("[data-color-options]").find(".is-swatch--selected").removeClass("is-swatch--selected"),t.addClass("is-swatch--selected"),n.addClass("t4s-colors-selected"),l.attr("data-srcset",s),"1"!=q&&void 0!==r)if(c.attr("href",(a=r,/variant=/.test(o=d)?o.replace(/(variant=)[^&]+/,"$1"+a):/\?/.test(o)?o.concat("&variant=").concat(a):o.concat("?variant=").concat(a))),void 0!==T4sFunc.psjson_lib[i.id]){let t=n.find(N),o=t.find(">span"),a=T4sFunc.psjson_lib[i.id].variants,s=a.find((e=>e.id===r)),l=a.filter((e=>e[i.index_color]===s[i.index_color]));o.removeClass("t4s-product-sizes--sold-out");let c=0;e.map(l,(function(e,t){e.available?++c:o.eq(t).addClass("t4s-product-sizes--sold-out")})),t.attr("data-size",c)}}else{let e=n.find(".flickityt4s-enabled[data-product-img-carousel]");if(0==e.length)return;let i=t.data("imgid");t.data("isWrapped"),t.data("isInstant");e.flickityt4s("select",e.find(`[data-product-img-slide="${i}"]`).index())}}b&&(c.addClass("t4s-pr-item-sw-limit"),C&&C>0&&(P=C));var J,Q,ee="data-collection-url",te="[data-collection-url]",ne="is--href-replaced",ie="[data-pr-href]:not(.is--href-replaced)",oe="/products/";function ae(){T4Sconfigs.within_cat&&0!=e(ie).length&&e(te).each((function(){J=e(this).attr(ee),e.trim(J).length<1||e(this).find(ie).each((function(){Q=(Q=e(this).attr("href")).split(oe)[1],Q=oe+Q,e(this).attr("href",J+Q).addClass(ne)}))}))}var se=[];return{init:function(){ae(),e("[data-product-options]:not(.is-t4s-pr-created)").each((function(){var s=e(this),r=_(s.attr("data-product-options")),l=s.find("[data-pr-href]").attr("href");"boolean"!=typeof r.unQuickShopInline&&(r.unQuickShopInline=!0),function(e,t,n){var i="";"2"==f&&t&&(i=u.replace("image_src",t).replace("image_alt",n),e.find(".t4s-product-img").addClass("is-show-img2")),e.find("[data-replace-img2]").replaceWith(i)}(s,r.image2,r.alt),function(s,r,l){var c=s.find("[data-replace-quickview]"),d=c.attr(n)||"",u=r.id,p=s.find("[data-replace-compare]"),m=p.attr(n)||"",f=s.find("[data-replace-wishlist]"),g=f.attr(n)||"",v=s.find("[data-replace-atc]"),T=v.is("[data-has-qty]"),y=v.attr(n)||"",S=t.replace(/#t4s_pr_url/g,l).split("[split_t4snt]");c.each((function(t){d=e(this).attr(n)||"",e(this).replaceWith(S[0].replace(i,i+d).replace(o,u))})),p.each((function(t){m=e(this).attr(n)||"",e(this).replaceWith(S[1].replace(i,i+m).replace(o,u).replace(a,r.handle))})),f.each((function(t){g=e(this).attr(n)||"",e(this).replaceWith(S[2].replace(i,i+g).replace(o,u).replace(a,r.handle))})),v.each((function(t){y=v.attr(n)||"",e(this).replaceWith(S[3].replace(i,i+y).replace(o,u))}));var w=(v=s.find("[data-atc-selector]")).find(E),b=v.find(R);if(r.isExternal)v.attr("href",r.external_link).attr("target","_blank"),w.text(r.external_title),b.attr("xlink:href","#t4s-icon-external");else if(r.available)if(r.isGrouped)w.text(M);else if(r.isDefault){if(r.isPreoder&&w.text(k),v.attr({"data-action-atc":"","data-variant-id":r.VariantFirstID,"data-qty":r.cusQty||1}),j&&T&&v[0]){var x=v[0].outerHTML,C=S[4].replace('max="9999"',`max="${r.maxQuantity}"`).replace('min="1"',`min="${r.cusQty||1}"`);v.replaceWith('<div class="t4s-product-atc-qty">'+C+x+"</div>")}}else h&&r.unQuickShopInline?(v.attr("data-action-quickshop",""),w.text(A)):w.text(D);else w.text(I),b.attr("xlink:href","#t4s-icon-link");r.unQuickShopInline||s.one("replace:btnAtc",(function(){if(v.attr({"data-action-atc":"","data-variant-id":r.VariantFirstID,"data-qty":r.cusQty||1}),j&&T&&v[0]){var e=v[0].outerHTML,t=S[4].replace('max="9999"',`max="${r.maxQuantity}"`).replace('min="1"',`min="${r.cusQty||1}"`);v.replaceWith('<div class="t4s-product-atc-qty">'+t+e+"</div>")}}))}(s,r,l),function(e,t){var n=e.find("[data-product-badge]"),i=(n.attr("data-sort")||"").replace(/ /g,"").split(","),o="",a=!t.unQuickShopInline;if(0!=i.length&&0!=n.length){for(var s=i.length,r=0;r<s;r++)switch(i[r]){case"sale":var l=t.compare_at_price,c=t.price;if(l<=c||!g){a&&(o+='<span data-badge-sale class="t4s-badge-item t4s-badge-sale" hidden></span>');break}if("2"==v)var d=100*(l-c)/l,u=K.SavePercent.replace("[sale]",Math.round(d));else if("3"==v){var f=l-c;u=T4SThemeSP.Currency.formatMoney(f)}else u=K[i[r]];o+='<span data-badge-sale class="t4s-badge-item t4s-badge-sale">'+u+"</span>";break;case"preOrder":if(!t.isPreoder||!T){a&&(o+='<span data-badge-preorder class="t4s-badge-item t4s-badge-preorder" hidden>'+K[i[r]]+"</span>");break}o+='<span data-badge-preorder class="t4s-badge-item t4s-badge-preorder">'+K[i[r]]+"</span>";break;case"new":var h=p-t.dateStart,b=Math.floor(h/3600);if((b=Math.floor(b/24))>=m||!y)break;o+='<span class="t4s-badge-item t4s-badge-new">'+K[i[r]]+"</span>";break;case"soldout":if(t.available||!S){a&&(o+='<span data-badge-soldout class="t4s-badge-item t4s-badge-soldout" hidden>'+K[i[r]]+"</span>");break}o+='<span data-badge-soldout class="t4s-badge-item t4s-badge-soldout">'+K[i[r]]+"</span>";break;default:var x=t.customBadge;if(!x||!w)break;for(var C=x.length,_=0;_<C;_++)o+='<span class="t4s-badge-item t4s-badge-custom t4s-badge-'+t.customBadgeHandle[_]+'">'+x[_]+"</span>"}n.html(o)}}(s,r),function(t,n){var i=t.find("["+O+"]");if(0!=i.length){for(var o=_(i.attr(O)),a=o.color_variants,s=o.color_variants_avai,r=o.color_variants_handle,l=o.img_options,c=o.img_variants,d=o.id_variants,u=o.id_images||[],p=o.img_ratios,m="",f=c.length>0,h=o.colors_swatch||[],g=o.images_swatch||[],v=a.length,T=v!=s.length,y=0;y<v;y++){var S,w=a[y],x=l.indexOf(w),C="nt94"!=(S=(S=f?c[x]:"nt94")||"nt94")?T4SThemeSP.Images.lazyloadImagePath(S):"none",k="nt94"!=S&&"2"==H?'data-bg="'+T4SThemeSP.Images.getNewImageUrl(S,100)+'"':"",I=T&&s.indexOf(w)<0?" t4s-pr-color--sold-out":"",D="";(""==k&&h[y]||g[y])&&(D=`style=--swatch--background:${g[y]?"url("+T4SThemeSP.Images.getNewImageUrl(g[y],100)+")":h[y]};`),m+='<span data-imgid="'+(u[x]||"0")+'" class="t4s-pr-color__item'+I+'" data-vid="'+d[x]+'" data-tooltip="top" data-img="'+C+'" data-ratio="'+(p[x]||"")+'"><span class="t4s-pr-color__name">'+w+'</span><span class="t4s-pr-color__value bg_color_'+r[y]+' lazyloadt4s" '+k+" "+D+"></span></span>"}if(v>P&&b&&(m+='<span class="t4s-pr-color__item is--colors-more" data-tooltip="top-end"><span class="t4s-pr-color__name">'+W+'</span><a href="'+n+'" class="t4s-pr-color__value bg_color_limit4s"></a></span>'),i.html(m),0!=t.find(N).length){var A=t.closest("[data-product-options]").data("product-options");null==T4sFunc.psjson_lib[A.id]&&e.ajax({url:Shopify.routes.root+"products/"+A.handle+".js",type:"GET",dataType:"json"}).done((function(e){T4sFunc.psjson_lib[A.id]=e})).fail((function(){})).always((function(e){}))}}}(s,l),Y(),function(e,t,n){if(n.unQuickShopInline||t.hasClass(V)||n.isGrouped||n.isExternal)return;var i=t.find("[data-qs-inl]");i.hasClass("lazyloadt4sed")?new T4SThemeSP.Product(e):i.one("lazyincluded",(function(){new T4SThemeSP.Product(e)}))}(this,s,r),s.addClass("is-t4s-pr-created")})),T4SThemeSP.Tooltip(),T4SThemeSP.Compare.updateAll(),T4SThemeSP.Wishlist.updateAll()},initQuickVS:function(){function t(){var t=e(".t4s-product-quick-view"),n=t.find("[data-product-featured]:not(."+V+")");n.addClass(V);var i=t.find("[data-main-media]");i.hasClass("flickityt4s")&&!i.hasClass("flickityt4s-enabled")&&(i[0].flickityt4s=new T4SThemeSP.Carousel(i[0])),new T4SThemeSP.Product(n[0]),T4SThemeSP.PopupMFP(),T4SThemeSP.initGroupsProduct(),window.Shopify&&Shopify.PaymentButton&&Shopify.PaymentButton.init(),T4SThemeSP.Wishlist.updateAll(),T4SThemeSP.Compare.updateAll(),T4SThemeSP.ProductItem.reloadReview(),T4SThemeSP.Tooltip(),d.trigger("currency:update")}function n(){var t=e(".t4s-product-quick-shop:not(."+V+")");t.addClass(V),new T4SThemeSP.Product(t[0]),T4SThemeSP.PopupMFP(),window.Shopify&&Shopify.PaymentButton&&Shopify.PaymentButton.init(),T4SThemeSP.Wishlist.updateAll(),T4SThemeSP.Compare.updateAll(),T4SThemeSP.ProductItem.reloadReview(),T4SThemeSP.Tooltip(),d.trigger("currency:update")}j&&c.addClass("t4s-pr-item-has-qty"),d.on("click","[data-action-quickview], [data-action-quickshop]",(function(i){i.preventDefault();var o=e(this);if(!o.hasClass($)){var a=o.attr("href"),s=o.is("[data-action-quickview]"),l=s?T4Srequest.quickviewId:T4Srequest.quickshopId,c=s?"t4s-opening-qv":"t4s-opening-qs",u=o.data("id"),p=se[l+u];if(T4SThemeSP.isEditCartReplace="0"==o.data("edit"),T4SThemeSP.iDVariantEdit=u,T4SThemeSP.keyVariantEdit=o.data("key"),p)T4SThemeSP.NTpopupInline(p,l,s?t:n,c),d.trigger("modalt4s:opened");else{if(T4SThemeSP.isEditCartReplace){var m=o.closest("[data-cart-item]"),f=o.closest("[data-cart-wrapper]"),h=m.find(".t4s-cart-ld__bar"),g=h.find(".t4s-cart-spinner");f.addClass("is--contentUpdate"),m.addClass("is--update"),h.removeAttr("hidden"),g.removeAttr("hidden"),r.on("cart:updated",(function(e,t="success"){f.removeClass("is--contentUpdate"),r.off("cart:updated"),m.removeClass("is--update"),h.attr("hidden",""),g.attr("hidden",""),"success"==t&&d.trigger("modalt4s:closed")}))}else o.addClass($);fetch(function(e,t){return e+(e.indexOf("?")>-1||e.indexOf("&")>-1?"&":"/?")+t}(a,"section_id="+l)).then((function(e){return e.text()})).then((function(i){o.removeClass($),r.trigger("cart:updated"),i=IsDesignMode?e(i).find("template").html():e(i).html(),T4SThemeSP.NTpopupInline(i,l,s?t:n,c),d.trigger("modalt4s:opened"),se[l+u]=i})).catch((function(e){o.removeClass($),r.trigger("cart:updated"),console.log(e)}))}}}))},recalculateSwatches:Y,clickMoreSwatches:function(){b&&"2"!=x&&d.on("click",".t4s-pr-color__item.is--colors-more>a",(function(t){t.preventDefault();var n=e(this).closest("."+U);n.hasClass(L)?(n.removeClass(L),e(this).siblings().text(W)):(n.addClass(L),e(this).siblings().text(z))}))},swatchesClickHover:function(){T4SThemeSP.isTouch?d.on("click",G,(function(){X(e(this))})):d.hoverIntent({selector:G,sensitivity:6,interval:100,timeout:100,over:function(t){X(e(this))},out:function(){}})},resizeObserver:function(){var t=e(B+".flickityt4s-enabled .t4s-product:not(.t4s_observered), "+B+".isotopet4s-enabled .t4s-product:not(.t4s_observered)");if(0!=t.length&&window.ResizeObserver){var n=new ResizeObserver((function(t){t.forEach((function(t){var n,i=e(t.target),o=i.is(B)?i:i.closest(B);clearTimeout(n),o.addClass("is-t4s--doing"),n=setTimeout((function(){o.hasClass("flickityt4s-enabled")?o.flickityt4s("resize"):o.hasClass("isotopet4s-enabled")&&o.isotopet4s("layout"),o.removeClass("is-t4s--doing")}),28)}))}));t.each((function(t){var i=e(this);n.observe(this),i.addClass("t4s_observered"),i.one("destroy.observered",(function(){n.unobserve(this),i.removeClass("t4s_observered")}))}))}},reloadReview:function(){if("1"==Z)try{window.SPR&&e(".spr-badge").length>0&&SPR.initDomEls(),SPR.loadBadges()}catch(e){}else"8"==Z?("undefined"!=typeof SMARTIFYAPPS&&SMARTIFYAPPS.rv.installed&&SMARTIFYAPPS.rv.scmReviewsRate.actionCreateReviews(),"undefined"!=typeof airReviewDisplayManager&&airReviewDisplayManager.initialize()&&airReviewDisplayManager.initialize()):"6"==Z&&d.trigger("reloadReview.t4s")},loadjsRevew:function(){"6"==Z&&$script(T4Sconfigs.script12)},updateColelction:ae}}(),T4SThemeSP.ProductAjax=function(){var t={loading:"is--loading"},n={disabled:"aria-disabled"},i="disable"==T4Sconfigs.cartType,o="cart"!=g?i?"cart_data":"cart_data,mini_cart":`cart_data,${window.cartT4SectionID}`,a=T4Sconfigs.enableAjaxATC,s=T4Sconfigs.enableAjaxCart,l=T4Sroutes.cart_add_url+".js",c=T4Sroutes.cart_change_url+".js",u=T4Sconfigs.disATCerror,p=o.split(","),m="change.required keyup.required",f="is--field-emty is--animated t4s-ani-shake";
function h(e="json"){return{method:"POST",headers:{"Content-Type":"application/json",Accept:`application/${e}`}}}function v(e=!1){e&&!u&&T4SThemeSP.Notices(e)}function T(e,t,n){var i=e.closest("[data-cart-item]"),a=e.closest("[data-cart-wrapper]"),s=i.find(".t4s-cart-ld__bar"),l=s.find(".t4s-cart-spinner");a.addClass("is--contentUpdate"),i.addClass("is--update"),s.removeAttr("hidden"),l.removeAttr("hidden"),r.on("cart:updated",(function(e){a.removeClass("is--contentUpdate"),r.off("cart:updated"),i.removeClass("is--update"),s.attr("hidden",""),l.attr("hidden","")}));const d=h("javascript");d.headers["X-Requested-With"]="XMLHttpRequest",d.body=JSON.stringify({line:i.index()+1,quantity:n,sections:o,sections_url:window.location.pathname}),fetch(`${c}`,d).then((e=>e.json())).then((e=>{if(e.status)return v(e.description),void r.trigger("cart:updated",["error"]);y(e.sections)})).catch((e=>{r.trigger("cart:updated"),console.error(e)}))}function y(e){e?T4SThemeSP.Cart.renderContents(e):T4SThemeSP.Cart.getToFetch()}function S(i){var o,s=i.find("[data-field-required]"),u=!!i.hasClass("has--properties")&&s.length>0,g=!1;i.on("click","[data-atc-form]",(function(T){if(e(this).attr(n.disabled))T.preventDefault(),T4SThemeSP.Notices(T4SProductStrings.pleaseChooseOptions);else{if(u&&(T.preventDefault(),g=!1,s.each((function(){let t=e(this),n=t.closest("[data-item-property-field]");n.hasClass("is--type-radio")||n.hasClass("is--type-checkbox")?n.find("input[name]").is(":checked")||(n.addClass(f),g=!0):0==t.val().length&&(n.addClass(f),g=!0)}))),g)return clearTimeout(o),o=setTimeout((function(){i.find(".is--animated.t4s-ani-shake").removeClass("is--animated t4s-ani-shake")}),999),void s.off(m).on(m,(function(t){let n=e(this),i=n.closest("[data-item-property-field]");i.hasClass("is--type-radio")||i.hasClass("is--type-checkbox")?i.find("input[name]").is(":checked")&&i.removeClass(f):n.val().length>0&&i.removeClass(f)}));if(!a)return;s.off(m),T.preventDefault(),function(e){e.addClass(t.loading).attr("aria-disabled",!0),e.find(".loading-overlay__spinner").removeAttr("hidden"),e.find(".t4s-svg-spinner").removeAttr("hidden"),r.on("cart:updated",(function(i,o="success"){e.removeClass(t.loading).removeAttr(n.disabled),e.find(".loading-overlay__spinner").attr("hidden",""),e.find(".t4s-svg-spinner").attr("hidden",""),r.off("cart:updated"),"success"==o&&d.trigger("modalt4s:closed")}));const i=h("javascript");i.headers["X-Requested-With"]="XMLHttpRequest",delete i.headers["Content-Type"];const o=new FormData(e.closest("form")[0]);o.append("sections",p),o.append("sections_url",window.location.pathname),i.body=o,fetch(`${l}`,i).then((e=>e.json())).then((e=>{if(e.status)return v(e.description),void r.trigger("cart:updated",["error"]);if(T4SThemeSP.isEditCartReplace&&e.variant_id!=T4SThemeSP.iDVariantEdit){const e=h("javascript");e.headers["X-Requested-With"]="XMLHttpRequest",e.body=JSON.stringify({id:`${T4SThemeSP.keyVariantEdit}`,quantity:0}),fetch(`${c}`,e).then((e=>e.json())).then((e=>{e.status?v(e.description):(r.trigger("add:cart:success").trigger("add:cart:upsell"),T4SThemeSP.isATCSuccess=!0,y(e.sections))})).catch((e=>{console.error(e)}))}else r.trigger("add:cart:success").trigger("add:cart:upsell"),T4SThemeSP.isATCSuccess=!0,y(e.sections)})).catch((e=>{r.trigger("cart:updated")})).finally((()=>{}))}(e(this))}}))}function b(){d.on("click","[data-action-atc]",(function(i){a&&(i.preventDefault(),function(e){e.addClass(t.loading).attr(n.disabled,!0);const i=h("javascript");i.headers["X-Requested-With"]="XMLHttpRequest";var a=e.attr("data-variant-id"),s=parseInt(e.prev("[data-quantity-wrapper]").find("[data-quantity-value]").val())||e.data("qty")||1,c=[];c.push({id:a,quantity:s}),i.body=JSON.stringify({items:c,sections:o,sections_url:window.location.pathname}),r.on("cart:updated",(function(i){e.removeClass(t.loading).removeAttr(n.disabled),r.off("cart:updated")})),fetch(`${l}`,i).then((e=>e.json())).then((e=>{if(e.status)return v(e.description),void r.trigger("cart:updated",["error"]);r.trigger("add:cart:success").trigger("add:cart:upsell"),T4SThemeSP.isATCSuccess=!0,y(e.sections)})).catch((e=>{r.trigger("cart:updated"),console.error(e)}))}(e(this)))}))}function x(){e("[data-cart-items]").off(w).on(w,"[data-action-change]",(function(t){var n=e(this),i=(n.closest("[data-cart-item]"),n.data("id"),n.val()||1);n.attr("max");s&&T(n,0,i)})).off("click.remove").on("click.remove","[data-cart-remove]",(function(t){if(s){t.preventDefault();var n=e(this);n.data("id");T(n,0,0)}}))}return{init:function(){r.on("submitAtc:t4s",(function(e){S(e.$container.find('[data-type="add-to-cart-form"]'))})),S(e('[data-type="add-to-cart-form"]')),b(),x()},change:x}}(),T4SThemeSP.T4sQuantityAdjust=(t=p.notice_stock_msg,n=T4Sconfigs.disOnlyStock,i="data-current-qty",String.prototype.getDecimals||(String.prototype.getDecimals=function(){var e=(""+this).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return e?Math.max(0,(e[1]?e[1].length:0)-(e[2]?+e[2]:0)):0}),function(){d.on("change","[data-quantity-value]",(function(o){var a=e(this),s=(a.data("id"),a.val()||1),r=a.attr("max")||9999,l=a.attr("min")||1,c=a.attr(i)||.1,u=a.closest(".fgr_frm");if(u.length>0&&(subtt_price_group(u),d.trigger("currency:update")),parseInt(s)>parseInt(r)){if(a.val(r),a.attr(i,r),c!=r&&a.attr(i,r).trigger(w),n)return;return T4SThemeSP.Notices(t.replace("[max]",r)),!1}if(parseInt(s)<parseInt(l))return a.val(l),c!=l&&a.attr(i,l).trigger(w),!1;a.trigger(w),a.attr(i,s)})),d.on("click","[data-quantity-selector]",(function(i){i.preventDefault();var o=e(this),a=o.closest("[data-quantity-wrapper]").find("[data-quantity-value]"),s=parseFloat(a.val()),r=parseFloat(a.attr("max")),l=parseFloat(a.attr("min")),c=a.attr("step");if(s&&""!==s&&"NaN"!==s||(s=0),""!==r&&"NaN"!==r||(r=""),""!==l&&"NaN"!==l||(l=0),"any"!==c&&""!==c&&void 0!==c&&"NaN"!==parseFloat(c)||(c=1),o.is("[data-increase-qty]")){if(r&&s>=r){if(a.val(r),n)return;return T4SThemeSP.Notices(t.replace("[max]",r)),!1}a.val((s+parseFloat(c)).toFixed(c.getDecimals()))}else l&&s<=l?a.val(l):s>0&&a.val((s-parseFloat(c)).toFixed(c.getDecimals()));a.trigger("change")}))}),T4SThemeSP.agreeForm=(o=!1,a="[data-agree-checkbox]",function(){o||0==e(a).length||(o=!0,d.on("click",'[data-agree-btn], [name="checkout"], [name="goto_pp"], [name="goto_gc"]',(function(t){var n=e(this).closest("form"),i=n.find(`[type="checkbox"]${a}`);n[0]&&0!=i.length&&(i.is(":checked")?e(this).submit():(t.preventDefault(),t.stopPropagation(),T4SThemeSP.Notices(p.agree_checkout)))})),d.on("click",a,(function(t){e(this).closest("form"),e("[data-add-ckt4]").addClass("t4s-pe-none"),e(this).is(":checked")&&(e("[data-add-ckt4]").removeClass("t4s-pe-none"),d.trigger("hide.t4s.notices"))})))}),T4SThemeSP.PhotoSwipe=function(){var t=e("#photoswipe_template").html(),n="pswp__thumbnails",i="pswp_thumb_active",o=!1,a="[data-pswp-btn-triger], [data-t4s-gallery--open]:not(.is-pswp-disable)",s="click.pswp",l="data-pswp-images-trigger",c=".pswp__t4s";function u(t,n,o,a){if(!(t<=1)){for(var s=0;s<t;s++)o.append('<div class="pswp_thumb_item" data-index="'+(s+1)+'"><img loading="lazy" src="'+n[s].src+'" alt="'+n[s].title+'"></div>');o.find('.pswp_thumb_item[data-index="'+(a.getCurrentIndex()+1)+'"]').addClass(i),a.listen("beforeChange",(function(){var e=a.getCurrentIndex()+1,t=o.find('.pswp_thumb_item[data-index="'+e+'"]');t.siblings().removeClass(i),t.addClass(i)})),a.listen("afterChange",(function(){!function(t){let n=e("."+i)[0],o=t,a=o[0],s=n.getBoundingClientRect(),r=a.getBoundingClientRect();s.left+s.width>r.width?o.animate({scrollLeft:n.offsetLeft+s.width-r.width+10},200):n.offsetLeft<a.scrollLeft&&o.animate({scrollLeft:n.offsetLeft-10},200)}(o)})),o.find(".pswp_thumb_item").on("click",(function(){var t=e(this).data("index");a.goTo(t-1)}))}}return{gallery:function(){e("[data-t4s-gallery].flickityt4s-enabled").on("dragEnd.flickityt4s",(function(e,t){o=!0})).on("staticClick.flickityt4s",(function(e,t,n,i){o=!1})),r.on(s,a,(function(i){i.preventDefault();var s=e(this),r=-1;if(s.is("[data-pswp-btn-triger]")){var l=s.closest("[data-t4s-group-btns]")||s,m=s[0].hasAttribute("data-pr-trigger-pswp"),f=l.siblings("[data-t4s-gallery]");r=function(t,n,i){if(n.hasClass("flickityt4s-enabled")){var o=0;return e.each(n.find("[data-product-single-media-wrapper]"),(function(t,n){if(e(this).hasClass("is-selected"))return!1;e(this).is(i)&&o++})),o}return e(t.currentTarget).is(a)?0:e(t.currentTarget).index()}(i,f,h=m?'[data-media-type="image"]:not(.is--media-hide)':"[data-t4s-gallery--item]"),s=f.find("[data-t4s-gallery--open]").eq(r),o=!1}else var h=(m=s.hasClass("t4s-product__media"))?'[data-media-type="image"]:not(.is--media-hide)':"[data-t4s-gallery--item]";var g=s.parents(h),v=s.closest("[data-t4s-gallery]"),T=v.find(m?'[data-media-type="image"]:not(.is--media-hide) [data-master]':"[data-pswp-src]");if(0==T.length||o)o=!1;else{var y=v.is("[data-t4s-thumb-true]"),S=function(t){var n,i=[];return t.each((function(){n=e(this),i.push({src:n.attr("data-pswp-src")||n.attr("data-master"),w:n.attr("data-pswp-w")||n.attr("width"),h:n.attr("data-pswp-h")||n.attr("height")})})),i}(T),w=parseFloat(v.attr("data-maxSpreadZoom")),b=v.attr("data-fullscreenEl"),x=v.attr("data-shareEl"),C=v.attr("data-counterEl");(function(i){var o=i.items,a=o.length,s=i.index,r=i.galleryItems;isThemeRTL&&(s=a-s-1);var l={closeEl:!0,captionEl:!0,fullscreenEl:i.fullscreenEl||!0,zoomEl:!0,shareEl:i.shareEl||!0,counterEl:i.counterEl||!0,arrowEl:!0,preloaderEl:!0,history:!1,maxSpreadZoom:i.maxSpreadZoom||2,showHideOpacity:!0,bgOpacity:1,index:i.index,tapToToggleControls:!0,shareButtons:[{id:"facebook",label:p.pswp_facebook,url:"https://www.facebook.com/sharer/sharer.php?u={{url}}"},{id:"twitter",label:p.pswp_twitter,url:"https://twitter.com/intent/tweet?text={{text}}&url={{url}}"},{id:"pinterest",label:p.pswp_pinterest,url:"https://www.pinterest.com/pin/create/button/?url={{url}}&media={{image_url}}&description={{text}}"}],getThumbBoundsFn:function(e){var t=r.find(i.parents).eq(e);i.global&&(t=r.find("a[data-index="+e+"]").parents(i.parents));var n=window.pageYOffset||document.documentElement.scrollTop,o=t[0].getElementsByTagName("img")[0].getBoundingClientRect();return{x:o.left,y:o.top+n,w:o.width}}};d.find(c).remove(),T4SThemeSP.$appendComponent.after(t);var m,f=document.querySelectorAll(c)[0],h=e("."+n),g=new PhotoSwipe(f,PhotoSwipeUI_Default,o,l);g.init(),h.empty(),clearTimeout(m),d.trigger("NTpopupInline:offClose"),g.listen("close",(function(){if(m=setTimeout((function(){d.trigger("NTpopupInline:onClose")}),500),r.hasClass("flickityt4s-enabled")){var t=e('[data-master="'+i.items[g.getCurrentIndex()].src+'"]').parents("[data-main-slide]").index();-1==t&&(t=g.getCurrentIndex()),r.flickityt4s("selectCell",t,!1,!0)}})),i.HasThumb&&u(a,o,h,g)})({index:r=r>-1?r:g.index(),items:S,HasThumb:y,galleryItems:v,parents:h,global:!1,maxSpreadZoom:w,fullscreenEl:b,shareEl:x,counterEl:C})}}))},image:function(){r.on(s,"[data-t4s-image-opend]",(function(n){n.preventDefault(),function(n){d.find(c).remove(),T4SThemeSP.$appendComponent.after(t);var i=e(c),o=i[0],a=n.attr("data-pswp-class");a&&i.addClass(a);var s={history:!1,maxSpreadZoom:2,showHideOpacity:!0,fullscreenEl:!1,shareEl:!1,counterEl:!1,bgOpacity:1,getThumbBoundsFn:function(e){var t=window.pageYOffset||document.documentElement.scrollTop,i=n[0].getBoundingClientRect();return{x:i.left,y:i.top+t,w:i.width}}},r=[],l=n.attr("data-pswp-w"),u=n.attr("data-pswp-h"),p=n.attr("data-pswp-src");r.push({src:p,w:l,h:u,title:n.text()});var m,f=new PhotoSwipe(o,PhotoSwipeUI_Default,r,s);f.init(),clearTimeout(m),d.trigger("NTpopupInline:offClose"),f.listen("close",(function(){m=setTimeout((function(){d.trigger("NTpopupInline:onClose")}),500)}))}(e(this))}))},images:function(){r.on(s,`[${l}]`,(function(i){i.preventDefault();let o=e(this),a=_(o.attr(l),"[]");0!=a.length&&function(i,o){if(!i)return;d.find(c).remove(),T4SThemeSP.$appendComponent.after(t);let a=i.length,s=document.querySelectorAll(c)[0],r=e("."+n),l=new PhotoSwipe(s,PhotoSwipeUI_Default,i,{history:!1,focus:!1,showAnimationDuration:0,hideAnimationDuration:0});if(l.init(),o.is("data-disable-thumb"))return;u(a,i,r,l)}(a,o)}))}}}(),T4SThemeSP.Video=function(){var t="t4s-postervideo-playing";function n(){e(".js-youtube").each((function(){this.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}',"*")})),e(".js-vimeo").each((function(){this.contentWindow.postMessage('{"method":"pause"}',"*")})),e("video:not(.t4s_bg_vid_html5)").each((function(){this.pause()})),e(".product-model").each((function(){this.modelViewerUI&&this.modelViewerUI.pause()}))}function i(e){e.target.playVideo()}return{initPoster:function(){e("[data-video-poster-btn]").on("click",(function(o){o.preventDefault();var a=e(this).closest("[data-video-poster]"),s=a.find("video, iframe");n(),function(t,n){if(!t.is("[loaded]")){var o=t.find("[data-video-insert]").length?t.find("[data-video-insert]"):t,a='<iframe src="src_t4s" id="id_t4s" class="class_t4s" title="" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen frameborder="0"></iframe>',s=JSON.parse(n.attr("data-options")||"{}"),r=s.type,l=s.vid,c=s.id,d=s.srcDefault,u=s.autoplay,p=s.loop,m="youtube",f="vimeo";if(r==m?a='<div id="'+c.replace("#","t4s_yt_")+'"></div>':r==f?a=a.replace("src_t4s","//player.vimeo.com/video/"+l+"?&portrait=0&byline=0&color="+s.accent_color+"&autoplay="+ +u+"&loop="+ +p).replace("class_t4s","js-vimeo"):c&&e(c)[0]?a=e(c).html():(a='<video src="src_t4s" preload="auto" controls data-autoplay data-loop playsinline></video>'.replace("src_t4s",d),u&&(a=a.replace("data-autoplay","autoplay")),p&&(a=a.replace("data-loop","loop"))),o.append(a),t.attr("data-type-video-inline","").attr("loaded",!0),r==m){if(e("#YTAPI").length<=0){var h=document.createElement("script");h.src="https://www.youtube.com/iframe_api",h.id="YTAPI";var g=document.getElementsByTagName("script")[0];g.parentNode.insertBefore(h,g)}var v=setInterval((function(){"function"==typeof onYouTubeIframeAPIReady&&"function"==typeof YT.Player&&(new YT.Player(c.replace("#","t4s_yt_"),{height:"315",width:"560",videoId:l,playerVars:{playsinline:1,rel:0,playlist:l,loop:p?1:0},events:{onReady:i}}),clearInterval(v))}),100)}}}(a,e(this)),setTimeout((function(){a.addClass(t),s.focus()}),50)})),e("[data-video-poster-close]").on("click",(function(i){i.preventDefault(),n();var o=e(this).closest("[data-video-poster]");o.removeAttr("loaded").removeClass(t),o.find("video, iframe").remove()}))}}}();var P,k,I,D,A,M=function(){var t,n="[data-wrap-lm]",i="[data-wrap-lm-prev]",o="[data-contentlm-replace]",a="is--loading";function s(t){this.container=t,this.$container=e(t),this.options=JSON.parse(this.$container.attr("data-ntajax-options")),this.main=this.options.id||"",this.typeAjax=this.options.type||"LmDefault",this.isProduct=this.options.isProduct||!1,this.$section=e(`#shopify-section-${this.main}`),self.isbtnLoadMore=!0,this.setupEventListeners()}return s.prototype=Object.assign({},s.prototype,{setupEventListeners:function(){var n=this;n.$container.on("click.ntlm","[data-load-more]",(function(i){i.preventDefault();const o=e(this);o.addClass(a),t=`${o.attr("data-href")||o.attr("href")}&section_id=${n.main}`,n.isbtnLoadMore=!e(this).is("[data-is-prev]"),n.$btnCurrent=e(this),n.renderSectionFromFetch(t)}))},renderSectionFromFetch:function(e){var t=this;T4SThemeSP.getToFetchSection(null,"text",e).then((e=>{"NVT_94"!=e?(t.$btnCurrent.removeClass(a),t[t.typeAjax](e),this.isProduct&&T4SThemeSP.reinitProductGridItem(),d.trigger("t4s:hideTooltip")):t.$btnCurrent.removeClass(a)}))},LmDefault:function(t){var a=e(t),s=a.find(o).html(),r=this.isbtnLoadMore?a.find(n):a.find(i),l=this.$container.find(o);l[0]||(l=this.$section.find(o)),this.isbtnLoadMore?l.append(s):l.prepend(s),this.initLoamoreUpdate(r)},LmIsotope:function(t){var a=e(t),s=a.find(o).html(),r=this.isbtnLoadMore?a.find(n):a.find(i),l=this.$container.find(o);l[0]||(l=this.$section.find(o)),s=e(s),this.isbtnLoadMore?l.append(s).isotopet4s("appended",s):l.prepend(s).isotopet4s("prepended",s),this.initLoamoreUpdate(r)},initLoamoreUpdate:function(e){let t=this.isbtnLoadMore?n:i,o=this.$container.find(t);o[0]||(o=this.$section.find(t)),e.length>0?o.html(e.html()):o.hide()}}),s}();T4SThemeSP.initLoadMore=function(){e("[data-ntajax-options][data-not-main]:not(.is--enabled)").each((function(){e(this).addClass("is--enabled"),this.LoadMore=new M(this)}))},T4SThemeSP.reinitProductGridItem=function(){this.ProductItem.init(),this.ProductItem.reloadReview(),this.Tooltip(),T4SThemeSP.Countdown(),T4SThemeSP.Compare.updateAll(),T4SThemeSP.Wishlist.updateAll(),d.trigger("currency:update")},T4SThemeSP.instagram=function(){var t="https://d3ejra0xbg20rg.cloudfront.net/instagram/media?shop="+Shopify.shop+"&resource=default",n="ins_19041994",i="ig_t4s_token",o="true"===CookiesT4.get(i),a=".t4s-icons-ins-svg",s=e(a).html()||"",r=s.split("[t4split]"),l={loaded:"ins-is--loaded"};function c(t,n,i,o,c,d,u){1==r.length&&(s=e(a).html()||"",r=s.split("[t4split]"));var p="",m=(i=i||!0,{IMAGE:"image",VIDEO:"video",CAROUSEL_ALBUM:"carousel_album"}),f={image:r[0],video:r[1],carousel_album:r[2]};e.each(n,(function(e,t){if(e>=c)return 0;var n=t.thumbnail_url||t.media_url,i=m[t.media_type];p+='<div class="t4s-col-ins'+e+" t4s-col-ins t4s-col-item t4s-ins-type-"+i+'"><a data-no-instant rel="nofollow" class="t4s-d-block t4s-pr t4s-oh" href="'+t.permalink+'" target="'+u+'"><div class="t4s_ratio t4s_bg lazyloadt4s t4s-lz--ins" data-bg="'+n+'" data-sizes="auto"></div><span class="lazyloadt4s-loader"></span><div class="t4s-ins-info"><span class="t4s-ins-icon">'+f[i]+"</span></div></a></div>"})),t.html(p).parent().addClass(l.loaded),t.hasClass("flickityt4s-later")&&(t[0].flickityt4s=new T4SThemeSP.Carousel(t[0])),isStorageSpSessionAll&&i&&(sessionStorage.setItem("nt_ins"+o+d,p),sessionStorage.setItem("nt_ins"+o,JSON.stringify({timestamp:new Date,content:n})))}return function(){0!=e("[data-inst4s-options]").length&&e("[data-inst4s-options]").each((function(a){!function(e){var a,s,r=_(e.attr("data-inst4s-options")),d=null,u=null,p=r.id,m=r.limit,f="spnt_t4s",h=r.acc||f,g=r.target;if(h!=f){if(h=atob(h),isStorageSpSessionAll&&(d=sessionStorage.getItem("nt_ins"+h+p),null!=(u=sessionStorage.getItem("nt_ins"+h))&&""!=u&&(a=new Date,(s=new Date(u.timestamp)).setMinutes(s.getMinutes()+30),a.getTime()>s.getTime()&&(u=null,d=null,sessionStorage.removeItem("nt_ins"+h+p),sessionStorage.removeItem("nt_ins"+h)))),null!=d&&""!=u){if(e.html(d).parent().addClass(l.loaded),!e.hasClass("flickityt4s-later"))return;return e[0].flickityt4s=new T4SThemeSP.Carousel(e[0]),!1}null!=u&&""!=u?(u=JSON.parse(u).content,c(e,u,!1,h,m,p,g)):(h!=n&&(t="https://graph.instagram.com/me/media?fields=id,media_type,media_url,permalink,thumbnail_url,caption,children&access_token="+h),fetch(t).then((function(e){if(!e.ok)throw new Error("not ok");return e.json()})).then((t=>{var a=h==n?t:t.data;c(e,a,!0,h,m,p,g),o||(CookiesT4.set(i,"true",{expires:7}),fetch("https://graph.instagram.com/refresh_access_token?grant_type=ig_refresh_token&access_token="+h))})).catch((t=>{e.html(""),console.error("Instagram Feed:error fetch")})))}}(e(this))}))}}(),T4SThemeSP.sideBarInit=function(){var t=e("[data-sidebar-id]"),n=t.is("[data-sidebar-true]"),i=e("[data-sidebar-content]"),o=t.is("[data-is-disableDrawer]");if(0!=t.length){var a=t.attr("data-sidebar-id"),l=window.location.search.slice(1),c=`${window.location.pathname}?section_id=${a}&${l}`;T4SThemeSP.getToFetchSection(null,"text",c).then((a=>{if("NVT_94"==a)return i.html(""),void console.log(error);if(n||s.width()<1024&&!o){var l=(c=a.split("[t4splitlz]"))[2].split("[t4splitlz2]");T4SThemeSP.$appendComponent.after(l[0]+c[1]+l[1])}else{var c;l=(c=a.split("[t4splitlz]"))[2].split("[t4splitlz2]");i.html(c[1]),T4SThemeSP.$appendComponent.after(l[0]+c[1]+l[1])}T4SThemeSP.instagram(),T4SThemeSP.Countdown(),T4SThemeSP.Tooltip(),T4SThemeSP.reinitProductGridItem(),T4SThemeSP.Tabs.Accordion(),d.trigger("currency:update"),r.trigger("sidebar:updated");let u=t.attr("data-drawer-options");e.each(e(JSON.parse(u).id).find(".flickityt4s-later"),(function(e){this.flickityt4s=new T4SThemeSP.Carousel(this)})),e.each(e("[data-sidebar-content]").find(".flickityt4s-later"),(function(e){this.flickityt4s=new T4SThemeSP.Carousel(this)}))}))}},T4SThemeSP.BackToTop=(I=e("[data-t4sBackToTop]"),D=parseInt(I.data("scrolltop")),A=I.find(".t4s-circle-css")[0],function(){s.width()<768&&I.data("hidden-mobile")||0==I.length||(window.addEventListener("scroll",(()=>{P&&clearTimeout(P),P=setTimeout((function(){window.scrollY>D?I.addClass("is--show"):I.removeClass("is--show")}),40),A&&(k&&clearTimeout(k),k=setTimeout((function(){let e=window.scrollY/(document.body.offsetHeight-window.innerHeight),t=(Math.round(100*e),360*e);A.style.setProperty("--cricle-degrees",t+"deg")}),6))})),I.on("click",(function(t){t.preventDefault(),e("html, body").animate({scrollTop:0},isBehaviorSmooth?0:800)})))}),T4SThemeSP.Header=function(){var t,n,i=".t4s-section-header",o=document.querySelector(i),a={},d=0,u=!1,p=0,m="data-header-options",f=e(o).find("["+m+"]").attr(m)||"{}",h=(f=JSON.parse(f)).isSticky,g=f.hideScroldown,T=document.documentElement,y="is-action__hover",w=e("[data-menu-nav]"),b=w.find(">li.has--children"),x=(b.find(">.t4s-sub-menu"),e(o)),C=(e(".t4s-website-wrapper"),"calc-pos-submenu"),_=x.hasClass("header-vertical"),P=".t4s-is-header-categories-menu",k=".t4s-is-header-categories",I=e(P),D=(e(k),"is--child-open"),A="no-transiton-nav-a",M="is-header--stuck",E=200,R=!0,O=T4Sconfigs.onlyClickDropIcon?".t4s-menu-item.has--children>a>.t4s--menu-toggle":".t4s-menu-item.has--children>a";function N(){if(IsDesignMode){if(o=document.querySelector(".t4s-section-header"),a={},d=0,u=!1,f=e(o).find("["+m+"]").attr(m)||"{}",f=JSON.parse(f),h=f.isSticky,g=f.hideScroldown,!o)return;o.removeEventListener("preventHeaderReveal",$),window.removeEventListener("scroll",L)}h&&g&&!_?(o.addEventListener("preventHeaderReveal",$),window.addEventListener("scroll",L,!1),new IntersectionObserver(((e,t)=>{a=e[0].intersectionRect,t.disconnect()})).observe(o)):o.classList.remove("shopify-section-header-hidden","animate")}function $(){u=!0}function L(){const e=window.pageYOffset||document.documentElement.scrollTop;e>d&&e>a.bottom?requestAnimationFrame(F):e<d&&e>a.bottom?u?(window.clearTimeout(p),p=setTimeout((()=>{u=!1}),366),requestAnimationFrame(F)):requestAnimationFrame(U):e<=a.top&&requestAnimationFrame(B),d=e}function F(){o.dispatchEvent(new Event("T4sHeaderHide")),o.classList.add("shopify-section-header-hidden","shopify-section-header-sticky")}function U(){o.dispatchEvent(new Event("T4sHeaderReveal")),T.classList.add(M),o.classList.add("shopify-section-header-sticky","animate"),o.classList.remove("shopify-section-header-hidden")}function B(){T.classList.remove(M),R&&0==a.top&&0==a.height&&(a=o.getBoundingClientRect(),R=!1),o.classList.remove("shopify-section-header-hidden","shopify-section-header-sticky","animate"),T.classList.add(A),clearTimeout(n),n=setTimeout((()=>{T.classList.remove(A)}),366)}function H(t,n){var i=e(n);t.each((function(t){var n,o=e(this),a="#t4s-mega-contents"+o.data("id"),s=i.find(a).html();s?(o.html(s),o.find(".t4s-products .t4s-product").length>0&&T4SThemeSP.reinitProductGridItem(),setTimeout((function(){o.hasClass("isotopet4s")&&T4SThemeSP.Isotopet4s.init(o),Z(o.closest(".has--children")),T4SThemeSP.PopupMFP(),0!=(n=o.find(".flickityt4s-later")).length&&n.each((function(e){this.flickityt4s=new T4SThemeSP.Carousel(this)}))}),600)):o.html("")})),setTimeout((function(){x.find(".isotopet4s-enabled").isotopet4s("layout")}),800)}function W(t){t.find(O).off("click").on("click",(function(t){t.preventDefault();var n=e(this);n.hasClass("t4s--menu-toggle")&&(n=n.closest("a")),n.hasClass(D)?n.removeClass(D).siblings("ul").slideUp(E):n.addClass(D).siblings("ul").slideDown(E)}))}function z(t){if(t&&(b=t),!(l<1024||0==b.length)&&T4SThemeSP.isHover){b.each((function(t,n){var i=e(this);i.hoverIntent({sensitivity:3,interval:35,timeout:150,over:function(e){i.addClass(y)},out:function(){i.removeClass(y)}})}))}}function j(n){t=n?n.find(">a"):b.find(">a"),T4SThemeSP.isHover||l<1024||0==t.length?t.off("click.menu click.menuIntent"):t.on("click.menu",(function(t){let n=e(this).parent();n.hasClass(y)?(T4SThemeSP.isHover&&t.preventDefault(),n.removeClass(y),r.off("click.menuIntent")):(t.preventDefault(),n.addClass(y).siblings().removeClass(y),r.on("click.menuIntent",(function(t){var n=t.target;e(n).is("."+y)||e(n).parents("li").is("."+y)||(w.find("."+y).removeClass(y),r.off("click.menuIntent"))})))}))}function G(t,n){t&&(b=t),l<1024||0==b.length||(IsDesignMode&&(x=e(i)),b.each((function(t,i){Z(e(this),n)})),x.addClass(C))}function q(){if(s.width()<1024)return;IsDesignMode&&(x=e(i)),x.removeAttr("style").css({"--t4s-max-width":s.width()-10+"px","--t4s-max-height":s.height()-Math.max(0,o.getBoundingClientRect().top)-Math.max(0,o.offsetHeight)-20+"px"}),x.find(".isotopet4s-enabled").isotopet4s("layout"),setTimeout((function(){x.find(".isotopet4s-enabled").isotopet4s("layout")}),500);var t=e("#t4s-nav-categories");if(0==t.length)return;let n=s.width()-Math.max(0,t[0].getBoundingClientRect().left)-t.width();isThemeRTL&&(n=s.width()-(s.width()-Math.max(0,t[0].getBoundingClientRect().left))),t.removeAttr("style").css({"--t4s-max-width":`${n}px`,"--t4s-max-height":s.height()-Math.max(0,t[0].getBoundingClientRect().top)-10+"px"}),t.find(".isotopet4s-enabled").isotopet4s("layout"),setTimeout((function(){t.find(".isotopet4s-enabled").isotopet4s("layout")}),500)}function Z(t,n=!1){var i=t.find(">a")[0],o=t.find(">.t4s-sub-menu")[0],a=t.data("placement")||"bottom";if((!t.hasClass("menu-width__full")||"right-start"==a)&&o&&(e(o).attr("style",""),("bottom"!=a||t.hasClass("t4s-type__drop")||!function(t){var n=e(t);n.attr("style","");var i=n.outerWidth(),o=n.offset();if(!i||!o)return!1;var a=o.left,s=(l-i)/2;return!isThemeRTL&&s<=a&&a<=s+i&&l>=s+i||isThemeRTL&&s<=a+i&&a<=s&&l>=s+i?(n.addClass("is--center-screen"),!0):(n.removeClass("is--center-screen"),!1)}(o))&&(FloatingUIT4sDOM.computePosition(i,o,{placement:a,middleware:[FloatingUIT4sDOM.flip({}),FloatingUIT4sDOM.shift({padding:5})]}).then((({x:e,y:t,placement:n,middlewareData:i})=>{Object.assign(o.style,{left:`${e}px`,top:n.indexOf("bottom")>-1?"100%":`${t}px`})})),!n))){let t=e(o).find(".t4s-lazy_menu.isotopet4s.isotopet4s-enabled");t.length>0&&t.isotopet4s("layout")}}function K(t,n){var i=void 0===n?I.find("[data-wrapper-categories]"):n;i.html(t),q();var o=i.find("#t4s-nav-categories>.has--children");z(o),j(o),document.addEventListener("theme:hover",(function(e){j(o)})),G(o),setTimeout((()=>{G(o)}),1e3),i.find(".t4s-type__drop .t4s-lazy_menu").each((function(t){W(e(this))})),i.find(".t4s-products .t4s-product").length>0&&T4SThemeSP.reinitProductGridItem();var a=i.find(".isotopet4s-later"),s=i.find(".flickityt4s-later");a.length>0&&a.each((function(t){T4SThemeSP.Isotopet4s.init(e(this))})),a.length>0&&s.each((function(e){this.flickityt4s=new T4SThemeSP.Carousel(this)}))}return{stickyInit:function(){!function(){if(!h||h&&g||_)return;let t=new IntersectionObserver((function(e){0===Math.round(e[0].intersectionRatio)?(T.classList.add(M),R&&(T.classList.add("t4s-hsticky__ready"),R=!1)):1===Math.round(e[0].intersectionRatio)&&(T.classList.remove(M),T.classList.add(A),clearTimeout(n),n=setTimeout((()=>{T.classList.remove(A)}),366))}),{threshold:[0,1]});e("#t4s-hsticky__sentinel").length>0&&t.observe(document.querySelector("#t4s-hsticky__sentinel")),setTimeout((()=>{T.classList.add("t4s-hsticky__ready"),R=!1}),396)}(),N(),c.css({"--topbar-height":(e("#t4s-top-bar-main").is(":visible")?e("#t4s-top-bar-main").height():0)+"px","--header-height":(e(".t4s-section-header").is(":visible")?e(".t4s-section-header").height():0)+"px"}),s.on("resize",(function(){c.css({"--topbar-height":(e("#t4s-top-bar-main").is(":visible")?e("#t4s-top-bar-main").height():0)+"px","--header-height":(e(".t4s-section-header").is(":visible")?e(".t4s-section-header").height():0)+"px"})}))},init:function(t){!function(){var t=e(".t4s-type__mega .t4s-lazy_menu"),n=e(".t4s-list-categories--item.is--active"),i=n.index(),o=i>0?i:"",a=S+"timeMegaT4s"+o,s=S+"dataMegaT4s"+o,r=isStorageSpSession&&sessionStorage.getItem(a)||0,c=(r=parseInt(r),"t4s-section-mega__menu"),d="[data-section-id]";if(!(0==t.length||l<1024))if(!IsDesignMode&&r>0&&r>=Date.now())H(t,sessionStorage.getItem(s));else{var u=e("."+c),p=u.length>0?u.find(d).data("section-id"):"mega-menu,mega-menu2",m=n.find(">a").attr("href");function f(e){T4SThemeSP.getToFetchSection("?sections="+e,"json").then((e=>{if("NVT_94"==e||e.status)e.status&&console.error(e.description);else{var n="";for(const t in e)n+=e[t].split("[nt_mega_split1]")[1];H(t,"<div>"+n+"</div>"),isStorageSpSession&&(r=Date.now()+18e5,sessionStorage.setItem(a,r),sessionStorage.setItem(s,"<div>"+n+"</div>"))}}))}i>0&&0==u.length&&location.pathname!=m?T4SThemeSP.getToFetchSection(null,"text",m).then((t=>{if("NVT_94"==t)return;const n=e(t).find(`.${c} ${d}`).data("section-id");n&&f(n)})):f(p)}}(),function(){var t=e(".t4s-type__drop .t4s-lazy_menu"),n=e(".t4s-list-categories--item.is--active").index(),i=n>0?n:"",o=S+"timeDropT4s"+i,a=S+"dataDropT4s"+i,s=isStorageSpSession&&sessionStorage.getItem(o)||0;if(s=parseInt(s),IsDesignMode&&t.each((function(t){Z(e(this).closest(".has--children")),W(e(this))})),!(0==t.length||l<1024||IsDesignMode))if(s>0&&s>=Date.now()){var r=sessionStorage.getItem(a).split("[nt_drop_split2]");t.each((function(t){e(this).html(r[t]),Z(e(this).closest(".has--children")),W(e(this))}))}else{var c=[];t.each((function(t){c.push(e(this).data("handle"))})),c=c.join(" "),T4SThemeSP.getToFetchSection(null,"text",`${window.T4Sroutes.search_url}?type=article&q=${c}&section_id=dropdown-menu`).then((n=>{if("NVT_94"!=n){var i=n.split("[nt_drop_split1]")[1],r=i.split("[nt_drop_split2]");t.each((function(t){e(this).html(r[t]),Z(e(this).closest(".has--children")),W(e(this))})),isStorageSpSession&&(s=Date.now()+18e5,sessionStorage.setItem(o,s),sessionStorage.setItem(a,"<div>"+i+"</div>"))}}))}}(),z(t),j(t),document.addEventListener("theme:hover",(function(e){j(t)})),q(),G(t,!0),function(){if(0!=I.find("[data-wrapper-categories]").length)if(IsDesignMode)IsDesignMode&&(I=e(P),K(e(k).html()));else{var t=S+"timeCatT4s",n=S+"dataCatT4s",i=isStorageSpSession&&sessionStorage.getItem(t)||0;if((i=parseInt(i))>0&&i>=Date.now())K(sessionStorage.getItem(n));else{var o="[data-section-id]",a=e(".t4s-is-header-categories"),s=a.length>0?a.find(o).data("section-id")+"&q="+a.find(o).data("section-id"):"header-categories";T4SThemeSP.getToFetchSection(null,"text",v+"/?section_id="+s).then((e=>{if("NVT_94"!=e){var o=(new DOMParser).parseFromString(e,"text/html").querySelector("div").innerHTML;K(o),isStorageSpSession&&(i=Date.now()+18e5,sessionStorage.setItem(t,i),sessionStorage.setItem(n,o))}}))}}}(),setTimeout((function(){s.on("resize.menu",q)}),2e3)},updateCat:K}}(),T4SThemeSP.MobileNav=function(){var t,n,i={tabNavActive:"is--active",opend:"is--opend"},o="resize.navmb",a="opendDrawer",r=e(".t4s-list-categories--item.is--active").index(),c=r>0?r:"",d=S+"timeMenuT4s"+c,u=S+"dataMenuT4s"+c,p=!1,m=18e5,f=e(".t4s-sp-section-mb-nav [data-section-id]"),h=e(".t4s-sp-section-mb-cat [data-section-id]"),g=f.length,v=h.length;r>0&&(t=f.data("section-id"),n=h.data("section-id"));var T=isStorageSpSession&&sessionStorage.getItem(d)||0,y=(T=parseInt(T),{});function w(e){e.hasClass(i.opend)?e.removeClass(i.opend).children("ul").slideUp(200):e.addClass(i.opend).children("ul").slideDown(200)}function b(e=4){IsDesignMode||p||0==y.$mobileNav.is(":visible")&&l>1024||setTimeout((function(){if(r>0){t=1==g?t:"mb_nav",n=1==v?n:"mb_cat";var e=!1,i=!1;T4SThemeSP.getToFetchSection("?section_id="+t,"text").then((t=>{"NVT_94"!=t&&(y.$mobileNav.find("#shopify-mb_nav").html(t),i?x("indexPage"):e=!0)})),T4SThemeSP.getToFetchSection("?section_id="+n,"text").then((t=>{"NVT_94"!=t&&(y.$mobileNav.find("#shopify-mb_cat").html(t),e?x("indexPage"):i=!0)}))}else T4SThemeSP.getToFetchSection(null,"text",T4Sroutes.search_url+"/?view=mn").then((e=>{"NVT_94"!=e&&x(e)}))}),e)}function x(e){T4SThemeSP.Helpers.promiseStylesheet(T4Sconfigs.stylesheet2).then((function(){p=!0,r<=0&&"indexPage"!=e&&y.$mobileNav.html(e),r>0&&"indexPage"==e&&(e=y.$mobileNav.html()),s.off(o),y.$mobileNav.off(a),y.$mobileNav.trigger("lazyincluded"),isStorageSpSession&&(T=Date.now()+m,sessionStorage.setItem(d,T),sessionStorage.setItem(u,e))}))}return function(){y={$mobileNav:e("#t4s-menu-drawer")},isStorageSpSession&&T>0&&T>=Date.now()?T4SThemeSP.Helpers.promiseStylesheet(T4Sconfigs.stylesheet2).then((function(){y.$mobileNav.html(sessionStorage.getItem(u)),y.$mobileNav.trigger("lazyincluded")})):(s.on(o,T4SThemeSP.debounce(300,(function(){l=s.width(),b(0)}))),b(500),IsDesignMode||y.$mobileNav.on(a,(function(){b(0)}))),y.$mobileNav.on("click","[data-tab-mb-nav]>[data-tab-mb-item]",(function(){var t=e(this);t.hasClass(i.tabNavActive)||(t.addClass(i.tabNavActive).siblings().removeClass(i.tabNavActive),e("[data-tab-mb-content]."+i.tabNavActive).removeClass(i.tabNavActive),e(t.data("id")).addClass(i.tabNavActive))})),y.$mobileNav.on("click",".t4s-menu-item-has-children.t4s-only_icon_false>a",(function(t){t.preventDefault(),t.stopPropagation(),w(e(this).parent())})),y.$mobileNav.on("click",".t4s-menu-item-has-children > a > .t4s-mb-nav__icon",(function(t){t.preventDefault(),t.stopPropagation(),w(e(this).parent().parent())}))}}(),T4SThemeSP.loadingBar=function(){console.log("loadingBar")},T4SThemeSP.currencyForm=function(){var t={select:"is--selected",opend:"is--opend"},n={},i=T4Sconfigs.cartCurrency;function o(t){i!=t&&(isStorageSpdLocalAll&&localStorage.setItem("T4Currency",t),e(`[data-currency-wrap] [data-iso="${t}"]`).first().trigger("click"))}function a(){var i,o,a,s,r,l,c,u;d.on("click","[data-locale-wrap] [data-locale-item] ,[data-currency-wrap] [data-currency-item]",(function(d){if(d.preventDefault(),i=e(this),!("2"==T4Sconfigs.currency_type&&i.is("[data-currency-item]")||i.hasClass(t.select))){if(i.is("[data-locale-item]")?(o="[data-locale-wrap]","[data-locale-wrap] [data-locale-item]",a="$localeSelector",u=!1):(o="[data-currency-wrap]","[data-currency-wrap] [data-currency-item]",a="$currencySelector",u=!0),s=i.attr("data-iso"),r=e(o+" ."+t.select).first().attr("data-iso"),l=i.attr("data-language"),e(o+" ."+t.select).first().attr("data-language"),c=i.attr("data-country"),e(o+" ."+t.select).first().attr("data-country"),e(o+" [data-current]").text(l||s).removeClass("flagst4s-"+r).addClass("flagst4s-"+s),e(o+" [data-img-current]").length>0){var p=e(o+" [data-img-current]").attr("src");e(o+" [data-img-current]").attr("src",p.replace(/\/\w\w.svg/g,"/"+c.toLowerCase()+".svg"))}e(o+" [data-iso="+s+"]").addClass(t.select).siblings().removeClass(t.select),function(e,t,i){n[e].val(t),"$currencySelector"===e&&n.$countryMirror.val(i),n.$formCurrencyLocale.submit()}(a,s,c),isStorageSpdLocal&&u&&localStorage.setItem("T4Currency",s),T4SThemeSP.loadingBar()}}))}return function(){0!=(n={$formCurrencyLocale:e("#CurrencyLangSelector"),$countryMirror:e("#countryMirror"),$localeSelector:e("#LocaleSelector"),$currencySelector:e("#CurrencySelector")}).$formCurrencyLocale.length&&(a(),function(){var t=isStorageSpdLocalAll?localStorage.getItem("T4Currency"):null;if(T4Sconfigs.auto_currency&&!navigator.userAgent.match(/bot|spider/i)&&!t&&!IsDesignMode){var n,i=isStorageSpdLocalAll?localStorage.getItem("nt_currency"):null,a={AF:"AFN",AX:"EUR",AL:"ALL",DZ:"DZD",AS:"USD",AD:"EUR",AO:"AOA",AI:"XCD",AQ:"XCD",AG:"XCD",AR:"ARS",AM:"AMD",AW:"AWG",AU:"AUD",AT:"EUR",AZ:"AZN",BS:"BSD",BH:"BHD",BD:"BDT",BB:"BBD",BY:"BYN",T4:"EUR",BZ:"BZD",BJ:"XOF",BM:"BMD",BT:"BTN",BO:"BOB",BA:"BAM",BW:"BWP",BV:"NOK",BR:"BRL",IO:"USD",BN:"BND",BG:"BGN",BF:"XOF",BI:"BIF",KH:"KHR",CM:"XAF",CA:"CAD",CV:"CVE",KY:"KYD",CF:"XAF",TD:"XAF",CL:"CLP",CN:"CNY",CX:"AUD",CC:"AUD",CO:"COP",KM:"KMF",CG:"XAF",CD:"CDF",CK:"NZD",CR:"CRC",CI:"XOF",HR:"HRK",CU:"CUP",CY:"EUR",CZ:"CZK",DK:"DKK",DJ:"DJF",DM:"XCD",DO:"DOP",EC:"USD",EG:"EGP",SV:"USD",GQ:"XAF",ER:"ERN",EE:"EUR",ET:"ETB",FK:"FKP",FO:"DKK",FJ:"FJD",FI:"EUR",FR:"EUR",GF:"EUR",PF:"XPF",TF:"EUR",GA:"XAF",GM:"GMD",GE:"GEL",DE:"EUR",GH:"GHS",GI:"GIP",GR:"EUR",GL:"DKK",GD:"XCD",GP:"EUR",GU:"USD",GT:"GTQ",GG:"GBP",GN:"GNF",GW:"XOF",GY:"GYD",HT:"HTG",HM:"AUD",VA:"EUR",HN:"HNL",HK:"HKD",HU:"HUF",IS:"ISK",IN:"INR",ID:"IDR",IR:"IRR",IQ:"IQD",IE:"EUR",IM:"GBP",IL:"ILS",IT:"EUR",JM:"JMD",JP:"JPY",JE:"GBP",JO:"JOD",KZ:"KZT",KE:"KES",KI:"AUD",KR:"KRW",KW:"KWD",KG:"KGS",LA:"LAK",LV:"EUR",LB:"LBP",LS:"LSL",LR:"LRD",LY:"LYD",LI:"CHF",LT:"EUR",LU:"EUR",MO:"MOP",MK:"MKD",MG:"MGA",MW:"MWK",MY:"MYR",MV:"MVR",ML:"XOF",MT:"EUR",MH:"USD",MQ:"EUR",MR:"MRU",MU:"MUR",YT:"EUR",MX:"MXN",FM:"USD",MD:"MDL",MC:"EUR",MN:"MNT",ME:"EUR",MS:"XCD",MA:"MAD",MZ:"MZN",MM:"MMK",NA:"NAD",NR:"AUD",NP:"NPR",NL:"EUR",AN:"",NC:"XPF",NZ:"NZD",NI:"NIO",NE:"XOF",NG:"NGN",NU:"NZD",NF:"AUD",MP:"USD",NO:"NOK",OM:"OMR",PK:"PKR",PW:"USD",PS:"ILS",PA:"PAB",PG:"PGK",PY:"PYG",PE:"PEN",PH:"PHP",PN:"NZD",PL:"PLN",PT:"EUR",PR:"USD",QA:"QAR",RE:"EUR",RO:"RON",RU:"RUB",RW:"RWF",BL:"EUR",SH:"SHP",KN:"XCD",LC:"XCD",MF:"EUR",PM:"EUR",VC:"XCD",WS:"WST",SM:"EUR",ST:"STN",SA:"SAR",SN:"XOF",RS:"RSD",SC:"SCR",SL:"SLL",SG:"SGD",SK:"EUR",SI:"EUR",SB:"SBD",SO:"SOS",ZA:"ZAR",GS:"GBP",ES:"EUR",LK:"LKR",SD:"SDG",SR:"SRD",SJ:"NOK",SZ:"SZL",SE:"SEK",CH:"CHF",SY:"SYP",TW:"TWD",TJ:"TJS",TZ:"TZS",TH:"THB",TL:"USD",TG:"XOF",TK:"NZD",TO:"TOP",TT:"TTD",TN:"TND",TR:"TRY",TM:"TMT",TC:"USD",TV:"AUD",UG:"UGX",UA:"UAH",AE:"AED",GB:"GBP",US:"USD",UM:"USD",UY:"UYU",UZ:"UZS",VU:"VUV",VE:"VEF",VN:"VND",VG:"USD",VI:"USD",WF:"XPF",EH:"MAD",YE:"YER",ZM:"ZMW",ZW:"ZWD"};if(i){let e=JSON.parse(i);try{n=t||e.currency.handle}catch(t){n=a[e.countryCode]||a[e.country]||e.currency}o(n)}else{var s={type:"get",url:"https://extreme-ip-lookup.com/json/?key=demo2",dataType:"json",success:function(t){"success"==t.status?(o(a[t.countryCode]),isStorageSpdLocal&&localStorage.setItem("nt_currency",JSON.stringify(t))):e.ajax(r)},error:function(t,n){e.ajax(r)}},r={type:"get",url:"https://ipinfo.io/json",dataType:"json",success:function(e){o(a[e.country]),isStorageSpdLocal&&localStorage.setItem("nt_currency",JSON.stringify(e))},error:function(t,n){e.ajax(l)}},l={type:"get",url:"https://d1hcrjcdtouu7e.cloudfront.net/users/countryDetection",dataType:"json",success:function(e){o(a[e.country]),isStorageSpdLocal&&localStorage.setItem("nt_currency",JSON.stringify(e))}};e.ajax({type:"get",url:"/browsing_context_suggestions.json?source=geolocation_recommendation&currency[enabled]=true&language[enabled]=true",dataType:"json",success:function(t){try{var n=t.suggestions[0].parts;o(n.currency.handle),isStorageSpdLocal&&localStorage.setItem("nt_currency",JSON.stringify(n))}catch(t){e.ajax(s)}},error:function(t,n){e.ajax(s)}})}}}())}}(),T4SThemeSP.productRecommendations=function(){var t={};function n(n){n[0]?n.hide():t.$recommendationsWrap.hasClass("t4s-pr-single_tab-content")?(t.$recommendationsWrap.find(".t4s-loading--bg").hide(),t.$recommendationsWrap.find("[data-emty-product]").show()):(T4SThemeSP.isRelatedEmty=!0,T4SThemeSP.isRencentEmty?e(".t4s-tp-rencent-related").hide():t.$recommendationsWrap.hide())}return function(i){t={$recommendationsWrap:e("#pr_recommendations:not(.is--not-rub-js)")},0!=(i=i||t.$recommendationsWrap).length&&function(t){var i=t.data("type"),o=t.data("sid"),a=t.data("baseurl"),s=t.closest(".id_product-recommendations"),r=v+a+"&section_id="+o;"3"==i&&(r=a+"?section_id="+o+"&product_id="+t.data("id")+"&limit="+t.data("limit")),T4SThemeSP.getToFetchSection(null,"text",r).then((i=>{if("NVT_94"!=i){var o=IsDesignMode?e(e(i)[2]).html():e(i).html();try{o.trim()}catch(t){o=e(i).html()}if(""!==o.trim()){if(t.html(o),t.find(".t4s-product").length>0&&T4SThemeSP.reinitProductGridItem(),t.find(".flickityt4s").length>0){var a=t.find(".flickityt4s")[0];a.flickityt4s=new T4SThemeSP.Carousel(a),T4SThemeSP.ProductItem.resizeObserver()}}else n(s)}else n(s)}))}(i)}}(),T4SThemeSP.recentlyViewed=function(){var t={};function n(n,i=!0){n[0]?i?n.hide():n.slideUp():t.$recentlyWrap.hasClass("t4s-pr-single_tab-content")?(t.$recentlyWrap.find(".t4s-loading--bg").hide(),t.$recentlyWrap.find("[data-emty-product]").show()):(T4SThemeSP.isRencentEmty=!0,T4SThemeSP.isRelatedEmty?e(".t4s-tp-rencent-related").hide():t.$recentlyWrap.hide())}return function(i){t={$recentlyWrap:e("#recently_wrap")},i=i||t.$recentlyWrap,isStorageSpdLocalAll&&0!=i.length&&function(t){var i=localStorage.getItem("nt_recent"),o="product"==g?t.data("id"):"19041994",a=t.data("sid"),s=t.data("unpr"),r=t.data("limit"),l=t.closest(".id_recently_viewed");if(null!=i){var c=(p=i.split(",")).indexOf(o);if(c>-1?(p=p.splice(0,r+1)).splice(c,1):p=p.splice(0,r),0==p.length)return n(l,!1),!1;var d=p.toString().replace(/,/g," OR "),u=encodeURI(d);T4SThemeSP.getToFetchSection(null,"text",v+"/?section_id="+a+"&type=product&options[unavailable_products]="+s+"&q="+u).then((i=>{if("NVT_94"!=i){var o=IsDesignMode?e(e(i)[2]).html():e(i).html();try{o.trim()}catch(t){o=e(i).html()}if(""!==o.trim()){if(t.html(o),t.find(".t4s-product").length>0&&T4SThemeSP.reinitProductGridItem(),t.find(".flickityt4s").length>0){var a=t.find(".flickityt4s")[0];a.flickityt4s=new T4SThemeSP.Carousel(a),T4SThemeSP.ProductItem.resizeObserver()}}else n(l)}else n(l)}))}else{n(l);var p=new Array}p.indexOf(o)<0&&"19041994"!=o&&(p.length>r&&(p=p.splice(0,r)),p.unshift(o),localStorage.setItem("nt_recent",p.toString()))}(i)}}(),T4SThemeSP.Cart=function(){var t="[data-cart-items]",n="[data-cart-prices]",i="[data-cart-calc-shipping]",o="[data-cart-ship-text]",a="[data-cart-ship-bar]",s="[data-cart-discounts]",l="data-cart-upsell-options",u="data-t4s-percent",m=!0,f=!0,h="disable"==T4Sconfigs.cartType,T="cart"!=g?T4Sconfigs.afterActionATC:"4",S=h?"cart_data":"cart_data,mini_cart",w={loading:"is--loading",none:"t4s-d-none",active:"is--active"},b=window.cartT4SectionID,x="cart"!=g?S:`cart_data,${b}`,C=19041994,P=!1,k={},I=window.T4Sroutes.cart_url;function D(){e("[data-cart-count]").html(e("#t4s-mini_cart").data("ccount")),T4SThemeSP.Tooltip(),d.trigger("currency:update"),h||(T4SThemeSP.ProductAjax.change(),T4SThemeSP.agreeForm(),function(){if("cart"==g)return;var t=e("[data-cart-wrapper]"),n=".t4s-mini_cart-tool__content";e("[data-cart-tools]").on("click","[data-cart-tool_action]",(function(i){i.preventDefault(),i.stopPropagation();var o=e(this).data("id"),a=e(n+".is--"+o),s=a.find("[data-cart-tool_close]");a.addClass("is--opend"),t.addClass("is--contentUpdate"),a.removeAttr("style"),T4SThemeSP.isTouch||a.one("transitionend webkitTransitionEnd oTransitionEnd",(function(){a.find("[data-opend-focus]").focus()})),t.on("click.tool",(function(i){i.preventDefault(),e(i.target).is(n)||e(i.target).closest(n).length>0||(e(n+".is--"+o).removeClass("is--opend"),t.removeClass("is--contentUpdate"),t.off("click.tool"),s.off("click"))})),s.on("click",(function(e){e.preventDefault(),a.removeClass("is--opend"),t.removeClass("is--contentUpdate"),t.off("click.tool"),s.off("click")})),d.off("keyup.drawer").on("keyup.toolCart",(function(i){27===i.keyCode&&(e(n+".is--"+o).removeClass("is--opend"),t.removeClass("is--contentUpdate"),t.off("click.tool"),s.off("click"),d.off("keyup.toolCart").on("keyup.drawer",(function(e){27===e.keyCode&&T4SThemeSP.Drawer.close(e)})))}))}))}(),O(),$(),N(),L(),function(){let t=e("[data-tab-cart-wrap]");if(T4SThemeSP.cartTabActive=!0,!t[0])return;let n=e("[data-cart-tab-title]"),i=e("[data-cart-tab-content]");t.on("click","[data-tab-cart-item]",(function(t){t.preventDefault();let o=e(this);T4SThemeSP.cartTabActive=o.is("[data-is-tab-cart]"),n.text(o.data("title")),o.addClass(w.active).siblings("."+w.active).removeClass(w.active),i.attr("aria-hidden",!0).eq(o.index()).attr("aria-hidden",!1)}))}(),U(),B(),function(){if(0==e("#t4s-tab-visited").length||!isStorageSpdLocalAll)return;let t=localStorage.getItem("nt_recent");if(null==t)return;let n=t.split(",").toString().replace(/,/g," OR "),i=encodeURI(n),o=e(".t4s-tab-visited-empty"),a=e(".t4s-tab-visited-skeleton"),s="show",r="mini_cart_visited";o.hide(),a.show(),T4SThemeSP.getToFetchSection(null,"text",v+"/?section_id="+r+"&type=product&options[unavailable_products]="+s+"&q="+i).then((t=>{a.hide(),"NVT_94"!=t&&(a.after(e(t).html()),d.trigger("currency:update"),T4SThemeSP.Wishlist.updateAll(),T4SThemeSP.Tooltip())}))}(),r.on("update:mini_cart:wishlist",H),r.trigger("update:mini_cart:wishlist"),document.dispatchEvent(new CustomEvent("cart:updated",{detail:{count:e("#t4s-mini_cart").data("ccount")},bubbles:!0,cancelable:!0})))}function A(e=!1){T4SThemeSP.getToFetchSection("?sections="+x,"json").then((t=>{"NVT_94"!=t&&M(t,e)}))}function M(l,m=!1){var f=l.cart_data,h=l.mini_cart||l[b],v=e(h),y=e(t),S=e(n),w=e(i),x=w.find(o),C=w.find(a),_=e(s);if(0!=(f=(f=f.split("[t4s_split1]")[1]).split("[t4s_split2]"))[0]&&!T4SThemeSP.isATCSuccess||"cart"!=g){!T4SThemeSP.isATCSuccess&&m&&(T4SThemeSP.isATCSuccess=m),y.html(v.find(t).html()),S.html(v.find(n).html()),w.attr(u,v.find(i).attr(u)),x.replaceWith(v.find(o).wrap()),C.attr("style",v.find(a).attr("style")),_.html(v.find(s).html());var k=parseFloat(f[0]);e("[data-cart-count]").html(k),e("[data-cart-ttcount]").html(k<2?p.item_cart[k]:p.item_cart[2]),e("[data-cart-tt-price]").html(f[1]),"1"==f[2]?(e(".t4s-mini_cart-tool__content.is--gift.is--opend [data-cart-tool_close]").trigger("click"),e("[data-toogle-gift]").hide()):e("[data-toogle-gift]").show(),0==f[0]?c.addClass("t4s-cart-count-0"):(c.removeClass("t4s-cart-count-0"),P&&(B(),P=!1)),document.dispatchEvent(new CustomEvent("cart:update:count",{detail:{count:f[0]},bubbles:!0,cancelable:!0})),T4SThemeSP.Tooltip(),d.trigger("currency:update"),"0"!=T&&T4SThemeSP.isATCSuccess&&E(),document.dispatchEvent(new CustomEvent("cart:updated",{detail:{count:f[0]},bubbles:!0,cancelable:!0})),r.trigger("cart:updated"),U()}else E()}function E(){T4SThemeSP.isATCSuccess=!1,"1"==T||"2"==T||("3"==T?(T4SThemeSP.cartTabActive||e("[data-is-tab-cart]").trigger("click"),T4SThemeSP.Drawer.opend(e("#t4s-mini_cart"))):document.location.href="5"==T?T4SThemeSP.root_url+"checkout":I)}function R(t){var n,i=new Headers({"Content-Type":"application/json"}),o=("string"!=typeof(n=t)&&"undefined"==(n+="")&&(n=""),e.trim(n)),a={method:"POST",headers:i,body:JSON.stringify({note:o})};fetch(I+"/update.js",a).then((function(e){return e.json()})).then((function(t){o.length>0?(e('[data-id="note"].is--editNote, .t4s-txt_edit_note').removeClass(w.none),e('[data-id="note"].is--addNote, .t4s-txt_add_note').addClass(w.none)):(e('[data-id="note"].is--editNote, .t4s-txt_edit_note').addClass(w.none),e('[data-id="note"].is--addNote, .t4s-txt_add_note').removeClass(w.none))})).catch((function(e){console.log("cart update error: ",e)}))}function O(){e('textarea[name="note"]').on("change",(function(){R(e(this).val())}))}function N(){if(isStorageSpdLocal){var t="CartDiscountcode",n=e(`#${t}`),i=e(`#${t}, [data-cart-discount]`),o=localStorage.getItem(t);i.val(o).trigger("keyup"),e('[data-action="save-discountcode"]').click((function(o){var a=e.trim(n.val());localStorage.setItem(t,a),i.val(a).trigger("keyup"),""!=a&&fetch(window.Shopify.routes.root+"discount/"+a).then((function(e){return e})).then((function(e){document.dispatchEvent(new CustomEvent("cart:refresh"))})).catch((function(e){console.log("save discountcode error: ",e)}))}))}}function $(){let e;const t=document.getElementById("CartDiscountcode");t&&t.addEventListener("input",(function(){clearTimeout(e),e=setTimeout((function(){t.value.length>0?t.setAttribute("name","discount"):t.removeAttribute("name")}),300)}))}function L(){var t,n=e("[data-estimate-shipping-wrap]");n[0]&&(n[0].langRates=JSON.parse(n.find("template[data-lang-rates]").html()||"{}"),t=n.data("id"),Shopify&&new Shopify.CountryProvinceSelector(`ShippingCountry_${t}`,`ShippingProvince_${t}`,{hideElement:`ShippingProvinceContainer_${t}`}),n.on("click",'[data-action="estimate-shipping"]',F.bind(n[0])))}function F(t){t.preventDefault(),t.stopPropagation();var n=this,i=e(n),o=i.find("[data-response-rates]"),a={},s=e(t.currentTarget);s.addClass(w.loading),document.dispatchEvent(new CustomEvent("theme:loading:start")),a.country=i.find('[name="country"]').val()||"",a.province=i.find('[name="province"]').val()||"",a.zip=i.find('[name="zip"]').val()||"",fetch(I+"/shipping_rates.json?"+e.param({shipping_address:a}),{credentials:"same-origin",method:"GET"}).then((function(t){document.dispatchEvent(new CustomEvent("theme:loading:end")),s.removeClass(w.loading),t.json().then((function(i){!function(t,n,i,o,a){t?function(t,n,i,o){var a="",s="",r="";n.zip&&(a+=n.zip+", ");n.province&&(a+=n.province+", ");if(a+=n.country,t.length>1){var l=T4SThemeSP.Currency.formatMoney(t[0].price);s=o.multiple_rates.replace("[number_of_rates]",t.length).replace("[address]",a).replace("[rate]",l)}else s=1===t.length?o.one_rate.replace("[address]",a):o.no_rates;r="";e.each(t,(function(e,t){var n=T4SThemeSP.Currency.formatMoney(t.price),i=o.rate_value.replace("[rate_title]",t.name).replace("[rate]",n);r+="<li>"+i+"</li>"})),i.html('<div class="t4s-mess__rates is--rates-success">'+s+'</div><div class="t4s-results__rates"><ul>'+r.toString().replace(/<\/li>,<li>/g,"</li><li>")+"</ul></div>").fadeIn(),d.trigger("currency:update")}(n.shipping_rates,i,o,a):function(e,t,n){var i="";Object.keys(e).forEach((function(t){i+="<li><span class='t4s-key__rate'>".concat(t,"</span> ").concat(e[t],"</li>")})),"country is not supported."===i&&(i=`<li>${n.no_rates}</li>`);t.html(`<p>${n.errors}</p><ul class="t4s-mess__rates is--rates-error">${i}</ul>`).fadeIn()}(n,o,a)}(t.ok,i,a,o,n.langRates)}))}))}function U(){if(!y)return;let t=e("[data-cart-ship-done]").length;t>0&&m?(T4SThemeSP.CanvasConfetti(),m=!1,f=!1):e(i).length>0&&f?(T4SThemeSP.CanvasConfetti(),f=!1):0==t&&(f=!1,m=!0)}function B(){let t=e(`[${l}]`);if(0==t.length)return;let n=_(t.attr(l));if(n.product_id=e("[data-cart-items] [data-cart-item]:first").data("pid")||n.product_id||19041994,C==n.product_id)return;C=n.product_id;let i=`${n.baseurl}?section_id=${n.section_id}&product_id=${n.product_id}&limit=${n.limit}`;T4SThemeSP.getToFetchSection(null,"text",i).then((n=>{if("NVT_94"==n)return void t.hide();k.flickityt4s&&k.flickityt4s.destroy(),t.html(e(n).html()),d.trigger("currency:update"),T4SThemeSP.Tooltip();let i=t.find(".flickityt4s")[0];i&&(k.flickityt4s=new T4SThemeSP.Carousel(i),setTimeout((function(){e(i).flickityt4s("resize")}),150),setTimeout((function(){e(i).flickityt4s("resize")}),450))}))}function H(){let t=e("#t4s-tab-wishlist"),n=T4SThemeSP.linkWishlist||"";if(0==t.length||n.indexOf("id:")<0)return;let i=e(".t4s-tab-wishlist-empty"),o=e(".t4s-tab-wishlist-skeleton"),a=n.replace("view=wishlist","section_id=mini_cart_wishlist");i.hide(),o.show(),T4SThemeSP.getToFetchSection(null,"text",a).then((t=>{o.hide(),"NVT_94"!=t&&(o.siblings(".t4s-widget__pr").remove(),o.after(e(t).html()),d.trigger("currency:update"),T4SThemeSP.Wishlist.updateAll(),T4SThemeSP.Tooltip())}))}return{renderContents:M,getToFetch:A,init:function(){!function(){if("cart"==g)return O(),$(),N(),L(),T4SThemeSP.agreeForm(),U(),void B();IsDesignMode?D():h||T4SThemeSP.getToFetchSection("?section_id=mini_cart").then((t=>{"NVT_94"!=t&&T4SThemeSP.Helpers.promiseStylesheet(T4Sconfigs.stylesheet1).then((function(){e("#t4s-mini_cart").html(e(t).html()),D()}))}))}(),document.addEventListener("cart:refresh",(function(e){A(!1)})),document.addEventListener("cart:refresh:opend",(function(e){A(!0)})),r.on("add:cart:upsell",(function(e){P=!0}))}}}(),T4SThemeSP.Login=function(){var t="data-login-sidebar";function n(){var n="#t4s-login-sidebar",i=e(n);T4SThemeSP.Drawer.remove("t4s-login-sidebar"),i.on("click","["+t+"]",(function(o){o.preventDefault();var a=e(this).attr(t);e(n+" .t4s-content-login-sidebar.is--"+a).attr("aria-hidden","false").siblings().attr("aria-hidden","true"),e(n+" .t4s-drawer__header .is--"+a).attr("aria-hidden","false").siblings().attr("aria-hidden","true"),i.attr("data-target",a)}))}return function(){IsDesignMode?n():function(){var t=e("#t4s-login-sidebar");if(0!=t.length){var i=S+"timeLoginT4s",o=S+"dataLoginT4s",a=isStorageSpSession&&sessionStorage.getItem(i)||0;(a=parseInt(a))>0&&a>=Date.now()?T4SThemeSP.Helpers.promiseStylesheet(T4Sconfigs.stylesheet3).then((function(){t.html(sessionStorage.getItem(o)),n()})):T4SThemeSP.getToFetchSection("?section_id=login-sidebar").then((s=>{"NVT_94"!=s&&T4SThemeSP.Helpers.promiseStylesheet(T4Sconfigs.stylesheet3).then((function(){t.html(e(s).html()),n(),isStorageSpSession&&(a=Date.now()+24e6,sessionStorage.setItem(i,a),sessionStorage.setItem(o,e(s).html()))}))}))}}()}}(),T4SThemeSP.Compare=function(){var t,n,i=!T4Sconfigs.enable_compare,o=6,a="[data-link-compare]",s="[data-action-compare]",r="[data-remove-compare]",l="[data-count-compare]",c="[data-ttcount-compare]",u="[data-clear-compare]",m="[data-close-compare]",f="t4s_cp",h="is--pe-none",g="is--added",T="is--activate",y=T4SProductStrings.added_text_cp,S=T4SProductStrings.compare,w=0,b=v+"/?view=compare&type=product&options[unavailable_products]=last&q=",x="",C=T4Sconfigs.cp_icon,_=T4Sconfigs.cp_icon_added,P=T4Sconfigs.enableCompePopup,k=window.isPageCompare;function I(e,t,n){return n.indexOf(e)===t}function D(){var e=localStorage.getItem(f);if(null!=e){t=e.split(",");var n=e.replace(/,/g," OR "),i=encodeURI(n);x=b+i}}function A(){if(isStorageSpdLocalAll&&!i){var n=localStorage.getItem(f);if(null!=n){var o=n.replace(/id:/g,"");t=o.split(","),w=""==n?0:t.length,t.forEach((function(e,t){M(e.replace("id:",""))})),e(l).html(window.countComparePage||w)}}}function M(t,n){var i=e(s+'[data-id="'+t+'"]:not(.'+g+")");i.addClass(g).removeClass(h).find(".t4s-text-pr").text(y),i.find(".t4s-svg-pr-icon").html(_),n&&n.trigger("updateTooltip")}function E(t,i=!1){P&&(null==T4Sconfigs.compePopupDes||"canvas"==T4Sconfigs.compePopupDes?T4SThemeSP.getToFetchSection(null,"text",t.replace("view=compare","section_id=compare-popup")).then((t=>{"NVT_94"!=t&&(i&&n&&n.remove(),T4SThemeSP.$appendComponent.after(t),n=e(".t4s_section__compare-popup"),i&&setTimeout((function(){n.addClass(T)}),20),T4SThemeSP.Tooltip())})):i&&"modal"==T4Sconfigs.compePopupDes&&T4SThemeSP.getToFetchSection(null,"text",t.replace("view=compare","section_id=compare-modal")).then((e=>{"NVT_94"!=e&&(i&&n&&n.remove(),T4SThemeSP.NTpopupInline(e,"",R,"t4s-opening-cp"),d.trigger("modalt4s:opened"),T4SThemeSP.Tooltip())})))}function R(){T4SThemeSP.Wishlist.updateAll(),T4SThemeSP.Compare.updateAll(),T4SThemeSP.ProductItem.reloadReview(),T4SThemeSP.Tooltip(),d.trigger("currency:update")}return{init:function(){isStorageSpdLocalAll&&!i&&(history.replaceState&&k&&window.history.replaceState({},document.title,v+"/?view=compare"),D(),k&&(window.isEmtyCompare&&window.isComparePerformed?localStorage.removeItem(f):(window.countComparePage!=t.length&&window.isComparePerformed&&(e(dt_count_wishlist).html(window.countComparePage),localStorage.setItem(t4s_wis,window.listIDPrs)),!window.isEmtyCompare||window.isComparePerformed||""==t.toString()||IsDesignMode||(window.location.href=x))),d.on("click",s+"."+g,(function(e){e.preventDefault(),e.stopPropagation(),P?null==T4Sconfigs.compePopupDes||"canvas"==T4Sconfigs.compePopupDes?n.addClass(T):"modal"==T4Sconfigs.compePopupDes&&E(x,!0):window.location.href=x})),d.on("click",a,(function(e){e.preventDefault(),window.location.href=x})),d.on("click",u,(function(i){i.preventDefault(),null==T4Sconfigs.compePopupDes||"canvas"==T4Sconfigs.compePopupDes?n.removeClass(T):"modal"==T4Sconfigs.compePopupDes&&d.trigger("modalt4s:closed");let o=t.length;for(let n=0;n<o;n++){let i=t[n].replace("id:","");e(s+'[data-id="'+i+'"]').removeClass(g).find(".t4s-text-pr").text(S),e(s+'[data-id="'+i+'"]').find(".t4s-svg-pr-icon").html(C)}t=[],localStorage.setItem(f,t.toString()),w=0,e(l).html(w),e(c).html(w<2?p.item_compare[w]:p.item_compare[2]),D()})),d.on("click",m,(function(e){e.preventDefault(),n.removeClass(T)})),null!=localStorage.getItem(f)&&""!=t.toString()&&E(x),d.on("click",s+":not(."+g+")",(function(t){t.preventDefault(),t.stopPropagation();var n=e(this),i=n.data("id")||"",a="id:"+i,r=localStorage.getItem(f),d=!1;if(""!=i){if(n.addClass(h),null!=r&&r.length>0)(u=r.split(",")).unshift(a);else{var u=new Array;u.unshift(a)}if((u=u.filter(I)).length>o&&(u=u.splice(0,o),d=!0),localStorage.setItem(f,u.toString()),w=u.length,d){var m=e(s+g);m.removeClass(g).find(".t4s-text-pr").text(S),m.find(".t4s-svg-pr-icon").html(C),A()}else M(i,n);e(l).html(w),e(c).html(w<2?p.item_compare[w]:p.item_compare[2]),D(),E(x,!0),k&&(window.location.href=x)}})),d.on("click",r,(function(i){i.preventDefault(),i.stopPropagation();var a=e(this),r=a.data("id"),u="id:"+r,m=localStorage.getItem(f);a.addClass(h);var v=(t=m.split(",")).indexOf(u);v>-1?(t=t.splice(0,o+1)).splice(v,1):t=t.splice(0,o),localStorage.setItem(f,t.toString()),a.removeClass(h),a.trigger("destroyTooltip"),e(".t4s_compare_id_"+r).remove(),e(s+'[data-id="'+r+'"]').removeClass(g).find(".t4s-text-pr").text(S),e(s+'[data-id="'+r+'"]').find(".t4s-svg-pr-icon").html(C),w=t.length,e(l).html(w),e(c).html(w<2?p.item_compare[w]:p.item_compare[2]),D(),k&&""==t.toString()&&(e(".t4s_compare_table").fadeTo(300,0),window.location.href=x),0==w&&(P?null==T4Sconfigs.compePopupDes||"canvas"==T4Sconfigs.compePopupDes?n.removeClass(T):"modal"==T4Sconfigs.compePopupDes&&d.trigger("modalt4s:closed"):window.location.href=x)})))},updateAll:A}}(),T4SThemeSP.Wishlist=function(){var t=T4Sconfigs.wishlist_mode,n="2"==T4Sconfigs.wis_atc_added,i="1"==t,o="2"==t,a=!(i||o),s=50,l="[data-link-wishlist]",c="data-action-wishlist",u="["+c+"]",p="data-remove-wishlist",m="["+p+"]",f="[data-count-wishlist]",h="t4s_wis",g="is--pe-none",T="is--added",y=T4SProductStrings.browse_wishlist,S=T4SProductStrings.remove_wishlist,w=T4SProductStrings.add_to_wishlist,b=n?S:y,x=0,C=v+"/?view=wishlist&type=product&options[unavailable_products]=last&q=",_="",P="",k=T4Sconfigs.wis_icon,I=T4Sconfigs.wis_icon_remove,D=T4Sconfigs.wis_icon_added,A="is--loading",M="/apps/ecomrise/wishlist",E="",R=window.isPageWishlist,O=window.hasPaginateWishlist;if(o)var N=e("#wis_t4s_list").html()||"",$=(E=N.length>0?N.split(" "):[],e("#wis_t4s_list_old").html()||""),L=$.length>0?$.split(" "):[];function F(e,t,n){return n.indexOf(e)===t}function U(){d.on("click",u+":not(."+T+")",(function(t){t.preventDefault(),t.stopPropagation(),i?function(t){var n=t.data("id")||"",i="id:"+n,o=localStorage.getItem(h),a=!1;if(""==n)return;if(t.addClass(g),null!=o&&o.length>0)(r=o.split(",")).unshift(i);else{var r=new Array;r.unshift(i)}r=r.filter(F),r.length>s&&(r=r.splice(0,s),a=!0);if(localStorage.setItem(h,r.toString()),x=r.length,a){var l=e(u+T);l.removeClass(T).find(".t4s-text-pr").text(w),l.find(".t4s-svg-pr-icon").html(k),j()}else G(n,t);e(f).html(x),W(),R&&(window.location.href=_)}(e(this)):function(t){var n=t.attr("data-id")||"",i=t.attr("data-handle")||"ntt4s"+n;if(""==n)return;t.addClass(A+" "+g),fetch(M,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({product_id:n,product_handle:i,action:"add"})}).then((function(e){return e.json()})).then((function(t){"success"==t.status?(L=JSON.parse(t.response.metafield.value).the4_ids,E=JSON.parse(t.response.metafield.value).ecomrise_ids,L&&(E=E.concat(L)),Array.isArray(E)||(E=E.split(",")),G(n),x=E.length,e(f).html(x),z(),R&&(window.location.href=_)):console.error(t.message||"Unknow error")})).catch((function(e){console.log("Error: "+e)})).finally((()=>{t.removeClass(A+" "+g)}))}(e(this))}))}function B(t){var n=t.data("id"),i="id:"+n,o=localStorage.getItem(h);t.addClass(g);var a=(P=o.split(",")).indexOf(i);a>-1?(P=P.splice(0,s+1)).splice(a,1):P=P.splice(0,s),localStorage.setItem(h,P.toString()),t.removeClass(g),t.trigger("destroyTooltip");var r=e(".t4s-products-wishlist .t4s-products");if(r.length>0){let i=t.closest(".t4s-product");i=i[0]?i:e(`[data-remove-wishlist][data-id="${n}"]`).closest(".t4s-product"),r.hasClass("isotopet4s-enabled")?r.isotopet4s("remove",i[0]).isotopet4s("layout"):i.remove()}e(u+'[data-id="'+n+'"]').removeClass(T).find(".t4s-text-pr").text(w),e(u+'[data-id="'+n+'"]').find(".t4s-svg-pr-icon").html(k),x=P.length,e(f).html(x),W(),R&&(""==P.toString()||O)&&(e(".t4s-products-wishlist").fadeTo(300,0),window.location.href=_)}function H(t){var n=t.attr("data-id")||"",i=t.attr("data-handle")||"ntt4s"+n;t.addClass(A+" "+g),fetch(M,{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({product_id:n,product_handle:i,namespace:L.indexOf(n)>=0?"the4":"",action:"add",_method:"DELETE"})}).then((function(e){return e.json()})).then((function(i){if("success"==i.status){var o=JSON.parse(i.response.metafield.value).the4_ids;P=JSON.parse(i.response.metafield.value).ecomrise_ids||new Array,o&&(P=P.concat(o)),E=P,Array.isArray(E)||(E=E.split(",")),t.trigger("destroyTooltip");var a=e(".t4s-products-wishlist .t4s-products");if(a.length>0){let i=t.closest(".t4s-product");i=i[0]?i:e(`[data-remove-wishlist][data-id="${n}"]`).closest(".t4s-product"),a.hasClass("isotopet4s-enabled")?a.isotopet4s("remove",i[0]).isotopet4s("layout"):i.remove()}e(u+'[data-id="'+n+'"]').removeClass(T).find(".t4s-text-pr").text(w),e(u+'[data-id="'+n+'"]').find(".t4s-svg-pr-icon").html(k),x=E.length,e(f).html(x),z(),R&&(""==P.toString()||O)&&(e(".t4s-products-wishlist").fadeTo(300,0),window.location.href=C)}else console.error(i.message||"Unknow error")})).catch((function(e){console.log("Error: "+e)})).finally((()=>{t.removeClass(A+" "+g)}))}function W(){var e=localStorage.getItem(h);if(null!=e){P=e.split(",");var t=e.replace(/,/g," OR "),n=encodeURI(t);_=C+n,T4SThemeSP.linkWishlist=_,r.trigger("update:mini_cart:wishlist")}}function z(){if(0!=(P=E).length){var e="id:"+P.join(" OR id:"),t=encodeURI(e);_=C+t,T4SThemeSP.linkWishlist=_,r.trigger("update:mini_cart:wishlist")}}function j(){if(!(!isStorageSpdLocalAll&&i||a)){if(i){var t=localStorage.getItem(h);if(null==t)return;var n=t.replace(/id:/g,"");P=n.split(","),x=""==t?0:P.length}else{if(""==(P=E).toString())return;x=P.length}if(P.forEach((function(e,t){G(e.replace("id:",""))})),R){var o=e(".t4s-products-wishlist "+u);o.removeClass(T).removeAttr(c,"").attr(p,"").find(".t4s-text-pr").html(S),o.find(".t4s-svg-pr-icon").html(I)}e(f).html(x||window.countWishlistPage)}}function G(t,n){var i=e(u+'[data-id="'+t+'"]:not(.'+T+")");i.addClass(T).removeClass(g).find(".t4s-text-pr").text(b),i.find(".t4s-svg-pr-icon").html(D),n&&i.trigger("updateTooltip")}return{init:function(){!isStorageSpdLocalAll&&i||a||(history.replaceState&&R&&window.history.replaceState({},document.title,v+"/?view=wishlist"),i?W():z(),R&&(window.isEmtyWishlist&&window.isWishlistPerformed?localStorage.removeItem(h):(window.countWishlistPage!=P.length&&window.isWishlistPerformed&&(e(f).html(window.countWishlistPage),localStorage.setItem(h,window.listIDPrs)),!window.isEmtyWishlist||window.isWishlistPerformed||""==P.toString()||IsDesignMode||(window.location.href=_))),d.on("click",u+"."+T,(function(t){t.preventDefault(),t.stopPropagation(),n?i?B(e(this)):H(e(this)):window.location.href=_})),d.on("click",l,(function(e){0!=_.length&&(e.preventDefault(),window.location.href=_)})),U(),d.on("click",m,(function(t){t.preventDefault(),t.stopPropagation(),i?B(e(this)):H(e(this))})))},updateAll:j}}();let E=location.search.indexOf("customer_posted=true")>-1||location.search.indexOf("newsletter&form_type=customer")>-1;T4SThemeSP.PopupPro=function(){var t=p.mfp_close,n=(p.mfp_loading,T4Sconfigs.theme),i="is-opening-mfp",o="open.popup",a="close.popup",l="is--loaded",u={click:"click.age",popup:"#t4s-popup__age",CookiesName:`${n}_age_verify`},m={mouseleave:"mouseleave.exit",click:"click.exit",popup:"#t4s-popup__exit",CookiesName:`${n}_exit`},f={scroll:"scroll.newsletter",click:"click.newsletter",popup:"#t4s-popup__newsletter",canvas:".t4s-newsletter_canvas",CookiesName:`${n}_newsletter`},h={popup:"#t4s-popup__cookies-law",click:"click.cookies"},g=!0,v={popup:"#t4s-popup__sales-tmp",close:"[data-close-sale]"};function T(){e.magnificPopupT4s.open({items:{src:u.popup},type:"inline",closeOnBgClick:!1,closeBtnInside:!1,showCloseBtn:!1,enableEscapeKey:!1,removalDelay:500,tClose:t,callbacks:{beforeOpen:function(){c.addClass(i),this.st.mainClass="mfp-move-horizontal t4s-age_pp_wrapper"},open:function(){u.$popup.find(".t4s-age_verify_allowed").on(u.click,(function(){if(u.date_of_birth){var t=parseInt(e("#ageyear").val()),n=parseInt(e("#agemonth").val()),i=parseInt(e("#ageday").val()),o=new Date(t+u.age_limit,n,i);(new Date).getTime()-o.getTime()<0?(u.$popup.addClass("t4s-animated t4s-shake"),window.setTimeout((function(){u.$popup.removeClass("t4s-animated t4s-shake")}),1e3)):(CookiesT4.set(u.CookiesName,"confirmed",{expires:parseInt(u.day_next),path:"/"}),e.magnificPopupT4s.close())}else CookiesT4.set(u.CookiesName,"confirmed",{expires:parseInt(u.day_next),path:"/"}),e.magnificPopupT4s.close()})),u.$popup.find(".t4s-age_verify_forbidden").on(u.click,(function(){u.$popup.addClass("active_forbidden")}))},beforeClose:function(){},close:function(){u.$popup.find(".t4s-age_verify_allowed, .t4s-age_verify_forbidden").off(u.click)},afterClose:function(){c.removeClass(i)}}})}function y(){e.magnificPopupT4s.open({items:{src:m.popup},type:"inline",removalDelay:500,tClose:t,callbacks:{beforeOpen:function(){c.addClass(i),this.st.mainClass="mfp-move-horizontal t4s-exit_pp_wrapper"},open:function(){if(e(".t4s-exit_pp_wrapper .t4s-product").length>0&&T4SThemeSP.reinitProductGridItem(),e(".t4s-exit_pp_wrapper .flickityt4s").length>0){var t=e(".t4s-exit_pp_wrapper .flickityt4s")[0];t.flickityt4s=new T4SThemeSP.Carousel(t)}d.trigger("t4s:hideTooltip"),d.trigger("currency:update")},beforeClose:function(){},close:function(){r.off(m.mouseleave),CookiesT4.set(m.CookiesName,"shown",{expires:m.day_next,path:"/"})},afterClose:function(){c.removeClass(i)}}})}function S(n,o){1===n?e.magnificPopupT4s.open({items:{src:f.popup},type:"inline",removalDelay:500,tClose:t,callbacks:{beforeOpen:function(){c.addClass(i),this.st.mainClass="mfp-move-horizontal t4s-newsletter_pp_wrapper"},open:function(){d.on("mail.subscribe.success",(function(e){CookiesT4.set(f.CookiesName,"shown",{expires:f.day_next,path:"/"})}))},beforeClose:function(){(e("[data-checked-newsletter]:checked").length>0||!e("[data-checked-newsletter]")[0])&&CookiesT4.set(f.CookiesName,"shown",{expires:f.day_next,path:"/"})},close:function(){},afterClose:function(){c.removeClass(i)}}}):o?(f.$content.addClass("on--show").removeClass("on--shown"),setTimeout((()=>{f.$content.removeClass("on--show")}),500),(e("[data-checked-newsletter]:checked").length>0||!e("[data-checked-newsletter]")[0])&&CookiesT4.set(f.CookiesName,"shown",{expires:f.day_next,path:"/"})):(f.$content.addClass("on--show"),setTimeout((()=>{f.$content.removeClass("on--show").addClass("on--shown")}),100))}function w(){h.$popup.removeClass("on--hide").addClass("on--show"),h.$popup.on(h.click,".t4s-pp_cookies__accept-btn",(function(e){e.preventDefault(),g&&CookiesT4.set(h.CookiesName,"accepted",{expires:h.day_next,path:"/"}),window.Shopify.customerPrivacy.setTrackingConsent(!0,b)})),h.$popup.on(h.click,".t4s-pp_cookies__decline-btn",(function(e){e.preventDefault(),g&&CookiesT4.set(h.CookiesName,"accepted",{expires:h.day_next,path:"/"}),window.Shopify.customerPrivacy.setTrackingConsent(!1,b)}))}function b(){h.$popup.addClass("on--hide").removeClass("on--show")}function x(){const t=window.Shopify.customerPrivacy.userCanBeTracked(),n=window.Shopify.customerPrivacy.getTrackingConsent();(!t&&"no_interaction"===n||IsDesignMode||g)&&(IsDesignMode?h.$popup.on(o,(function(){h.$popup=e(h.popup),w()})).on(a,(function(){b()})):w())}function C(e,t){return Math.floor(Math.random()*(t-e+1))+e}function P(e){var t=v.imageArray[e],n=T4SThemeSP.Images.getNewImageUrl(t,65),i=T4SThemeSP.Images.getNewImageUrl(t,130);v.$temp.find("[data-img-sale]").attr("src",n).attr("srcset",`${n} 1x, ${i} 2x`),v.$temp.find("[data-title-sale]").text(v.titleArray[e]),v.$temp.find("[data-href-sale]").attr("href",v.urlArray[e]),v.$temp.find("[data-action-quickview]").attr("data-id",v.idArray[e]),v.$temp.find("[data-location-sale]").text(v.locationArray[C(v.min,v.max2)]),v.$temp.find("[data-ago-sale]").text(v.timeArray[C(v.min,v.max3)])}function k(){d.trigger("t4s:hideTooltip"),v.$temp&&v.$temp.removeClass(v.classUp).addClass(v.classDown).off("mouseenter mouseleave")}function I(){k(),v.starTimeout=setTimeout((function(){v.$temp&&v.$temp.remove(),T4SThemeSP.$appendComponent.after(v.temp),T4SThemeSP.Tooltip(),v.$temp=e(".t4s-popup__sales"),"1"==v.ppType?(P(v.index),++v.index,v.index>v.max&&(v.index=0)):P(C(v.min,v.max)),v.time.START=(new Date).getTime(),v.time.END=v.time.START+v.stayTime,v.stayTimeout=setTimeout((function(){clearTimeout(v.stayTimeout),I()}),v.stayTime),v.$progressbarSpan=e(".t4s-pp-slpr-progressbar>span"),v.pauseOnHover&&v.$temp.on("mouseenter",(function(e){v.resetOnHover?v.$progressbarSpan.css("animation-name","none"):v.time.REMAINING=v.time.END-(new Date).getTime(),clearTimeout(v.stayTimeout)})).on("mouseleave",(function(e){v.resetOnHover?(v.time.REMAINING=v.stayTime,v.$progressbarSpan.css("animation-name","t4s-ani-w")):v.time.END=(new Date).getTime()+v.time.REMAINING,v.stayTimeout=setTimeout((function(){I()}),v.time.REMAINING)})),v.$temp.find(v.close).on("click",(function(e){e.preventDefault(),k(),d.trigger("t4s:hideTooltip"),v.$temp.off("mouseenter mouseleave"),clearTimeout(v.stayTimeout),clearTimeout(v.starTimeout)}))}),v.starTime)}return function(){h.$popup=e(h.popup),0!=h.$popup.length&&(h.stts=h.$popup.data("stt"),h.day_next=h.stts.day_next||60,h.pp_version=h.stts.pp_version||1994,h.CookiesName=`${n}_cookies_${h.pp_version}`,g="1"==h.stts.show,"accepted"==CookiesT4.get(h.CookiesName)||h.$popup.hasClass(l)||(h.$popup.addClass(l),window.Shopify.customerPrivacy?x():window.Shopify.loadFeatures([{name:"consent-tracking-api",version:"0.1"}],(function(e){if(e)throw e;x()})))),function(){f.$content=e(f.popup).length>0?e(f.popup):e(f.canvas);var t=`${n}_shown_pages`,i=CookiesT4.get(t),c=e(f.popup).length>0?1:2;if(i||(i=0),0==f.$content.length)return i++,void CookiesT4.set(t,i,{expires:194,path:"/"});if(f.stts=f.$content.data("stt"),f.pp_version=f.stts.pp_version,f.CookiesName=f.CookiesName+f.pp_version,!(!IsDesignMode&&"shown"==CookiesT4.get(f.CookiesName)||!f.stts.isMobile&&s.width()<768||f.$content.hasClass(l))){var u="1"==f.pp_version?"2":"1",p=`${n}_newsletter${u}`;if("shown"==CookiesT4.get(p)&&CookiesT4.remove(p),f.$content.addClass(l),f.day_next=f.stts.day_next,f.scroll_delay=f.stts.scroll_delay,f.after=f.stts.after,E){let e=f.$content.find(".t4s-newsletter__success").length,t=f.$content.find(".t4s-newsletter__error").length;(e>0||t>0)&&(e>0&&CookiesT4.set(f.CookiesName,"shown",{expires:f.day_next,path:"/"}),f.after="auto",f.stts.time_delay=500)}if(IsDesignMode)1===c?f.$content.on(o,(function(){e.magnificPopupT4s.instance.isOpen?(e.magnificPopupT4s.close(),setTimeout((function(){f.$content.off(o).off(a),f.$content=e(f.popup),S(c),f.$content.on(o,(function(){S(c)})).on(a,(function(){e.magnificPopupT4s.close()}))}),e.magnificPopupT4s.instance.st.removalDelay+10)):S(c)})).on(a,(function(){e.magnificPopupT4s.close()})):(f.$content.on("shopify:block:select",(function(e){S(c)})),f.$content.on("shopify:block:deselect",(function(e){S(c,"close")})));else{var m=f.stts.number_pages;if(i<m)return i++,CookiesT4.set(t,i,{expires:194,path:"/"}),!1;CookiesT4.set(t,m,{expires:194,path:"/"}),"scroll"==f.after?s.on(f.scroll,(function(){r.scrollTop()<f.scroll_delay||(S(c),s.off(f.scroll))})):setTimeout((function(){S(c)}),f.stts.time_delay),e(document).on("click","[data-triger-newsletter]",(function(e){e.preventDefault(),S(c)})).on("click","[data-t4s-dismiss]",(function(e){e.preventDefault(),S(c,"close")})),d.on("mail.subscribe.success",(function(e){S(c),CookiesT4.set(f.CookiesName,"shown",{expires:f.day_next,path:"/"})}))}}}(),m.$popup=e(m.popup),0!=m.$popup.length&&(!IsDesignMode&&"shown"==CookiesT4.get(m.CookiesName)||m.$popup.hasClass(l)||(m.stts=m.$popup.data("stt"),m.day_next=m.stts.day_next,m.$popup.addClass(l),e(document).on("click",".t4s-btn-coupon",(function(){let t=e(this).data("coupon");navigator.clipboard.writeText(t),e(this).find(".tooltiptext").text(p.copied_tooltipText+": "+t)})),e(document).on("mouseleave",".t4s-btn-coupon",(function(){e(this).find(".tooltiptext").text(p.copy_tooltipText)})),IsDesignMode?m.$popup.on(o,(function(){e.magnificPopupT4s.instance.isOpen?(e.magnificPopupT4s.close(),setTimeout((function(){m.$popup.off(o).off(a),m.$popup=e(m.popup),y(),m.$popup.on(o,(function(){y()})).on(a,(function(){e.magnificPopupT4s.close()}))}),e.magnificPopupT4s.instance.st.removalDelay+10)):y()})).on(a,(function(){e.magnificPopupT4s.close()})):(r.on(m.mouseleave,(function(t){t.clientY<60&&0==e(".mfp-content").length&&y()})),e(window).width()<767&&0==e(".mfp-content").length&&y()))),u.$popup=e(u.popup),0!=u.$popup.length&&(!IsDesignMode&&"confirmed"==CookiesT4.get(u.CookiesName)||u.$popup.hasClass(l)||(u.stts=u.$popup.data("stt"),u.age_limit=u.stts.age_limit,u.date_of_birth=u.stts.date_of_birth,u.day_next=u.stts.day_next,u.$popup.addClass(l),IsDesignMode?u.$popup.on(o,(function(){e.magnificPopupT4s.instance.isOpen?(e.magnificPopupT4s.close(),setTimeout((function(){u.$popup.off(o).off(a),u.$popup=e(u.popup),T(),u.$popup.on(o,(function(){T()})).on(a,(function(){e.magnificPopupT4s.close()}))}),e.magnificPopupT4s.instance.st.removalDelay+10)):T()})).on(a,(function(){e.magnificPopupT4s.close()})):T())),function(){if(v.$popup=e(v.popup),0==v.$popup.length)return;let t=e("#t4s-popup__sales-JSON");v.stts=_(t.html()),t.remove(),!v.stts.isMobile&&s.width()<768||v.$popup.hasClass(l)||(v.$popup.addClass(l),v.temp=v.$popup.html(),v.starTime=v.stts.starTime*v.stts.starTimeUnit,v.stayTime=v.stts.stayTime*v.stts.stayTimeUnit,v.index=0,v.limit=v.stts.limit,v.max=v.stts.max,v.min=0,v.classUp=v.stts.classUp,v.classDown=v.stts.classDown[v.classUp],v.ppType=v.stts.ppType,v.pauseOnHover=v.stts.pauseOnHover,v.resetOnHover=v.stts.resetOnHover,v.idArray=v.stts.idArray,v.titleArray=v.stts.titleArray,v.urlArray=v.stts.urlArray,v.locationArray=v.stts.locationArray,v.timeArray=v.stts.timeArray,v.imageArray=v.stts.imageArray,v.max=v.urlArray.length-1,v.max2=v.locationArray.length-1,v.max3=v.timeArray.length-1,v.starTimeout,v.stayTimeout,v.time={},IsDesignMode?(T4SThemeSP.$appendComponent.after(v.temp),v.$temp=e(".t4s-popup__sales"),v.$temp.hide(),T4SThemeSP.Tooltip(),e(v.close).on("click",(function(e){e.preventDefault(),k()})),v.$popup.on(o,(function(){v.$temp.show(),v.$temp.addClass(v.classUp).removeClass(v.classDown)})).on(a,(function(){k()}))):(v.$popup.remove(),I()))}()}}(),T4SThemeSP.PopupFetch=function(){let e=T4Srequest.path,t=("/"!=e?e:"")+"/?section_id=popups";E&&(t+=location.href.indexOf("customer_posted=true")>-1?"&"+location.href.split("?")[1]:""),T4SThemeSP.getToFetchSection(null,"text",t).then((e=>{"NVT_94"!=e&&(T4SThemeSP.$appendComponent.after(e),T4SThemeSP.PopupPro(),T4SThemeSP.PlatFormMail(),T4SThemeSP.PopupMFP())}))},T4SThemeSP.PlatFormMail=function(){var t={loading:"is--loading",enabled:"is--enabled",errorCheckbox:"is--error-checkbox",errorEmail:"is--error-email"},n={klaviyo:"[data-t4s-klaviyo-submit]"},i={click:"click.mail",keyup:"keyup.mail"},o="[data-agreeMail-checkbox]";return function(){"4"==T&&e(`[data-t4s-mailChimp-ajax]:not(.${t.enabled})`).addClass(t.enabled).submit((function(n){n.preventDefault();var i=e(this),o=i.find("[data-new-response-form]"),a=i.find("[data-t4s-mailChimp-submit]"),s=o.find("[data-new-response-success]"),r=o.find("[data-new-response-error]");a.addClass(t.loading),e.ajax({type:"GET",url:i.attr("action"),data:i.serialize(),cache:!1,dataType:"jsonp",jsonp:"c",contentType:"application/json; charset=utf-8",error:function(e){a.removeClass(t.loading);try{var n=e.replace("0 - ","").replace("1 - ","").replace("2 - ","");r.html(n).slideDown(100)}catch(e){}},success:function(e){a.removeClass(t.loading);try{var n=e.msg.replace("0 - ","").replace("1 - ","").replace("2 - ","");"success"!=e.result?(s.slideUp(100),r.html(n).slideDown(100)):(d.trigger("mail.subscribe.success"),r.slideUp(100),s.slideDown(100))}catch(e){}}})})),"3"==T&&$script("//www.klaviyo.com/media/js/public/klaviyo_subscribe.js",(function(){e.each(e(`[data-t4s-klaviyo-ajax]:not(.${t.enabled})`),(function(){var i=e(this),o=i.attr("data-brand")||"Kalles Klaviyo";KlaviyoSubscribe.attachToForms("#"+i.attr("id"),{custom_success_message:!0,extra_properties:{$source:"Newsletter Popup",Brand:o},success:function(e){d.trigger("mail.subscribe.success"),e.find(n.klaviyo).removeClass(t.loading)}}),i.addClass(t.enabled).submit((function(i){e(this).find(n.klaviyo).addClass(t.loading)}))})),d.on("klaviyo.subscribe.success",(function(e){d.trigger("mail.subscribe.success")})),d.on("klaviyo.subscribe.success klaviyo.subscribe.error",(function(i){e(i.target).find(n.klaviyo).removeClass(t.loading)}))})),0!=e(o).length&&(e("[data-agreeMail-btn]").off(i.click).on(i.click,(function(n){var i=e(this).closest("form");i.find(`[type="checkbox"]${o}`).is(":checked")||(n.preventDefault(),n.stopPropagation(),i.addClass(t.errorCheckbox),i.find('[type="email"]').val().length<1&&i.addClass(t.errorEmail))})),e(o).off(i.click).on(i.click,(function(n){e(this).is(":checked")&&e(this).closest("form").removeClass(t.errorCheckbox)})),e('[data-form-mail-agree] [type="email"]').off(i.keyup).on(i.keyup,(function(n){var i=e(this).closest("form");e(this).val().length<1?i.addClass(t.errorEmail):i.removeClass(t.errorEmail)}))),"?contact%5Btags%5D=newsletter&form_type=customer"==location.search&&e("[data-new-response-form]").html(`<div class="t4s-newsletter__error">${p.error_exist}</div>`).slideDown(100)}}();var R=function(){var t="[data-groups-pr-sl]",n="[data-groups-pr-item]",i="[data-groups-qty-value]",o="change.groups",a="is--checked";function s(t){this.$form=e(t),this.$totalPrice=this.$form.find("[data-groups-total-price]"),this.ArrayPrice=[],this.ArrayComparePrice=[];let i=this;this.$form.find(n).each((function(e){i._updateItemPrice(this,e)})),i._updateTotalPrice(),i._eventListeners()}return s.prototype=Object.assign({},s.prototype,{_eventListeners:function(){let s=this;this.$form.on(o,`select${t}`,(function(){s._updateItem(this)})),this.$form.on(o,"[data-groups-pr-ck]",(function(){let t=e(this).closest(n),i=t.index(),o=s.$form.find(`[data-groups-img="${i}"]`),r=t.find('[name*="items[]"]');this.checked?(t.addClass(a),s._updateItemPrice(t,i),o.fadeIn(300),r.prop("disabled",!1)):(t.removeClass(a),s.ArrayPrice[i]=0,s.ArrayComparePrice[i]=0,o.fadeOut(300),r.prop("disabled",!0)),s._updateTotalPrice()})),this.$form.on(o,i,(function(){let t=e(this).closest(n),i=t.index();s._updateItemPrice(t[0],i),s._updateTotalPrice()}))},_updateItemPrice:function(n,o){let a=e(n),s=a.find(t),r=parseInt(a.find(i).val()),l=parseInt(s.attr("data-price")),c=parseInt(s.attr("data-cpprice"));l*=r,c*=r,this.ArrayPrice[o]=l,this.ArrayComparePrice[o]=c>l?c:l},_updateItem:function(t){let o=e(t),a=o.find(":selected"),s=o.closest(n),r=s.data("index"),l=void 0===r?s.index():r,c=a.data("img"),d=a.data("max"),u=s.find(i),p=u.val(),m=this.$form.find(`[data-groups-img="${l}"] img`),f=parseInt(a.data("price")),h=parseInt(a.data("cpprice")),g=s.find("[data-groups-item-price]");u.attr("max",d),m.attr("data-orginal")!=c&&m.attr({"data-src":c,"data-orginal":c}).removeClass("lazyloadt4sed").addClass("lazyloadt4s"),h=h>f?h:f;let v=0==p?f:p*f,T=0==p?h:p*h;f*=p,h*=p,T>v?void 0!==T4SProductStrings.price_template?g.html(T4SProductStrings.price_template.replace("INS",T4SThemeSP.Currency.formatMoney(v)).replace("DEL",T4SThemeSP.Currency.formatMoney(T))):g.html("<del>"+T4SThemeSP.Currency.formatMoney(T)+"</del> <ins>"+T4SThemeSP.Currency.formatMoney(v)+"</ins>"):g.html(T4SThemeSP.Currency.formatMoney(v)),o.attr({"data-price":v,"data-cpprice":T}),this.ArrayPrice[l]==f&&this.ArrayComparePrice[l]==h||(this.ArrayPrice[l]=f,this.ArrayComparePrice[l]=h,this._updateTotalPrice())},_updateTotalPrice:function(){let e=this.ArrayPrice.reduce(((e,t)=>e+t),0),t=this.ArrayComparePrice.reduce(((e,t)=>e+t),0);t>e?void 0!==T4SProductStrings.price_template?this.$totalPrice.html(T4SProductStrings.price_template.replace("INS",T4SThemeSP.Currency.formatMoney(e)).replace("DEL",T4SThemeSP.Currency.formatMoney(t))):this.$totalPrice.html("<del>"+T4SThemeSP.Currency.formatMoney(t)+"</del> <ins>"+T4SThemeSP.Currency.formatMoney(e)+"</ins>"):this.$totalPrice.html(T4SThemeSP.Currency.formatMoney(e)),d.trigger("currency:update")}}),s}();T4SThemeSP.initGroupsProduct=function(){var t="[data-groups-pr-form]:not(.is--enabled)";0!=e(t).length&&e(t).each((function(t){e(this).addClass("is--enabled"),new R(this)}))},T4SThemeSP.goToID=function(){e("html,body");var t,n,i=0;return function(){0!=(t=e("[data-go-id]")).length&&t.click((function(t){t.preventDefault(),t.stopPropagation();let o=e(this),a=o.data("go-id")||o.attr("href"),s=e(a),r=o.data("offset")||100;0!=s.length&&(s.is(":hidden")&&(e(`[data-t4s-tab-item][href="${a}"]:visible`).trigger("click"),i=100),clearTimeout(n),n=setTimeout((function(){window.scrollTo({behavior:"smooth",top:s.offset().top-r})}),i))}))}}(),T4SThemeSP.CanvasConfetti=function(){var e,t,n,i,o,a,r=window.innerWidth<988?75:150,l=[],c=0,d=!0,u=!0,p=!1,m={colorOptions:["DodgerBlue","OliveDrab","Gold","pink","SlateBlue","lightblue","Violet","PaleGreen","SteelBlue","SandyBrown","Chocolate","Crimson"],colorIndex:0,colorIncrementer:0,colorThreshold:10,getColor:function(){return this.colorIncrementer>=10&&(this.colorIncrementer=0,this.colorIndex++,this.colorIndex>=this.colorOptions.length&&(this.colorIndex=0)),this.colorIncrementer++,this.colorOptions[this.colorIndex]}};function f(e){var o,a;this.x=Math.random()*n,this.y=Math.random()*i-i,this.r=(o=10,a=30,Math.floor(Math.random()*(a-o+1)+o)),this.d=Math.random()*r+10,this.color=e,this.tilt=Math.floor(10*Math.random())-10,this.tiltAngleIncremental=.07*Math.random()+.05,this.tiltAngle=0,this.draw=function(){return t.beginPath(),t.lineWidth=this.r/2,t.strokeStyle=this.color,t.moveTo(this.x+this.tilt+this.r/4,this.y),t.lineTo(this.x+this.tilt,this.y+this.tilt+this.r/4),t.stroke()}}function h(e,t){(e.x>n+20||e.x<-20||e.y>i)&&d&&(t%5>0||t%2==0?v(e,Math.random()*n,-10,Math.floor(10*Math.random())-10):Math.sin(c)>0?v(e,-5,Math.random()*i,Math.floor(10*Math.random())-10):v(e,n+5,Math.random()*i,Math.floor(10*Math.random())-10))}function g(e,t){e.tiltAngle+=e.tiltAngleIncremental,e.y+=(Math.cos(c+e.d)+3+e.r/2)/2,e.x+=Math.sin(c),e.tilt=15*Math.sin(e.tiltAngle-t/3)}function v(e,t,n,i){e.x=t,e.y=n,e.tilt=i}function T(){clearTimeout(o),clearTimeout(a)}function y(){u=!0,null!=t&&(t.clearRect(0,0,n,i),e.style.display="none")}return window.requestAnimFrameT4=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e){return window.setTimeout(e,1e3/60)},function(){if(p)return T(),y(),o=setTimeout((function(){d=!0,u=!1,function(){e.style.display="block",l=[],u=!1;for(var o=0;o<r;o++){var s=m.getColor();l.push(new f(s))}n=window.innerWidth,i=window.innerHeight,e.width=n,e.height=i,function e(){return u?null:(a=requestAnimFrameT4(e),function(){t.clearRect(0,0,n,i);for(var e=[],o=0;o<r;o++)a=o,e.push(l[a].draw());var a;return function(){var e,t=0;c+=.01,.1;for(var n=0;n<r;n++){if(e=l[n],u)return;!d&&e.y<-15?e.y=i+100:(g(e,n),e.y<=i&&t++,h(e,n))}0===t&&y()}(),e}())}()}()}),100),void setTimeout((function(){d=!1,T()}),3500);T4SThemeSP.$appendComponent.after('<canvas id="confettiCanvas" style="position:fixed;top:0;left:0;display:none;z-index:9999;pointer-events: none;"></canvas>'),e=document.getElementById("confettiCanvas"),t=e.getContext("2d"),n=window.innerWidth,i=window.innerHeight,e.width=n,e.height=i,s.resize((function(){n=window.innerWidth,i=window.innerHeight,e.width=n,e.height=i})),p=!0}}(),T4SThemeSP.ToggleClass=void e(document).on("click","[data-toggle-class]",(function(t){var n=e(this).attr("data-toggle-class"),i=e(this).attr("data-toggle-trigger");e(this).toggleClass(n),e(i).toggleClass(n)}))}(jQuery_T4NT),jQuery_T4NT(document).ready((function(){T4SThemeSP.Hover(),T4SThemeSP.Header.stickyInit(),T4SThemeSP.MobileNav(),T4SThemeSP.Cart.init(),T4SThemeSP.agreeForm(),T4SThemeSP.Login(),T4SThemeSP.Compare.init(),T4SThemeSP.Wishlist.init(),T4SThemeSP.recentlyViewed(),T4SThemeSP.productRecommendations(),T4SThemeSP.ProductItem.init(),T4SThemeSP.ProductItem.loadjsRevew(),T4SThemeSP.Tooltip(),T4SThemeSP.ProductItem.clickMoreSwatches(),T4SThemeSP.ProductItem.swatchesClickHover(),T4SThemeSP.ProductItem.resizeObserver(),T4SThemeSP.ProductItem.initQuickVS(),T4SThemeSP.RenderRefresh(),T4SThemeSP.ProductAjax.init(),T4SThemeSP._initBundlePrs(),T4SThemeSP.T4sQuantityAdjust(),T4SThemeSP.PhotoSwipe.gallery(),T4SThemeSP.PhotoSwipe.images(),T4SThemeSP.PhotoSwipe.image(),T4SThemeSP.Video.initPoster(),T4SThemeSP.initLoadMore(),(jQuery_T4NT(".t4s-section-main [data-ntajax-container]").length>0||IsDesignMode)&&$script(T4Sconfigs.script7,"t4s:facets"),T4SThemeSP.instagram(),T4SThemeSP.sideBarInit(),T4SThemeSP.LookBook(),T4SThemeSP.initGroupsProduct(),IsDesignMode?T4SThemeSP.PopupPro():setTimeout((function(){T4SThemeSP.PopupFetch()}),686),setTimeout((function(){T4SThemeSP.PlatFormMail(),T4SThemeSP.goToID()}),500),T4SThemeSP.isHover&&jQuery_T4NT("[data-zoom-options]").length>0&&$script(T4Sconfigs.script5,"t4s:zoom"),document.addEventListener("theme:hover",(function(e){jQuery_T4NT("[data-zoom-options]").length>0&&$script(T4Sconfigs.script5,"t4s:zoom")})),T4SThemeSP.BackToTop(),T4SThemeSP.Header.init(),T4SThemeSP.currencyForm(),"2"==T4Sconfigs.currency_type&&$script(T4Sconfigs.script12a),"none"!=T4Sconfigs.script11&&$script(T4Sconfigs.script11,"t4s:customjs")})),jQuery_T4NT(window).resize((function(){T4SThemeSP.ProductItem.recalculateSwatches(!0)}));