"undefined"!=typeof exports&&(exports=void 0)
/*!
 * Jarallax v2.1.3 (https://github.com/nk-o/jarallax)
 * Copyright 2023 nK <https://nkdev.info>
 * Licensed under MIT (https://github.com/nk-o/jarallax/blob/master/LICENSE)
 *  Replace jquery to jQuery_T4NT
*/,function(t,e){(t="undefined"!=typeof globalThis?globalThis:t||self).jarallax=function(){"use strict";function t(t){"complete"===document.readyState||"interactive"===document.readyState?t():document.addEventListener("DOMContentLoaded",t,{capture:!0,once:!0,passive:!0})}let e;e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};var i=e,a={type:"scroll",speed:.5,containerClass:"jarallax-container",imgSrc:null,imgElement:".jarallax-img",imgSize:"cover",imgPosition:"50% 50%",imgRepeat:"no-repeat",keepImg:!1,elementInViewport:null,zIndex:-100,disableParallax:!1,onScroll:null,onInit:null,onDestroy:null,onCoverImage:null,videoClass:"jarallax-video",videoSrc:null,videoStartTime:0,videoEndTime:0,videoVolume:0,videoLoop:!0,videoPlayOnlyVisible:!0,videoLazyLoading:!0,disableVideo:!1,onVideoInsert:null,onVideoWorkerInit:null};const{navigator:n}=i,o=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(n.userAgent);let s,r,l;function d(){s=i.innerWidth||document.documentElement.clientWidth,o?(!l&&document.body&&(l=document.createElement("div"),l.style.cssText="position: fixed; top: -9999px; left: 0; height: 100vh; width: 0;",document.body.appendChild(l)),r=(l?l.clientHeight:0)||i.innerHeight||document.documentElement.clientHeight):r=i.innerHeight||document.documentElement.clientHeight}function c(){return{width:s,height:r}}d(),i.addEventListener("resize",d),i.addEventListener("orientationchange",d),i.addEventListener("load",d),t((()=>{d()}));const u=[];function f(){if(!u.length)return;const{width:t,height:e}=c();u.forEach(((i,a)=>{const{instance:n,oldData:o}=i;if(!n.isVisible())return;const s=n.$item.getBoundingClientRect(),r={width:s.width,height:s.height,top:s.top,bottom:s.bottom,wndW:t,wndH:e},l=!o||o.wndW!==r.wndW||o.wndH!==r.wndH||o.width!==r.width||o.height!==r.height,d=l||!o||o.top!==r.top||o.bottom!==r.bottom;u[a].oldData=r,l&&n.onResize(),d&&n.onScroll()})),i.requestAnimationFrame(f)}const p=new i.IntersectionObserver((t=>{t.forEach((t=>{t.target.jarallax.isElementInViewport=t.isIntersecting}))}),{rootMargin:"50px"}),{navigator:h}=i;let m=0;class g{constructor(t,e){const i=this;i.instanceID=m,m+=1,i.$item=t,i.defaults={...a};const n=i.$item.dataset||{},o={};if(Object.keys(n).forEach((t=>{const e=t.substr(0,1).toLowerCase()+t.substr(1);e&&void 0!==i.defaults[e]&&(o[e]=n[t])})),i.options=i.extend({},i.defaults,o,e),i.pureOptions=i.extend({},i.options),Object.keys(i.options).forEach((t=>{"true"===i.options[t]?i.options[t]=!0:"false"===i.options[t]&&(i.options[t]=!1)})),i.options.speed=Math.min(2,Math.max(-1,parseFloat(i.options.speed))),"string"==typeof i.options.disableParallax&&(i.options.disableParallax=new RegExp(i.options.disableParallax)),i.options.disableParallax instanceof RegExp){const t=i.options.disableParallax;i.options.disableParallax=()=>t.test(h.userAgent)}if("function"!=typeof i.options.disableParallax&&(i.options.disableParallax=()=>!1),"string"==typeof i.options.disableVideo&&(i.options.disableVideo=new RegExp(i.options.disableVideo)),i.options.disableVideo instanceof RegExp){const t=i.options.disableVideo;i.options.disableVideo=()=>t.test(h.userAgent)}"function"!=typeof i.options.disableVideo&&(i.options.disableVideo=()=>!1);let s=i.options.elementInViewport;s&&"object"==typeof s&&void 0!==s.length&&([s]=s),s instanceof Element||(s=null),i.options.elementInViewport=s,i.image={src:i.options.imgSrc||null,$container:null,useImgTag:!1,position:"fixed"},i.initImg()&&i.canInitParallax()&&i.init()}css(t,e){return function(t,e){return"string"==typeof e?i.getComputedStyle(t).getPropertyValue(e):(Object.keys(e).forEach((i=>{t.style[i]=e[i]})),t)}(t,e)}extend(t,...e){return function(t,...e){return t=t||{},Object.keys(e).forEach((i=>{e[i]&&Object.keys(e[i]).forEach((a=>{t[a]=e[i][a]}))})),t}(t,...e)}getWindowData(){const{width:t,height:e}=c();return{width:t,height:e,y:document.documentElement.scrollTop}}initImg(){const t=this;let e=t.options.imgElement;return e&&"string"==typeof e&&(e=t.$item.querySelector(e)),e instanceof Element||(t.options.imgSrc?(e=new Image,e.src=t.options.imgSrc):e=null),e&&(t.options.keepImg?t.image.$item=e.cloneNode(!0):(t.image.$item=e,t.image.$itemParent=e.parentNode),t.image.useImgTag=!0),!(!t.image.$item&&(null===t.image.src&&(t.image.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",t.image.bgImage=t.css(t.$item,"background-image")),!t.image.bgImage||"none"===t.image.bgImage))}canInitParallax(){return!this.options.disableParallax()}init(){const t=this,e={position:"absolute",top:0,left:0,width:"100%",height:"100%",overflow:"hidden"};let a={pointerEvents:"none",transformStyle:"preserve-3d",backfaceVisibility:"hidden"};if(!t.options.keepImg){const e=t.$item.getAttribute("style");if(e&&t.$item.setAttribute("data-jarallax-original-styles",e),t.image.useImgTag){const e=t.image.$item.getAttribute("style");e&&t.image.$item.setAttribute("data-jarallax-original-styles",e)}}if("static"===t.css(t.$item,"position")&&t.css(t.$item,{position:"relative"}),"auto"===t.css(t.$item,"z-index")&&t.css(t.$item,{zIndex:0}),t.image.$container=document.createElement("div"),t.css(t.image.$container,e),t.css(t.image.$container,{"z-index":t.options.zIndex}),"fixed"===this.image.position&&t.css(t.image.$container,{"-webkit-clip-path":"polygon(0 0, 100% 0, 100% 100%, 0 100%)","clip-path":"polygon(0 0, 100% 0, 100% 100%, 0 100%)"}),t.image.$container.setAttribute("id",`jarallax-container-${t.instanceID}`),t.options.containerClass&&t.image.$container.setAttribute("class",t.options.containerClass),t.$item.appendChild(t.image.$container),t.image.useImgTag?a=t.extend({"object-fit":t.options.imgSize,"object-position":t.options.imgPosition,"max-width":"none"},e,a):(t.image.$item=document.createElement("div"),t.image.src&&(a=t.extend({"background-position":t.options.imgPosition,"background-size":t.options.imgSize,"background-repeat":t.options.imgRepeat,"background-image":t.image.bgImage||`url("${t.image.src}")`},e,a))),"opacity"!==t.options.type&&"scale"!==t.options.type&&"scale-opacity"!==t.options.type&&1!==t.options.speed||(t.image.position="absolute"),"fixed"===t.image.position){const e=function(t){const e=[];for(;null!==t.parentElement;)1===(t=t.parentElement).nodeType&&e.push(t);return e}(t.$item).filter((t=>{const e=i.getComputedStyle(t),a=e["-webkit-transform"]||e["-moz-transform"]||e.transform;return a&&"none"!==a||/(auto|scroll)/.test(e.overflow+e["overflow-y"]+e["overflow-x"])}));t.image.position=e.length?"absolute":"fixed"}var n;a.position=t.image.position,t.css(t.image.$item,a),t.image.$container.appendChild(t.image.$item),t.onResize(),t.onScroll(!0),t.options.onInit&&t.options.onInit.call(t),"none"!==t.css(t.$item,"background-image")&&t.css(t.$item,{"background-image":"none"}),n=t,u.push({instance:n}),1===u.length&&i.requestAnimationFrame(f),p.observe(n.options.elementInViewport||n.$item)}destroy(){const t=this;var e;e=t,u.forEach(((t,i)=>{t.instance.instanceID===e.instanceID&&u.splice(i,1)})),p.unobserve(e.options.elementInViewport||e.$item);const i=t.$item.getAttribute("data-jarallax-original-styles");if(t.$item.removeAttribute("data-jarallax-original-styles"),i?t.$item.setAttribute("style",i):t.$item.removeAttribute("style"),t.image.useImgTag){const e=t.image.$item.getAttribute("data-jarallax-original-styles");t.image.$item.removeAttribute("data-jarallax-original-styles"),e?t.image.$item.setAttribute("style",i):t.image.$item.removeAttribute("style"),t.image.$itemParent&&t.image.$itemParent.appendChild(t.image.$item)}t.image.$container&&t.image.$container.parentNode.removeChild(t.image.$container),t.options.onDestroy&&t.options.onDestroy.call(t),delete t.$item.jarallax}coverImage(){const t=this,{height:e}=c(),i=t.image.$container.getBoundingClientRect(),a=i.height,{speed:n}=t.options,o="scroll"===t.options.type||"scroll-opacity"===t.options.type;let s=0,r=a,l=0;return o&&(n<0?(s=n*Math.max(a,e),e<a&&(s-=n*(a-e))):s=n*(a+e),n>1?r=Math.abs(s-e):n<0?r=s/n+Math.abs(s):r+=(e-a)*(1-n),s/=2),t.parallaxScrollDistance=s,l=o?(e-r)/2:(a-r)/2,t.css(t.image.$item,{height:`${r}px`,marginTop:`${l}px`,left:"fixed"===t.image.position?`${i.left}px`:"0",width:`${i.width}px`}),t.options.onCoverImage&&t.options.onCoverImage.call(t),{image:{height:r,marginTop:l},container:i}}isVisible(){return this.isElementInViewport||!1}onScroll(t){const e=this;if(!t&&!e.isVisible())return;const{height:i}=c(),a=e.$item.getBoundingClientRect(),n=a.top,o=a.height,s={},r=Math.max(0,n),l=Math.max(0,o+n),d=Math.max(0,-n),u=Math.max(0,n+o-i),f=Math.max(0,o-(n+o-i)),p=Math.max(0,-n+i-o),h=1-(i-n)/(i+o)*2;let m=1;if(o<i?m=1-(d||u)/o:l<=i?m=l/i:f<=i&&(m=f/i),"opacity"!==e.options.type&&"scale-opacity"!==e.options.type&&"scroll-opacity"!==e.options.type||(s.transform="translate3d(0,0,0)",s.opacity=m),"scale"===e.options.type||"scale-opacity"===e.options.type){let t=1;e.options.speed<0?t-=e.options.speed*m:t+=e.options.speed*(1-m),s.transform=`scale(${t}) translate3d(0,0,0)`}if("scroll"===e.options.type||"scroll-opacity"===e.options.type){let t=e.parallaxScrollDistance*h;"absolute"===e.image.position&&(t-=n),s.transform=`translate3d(0,${t}px,0)`}e.css(e.image.$item,s),e.options.onScroll&&e.options.onScroll.call(e,{section:a,beforeTop:r,beforeTopEnd:l,afterTop:d,beforeBottom:u,beforeBottomEnd:f,afterBottom:p,visiblePercent:m,fromViewportCenter:h})}onResize(){this.coverImage()}}const v=function(t,e,...i){("object"==typeof HTMLElement?t instanceof HTMLElement:t&&"object"==typeof t&&null!==t&&1===t.nodeType&&"string"==typeof t.nodeName)&&(t=[t]);const a=t.length;let n,o=0;for(;o<a;o+=1)if("object"==typeof e||void 0===e?t[o].jarallax||(t[o].jarallax=new g(t[o],e)):t[o].jarallax&&(n=t[o].jarallax[e].apply(t[o].jarallax,i)),void 0!==n)return n;return t};v.constructor=g;const y=i.jQuery_T4NT;if(void 0!==y){const t=function(...t){Array.prototype.unshift.call(t,this);const e=v.apply(i,t);return"object"!=typeof e?e:this};t.constructor=v.constructor;const e=y.fn.jarallax;y.fn.jarallax=t,y.fn.jarallax.noConflict=function(){return y.fn.jarallax=e,this}}return t((()=>{v(document.querySelectorAll("[data-jarallax]"))})),v}()}(this),jQuery_T4NT.fn.t4sJarallax=jQuery_T4NT.fn.jarallax.noConflict(),function(t){"use strict";"function"==typeof define&&define.amd?define(["jQuery_T4NT"],t):t(jQuery_T4NT)}((function(t){"use strict";function e(t){var e=t.toString().replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1");return new RegExp(e)}function i(t){return function(i){var n=i.match(/%(-|!)?[A-Z]{1}(:[^;]+;)?/gi);if(n)for(var o=0,s=n.length;o<s;++o){var l=n[o].match(/%(-|!)?([a-zA-Z]{1})(:[^;]+;)?/),d=e(l[0]),c=l[1]||"",u=l[3]||"",f=null;l=l[2],r.hasOwnProperty(l)&&(f=r[l],f=Number(t[f])),null!==f&&("!"===c&&(f=a(u,f)),""===c&&f<10&&(f="0"+f.toString()),i=i.replace(d,f.toString()))}return i.replace(/%%/,"%")}}function a(t,e){var i="s",a="";return t&&(1===(t=t.replace(/(:|;|\s)/gi,"").split(/\,/)).length?i=t[0]:(a=t[0],i=t[1])),Math.abs(e)>1?i:a}var n=[],o=[],s={precision:100,elapse:!1,defer:!1};o.push(/^[0-9]*$/.source),o.push(/([0-9]{1,2}\/){2}[0-9]{4}( [0-9]{1,2}(:[0-9]{2}){2})?/.source),o.push(/[0-9]{4}([\/\-][0-9]{1,2}){2}( [0-9]{1,2}(:[0-9]{2}){2})?/.source),o=new RegExp(o.join("|"));var r={Y:"years",m:"months",n:"daysToMonth",d:"daysToWeek",w:"weeks",W:"weeksToMonth",H:"hours",M:"minutes",S:"seconds",D:"totalDays",I:"totalHours",N:"totalMinutes",T:"totalSeconds"},l=function(e,i,a){this.el=e,this.$el=t(e),this.interval=null,this.offset={},this.options=t.extend({},s),this.instanceNumber=n.length,n.push(this),this.$el.data("countdown-instance",this.instanceNumber),a&&("function"==typeof a?(this.$el.on("update.countdown",a),this.$el.on("stoped.countdown",a),this.$el.on("finish.countdown",a)):this.options=t.extend({},s,a)),this.setFinalDate(i),!1===this.options.defer&&this.start()};t.extend(l.prototype,{start:function(){null!==this.interval&&clearInterval(this.interval);var t=this;this.update(),this.interval=setInterval((function(){t.update.call(t)}),this.options.precision)},stop:function(){clearInterval(this.interval),this.interval=null,this.dispatchEvent("stoped")},toggle:function(){this.interval?this.stop():this.start()},pause:function(){this.stop()},resume:function(){this.start()},remove:function(){this.stop.call(this),n[this.instanceNumber]=null,delete this.$el.data().countdownInstance},setFinalDate:function(t){this.finalDate=function(t){if(t instanceof Date)return t;if(String(t).match(o))return String(t).match(/^[0-9]*$/)&&(t=Number(t)),String(t).match(/\-/)&&(t=String(t).replace(/\-/g,"/")),new Date(t);throw new Error("Couldn't cast `"+t+"` to a date object.")}(t)},update:function(){if(0!==this.$el.closest("html").length){var e,i=void 0!==t._data(this.el,"events"),a=new Date;e=this.finalDate.getTime()-a.getTime(),e=Math.ceil(e/1e3),e=!this.options.elapse&&e<0?0:Math.abs(e),this.totalSecsLeft!==e&&i&&(this.totalSecsLeft=e,this.elapsed=a>=this.finalDate,this.offset={seconds:this.totalSecsLeft%60,minutes:Math.floor(this.totalSecsLeft/60)%60,hours:Math.floor(this.totalSecsLeft/60/60)%24,days:Math.floor(this.totalSecsLeft/60/60/24)%7,daysToWeek:Math.floor(this.totalSecsLeft/60/60/24)%7,daysToMonth:Math.floor(this.totalSecsLeft/60/60/24%30.4368),weeks:Math.floor(this.totalSecsLeft/60/60/24/7),weeksToMonth:Math.floor(this.totalSecsLeft/60/60/24/7)%4,months:Math.floor(this.totalSecsLeft/60/60/24/30.4368),years:Math.abs(this.finalDate.getFullYear()-a.getFullYear()),totalDays:Math.floor(this.totalSecsLeft/60/60/24),totalHours:Math.floor(this.totalSecsLeft/60/60),totalMinutes:Math.floor(this.totalSecsLeft/60),totalSeconds:this.totalSecsLeft},this.options.elapse||0!==this.totalSecsLeft?this.dispatchEvent("update"):(this.stop(),this.dispatchEvent("finish")))}else this.remove()},dispatchEvent:function(e){var a=t.Event(e+".countdown");a.finalDate=this.finalDate,a.elapsed=this.elapsed,a.offset=t.extend({},this.offset),a.strftime=i(this.offset),this.$el.trigger(a)}}),t.fn.countdown=function(){var e=Array.prototype.slice.call(arguments,0);return this.each((function(){var i=t(this).data("countdown-instance");if(void 0!==i){var a=n[i],o=e[0];l.prototype.hasOwnProperty(o)?a[o].apply(a,e.slice(1)):null===String(o).match(/^[$A-Z_][0-9A-Z_$]*$/i)?(a.setFinalDate.call(a,o),a.start()):t.error("Method %s does not exist on jQuery_T4NT.countdown".replace(/\%s/gi,o))}else new l(this,e[0],e[1])}))}})),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).dayjs=e()}(this,(function(){"use strict";var t=6e4,e=36e5,i="millisecond",a="second",n="minute",o="hour",s="day",r="week",l="month",d="quarter",c="year",u="date",f="Invalid Date",p=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,h=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,m={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_")},g=function(t,e,i){var a=String(t);return!a||a.length>=e?t:""+Array(e+1-a.length).join(i)+t},v={s:g,z:function(t){var e=-t.utcOffset(),i=Math.abs(e),a=Math.floor(i/60),n=i%60;return(e<=0?"+":"-")+g(a,2,"0")+":"+g(n,2,"0")},m:function t(e,i){if(e.date()<i.date())return-t(i,e);var a=12*(i.year()-e.year())+(i.month()-e.month()),n=e.clone().add(a,l),o=i-n<0,s=e.clone().add(a+(o?-1:1),l);return+(-(a+(i-n)/(o?n-s:s-n))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:l,y:c,w:r,d:s,D:u,h:o,m:n,s:a,ms:i,Q:d}[t]||String(t||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},y="en",b={};b[y]=m;var S=function(t){return t instanceof $},w=function(t,e,i){var a;if(!t)return y;if("string"==typeof t)b[t]&&(a=t),e&&(b[t]=e,a=t);else{var n=t.name;b[n]=t,a=n}return!i&&a&&(y=a),a||!i&&y},_=function(t,e){if(S(t))return t.clone();var i="object"==typeof e?e:{};return i.date=t,i.args=arguments,new $(i)},T=v;T.l=w,T.i=S,T.w=function(t,e){return _(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var $=function(){function m(t){this.$L=w(t.locale,null,!0),this.parse(t)}var g=m.prototype;return g.parse=function(t){this.$d=function(t){var e=t.date,i=t.utc;if(null===e)return new Date(NaN);if(T.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var a=e.match(p);if(a){var n=a[2]-1||0,o=(a[7]||"0").substring(0,3);return i?new Date(Date.UTC(a[1],n,a[3]||1,a[4]||0,a[5]||0,a[6]||0,o)):new Date(a[1],n,a[3]||1,a[4]||0,a[5]||0,a[6]||0,o)}}return new Date(e)}(t),this.$x=t.x||{},this.init()},g.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},g.$utils=function(){return T},g.isValid=function(){return!(this.$d.toString()===f)},g.isSame=function(t,e){var i=_(t);return this.startOf(e)<=i&&i<=this.endOf(e)},g.isAfter=function(t,e){return _(t)<this.startOf(e)},g.isBefore=function(t,e){return this.endOf(e)<_(t)},g.$g=function(t,e,i){return T.u(t)?this[e]:this.set(i,t)},g.unix=function(){return Math.floor(this.valueOf()/1e3)},g.valueOf=function(){return this.$d.getTime()},g.startOf=function(t,e){var i=this,d=!!T.u(e)||e,f=T.p(t),p=function(t,e){var a=T.w(i.$u?Date.UTC(i.$y,e,t):new Date(i.$y,e,t),i);return d?a:a.endOf(s)},h=function(t,e){return T.w(i.toDate()[t].apply(i.toDate("s"),(d?[0,0,0,0]:[23,59,59,999]).slice(e)),i)},m=this.$W,g=this.$M,v=this.$D,y="set"+(this.$u?"UTC":"");switch(f){case c:return d?p(1,0):p(31,11);case l:return d?p(1,g):p(0,g+1);case r:var b=this.$locale().weekStart||0,S=(m<b?m+7:m)-b;return p(d?v-S:v+(6-S),g);case s:case u:return h(y+"Hours",0);case o:return h(y+"Minutes",1);case n:return h(y+"Seconds",2);case a:return h(y+"Milliseconds",3);default:return this.clone()}},g.endOf=function(t){return this.startOf(t,!1)},g.$set=function(t,e){var r,d=T.p(t),f="set"+(this.$u?"UTC":""),p=(r={},r[s]=f+"Date",r[u]=f+"Date",r[l]=f+"Month",r[c]=f+"FullYear",r[o]=f+"Hours",r[n]=f+"Minutes",r[a]=f+"Seconds",r[i]=f+"Milliseconds",r)[d],h=d===s?this.$D+(e-this.$W):e;if(d===l||d===c){var m=this.clone().set(u,1);m.$d[p](h),m.init(),this.$d=m.set(u,Math.min(this.$D,m.daysInMonth())).$d}else p&&this.$d[p](h);return this.init(),this},g.set=function(t,e){return this.clone().$set(t,e)},g.get=function(t){return this[T.p(t)]()},g.add=function(i,d){var u,f=this;i=Number(i);var p=T.p(d),h=function(t){var e=_(f);return T.w(e.date(e.date()+Math.round(t*i)),f)};if(p===l)return this.set(l,this.$M+i);if(p===c)return this.set(c,this.$y+i);if(p===s)return h(1);if(p===r)return h(7);var m=(u={},u[n]=t,u[o]=e,u[a]=1e3,u)[p]||1,g=this.$d.getTime()+i*m;return T.w(g,this)},g.subtract=function(t,e){return this.add(-1*t,e)},g.format=function(t){var e=this,i=this.$locale();if(!this.isValid())return i.invalidDate||f;var a=t||"YYYY-MM-DDTHH:mm:ssZ",n=T.z(this),o=this.$H,s=this.$m,r=this.$M,l=i.weekdays,d=i.months,c=function(t,i,n,o){return t&&(t[i]||t(e,a))||n[i].substr(0,o)},u=function(t){return T.s(o%12||12,t,"0")},p=i.meridiem||function(t,e,i){var a=t<12?"AM":"PM";return i?a.toLowerCase():a},m={YY:String(this.$y).slice(-2),YYYY:this.$y,M:r+1,MM:T.s(r+1,2,"0"),MMM:c(i.monthsShort,r,d,3),MMMM:c(d,r),D:this.$D,DD:T.s(this.$D,2,"0"),d:String(this.$W),dd:c(i.weekdaysMin,this.$W,l,2),ddd:c(i.weekdaysShort,this.$W,l,3),dddd:l[this.$W],H:String(o),HH:T.s(o,2,"0"),h:u(1),hh:u(2),a:p(o,s,!0),A:p(o,s,!1),m:String(s),mm:T.s(s,2,"0"),s:String(this.$s),ss:T.s(this.$s,2,"0"),SSS:T.s(this.$ms,3,"0"),Z:n};return a.replace(h,(function(t,e){return e||m[t]||n.replace(":","")}))},g.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},g.diff=function(i,u,f){var p,h=T.p(u),m=_(i),g=(m.utcOffset()-this.utcOffset())*t,v=this-m,y=T.m(this,m);return y=(p={},p[c]=y/12,p[l]=y,p[d]=y/3,p[r]=(v-g)/6048e5,p[s]=(v-g)/864e5,p[o]=v/e,p[n]=v/t,p[a]=v/1e3,p)[h]||v,f?y:T.a(y)},g.daysInMonth=function(){return this.endOf(l).$D},g.$locale=function(){return b[this.$L]},g.locale=function(t,e){if(!t)return this.$L;var i=this.clone(),a=w(t,e,!0);return a&&(i.$L=a),i},g.clone=function(){return T.w(this.$d,this)},g.toDate=function(){return new Date(this.valueOf())},g.toJSON=function(){return this.isValid()?this.toISOString():null},g.toISOString=function(){return this.$d.toISOString()},g.toString=function(){return this.$d.toUTCString()},m}(),C=$.prototype;return _.prototype=C,[["$ms",i],["$s",a],["$m",n],["$H",o],["$W",s],["$M",l],["$y",c],["$D",u]].forEach((function(t){C[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),_.extend=function(t,e){return t.$i||(t(e,$,_),t.$i=!0),_},_.locale=w,_.isDayjs=S,_.unix=function(t){return _(1e3*t)},_.en=b[y],_.Ls=b,_.p={},_})),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).dayjs_plugin_utc=e()}(this,(function(){"use strict";var t="minute",e=/[+-]\d\d(?::?\d\d)?/g,i=/([+-]|\d\d)/g;return function(a,n,o){var s=n.prototype;o.utc=function(t){return new n({date:t,utc:!0,args:arguments})},s.utc=function(e){var i=o(this.toDate(),{locale:this.$L,utc:!0});return e?i.add(this.utcOffset(),t):i},s.local=function(){return o(this.toDate(),{locale:this.$L,utc:!1})};var r=s.parse;s.parse=function(t){t.utc&&(this.$u=!0),this.$utils().u(t.$offset)||(this.$offset=t.$offset),r.call(this,t)};var l=s.init;s.init=function(){if(this.$u){var t=this.$d;this.$y=t.getUTCFullYear(),this.$M=t.getUTCMonth(),this.$D=t.getUTCDate(),this.$W=t.getUTCDay(),this.$H=t.getUTCHours(),this.$m=t.getUTCMinutes(),this.$s=t.getUTCSeconds(),this.$ms=t.getUTCMilliseconds()}else l.call(this)};var d=s.utcOffset;s.utcOffset=function(a,n){var o=this.$utils().u;if(o(a))return this.$u?0:o(this.$offset)?d.call(this):this.$offset;if("string"==typeof a&&null===(a=function(t){void 0===t&&(t="");var a=t.match(e);if(!a)return null;var n=(""+a[0]).match(i)||["-",0,0],o=n[0],s=60*+n[1]+ +n[2];return 0===s?0:"+"===o?s:-s}(a)))return this;var s=Math.abs(a)<=16?60*a:a,r=this;if(n)return r.$offset=s,r.$u=0===a,r;if(0!==a){var l=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(r=this.local().add(s+l,t)).$offset=s,r.$x.$localOffset=l}else r=this.utc();return r};var c=s.format;s.format=function(t){var e=t||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return c.call(this,e)},s.valueOf=function(){var t=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||(new Date).getTimezoneOffset());return this.$d.valueOf()-6e4*t},s.isUTC=function(){return!!this.$u},s.toISOString=function(){return this.toDate().toISOString()},s.toString=function(){return this.toDate().toUTCString()};var u=s.toDate;s.toDate=function(t){return"s"===t&&this.$offset?o(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():u.call(this)};var f=s.diff;s.diff=function(t,e,i){if(t&&this.$u===t.$u)return f.call(this,t,e,i);var a=this.local(),n=o(t).local();return f.call(a,n,e,i)}}})),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).dayjs_plugin_timezone=e()}(this,(function(){"use strict";var t={year:0,month:1,day:2,hour:3,minute:4,second:5},e={};return function(i,a,n){var o,s=function(t,i,a){void 0===a&&(a={});var n=new Date(t);return function(t,i){void 0===i&&(i={});var a=i.timeZoneName||"short",n=t+"|"+a,o=e[n];return o||(o=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:t,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:a}),e[n]=o),o}(i,a).formatToParts(n)},r=function(e,i){for(var a=s(e,i),o=[],r=0;r<a.length;r+=1){var l=a[r],d=l.type,c=l.value,u=t[d];u>=0&&(o[u]=parseInt(c,10))}var f=o[3],p=24===f?0:f,h=o[0]+"-"+o[1]+"-"+o[2]+" "+p+":"+o[4]+":"+o[5]+":000",m=+e;return(n.utc(h).valueOf()-(m-=m%1e3))/6e4},l=a.prototype;l.tz=function(t,e){void 0===t&&(t=o);var i=this.utcOffset(),a=this.toDate(),s=a.toLocaleString("en-US",{timeZone:t}),r=Math.round((a-new Date(s))/1e3/60),l=n(s).$set("millisecond",this.$ms).utcOffset(15*-Math.round(a.getTimezoneOffset()/15)-r,!0);if(e){var d=l.utcOffset();l=l.add(i-d,"minute")}return l.$x.$timezone=t,l},l.offsetName=function(t){var e=this.$x.$timezone||n.tz.guess(),i=s(this.valueOf(),e,{timeZoneName:t}).find((function(t){return"timezonename"===t.type.toLowerCase()}));return i&&i.value};var d=l.startOf;l.startOf=function(t,e){if(!this.$x||!this.$x.$timezone)return d.call(this,t,e);var i=n(this.format("YYYY-MM-DD HH:mm:ss:SSS"));return d.call(i,t,e).tz(this.$x.$timezone,!0)},n.tz=function(t,e,i){var a=i&&e,s=i||e||o,l=r(+n(),s);if("string"!=typeof t)return n(t).tz(s);var d=function(t,e,i){var a=t-60*e*1e3,n=r(a,i);if(e===n)return[a,e];var o=r(a-=60*(n-e)*1e3,i);return n===o?[a,n]:[t-60*Math.min(n,o)*1e3,Math.max(n,o)]}(n.utc(t,a).valueOf(),l,s),c=d[0],u=d[1],f=n(c).utcOffset(u);return f.$x.$timezone=s,f},n.tz.guess=function(){return Intl.DateTimeFormat().resolvedOptions().timeZone},n.tz.setDefault=function(t){o=t}}}));var dayjs_utc=window.dayjs_plugin_utc,dayjs_timezone=window.dayjs_plugin_timezone;function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}function createCommonjsModule(t,e,i){return t(i={path:e,exports:{},require:function(t,e){return commonjsRequire(t,null==e?i.path:e)}},i.exports),i.exports}function commonjsRequire(){throw new Error("Error commonjs")}dayjs.locale("en"),dayjs.extend(dayjs_utc),dayjs.extend(dayjs_timezone),function(t){"function"==typeof define&&define.amd?define(["jQuery_T4NT"],t):"object"==typeof exports?t(require("jQuery_T4NT")):t(window.jQuery_T4NT||window.Zepto)}((function(t){var e,i,a,n,o,s,r={},l=function(){},d=!!window.jQuery_T4NT,c=t(window),u=function(t,i){e.ev.on("mfp"+t+".mfp",i)},f=function(e,i,a,n){var o=document.createElement("div");return o.className="mfp-"+e,a&&(o.innerHTML=a),n?i&&i.appendChild(o):(o=t(o),i&&o.appendTo(i)),o},p=function(i,a){e.ev.triggerHandler("mfp"+i,a),e.st.callbacks&&(i=i.charAt(0).toLowerCase()+i.slice(1),e.st.callbacks[i]&&e.st.callbacks[i].apply(e,t.isArray(a)?a:[a]))},h=function(i){return i===s&&e.currTemplate.closeBtn||(e.currTemplate.closeBtn=t(e.st.closeMarkup.replace("%title%",e.st.tClose)),s=i),e.currTemplate.closeBtn},m=function(){t.magnificPopupT4s.instance||((e=new l).init(),t.magnificPopupT4s.instance=e)};l.prototype={constructor:l,init:function(){var i=navigator.appVersion;e.isLowIE=e.isIE8=document.all&&!document.addEventListener,e.isAndroid=/android/gi.test(i),e.isIOS=/iphone|ipad|ipod/gi.test(i),e.supportsTransition=function(){var t=document.createElement("p").style,e=["ms","O","Moz","Webkit"];if(void 0!==t.transition)return!0;for(;e.length;)if(e.pop()+"Transition"in t)return!0;return!1}(),e.probablyMobile=e.isAndroid||e.isIOS||/(Opera Mini)|Kindle|webOS|BlackBerry|(Opera Mobi)|(Windows Phone)|IEMobile/i.test(navigator.userAgent),a=t(document),e.popupsCache={}},open:function(i){var n;if(!1===i.isObj){e.items=i.items.toArray(),e.index=0;var s,r=i.items;for(n=0;n<r.length;n++)if((s=r[n]).parsed&&(s=s.el[0]),s===i.el[0]){e.index=n;break}}else e.items=t.isArray(i.items)?i.items:[i.items],e.index=i.index||0;if(!e.isOpen){e.types=[],o="",i.mainEl&&i.mainEl.length?e.ev=i.mainEl.eq(0):e.ev=a,i.key?(e.popupsCache[i.key]||(e.popupsCache[i.key]={}),e.currTemplate=e.popupsCache[i.key]):e.currTemplate={},e.st=t.extend(!0,{},t.magnificPopupT4s.defaults,i),e.fixedContentPos="auto"===e.st.fixedContentPos?!e.probablyMobile:e.st.fixedContentPos,e.st.modal&&(e.st.closeOnContentClick=!1,e.st.closeOnBgClick=!1,e.st.showCloseBtn=!1,e.st.enableEscapeKey=!1),e.bgOverlay||(e.bgOverlay=f("bg").on("click.mfp",(function(){e.close()})),e.wrap=f("wrap").attr("tabindex",-1).on("click.mfp",(function(t){e._checkIfClose(t.target)&&e.close()})),e.container=f("container",e.wrap)),e.contentContainer=f("content"),e.st.preloader&&(e.preloader=f("preloader",e.container,e.st.tLoading));var l=t.magnificPopupT4s.modules;for(n=0;n<l.length;n++){var d=l[n];d=d.charAt(0).toUpperCase()+d.slice(1),e["init"+d].call(e)}p("BeforeOpen"),e.st.showCloseBtn&&(e.st.closeBtnInside?(u("MarkupParse",(function(t,e,i,a){i.close_replaceWith=h(a.type)})),o+=" mfp-close-btn-in"):e.wrap.append(h())),e.st.alignTop&&(o+=" mfp-align-top"),e.fixedContentPos?e.wrap.css({overflow:e.st.overflowY,overflowX:"hidden",overflowY:e.st.overflowY}):e.wrap.css({top:c.scrollTop(),position:"absolute"}),(!1===e.st.fixedBgPos||"auto"===e.st.fixedBgPos&&!e.fixedContentPos)&&e.bgOverlay.css({height:a.height(),position:"absolute"}),e.st.enableEscapeKey&&a.on("keyup.mfp",(function(t){27===t.keyCode&&e.close()})),c.on("resize.mfp",(function(){e.updateSize()})),e.st.closeOnContentClick||(o+=" mfp-auto-cursor"),o&&e.wrap.addClass(o);var m=e.wH=c.height(),g={};if(e.fixedContentPos&&e._hasScrollBar(m)){var v=e._getScrollbarSize();v&&(g.marginRight=v)}e.fixedContentPos&&(e.isIE7?t("body, html").css("overflow","hidden"):g.overflow="hidden");var y=e.st.mainClass;return e.isIE7&&(y+=" mfp-ie7"),y&&e._addClassToMFP(y),e.updateItemHTML(),p("BuildControls"),t("html").css(g),e.bgOverlay.add(e.wrap).prependTo(e.st.prependTo||t(document.body)),e._lastFocusedEl=document.activeElement,setTimeout((function(){e.content?(e._addClassToMFP("mfp-ready"),e._setFocus()):e.bgOverlay.addClass("mfp-ready"),a.on("focusin.mfp",e._onFocusIn)}),16),e.isOpen=!0,e.updateSize(m),p("Open"),i}e.updateItemHTML()},close:function(){e.isOpen&&(p("BeforeClose"),e.isOpen=!1,e.st.removalDelay&&!e.isLowIE&&e.supportsTransition?(e._addClassToMFP("mfp-removing"),setTimeout((function(){e._close()}),e.st.removalDelay)):e._close())},_close:function(){p("Close");var i="mfp-removing mfp-ready ";if(e.bgOverlay.detach(),e.wrap.detach(),e.container.empty(),e.st.mainClass&&(i+=e.st.mainClass+" "),e._removeClassFromMFP(i),e.fixedContentPos){var n={marginRight:""};e.isIE7?t("body, html").css("overflow",""):n.overflow="",t("html").css(n)}a.off("keyup.mfp focusin.mfp"),e.ev.off(".mfp"),e.wrap.attr("class","mfp-wrap").removeAttr("style"),e.bgOverlay.attr("class","mfp-bg"),e.container.attr("class","mfp-container"),!e.st.showCloseBtn||e.st.closeBtnInside&&!0!==e.currTemplate[e.currItem.type]||e.currTemplate.closeBtn&&e.currTemplate.closeBtn.detach(),e.st.autoFocusLast&&e._lastFocusedEl&&t(e._lastFocusedEl).focus(),e.currItem=null,e.content=null,e.currTemplate=null,e.prevHeight=0,p("AfterClose")},updateSize:function(t){if(e.isIOS){var i=document.documentElement.clientWidth/window.innerWidth,a=window.innerHeight*i;e.wrap.css("height",a),e.wH=a}else e.wH=t||c.height();e.fixedContentPos||e.wrap.css("height",e.wH),p("Resize")},updateItemHTML:function(){var i=e.items[e.index];e.contentContainer.detach(),e.content&&e.content.detach(),i.parsed||(i=e.parseEl(e.index));var a=i.type;if(p("BeforeChange",[e.currItem?e.currItem.type:"",a]),e.currItem=i,!e.currTemplate[a]){var o=!!e.st[a]&&e.st[a].markup;p("FirstMarkupParse",o),e.currTemplate[a]=!o||t(o)}n&&n!==i.type&&e.container.removeClass("mfp-"+n+"-holder");var s=e["get"+a.charAt(0).toUpperCase()+a.slice(1)](i,e.currTemplate[a]);e.appendContent(s,a),i.preloaded=!0,p("Change",i),n=i.type,e.container.prepend(e.contentContainer),p("AfterChange")},appendContent:function(t,i,a){if(""==t&&"ajax"==i)return!1;e.content=t,t?e.st.showCloseBtn&&e.st.closeBtnInside&&!0===e.currTemplate[i]?e.content.find(".mfp-close").length||e.content.append(h()):e.content=t:e.content="",p("BeforeAppend"),e.container.addClass("mfp-"+i+"-holder"),e.contentContainer.append(e.content)},parseEl:function(i){var a,n=e.items[i];if(n.tagName?n={el:t(n)}:(a=n.type,n={data:n,src:n.src}),n.el){for(var o=e.types,s=0;s<o.length;s++)if(n.el.hasClass("mfp-"+o[s])){a=o[s];break}n.src=n.el.attr("data-mfp-src"),n.src||(n.src=n.el.attr("href"))}return n.type=a||e.st.type||"inline",n.index=i,n.parsed=!0,e.items[i]=n,p("ElementParse",n),e.items[i]},addGroup:function(t,i){var a=function(a){a.mfpEl=this,e._openClick(a,t,i)};i||(i={});var n="click.magnificPopupT4s";i.mainEl=t,i.items?(i.isObj=!0,t.off(n).on(n,a)):(i.isObj=!1,i.delegate?t.off(n).on(n,i.delegate,a):(i.items=t,t.off(n).on(n,a)))},_openClick:function(i,a,n){if((void 0!==n.midClick?n.midClick:t.magnificPopupT4s.defaults.midClick)||!(2===i.which||i.ctrlKey||i.metaKey||i.altKey||i.shiftKey)){var o=void 0!==n.disableOn?n.disableOn:t.magnificPopupT4s.defaults.disableOn;if(o)if(t.isFunction(o)){if(!o.call(e))return!0}else if(c.width()<o)return!0;i.type&&(i.preventDefault(),e.isOpen&&i.stopPropagation()),n.el=t(i.mfpEl),n.delegate&&(n.items=a.find(n.delegate)),e.open(n)}},updateStatus:function(t,a){if(e.preloader){i!==t&&e.container.removeClass("mfp-s-"+i),a||"loading"!==t||(a=e.st.tLoading);var n={status:t,text:a};p("UpdateStatus",n),t=n.status,a=n.text,e.preloader.html(a),e.preloader.find("a").on("click",(function(t){t.stopImmediatePropagation()})),e.container.addClass("mfp-s-"+t),i=t}},_checkIfClose:function(i){if(!t(i).hasClass("mfp-prevent-close")){var a=e.st.closeOnContentClick,n=e.st.closeOnBgClick;if(a&&n)return!0;if(!e.content||t(i).hasClass("mfp-close")||e.preloader&&i===e.preloader[0])return!0;if(i===e.content[0]||t.contains(e.content[0],i)){if(a)return!0}else if(n&&t.contains(document,i))return!0;return!1}},_addClassToMFP:function(t){e.bgOverlay.addClass(t),e.wrap.addClass(t)},_removeClassFromMFP:function(t){this.bgOverlay.removeClass(t),e.wrap.removeClass(t)},_hasScrollBar:function(t){return(e.isIE7?a.height():document.body.scrollHeight)>(t||c.height())},_setFocus:function(){(e.st.focus?e.content.find(e.st.focus).eq(0):e.wrap).focus()},_onFocusIn:function(i){if(i.target!==e.wrap[0]&&!t.contains(e.wrap[0],i.target))return e._setFocus(),!1},_parseMarkup:function(e,i,a){var n;a.data&&(i=t.extend(a.data,i)),p("MarkupParse",[e,i,a]),t.each(i,(function(i,a){if(void 0===a||!1===a)return!0;if((n=i.split("_")).length>1){var o=e.find(".mfp-"+n[0]);if(o.length>0){var s=n[1];"replaceWith"===s?o[0]!==a[0]&&o.replaceWith(a):"img"===s?o.is("img")?o.attr("src",a):o.replaceWith(t("<img>").attr("src",a).attr("class",o.attr("class"))):o.attr(n[1],a)}}else e.find(".mfp-"+i).html(a)}))},_getScrollbarSize:function(){if(void 0===e.scrollbarSize){var t=document.createElement("div");t.style.cssText="width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;",document.body.appendChild(t),e.scrollbarSize=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return e.scrollbarSize}},t.magnificPopupT4s={instance:null,proto:l.prototype,modules:[],open:function(e,i){return m(),(e=e?t.extend(!0,{},e):{}).isObj=!0,e.index=i||0,this.instance.open(e)},close:function(){return t.magnificPopupT4s.instance&&t.magnificPopupT4s.instance.close()},registerModule:function(e,i){i.options&&(t.magnificPopupT4s.defaults[e]=i.options),t.extend(this.proto,i.proto),this.modules.push(e)},defaults:{disableOn:0,key:null,midClick:!1,mainClass:"",preloader:!0,focus:"",closeOnContentClick:!1,closeOnBgClick:!0,closeBtnInside:!0,showCloseBtn:!0,enableEscapeKey:!0,modal:!1,alignTop:!1,removalDelay:0,prependTo:null,fixedContentPos:"auto",fixedBgPos:"auto",overflowY:"auto",closeMarkup:'<button title="%title%" type="button" class="mfp-close"><svg class="t4smfp-icon-close" role="presentation" viewBox="0 0 16 14"><path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path></svg></button>',tClose:"Close (Esc)",tLoading:"Loading...",autoFocusLast:!0}},t.fn.magnificPopupT4s=function(i){m();var a=t(this);if("string"==typeof i)if("open"===i){var n,o=d?a.data("magnificPopup"):a[0].magnificPopupT4s,s=parseInt(arguments[1],10)||0;o.items?n=o.items[s]:(n=a,o.delegate&&(n=n.find(o.delegate)),n=n.eq(s)),e._openClick({mfpEl:n},a,o)}else e.isOpen&&e[i].apply(e,Array.prototype.slice.call(arguments,1));else i=t.extend(!0,{},i),d?a.data("magnificPopup",i):a[0].magnificPopupT4s=i,e.addGroup(a,i);return a};var g,v,y,b=function(){y&&(v.after(y.addClass(g)).detach(),y=null)};t.magnificPopupT4s.registerModule("inline",{options:{hiddenClass:"hide",markup:"",tNotFound:"Content not found"},proto:{initInline:function(){e.types.push("inline"),u("Close.inline",(function(){b()}))},getInline:function(i,a){if(b(),i.src){var n=e.st.inline,o=t(i.src);if(o.length){var s=o[0].parentNode;s&&s.tagName&&(v||(g=n.hiddenClass,v=f(g),g="mfp-"+g),y=o.after(v).detach().removeClass(g)),e.updateStatus("ready")}else e.updateStatus("error",n.tNotFound),o=t("<div>");return i.inlineElement=o,o}return e.updateStatus("ready"),e._parseMarkup(a,{},i),a}}});var S,w,_,T=function(){S&&t(document.body).removeClass(S)},$=function(){T(),e.req&&e.req.abort()};t.magnificPopupT4s.registerModule("ajax",{options:{settings:null,cursor:"mfp-ajax-cur",tError:'<a href="%url%">The content</a> could not be loaded.'},proto:{initAjax:function(){e.types.push("ajax"),S=e.st.ajax.cursor,u("Close.ajax",$),u("BeforeChange.ajax",$)},getAjax:function(i){S&&t(document.body).addClass(S),e.updateStatus("loading");var a=t(i.el).attr("data-storageid")||"nt94",n=t.extend({url:i.src,success:function(n,o,s){var l={data:n,xhr:s};p("ParseAjax",l),e.appendContent(t(l.data),"ajax"),i.finished=!0,T(),e._setFocus(),setTimeout((function(){e.wrap.addClass("mfp-ready")}),16),e.updateStatus("ready"),p("AjaxContentAdded"),r[a]=n},error:function(){T(),i.finished=i.loadError=!0,e.updateStatus("error",e.st.ajax.tError.replace("%url%",i.src))}},e.st.ajax.settings),o=r[a];if(void 0!==o){var s={data:o};p("ParseAjax",s),e.appendContent(t(s.data),"ajax"),T(),e._setFocus(),setTimeout((function(){e.wrap.addClass("mfp-ready")}),16),e.updateStatus("ready"),p("AjaxContentAdded")}else e.req=t.ajax(n);return""}}}),t.magnificPopupT4s.registerModule("image",{options:{markup:'<div class="mfp-figure"><div class="mfp-close"></div><figure><div class="mfp-img"></div><figcaption><div class="mfp-bottom-bar"><div class="mfp-title"></div><div class="mfp-counter"></div></div></figcaption></figure></div>',cursor:"mfp-zoom-out-cur",titleSrc:"title",verticalFit:!0,tError:'<a href="%url%">The image</a> could not be loaded.'},proto:{initImage:function(){var i=e.st.image,a=".image";e.types.push("image"),u("Open"+a,(function(){"image"===e.currItem.type&&i.cursor&&t(document.body).addClass(i.cursor)})),u("Close"+a,(function(){i.cursor&&t(document.body).removeClass(i.cursor),c.off("resize.mfp")})),u("Resize"+a,e.resizeImage),e.isLowIE&&u("AfterChange",e.resizeImage)},resizeImage:function(){var t=e.currItem;if(t&&t.img&&e.st.image.verticalFit){var i=0;e.isLowIE&&(i=parseInt(t.img.css("padding-top"),10)+parseInt(t.img.css("padding-bottom"),10)),t.img.css("max-height",e.wH-i)}},_onImageHasSize:function(t){t.img&&(t.hasSize=!0,w&&clearInterval(w),t.isCheckingImgSize=!1,p("ImageHasSize",t),t.imgHidden&&(e.content&&e.content.removeClass("mfp-loading"),t.imgHidden=!1))},findImageSize:function(t){var i=0,a=t.img[0],n=function(o){w&&clearInterval(w),w=setInterval((function(){a.naturalWidth>0?e._onImageHasSize(t):(i>200&&clearInterval(w),3==++i?n(10):40===i?n(50):100===i&&n(500))}),o)};n(1)},getImage:function(i,a){var n=0,o=function(){i&&(i.img[0].complete?(i.img.off(".mfploader"),i===e.currItem&&(e._onImageHasSize(i),e.updateStatus("ready")),i.hasSize=!0,i.loaded=!0,p("ImageLoadComplete")):++n<200?setTimeout(o,100):s())},s=function(){i&&(i.img.off(".mfploader"),i===e.currItem&&(e._onImageHasSize(i),e.updateStatus("error",r.tError.replace("%url%",i.src))),i.hasSize=!0,i.loaded=!0,i.loadError=!0)},r=e.st.image,l=a.find(".mfp-img");if(l.length){var d=document.createElement("img");d.className="mfp-img",i.el&&i.el.find("img").length&&(d.alt=i.el.find("img").attr("alt")),i.img=t(d).on("load.mfploader",o).on("error.mfploader",s),d.src=i.src,l.is("img")&&(i.img=i.img.clone()),(d=i.img[0]).naturalWidth>0?i.hasSize=!0:d.width||(i.hasSize=!1)}return e._parseMarkup(a,{title:function(i){if(i.data&&void 0!==i.data.title)return i.data.title;var a=e.st.image.titleSrc;if(a){if(t.isFunction(a))return a.call(e,i);if(i.el)return i.el.attr(a)||""}return""}(i),img_replaceWith:i.img},i),e.resizeImage(),i.hasSize?(w&&clearInterval(w),i.loadError?(a.addClass("mfp-loading"),e.updateStatus("error",r.tError.replace("%url%",i.src))):(a.removeClass("mfp-loading"),e.updateStatus("ready")),a):(e.updateStatus("loading"),i.loading=!0,i.hasSize||(i.imgHidden=!0,a.addClass("mfp-loading"),e.findImageSize(i)),a)}}}),t.magnificPopupT4s.registerModule("zoom",{options:{enabled:!1,easing:"ease-in-out",duration:300,opener:function(t){return t.is("img")?t:t.find("img")}},proto:{initZoom:function(){var t,i=e.st.zoom,a=".zoom";if(i.enabled&&e.supportsTransition){var n,o,s=i.duration,r=function(t){var e=t.clone().removeAttr("style").removeAttr("class").addClass("mfp-animated-image"),a="all "+i.duration/1e3+"s "+i.easing,n={position:"fixed",zIndex:9999,left:0,top:0,"-webkit-backface-visibility":"hidden"},o="transition";return n["-webkit-"+o]=n["-moz-"+o]=n["-o-"+o]=n[o]=a,e.css(n),e},l=function(){e.content.css("visibility","visible")};u("BuildControls"+a,(function(){if(e._allowZoom()){if(clearTimeout(n),e.content.css("visibility","hidden"),!(t=e._getItemToZoom()))return void l();(o=r(t)).css(e._getOffset()),e.wrap.append(o),n=setTimeout((function(){o.css(e._getOffset(!0)),n=setTimeout((function(){l(),setTimeout((function(){o.remove(),t=o=null,p("ZoomAnimationEnded")}),16)}),s)}),16)}})),u("BeforeClose"+a,(function(){if(e._allowZoom()){if(clearTimeout(n),e.st.removalDelay=s,!t){if(!(t=e._getItemToZoom()))return;o=r(t)}o.css(e._getOffset(!0)),e.wrap.append(o),e.content.css("visibility","hidden"),setTimeout((function(){o.css(e._getOffset())}),16)}})),u("Close"+a,(function(){e._allowZoom()&&(l(),o&&o.remove(),t=null)}))}},_allowZoom:function(){return"image"===e.currItem.type},_getItemToZoom:function(){return!!e.currItem.hasSize&&e.currItem.img},_getOffset:function(i){var a,n=(a=i?e.currItem.img:e.st.zoom.opener(e.currItem.el||e.currItem)).offset(),o=parseInt(a.css("padding-top"),10),s=parseInt(a.css("padding-bottom"),10);n.top-=t(window).scrollTop()-o;var r={width:a.width(),height:(d?a.innerHeight():a[0].offsetHeight)-s-o};return void 0===_&&(_=void 0!==document.createElement("p").style.MozTransform),_?r["-moz-transform"]=r.transform="translate("+n.left+"px,"+n.top+"px)":(r.left=n.left,r.top=n.top),r}}});var C=function(t){if(e.currTemplate.iframe){var i=e.currTemplate.iframe.find("iframe");i.length&&(t||(i[0].src="//about:blank"),e.isIE8&&i.css("display",t?"block":"none"))}};t.magnificPopupT4s.registerModule("iframe",{options:{markup:'<div class="mfp-iframe-scaler"><div class="mfp-close"></div><iframe class="mfp-iframe" src="//about:blank" frameborder="0" allowfullscreen></iframe></div>',srcAction:"iframe_src",patterns:{youtube:{index:"youtube.com",id:"v=",src:"//www.youtube.com/embed/%id%?autoplay=1"},vimeo:{index:"vimeo.com/",id:"/",src:"//player.vimeo.com/video/%id%?autoplay=1"},gmaps:{index:"//maps.google.",src:"%id%&output=embed"}}},proto:{initIframe:function(){e.types.push("iframe"),u("BeforeChange",(function(t,e,i){e!==i&&("iframe"===e?C():"iframe"===i&&C(!0))})),u("Close.iframe",(function(){C()}))},getIframe:function(i,a){var n=i.src,o=e.st.iframe;t.each(o.patterns,(function(){if(n.indexOf(this.index)>-1)return this.id&&(n="string"==typeof this.id?n.substr(n.lastIndexOf(this.id)+this.id.length,n.length):this.id.call(this,n)),n=this.src.replace(/%id%/g,n),!1}));var s={};return o.srcAction&&(s[o.srcAction]=n),e._parseMarkup(a,s,i),e.updateStatus("ready"),a}}});var k=function(t){var i=e.items.length;return t>i-1?t-i:t<0?i+t:t},I=function(t,e,i){return t.replace(/%curr%/gi,e+1).replace(/%total%/gi,i)};t.magnificPopupT4s.registerModule("gallery",{options:{enabled:!1,arrowMarkup:'<button title="%title%" type="button" class="mfp-arrow mfp-arrow-%dir%"></button>',preload:[0,2],navigateByImgClick:!0,arrows:!0,tPrev:"Previous (Left arrow key)",tNext:"Next (Right arrow key)",tCounter:"%curr% of %total%"},proto:{initGallery:function(){var i=e.st.gallery,n=".mfp-gallery";if(e.direction=!0,!i||!i.enabled)return!1;o+=" mfp-gallery",u("Open"+n,(function(){i.navigateByImgClick&&e.wrap.on("click"+n,".mfp-img",(function(){if(e.items.length>1)return e.next(),!1})),a.on("keydown"+n,(function(t){37===t.keyCode?e.prev():39===t.keyCode&&e.next()}))})),u("UpdateStatus"+n,(function(t,i){i.text&&(i.text=I(i.text,e.currItem.index,e.items.length))})),u("MarkupParse"+n,(function(t,a,n,o){var s=e.items.length;n.counter=s>1?I(i.tCounter,o.index,s):""})),u("BuildControls"+n,(function(){if(e.items.length>1&&i.arrows&&!e.arrowLeft){var a=i.arrowMarkup,n=e.arrowLeft=t(a.replace(/%title%/gi,i.tPrev).replace(/%dir%/gi,"left")).addClass("mfp-prevent-close"),o=e.arrowRight=t(a.replace(/%title%/gi,i.tNext).replace(/%dir%/gi,"right")).addClass("mfp-prevent-close");n.click((function(){e.prev()})),o.click((function(){e.next()})),e.container.append(n.add(o))}})),u("Change"+n,(function(){e._preloadTimeout&&clearTimeout(e._preloadTimeout),e._preloadTimeout=setTimeout((function(){e.preloadNearbyImages(),e._preloadTimeout=null}),16)})),u("Close"+n,(function(){a.off(n),e.wrap.off("click"+n),e.arrowRight=e.arrowLeft=null}))},next:function(){e.direction=!0,e.index=k(e.index+1),e.updateItemHTML()},prev:function(){e.direction=!1,e.index=k(e.index-1),e.updateItemHTML()},goTo:function(t){e.direction=t>=e.index,e.index=t,e.updateItemHTML()},preloadNearbyImages:function(){var t,i=e.st.gallery.preload,a=Math.min(i[0],e.items.length),n=Math.min(i[1],e.items.length);for(t=1;t<=(e.direction?n:a);t++)e._preloadItem(e.index+t);for(t=1;t<=(e.direction?a:n);t++)e._preloadItem(e.index-t)},_preloadItem:function(i){if(i=k(i),!e.items[i].preloaded){var a=e.items[i];a.parsed||(a=e.parseEl(i)),p("LazyLoad",a),"image"===a.type&&(a.img=t('<img class="mfp-img" />').on("load.mfploader",(function(){a.hasSize=!0})).on("error.mfploader",(function(){a.hasSize=!0,a.loadError=!0,p("LazyLoadError",a)})).attr("src",a.src)),a.preloaded=!0}}}}),t.magnificPopupT4s.registerModule("retina",{options:{replaceSrc:function(t){return t.src.replace(/\.\w+$/,(function(t){return"@2x"+t}))},ratio:1},proto:{initRetina:function(){if(window.devicePixelRatio>1){var t=e.st.retina,i=t.ratio;(i=isNaN(i)?i():i)>1&&(u("ImageHasSize.retina",(function(t,e){e.img.css({"max-width":e.img[0].naturalWidth/i,width:"100%"})})),u("ElementParse.retina",(function(e,a){a.src=t.replaceSrc(a,i)})))}}}}),m()}));var fastdomT4s=createCommonjsModule((function(t){!function(e){var i=function(){},a=e.requestAnimationFrame||e.webkitRequestAnimationFrame||e.mozRequestAnimationFrame||e.msRequestAnimationFrame||function(t){return setTimeout(t,16)};function n(){var t=this;t.reads=[],t.writes=[],t.raf=a.bind(e)}function o(t){t.scheduled||(t.scheduled=!0,t.raf(s.bind(null,t)))}function s(t){var e,a=t.writes,n=t.reads;try{i("flushing reads",n.length),r(n),i("flushing writes",a.length),r(a)}catch(t){e=t}if(t.scheduled=!1,(n.length||a.length)&&o(t),e){if(i("task errored",e.message),!t.catch)throw e;t.catch(e)}}function r(t){for(var e;e=t.shift();)e()}function l(t,e){var i=t.indexOf(e);return!!~i&&!!t.splice(i,1)}n.prototype={constructor:n,measure:function(t,e){var i=e?t.bind(e):t;return this.reads.push(i),o(this),i},mutate:function(t,e){var i=e?t.bind(e):t;return this.writes.push(i),o(this),i},clear:function(t){return l(this.reads,t)||l(this.writes,t)},extend:function(t){if("object"!=_typeof(t))throw new Error("expected object");var e=Object.create(this);return function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])}(e,t),e.fastdom=this,e.initialize&&e.initialize(),e},catch:null};var d=e.fastdom=e.fastdom||new n;t.exports=d}("undefined"!=typeof window?window:commonjsGlobal)})),smoothscroll=createCommonjsModule((function(t,e){t.exports={polyfill:function(){var t=window,e=document;if(!("scrollBehavior"in e.documentElement.style)||!0===t.__forceSmoothScrollPolyfill__){var i,a=t.HTMLElement||t.Element,n=468,o={scroll:t.scroll||t.scrollTo,scrollBy:t.scrollBy,elementScroll:a.prototype.scroll||l,scrollIntoView:a.prototype.scrollIntoView},s=t.performance&&t.performance.now?t.performance.now.bind(t.performance):Date.now,r=(i=t.navigator.userAgent,new RegExp(["MSIE ","Trident/","Edge/"].join("|")).test(i)?1:0);t.scroll=t.scrollTo=function(){void 0!==arguments[0]&&(!0!==d(arguments[0])?h.call(t,e.body,void 0!==arguments[0].left?~~arguments[0].left:t.scrollX||t.pageXOffset,void 0!==arguments[0].top?~~arguments[0].top:t.scrollY||t.pageYOffset):o.scroll.call(t,void 0!==arguments[0].left?arguments[0].left:"object"!==_typeof(arguments[0])?arguments[0]:t.scrollX||t.pageXOffset,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:t.scrollY||t.pageYOffset))},t.scrollBy=function(){void 0!==arguments[0]&&(d(arguments[0])?o.scrollBy.call(t,void 0!==arguments[0].left?arguments[0].left:"object"!==_typeof(arguments[0])?arguments[0]:0,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:0):h.call(t,e.body,~~arguments[0].left+(t.scrollX||t.pageXOffset),~~arguments[0].top+(t.scrollY||t.pageYOffset)))},a.prototype.scroll=a.prototype.scrollTo=function(){if(void 0!==arguments[0])if(!0!==d(arguments[0])){var t=arguments[0].left,e=arguments[0].top;h.call(this,this,void 0===t?this.scrollLeft:~~t,void 0===e?this.scrollTop:~~e)}else{if("number"==typeof arguments[0]&&void 0===arguments[1])throw new SyntaxError("Value could not be converted");o.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left:"object"!==_typeof(arguments[0])?~~arguments[0]:this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top:void 0!==arguments[1]?~~arguments[1]:this.scrollTop)}},a.prototype.scrollBy=function(){void 0!==arguments[0]&&(!0!==d(arguments[0])?this.scroll({left:~~arguments[0].left+this.scrollLeft,top:~~arguments[0].top+this.scrollTop,behavior:arguments[0].behavior}):o.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left+this.scrollLeft:~~arguments[0]+this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top+this.scrollTop:~~arguments[1]+this.scrollTop))},a.prototype.scrollIntoView=function(){if(!0!==d(arguments[0])){var i=function(t){for(;t!==e.body&&!1===f(t);)t=t.parentNode||t.host;return t}(this),a=i.getBoundingClientRect(),n=this.getBoundingClientRect();i!==e.body?(h.call(this,i,i.scrollLeft+n.left-a.left,i.scrollTop+n.top-a.top),"fixed"!==t.getComputedStyle(i).position&&t.scrollBy({left:a.left,top:a.top,behavior:"smooth"})):t.scrollBy({left:n.left,top:n.top,behavior:"smooth"})}else o.scrollIntoView.call(this,void 0===arguments[0]||arguments[0])}}function l(t,e){this.scrollLeft=t,this.scrollTop=e}function d(t){if(null===t||"object"!==_typeof(t)||void 0===t.behavior||"auto"===t.behavior||"instant"===t.behavior)return!0;if("object"===_typeof(t)&&"smooth"===t.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+t.behavior+" is not a valid value for enumeration ScrollBehavior.")}function c(t,e){return"Y"===e?t.clientHeight+r<t.scrollHeight:"X"===e?t.clientWidth+r<t.scrollWidth:void 0}function u(e,i){var a=t.getComputedStyle(e,null)["overflow"+i];return"auto"===a||"scroll"===a}function f(t){var e=c(t,"Y")&&u(t,"Y"),i=c(t,"X")&&u(t,"X");return e||i}function p(e){var i,a,o,r,l=(s()-e.startTime)/n;r=l=l>1?1:l,i=.5*(1-Math.cos(Math.PI*r)),a=e.startX+(e.x-e.startX)*i,o=e.startY+(e.y-e.startY)*i,e.method.call(e.scrollable,a,o),a===e.x&&o===e.y||t.requestAnimationFrame(p.bind(t,e))}function h(i,a,n){var r,d,c,u,f=s();i===e.body?(r=t,d=t.scrollX||t.pageXOffset,c=t.scrollY||t.pageYOffset,u=o.scroll):(r=i,d=i.scrollLeft,c=i.scrollTop,u=l),p({scrollable:r,method:u,startTime:f,startX:d,startY:c,x:a,y:n})}}}}));function onYouTubeIframeAPIReady(){document.dispatchEvent(new CustomEvent("youtube:ready"))}!function(t){"use strict";var e=t(window),i=t(document),a=e.width(),n=t("html"),o=t("body"),s=a<768,r=a<=1024,l=window.T4Srequest.design_mode,d=window.T4Sstrings,c=T4SThemeSP.cacheNameFirst,u=!!("ontouchstart"in window||window.DocumentTouch&&window.document instanceof DocumentTouch||window.navigator.maxTouchPoints||window.navigator.msMaxTouchPoints);T4SThemeSP.isHover=n.hasClass("t4sp-hover"),T4SThemeSP.isTouch=u&&(!T4SThemeSP.isHover||r),document.addEventListener("theme:hover",(function(t){T4SThemeSP.isHover=!0,T4SThemeSP.isTouch=!1})),T4SThemeSP.getToFetchSection=function(t,e="text",i=null){let a=t?T4SThemeSP.root_url+t:i;return fetch(a,{method:"GET",headers:{"Cache-Control":"no-cache"}}).then((t=>t.redirected?"NVT_94":"text"==e?t.text():t.json())).then((t=>t)).catch((t=>(console.warn(t),"NVT_94")))},T4SThemeSP.OverflowScroller=function(){function t(t,e){!t&&a>767||(this.element=t,this.options=e,this.lastKnownY=window.scrollY,this.currentTop=0,this.initialTopOffset=e.offsetTop||parseInt(window.getComputedStyle(this.element).top),this._attachListeners(),e.updateOffsetTop&&(this.initialTopOffsetCache=this.initialTopOffset,this._updateInitialTopOffset()))}return t.prototype=Object.assign({},t.prototype,{_updateInitialTopOffset:function(){window.addEventListener("T4sHeaderReveal",(function(){this.initialTopOffset=this.initialTopOffsetCache})),window.addEventListener("T4sHeaderHide",(function(){this.initialTopOffset=30}))},_attachListeners:function(){this._checkPositionListener=this._checkPosition.bind(this),window.addEventListener("scroll",this._checkPositionListener)},_checkPosition:function(){var t=this;fastdomT4s.measure((function(){var e=t.element.getBoundingClientRect().top+window.scrollY-t.element.offsetTop+t.initialTopOffset,i=t.element.clientHeight-window.innerHeight+(t.options.offsetBottom||0);window.scrollY<t.lastKnownY?t.currentTop-=window.scrollY-t.lastKnownY:t.currentTop+=t.lastKnownY-window.scrollY,t.currentTop=Math.min(Math.max(t.currentTop,-i),e,t.initialTopOffset),t.lastKnownY=window.scrollY})),fastdomT4s.mutate((function(){t.element.style.top="".concat(t.currentTop,"px")}))},destroy:function(){window.removeEventListener("scroll",this._checkPositionListener)}}),t}();var f,p,h,m,g,v,y,b,S,w=function(){var e="[data-swatch-item]",i="[data-current-value]",a="is--soldout",n="is--unavailable",s=a+" "+n,r=".is--selected",l="is-nav-selected",d="is--selected",c="is--media-hide",u=window.T4SProductStrings,f=u.unavailable,p=u.addToCart,h=u.soldOut,m=u.preOrder,g=(p=u.addToCart,u.replace_qs_atc),v=u.replace_qs_pre,y=u.badgeSavePercent2,b=u.badgeSaveFixed2,S="aria-disabled";function w(e){this.$container=e.$container,this.variants=e.variants,this.productOptions=e.productOptions,this.productOptionSize=e.PrOptionsSize,this.formSelectorId=e.formSelectorId,this.$formSelectorId=t(this.formSelectorId),this.$originalSelectorId=e.$originalSelectorId,this.originalSelectorId=this.$originalSelectorId[0],this.enableHistoryState=e.enableHistoryState,this.removeSoldout=e.removeSoldout,this.$options1=e.$options1,this.$options2=e.$options2,this.$options3=e.$options3,this.isNoPick=e.isNoPick,this.isNoPickOriginal=e.isNoPick,this.hasSoldoutUnavailable=e.hasSoldoutUnavailable,this.canMediaGroup=e.canMediaGroup,this.badgesConfigs=e.badgesConfigs,this.$variantImg=e.$variantImg,this.disableVariantImage=e.disableVariantImage,this.swatchWidth=e.swatchWidth,this.$incomingMess=this.$formSelectorId.find("[data-incoming__mess"),this.isSticky=e.isSticky,this.useStickySelect=e.useStickySelect,this.isMainProduct=e.isMainProduct,this.$quantity=this.$formSelectorId.find("[data-quantity-value"),this.$mainMedia=this.$container.find("[data-main-media]"),this.$mainNav=this.$container.find(".t4s-carousel__nav"),this.clickedOptions=[],this.showFirstMedia=!this.isNoPickOriginal&&e.showFirstMedia,this.oldVariant={},this.currentVariant={},this.mediaID=0,this.eventClickedSwatch=!1,this.variantState={available:!0,soldOut:!1,onSale:!1,preOrder:!1,showUnitPrice:!1},this.$productPrice=this.$container.find("[data-product-price]"),this.formartPrice="ins-del"==this.$productPrice.data("formartPrice")?"<ins>money_ins</ins> <del>money_del</del>":"<del>money_del</del> <ins>money_ins</ins>",this.saletype=this.$productPrice.data("saletype");let i=this.$container.find("[data-product-unit-price]");this.$unit_price=i.find("[data-unit-price]"),this.$unit_base=i.find("[data-unit-base]");let a=this.$container.find("[data-product-single-badge]"),n=this.badgesConfigs.texts,o=this.badgesConfigs.saleStyle;if(this.badgeSelector={$onSale:a.find("[data-badge-sale]"),$preOrder:a.find("[data-badge-preorder]"),$soldOut:a.find("[data-badge-soldout]")},this.saleLabel="2"==o?n.SavePercent:n.sale,this.useComingMess=!1,this.$incomingMess[0]&&(this.useComingMess=!0,this.$incomingAvailable=this.$incomingMess.find("[data-incoming-available]"),this.$incomingSoldout=this.$incomingMess.find("[data-incoming-soldout]"),this.$incomingAvailableDate=this.$incomingAvailable.find("[data-incoming-date]"),this.$incomingSoldoutDate=this.$incomingSoldout.find("[data-incoming-date]")),this.$addToCartButton=this.formSelectorId.find('[type="submit"][name="add"]'),this.$quantityWrapper=this.formSelectorId.find("[data-quantity-wrapper]"),this.$paymentButton=this.formSelectorId.find(".shopify-payment-button"),this.$addToCartButtonText=this.$addToCartButton.find(".t4s-btn-atc_text"),this.isSticky){let e=t("[data-sticky-addtocart]");this.$stickyimg=e.find("[data-sticky-img] img"),this.$stickyVtitle=e.find("[data-sticky-v-title]"),this.$stickyPrice=e.find("[data-sticky-price]"),this.$stickyATC=e.find("[data-action-atc]"),this.$stickyATCText=this.$stickyATC.find(".t4s-btn-atc_text"),this.$stickySelect=e.find("[data-sticky-select]"),this.stickyImgOrginal=this.$stickyimg.data("orginal"),this.$stickyQuantityWrapper=e.find("[data-quantity-wrapper]"),this.$stickyQuantity=this.$stickyQuantityWrapper.find("[data-quantity-value]"),this.isStickyChanging=!1,(s=this).$stickySelect.on("change:drop",(function(t,e,i){s.eventClickedSwatch=!1,s.isStickyChanging=!0,s.originalSelectorId.value=i,s.originalSelectorId.dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0}))}))}var s;(T4SThemeSP.isEditCartReplace&&(this.txt_addToCart=g,this.txt_preOrder=v),this.unQuickShopInline=e.unQuickShopInline,this.isQuickShopForm=e.isQuickShopForm,this.$imgMainItem=this.$container.find("[data-main-img-change]"),e.unQuickShopInline)?(this.originalSelectorId.addEventListener("change",this._onSelectChange.bind(this)),this._updateSwatchFromSizeOne(),this.isNoPick?this.currentVariant=this._getVariantFromVariantid():this.originalSelectorId.dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0}))):((s=this).$container.one("replace:btnAtc",(function(){s.$addToCartButton=s.$container.find(".t4s-pr-addtocart"),s.$quantityWrapper=s.$container.find("[data-quantity-wrapper]"),s.$addToCartButtonText=s.$addToCartButton.find(".t4s-text-pr")})),a=s.$container.find("[data-product-badge]"),s.badgeSelector={$onSale:a.find("[data-badge-sale]"),$preOrder:a.find("[data-badge-preorder]"),$soldOut:a.find("[data-badge-soldout]")},s.$dataHref=s.$container.find("[data-pr-href]"),s.productHref=s.$dataHref.attr("href"),s.currentVariant=s._getVariantFromVariantid(),s.$originalSelectorId.on("change",s._onQuickShopInlineChange.bind(s)),s._updateSwatchFromSizeOne())}return w.prototype=Object.assign({},w.prototype,{_onSelectChange:function(){this.eventClickedSwatch||(this.oldVariant=this.currentVariant);var t=this.eventClickedSwatch?this.currentVariant:this._getVariantFromVariantid();this._setVariantState(t),this._updateSwatchSelector(t,this.oldVariant,this.formSelectorId,this.hasSoldoutUnavailable),this._updatePrice(t,this.oldVariant,this.$container),this._updateAddToCartButton(t,this.oldVariant,this.$addToCartButton,this.$quantityWrapper,this.$paymentButton,this.$addToCartButtonText),this._updateAvailability(t,this.oldVariant,this.$container),this._updateSKU(t,this.oldVariant,this.$container),this._updateBarcode(t,this.oldVariant,this.$container),this._updateMetafield(t,this.oldVariant,this.$container),this._updateDelivery(t,this.oldVariant,this.$container),this._updateInventoryQuantity(t,this.oldVariant,this.$container),this._updatePickupAvailabilityContent(t,this.$container),this._updateNotifyBackinStock(t,this.$container),this._updateBadges(),this._updateIncomingMess(t),t&&(this.currentVariant=t,this.canMediaGroup&&this._updateMediaFilter(t,this.oldVariant,this.$container),this._updateMedia(t,this.oldVariant,this.$container),this._updateQuantity(t),this.disableVariantImage||this._updateVariantImageSwatch(t),this.isSticky&&this._updateStickyATC(t),this.enableHistoryState&&this._updateHistoryState(t),this.$container.trigger({type:"variant:changed",currentVariant:t,oldVariant:this.oldVariant}))},_onQuickShopInlineChange:function(){this.notSelected=!0,this.eventClickedSwatch||(this.oldVariant=this.currentVariant);var t=this.eventClickedSwatch?this.currentVariant:this._getVariantFromVariantid();this._setVariantState(t),this._updateSwatchSelector(t,this.oldVariant,this.formSelectorId,this.hasSoldoutUnavailable),this._updatePrice(t,this.oldVariant,this.$container),this._updateAtcBtnQSInline(t,this.oldVariant,this.$addToCartButton,this.$quantityWrapper,this.$addToCartButtonText),this._updateBadges(),t&&(this.currentVariant=t,this._updateMedia(t,this.oldVariant,this.$container),this._updateQuantity(t),this.$dataHref.attr("href",this._getUrlWithVariant(this.productHref,t.id)),this.disableVariantImage||this._updateVariantImageSwatch(t),this.$container.trigger({type:"variant:changed",currentVariant:t,oldVariant:this.oldVariant}))},_getVariantFromOptions:function(){var t=this.clickedOptions;return this.variants.find((function(e){return t.every((function(t){return e[t.index]===t.value}))}))||"nathan"},_getVariantFromSize:function(){var e,i=this.variants,a=this.productOptionSize,n=this.removeSoldout,o=this.clickedOptions[0].value,s=this.clickedOptions[1],r=(this.clickedOptions[2],this.clickedCurrentValue),l=this.clickedCurrentIndex;return 1==a?e=t.grep(i,(function(t,e){return t.available})):3==a&n?(s=s.value,(e=t.grep(i,(function(t,e){return t.option1==o&&t.option2==s&&t.available})))[0]||(e=t.grep(i,(function(t,e){return t.available&&t[l]==r})))):n?(e=t.grep(i,(function(t,e){return t.option1==o&&t.available})))[0]||(e=t.grep(i,(function(t,e){return t.available&&t[l]==r}))):3==a?(s=s.value,(e=t.grep(i,(function(t,e){return t.option1==o&&t.option2==s})))[0]||(e=t.grep(i,(function(t,e){return t[l]==r})))):(e=t.grep(i,(function(t,e){return t.option1==o})))[0]||(e=t.grep(i,(function(t,e){return t[l]==r}))),e[0]},_getVariantFromVariantid:function(){var t=[],e=this.variants,i=e.length,a=this.$originalSelectorId.val();for(let n=0;n<i;n++)if(e[n].id==a){t[0]=e[n];break}return t[0]||null},_getVariantFromOptionIndex:function(e,i){var a,n=this.variants,o=i.option1,s=i.option2,r=i.option3;switch(e){case 1:a=t.grep(n,(function(t,e){return t.option1==o}));break;case 2:a=t.grep(n,(function(t,e){return t.option2==s}));break;case 3:a=t.grep(n,(function(t,e){return t.option3==r}));break;case 1.2:a=t.grep(n,(function(t,e){return t.option1==o&&t.option2==s}));break;default:a=t.grep(n,(function(t,e){return 0==t.available}))}return a||"nathan"},_updateMediaFilterNoPick:function(){if(this.clickedCurrentValue&&this.clickedCurrentIndex&&this.canMediaGroup){var t=this.clickedCurrentIndex.replace("option",""),e=this.productOptions[parseInt(t)-1].name||"not4s",i=this.clickedCurrentValue||"not4s",a=this.$mainMedia,n=this.$mainNav,o=`[data-grname="${(e+"").toLowerCase()}"][data-grpvl="${(i+"").toLowerCase()}"]`,s=a.find(o),r=n.find(o);0!=s.length&&(a.find("[data-main-slide]").addClass(c),s.removeClass(c),n.find(".t4s-carousel__nav-item").addClass(c),r.removeClass(c),a.hasClass("flickityt4s-enabled")?(a.trigger("update.flickityt4s"),r.hasClass(l)||r.first().addClass(l)):a.hasClass("isotopet4s-enabled")&&(x(a),a.isotopet4s()))}},_updateSwatchFromSizeOne:function(){var t,e=this,i=e.variants,n=i.length,o=!1,s=0,r=e.productOptionSize,l=e.productOptions;if(3==r)var d=l[0].values.length,c=l[1].values.length,u=l[2].values.length;else if(2==r)d=l[0].values.length,c=l[1].values.length;if(r<2?(o=1,t=e.$options1):2==r&&1==d?(o=1,t=e.$options2,s=1):2==r&&1==c?(o=1,t=e.$options1):3==r&&1==d&&1==c?(o=1,t=e.$options3,s=2):3==r&&1==d&&1==u?(o=1,t=e.$options2,s=1):3==r&&1==c&&1==u?(o=1,t=e.$options1):3==r&&1==u&&(o=2),e.hasSoldoutUnavailable){let t=l[s].values,o=t.length,r=e[`$options${s+1}`].find("[data-swatch-item]");for(let e=0;e<o;e++){let o=!0,l=t[e];for(let t=0;t<n;t++){let e=i[t];if(e.options[s]==l&&e.available){o=!1;break}}o&&r.eq(e).addClass(a)}}e.getProductSize=o,e.$optionsOne=t,e.$optionsOneIndex=s},_updateMediaFilter:function(t,e,i){if(this.currentVariant&&this.canMediaGroup){var a,n,o,s,r,l=this.productOptions,d=this.productOptionSize,u=this.currentVariant,f=this.$mainMedia,p=this.$mainNav;for(let t=0;t<d;t++)if(s=l[t].name||"not4s",0!=f.find(`[data-grname="${(s+"").toLowerCase()}"]`).length){r=u.options[t]+"",a=`[data-grname="${(s+"").toLowerCase()}"][data-grpvl="${r.toLowerCase()}"]`;break}if(n=f.find(a),o=p.find(a),0!=n.length&&r!=this.groupValue)if(this.groupValue=r,f.find("[data-main-slide]").addClass(c),n.removeClass(c),p.find(".t4s-carousel__nav-item").addClass(c),o.removeClass(c),f.hasClass("flickityt4s-enabled")){f.trigger("update.flickityt4s");var h=t.featured_media?f.find(`[data-media-id="${t.featured_media.id}"]:visible`).index():0;f.flickityt4s("selectCell",Math.max(h,0),!1,!1)}else f.hasClass("isotopet4s-enabled")&&(x(f),f.isotopet4s())}},_updateSwatchSelector:function(t,a,o,l){var c,u,f,p,h,m,g,v=this,y=1,b=v.$options1,S=v.$options2,w=v.$options3,_=v.getProductSize||v.productOptionSize,T=[],$=0,C=t.option1,k=t.option2,I=t.option3,x=[],P=[],M=[];if(o.find(r).removeClass(d),o.find(i).html(""),x=v.productOptions[0].values,b.find(i).html(C),b.find(e).eq(x.indexOf(C)).addClass(d),S[0]&&(P=v.productOptions[1].values,S.find(i).html(k),S.find(e).eq(P.indexOf(k)).addClass(d)),w[0]&&(M=v.productOptions[2].values,w.find(i).html(I),w.find(e).eq(M.indexOf(I)).addClass(d)),l)switch(_){case 3:for(1==M.length?(c=b,u=x,f="option3",p=I,h="option1",y=3):(c=w,u=M,f="option1",p=C,h="option3"),$=(T=this._getVariantFromOptionIndex(y,t)).length,c.find(e).addClass(s),S.find(e).addClass(s),g=0;g<$;g++)(m=T[g])[f]==p&&(m.available?(S.find(e).eq(P.indexOf(m.option2)).removeClass(s),m.option2==k&&c.find(e).eq(u.indexOf(m[h])).removeClass(s)):(S.find(e).eq(P.indexOf(m.option2)).removeClass(n),m.option2==k&&c.find(e).eq(u.indexOf(m[h])).removeClass(n)));break;case 2:for($=(T=this._getVariantFromOptionIndex(1,t)).length,S.find(e).addClass(s),g=0;g<$;g++)(m=T[g]).option1==C&&S.find(e).eq(P.indexOf(m.option2)).removeClass(m.available?s:n);break;default:v.removeSoldout&&v.$optionsOne.find(r).is(":hidden")&&v.$optionsOne.find(`${e}:visible:first`).trigger("click")}},_updateMetafield:function(e,i,a){e&&e.id!=i.id&&(a.find("[data-variant-toggle]").hide(),a.find(`[data-variant-toggle="${e.id}"]`).show(),this.isMainProduct&&(t("[data-variant-tab][data-variant-toggle]").hide(),t(`[data-variant-tab][data-variant-toggle="${e.id}"]`).show()))},_updateMedia:function(t,e,i){if(t.featured_media&&JSON.stringify(t.featured_media)!==JSON.stringify(e.featured_media)&&!this.showFirstMedia){if(!this.unQuickShopInline||this.isQuickShopForm){let e=t.featured_media.preview_image,i=!0,a=this.$imgMainItem.hasClass("lazyloadt4sed")&&i?0:100;return setTimeout(function(){this.$imgMainItem.attr("data-srcset",T4SThemeSP.Images.getNewImageUrl(e.src,1)),i=!1}.bind(this),a),void(this.notSelected&&(this.$container.addClass("t4s-colors-selected"),this.notSelected=!1))}this.mediaID=t.featured_media.id;var a=i.find("[data-main-media]");if(a.hasClass("flickityt4s-enabled")){var n=a.find('[data-media-id="'+this.mediaID+'"]:visible').index();a.flickityt4s("select",Math.max(n,0),!1,!0),this.eventClickedSwatch=!1}else if(!a.hasClass("t4s-of-scrollIntoView")){var o=a.find('[data-media-id="'+this.mediaID+'"]'),s=o[0];if(!s||T4SThemeSP.isVisible(o)||this.isStickyChanging)return;this.header||(this.header=document.querySelector(".t4s-section-header")),this.header.dispatchEvent(new Event("preventHeaderReveal")),window.setTimeout((()=>{a[0].scrollLeft=0,s.scrollIntoView({behavior:"smooth"})}))}}else this.showFirstMedia=!1},_updateMediaFirst:function(t){if(this.unQuickShopInline)return;var e=t.closest("[data-swatch-option]");if(!e.hasClass("is-t4s-style__color"))return;let i=this.variants,a=i.length,n=e.data("id");let o=function(t){for(let e=0;e<a;e++){let a=i[e];if(a.featured_media&&(a.options[n]+"").toLowerCase()==t)return a.featured_media.preview_image}}((t.data("value")+"").toLowerCase());o&&this.$imgMainItem.attr("data-srcset",T4SThemeSP.Images.getNewImageUrl(o.src,1))},_updatePrice:function(t,e,i){if(!t)return void this.$productPrice.hide();let a=t.price,n=t.compare_at_price;if(!this.isNoPickOriginal&&a===e.price&&n===e.compare_at_price&&t.unit_price===e.unit_price)return;this.isNoPickOriginal&&(this.isNoPickOriginal=!1);let s=T4SThemeSP.Currency.formatMoney(a);if(this.$productPrice.show(),this.variantState.onSale){let t=T4SThemeSP.Currency.formatMoney(n),e=this.formartPrice.replace("money_ins",s).replace("money_del",t),i=n-a,o=100*i/n,r=Math.round(o);void 0!==u.price_template&&(e=u.price_template.replace("INS",s).replace("DEL",t)),this.isSticky&&this.$stickyPrice.html(e),this.badgeSelector.$onSale.html(this.saleLabel.replace("[sale]",r)),"1"==this.saletype?e+=` <span class="t4s-badge-price">${y.replace("[sale]",r)}</span>`:"2"==this.saletype&&(e+=` <span class="t4s-badge-price">${b.replace("[sale]",T4SThemeSP.Currency.formatMoney(i))}</span>`),this.$productPrice.html(e)}else this.$productPrice.html(s),this.isSticky&&this.$stickyPrice.html(s);this.variantState.showUnitPrice&&(this.$unit_price.html(T4SThemeSP.Currency.formatMoney(t.unit_price)),this.$unit_base.html(T4SThemeSP.Currency.getBaseUnit(t))),this.$container.find("shopify-payment-terms").attr("variant-id",t.id),o.trigger("currency:update")},_updateQuantity:function(t){var e=t.quantity_rule?t.quantity_rule.min:1,i=t.quantity_rule&&t.quantity_rule.max?t.quantity_rule.max:9999;if(this.variantState.preOrder)this.$quantity.attr({min:e,max:i}),this.isSticky&&this.$stickyQuantity.attr({min:e,max:i});else if(null!=t.inventory_management&&"continue"!=t.inventory_policy){let a=t.inventory_quantity;a<i&&(i=a),this.$quantity.attr({min:e,max:i}),this.isSticky&&this.$stickyQuantity.attr({min:e,max:i}),parseInt(this.$quantity.val())>a&&this.$quantity.attr("value",1).val(1),this.isSticky&&parseInt(this.$stickyQuantity.val())>a&&this.$stickyQuantity.attr("value",1).val(1)}else this.$quantity.attr({min:e,max:i}),this.isSticky&&this.$stickyQuantity.attr({min:e,max:i})},_updateAvailability:function(t,e,i){var a=i.find("[data-product-available]");if(a[0]){var n=a.find("[data-available-status]"),o=a.find("[data-soldout-status]"),s=a.find("[data-instock-status]"),r=a.find("[data-preorder-status]");t?(a.show(),this.variantState.available?(n.show(),o.hide(),this.variantState.preOrder?(r.show(),s.hide()):(s.show(),r.hide())):(o.show(),n.hide())):a.hide()}},_updateBarcode:function(t,e,i){var a=i.find("[data-product-barcode]");if(a[0]){var n=a.find("[data-product__barcode-number]");if(t&&""!==t.barcode){if(e&&e.barcode===t.barcode)return;n.text(t.barcode),a.show(0)}else a.hide(0)}},_updateSKU:function(t,e,i){var a=i.find("[data-product-sku]");if(a[0]){var n=a.find("[data-product__sku-number]");if(t&&""!==t.sku){if(e&&e.sku===t.sku)return;n.text(t.sku),a.show(0)}else a.hide(0)}},_updateAddToCartButton:function(t,e,i,a,n,o){if(i[0]||n[0])if(T4SThemeSP.isEditCartReplace&&!i.is("[data-replace-item]")&&i.attr("data-replace-item",""),t&&"nathan"!=t)if(t.available){let t=this.variantState.preOrder?this.txt_preOrder||m:this.txt_addToCart||p;a.show(),i.removeAttr("disabled "+S).attr("data-atc-form",""),o.text(t),n.show(),this.isSticky&&(this.$stickyQuantityWrapper.show(),this.$stickyATC.removeAttr("disabled "+S),this.$stickyATCText.text(t))}else a.hide(),i.attr("disabled","disabled").attr(S,!0).removeAttr("data-atc-form",""),o.text(h),n.hide(),this.isSticky&&(this.$stickyQuantityWrapper.hide(),this.$stickyATC.attr("disabled","disabled").attr(S,!0),this.$stickyATCText.text(h));else i.attr("disabled","disabled").attr(S,!0).removeAttr("data-atc-form"),o.text(f),a.hide(),n.hide(),this.isSticky&&(this.$stickyQuantityWrapper.hide(),this.$stickyATC.attr("disabled","disabled").attr(S,!0),this.$stickyATCText.text(f))},_updateAtcBtnQSInline:function(t,e,i,a,n){if(i[0])if(t&&"nathan"!=t)if(t.available){let e=this.variantState.preOrder?this.txt_preOrder||m:this.txt_addToCart||p;a.show(),i.removeAttr("disabled "+S).attr("data-action-atc","").attr("data-variant-id",t.id),n.text(e)}else a.hide(),i.attr("disabled","disabled").attr(S,!0).removeAttr("data-action-atc",""),n.text(h);else i.attr("disabled","disabled").attr(S,!0).removeAttr("data-action-atc"),n.text(f),a.hide()},_updateDelivery:function(t,e,i){var a="data-order-delivery",n=i.find("["+a+"]");if(n[0])if(t&&t.available){var o=P(n.attr(a));this.variantState.preOrder&&o.hideWithPreorder?n.hide():n.show()}else n.hide()},_updateInventoryQuantity:function(t,e,i){var a=i.find("[data-inventory-qty]");a[0]&&(t&&t.available?a.trigger({type:"variant:inventory",currentVariant:t,oldVariant:this.oldVariant}):a.hide())},_updatePickupAvailabilityContent:function(t,e){let i=t.available?"pickupAvailability:update":"pickupAvailability:clear";e.trigger({type:i,currentVariant:t})},_updateNotifyBackinStock:function(t,e){let i=this.variantState.available?"notifyBackinStock:hide":"notifyBackinStock:show";e.trigger({type:i,currentVariant:t})},_updateBadges:function(){let t=this.variantState,e=this.badgeSelector;t.onSale?e.$onSale.removeAttr("hidden"):e.$onSale.attr("hidden",!0),t.preOrder?e.$preOrder.removeAttr("hidden"):e.$preOrder.attr("hidden",!0),t.soldOut?e.$soldOut.removeAttr("hidden"):e.$soldOut.attr("hidden",!0)},_setVariantState:function(t){t?this.variantState={available:t.available,soldOut:!t.available,onSale:t.compare_at_price>t.price,showUnitPrice:!!t.unit_price,preOrder:"shopify"==t.inventory_management&&t.inventory_quantity<=0&&t.available}:this.variantState.available=!1},_updateVariantImageSwatch:function(t){if(!t.featured_image)return;let e=this.$variantImg.find(r),i=e.find("[data-img-el]");e=i[0]?i:e,e.attr("data-bg",T4SThemeSP.Images.getNewImageUrl(t.featured_image.src,this.swatchWidth))},_updateIncomingMess:function(t){if(!this.useComingMess)return;let e=t.next_incoming_date,i=t.inventory_quantity,a=t.incoming,n=t.inventory_management;t&&e&&!(i>0)&&a&&"shopify"==n?(this.$incomingMess.removeAttr("hidden"),this.variantState.available?(this.$incomingAvailableDate.html(e),this.$incomingSoldout.hide(),this.$incomingAvailable.show()):(this.$incomingSoldoutDate.html(e),this.$incomingAvailable.hide(),this.$incomingSoldout.show())):this.$incomingMess.attr("hidden","")},_updateStickyATC:function(t){this.isStickyChanging=!1,this.$stickyimg.attr("data-src",t.featured_image?T4SThemeSP.Images.lazyloadImagePath(t.featured_image.src):this.stickyImgOrginal),this.useStickySelect?t.available&&(this.$stickyVtitle.find("[data-dropdown-open]>span").text(t.title),this.$stickySelect.find("[data-dropdown-item]").removeClass("is--selected"),this.$stickySelect.find(`[data-dropdown-item][data-value="${t.id}"]`).addClass("is--selected")):this.$stickyVtitle.html(t.title),this.$stickyATC.attr("data-variant-id",t.id)},_updateHistoryState:function(t){if(!history.replaceState||!t)return;let e=new URL(document.location);e.searchParams.set("variant",t.id),window.history.replaceState({path:e.href},"",e.href)},_getUrlWithVariant:function(t,e){return/variant=/.test(t)?t.replace(/(variant=)[^&]+/,"$1"+e):/\?/.test(t)?t.concat("&variant=").concat(e):t.concat("?variant=").concat(e)}}),w}(),_=function(){var e="data-live-view",i="data-flash-sold",a="data-animation-atc",n="data-order-delivery",o=T4Sconfigs.timezone,s="t4_nt_guess",r="data-inventory-qty";try{s=dayjs.tz.guess()}catch(t){}var d="not4"!=o&&s!=o;function c(t){this.$container=t,this.BootSalesInt()}function u(t){if(0!=t.length){var e=P(t.attr(a)),i=e.ani;if("none"!=i){var n="is--animated "+i,o=parseInt(e.time),s=parseInt(e.animTime)||1e3;setInterval((function(){t.addClass(n),setTimeout((function(){t.removeClass(n)}),s)}),o)}}}return c.prototype=Object.assign({},c.prototype,{BootSalesInt:function(){this._liveView(),this._flashSold(),this._animationATC(),this._orderDelivery(),this._inventoryQuantity(),this._countdown()},_getRandomInt:function(t,e){return Math.floor(Math.random()*(e-t+1))+t},_animationATC:function(){var t=this.$container.find("["+a+"]");return u(t),void t.length},_liveView:function(){var t=this.$container.find("["+e+"]");if(0!=t.length){var i=P(t.attr(e)),a=this,n=i.min,o=i.max,s=i.interval,r=a._getRandomInt(n,o),l=["1","2","4","3","6","10","-1","-3","-2","-4","-6"],d=["10","20","15"],c="",u="",f="",p=t.find("[data-count]");h(),t.show(),setInterval(h,s)}function h(){(c=Math.floor(Math.random()*l.length),u=l[c],r=parseInt(r)+parseInt(u),n>=r)&&(f=Math.floor(Math.random()*d.length),r+=d[f]);(r<n||r>o)&&(r=a._getRandomInt(n,o)),p.html(parseInt(r))}},_flashSold:function(){var t=this.$container.find("["+i+"]");if(0!=t.length){var e=P(t.attr(i)),a=this,n=e.mins,o=e.maxs,s=e.mint,r=e.maxt,l=e.id,d=sessionStorage.getItem("soldS"+l)||a._getRandomInt(n,o),c=sessionStorage.getItem("soldT"+l)||a._getRandomInt(s,r),u=parseInt(d),f=parseInt(c),p=parseInt(e.time),h=t.find("[data-sold]"),m=t.find("[data-hour]");v(),g(u,f),t.show(),setInterval((function(){u+=a._getRandomInt(1,4),f+=1*(Math.random()*(.8-.1)+.1).toFixed(1),v(),g(u,f)}),p)}function g(t,e){h.html(t),m.html(Math.floor(f)),sessionStorage.setItem("soldS"+l,t),sessionStorage.setItem("soldT"+l,e)}function v(){u>o&&(u=a._getRandomInt(n,o)),f>r&&(f=a._getRandomInt(s,r))}},_orderDelivery:function(){var e=this.$container.find("["+n+"]");if(0!=e.length){var i=P(e.attr(n)),a=i.format_day,s=i.time.replace("24:00:00","23:59:59")||"19041994",r=["SUN","MON","TUE","WED","THU","FRI","SAT"],l=i.estimateStartDate||0,c=i.estimateEndDate||0,u=i.cut_day.replace(/ /g,"").split(","),f=["280/12","100/01"],p=dayjs(),h=0,m=dayjs(),g=0,v=i.timezone,y=dayjs(),b=y.format("HHmmss"),S=s.replace(/ /g,"").replace(/:/g,""),w=T4SProductStrings.order_dayNames.replace(/ /g,"").split(","),_=T4SProductStrings.order_monthNames.replace(/ /g,"").split(",");if(d&&v)try{b=(y=dayjs.tz(y,o)).format("HHmmss")}catch(t){console.log("Timezone error: "+o)}if(parseInt(b)>=parseInt(S)&&(y=y.add(1,"day"),p=p.add(1,"day"),m=m.add(1,"day")),"2"==i.mode){for(;u.indexOf(r[p.format("d")])>-1||f.indexOf(p.format("DD/MM"))>-1;)p=p.add(1,"day");for(;h<l;)h++,p=p.add(1,"day"),(u.indexOf(r[p.format("d")])>-1||f.indexOf(p.format("DD/MM"))>-1)&&h--;for(;u.indexOf(r[m.format("d")])>-1;)m=m.add(1,"day");for(;g<c;)g++,m=m.add(1,"day"),u.indexOf(r[m.format("d")])>-1&&g--}else{for(p=p.add(l,"day");u.indexOf(r[p.format("d")])>-1||f.indexOf(p.format("DD/MM"))>-1;)p=p.add(1,"day");for(m=m.add(c,"day");u.indexOf(r[m.format("d")])>-1;)m=m.add(1,"day")}w=A(w),_=A(_);var T=parseInt(p.format("D")),$=T+L(T),C=_[parseInt(p.format("M"))-1],k=w[parseInt(p.format("d"))],I=parseInt(m.format("D")),x=I+L(I),M=_[parseInt(m.format("M"))-1],O=w[parseInt(m.format("d"))];if(e.find("[data-start-delivery]").html(p.format(a).replace("t44",k).replace("t45",$).replace("t46",C)),e.find("[data-end-delivery]").html(m.format(a).replace("t44",O).replace("t45",x).replace("t46",M)),"19041994"!=s){var V=e.find("[data-hour-delivery]");V.countdown(y.format("YYYY-MM-DD "+s),{elapse:!0}).on("update.countdown",(function(i){if(i.elapsed)e.hide();else{var a=24*i.offset.totalDays+i.offset.hours;V.html(i.strftime(t.trim(V.html().replace("[totalHours]",a)))).show()}}))}e.show()}function A(t){return t.filter((function(t,e,i){return i.indexOf(t)===e}))}function L(t){if(t>3&&t<21)return"th";switch(t%10){case 1:return"st";case 2:return"nd";case 3:return"rd";default:return"th"}}},_inventoryQuantity:function(){var t=this.$container.find("["+r+"]");if(0!=t.length){t.removeAttr("data-ttcalc");var e=P(t.attr(r)),i=this,a=e.stock,n=e.qty,o=e.total,s=e.min,d=e.max,c=e.reduce,u=e.bgprocess,f=e.bgten,p=e.id,h=e.inventoryQty||0,m=null,g=null,v=i._getRandomInt(s,d),y=t.find("[data-count]"),b=t.find("[data-progressbar]"),S=t.find("[data-message]"),w=b.find(">div");if(t.on("variant:inventory",(function(e){if("2"!=a){var o=e.currentVariant,r=o.inventory_quantity||0;if(p=o.id,(r>=n||r<1)&&"1"==a)t.hide();else{(r>=n||0==r)&&isStorageSpSession&&(r=sessionStorage.getItem("probar"+p)||i._getRandomInt(s,d),t.attr("data-variant-qty"+p,r)),t.attr("data-variant-qty"+p,r),sessionStorage.setItem("probar"+p,r),y.text(r);var l=100*r/t.attr("data-ttcalc"),c=r<10?f:u;w.css({"background-color":c,width:l+"%"}),S.show(),b.show(),t.show()}}else t.show()})),!(h>=n||h<1)||"1"!=a){if(h<n&&h>0&&"2"!==a&&(v=h),isStorageSpSession&&!l&&"1"!=a){var _=sessionStorage.getItem("probar"+p);_>0&&(v=_)}y.text(v).css({"background-color":"#fff",color:u}),T(v,u,f),S.show(),b.show(),function(){if(!c)return;m=setTimeout((function(){--v<1&&(v=$(t.attr("data-variant-qty"+p))||i._getRandomInt(s,d)),y.css({"background-color":u,color:"#fff"}),setTimeout((function(){y.css({"background-color":"#fff",color:u})}),1800),y.text(v),T(v,u,f)}),10200),g=setInterval((function(){--v<1&&(v=$(t.attr("data-variant-qty"+p))||i._getRandomInt(s,d),t.on("destroy:inventoryQty").hide()),y.css({"background-color":u,color:"#fff"}),setTimeout((function(){y.css({"background-color":"#fff",color:u})}),1800),y.text(v),T(v,u,f)}),102e3)}(),t.on("destroy:inventoryQty",(function(){clearTimeout(m),clearInterval(g)})),t.on("update:inventoryQty",(function(){var e=parseInt(y.text())-1;if(!(e<1)){y.text(e);var i=100*e/t.attr("data-ttcalc"),a=e<10?f:u;w.css({"background-color":a,width:i+"%"})}}))}}function T(e,i,a){e=parseInt(e),isStorageSpSession&&sessionStorage.setItem("probar"+p,e),o=t.attr("data-ttcalc")||o>e?o:e+o,t.attr("data-ttcalc",o);var n=100*e/o,s=e<10?a:i;w.css("background-color",s),setTimeout((function(){w.css("width",n+"%")}),300),w.css("background-color",s)}function $(t){return t||0}},_countdown:function(){var t=this.$container.find("[data-countdown-pr]");if(0!=t.length){var e,i="data-cd-options",a=t.find("["+i+"]"),n=P(a.attr(i));if(!n.isCountdownMeta){e=n.cd_date.replace("24:00:00","23:59:59").split(",");var s,r,l=dayjs(),c=l.format("HHmmss"),u=e.length;if(d)try{c=(l=dayjs.tz(l,o)).format("HHmmss")}catch(t){console.log("Timezone error: "+o)}for(s=0;s<u;s++){if(parseInt(e[s].replace(/:/g,""))>=c){r=e[s];break}s==u-1&&(r=e[s])}a.attr("data-date",l.format("YYYY-MM-DD")+" "+r)}a.attr("data-countdown-t4s",""),T4SThemeSP.Countdown()}}}),{init:c,ani:u}}(),T=function(){var t={},e={shopify:"shopify",external:"external"},i={productMediaWrapper:"[data-product-single-media-wrapper]"},a={enableVideoLooping:"enable-video-looping",enableVideoMuting:"enable-video-muting",enableVideoAutoplaying:"enable-video-autoplaying",videoId:"video-id"};function n(i){i?function(){for(var i in t)if(t.hasOwnProperty(i)){var a=t[i];if(a.nativeVideo)continue;a.host===e.shopify&&(a.element.setAttribute("controls","controls"),a.nativeVideo=!0)}}():s()}function o(t){return"VIDEO"===t.tagName?e.shopify:e.external}function s(){for(var e in t){if(t.hasOwnProperty(e))t[e].ready()}}return{init:function(e,s){if(e){var r=e.querySelector("iframe, video");if(r){var l=e.getAttribute("data-nt-media-id");t[l]={mediaId:l,sectionId:s,host:o(r),container:e,element:r,ready:function(){!function(t){if(t.player)return;var e=t.container.closest(i.productMediaWrapper),n="true"===e.getAttribute("data-"+a.enableVideoLooping),o="true"===e.getAttribute("data-"+a.enableVideoMuting),s="true"===e.getAttribute("data-"+a.enableVideoAutoplaying);t.player=new Shopify.Video(t.element,{loop:{active:n},muted:o}),e.classList.add("is-media__initialized");var r=function(){t.player&&t.player.pause()};e.addEventListener("mediaHidden",r),e.addEventListener("xrLaunch",r),e.addEventListener("mediaVisible",(function(){!T4SThemeSP.isTouch&&s&&t.player&&t.player.play()}))}(this)}},window.Shopify.loadFeatures([{name:"video-ui",version:"2.0",onLoad:n}]),T4SThemeSP.LibraryLoader.load("plyrShopifyStyles")}}},hosts:e,loadVideos:s,removeSectionVideos:function(e){for(var i in t)if(t.hasOwnProperty(i)){var a=t[i];a.sectionId===e&&(a.player&&a.player.destroy(),delete t[i])}}}}(),$=function(){var t={},e={},i={},a="[data-product-single-media-group]",n="[data-shopify-xr]";function o(e){if(!e)if(window.ShopifyXR){for(var i in t)if(t.hasOwnProperty(i)){var a=t[i];if(a.loaded)continue;var n=document.querySelector("#ModelJson-"+i);window.ShopifyXR.addModels(JSON.parse(n.innerHTML)),a.loaded=!0}window.ShopifyXR.setupXRElements()}else document.addEventListener("shopify_xr_initialized",(function(){o()}))}function s(t){if(!t)for(var i in e)if(e.hasOwnProperty(i)){var a=e[i];a.modelViewerUi||(a.modelViewerUi=new Shopify.ModelViewerUI(a.element)),r(a)}}function r(t){var e=i[t.sectionId];t.container.classList.add("is-media__initialized"),t.container.addEventListener("mediaVisible",(function(){e.element&&e.element.setAttribute("data-shopify-model3d-id",t.modelId),T4SThemeSP.isTouch||t.modelViewerUi.play()})),t.container.addEventListener("mediaHidden",(function(){e.element&&e.element.setAttribute("data-shopify-model3d-id",e.defaultId),t.modelViewerUi.pause()})),t.container.addEventListener("xrLaunch",(function(){t.modelViewerUi.pause()}))}return{init:function(r,l){t[l]={loaded:!1},r.forEach((function(t,o){var s=t.getAttribute("data-nt-media-id"),r=t.querySelector("model-viewer"),d=r.getAttribute("data-model-id");if(0===o){var c=t.closest(a).querySelector(n);i[l]={element:c,defaultId:d}}e[s]={modelId:d,sectionId:l,container:t,element:r}})),window.Shopify.loadFeatures([{name:"shopify-xr",version:"1.0",onLoad:o},{name:"model-viewer-ui",version:"1.0",onLoad:s}]),T4SThemeSP.LibraryLoader.load("modelViewerUiStyles")},removeSectionModels:function(i){for(var a in e){if(e.hasOwnProperty(a))e[a].sectionId===i&&(e[a].modelViewerUi.destroy(),delete e[a])}delete t[i]}}}(),C=function(){var e=!1;function i(e,i){e=e[0];let a=JSON.parse(document.querySelector("#Json360-"+i).innerHTML),n=a.imgArray,o=parseFloat(e.getAttribute("data-min"))||1.194,s=parseFloat(e.getAttribute("data-max"))||2,r=window.devicePixelRatio<o?o:window.devicePixelRatio,l=r>s?s:r,d=Math.round(e.clientWidth*l);a.imgArray=[];for(let t in n)a.imgArray.push(`${n[t]}&width=${d}`);a.onReady=function(){!function(e,i){i.classList.add("is-media__initialized"),i.addEventListener("mediaVisible",(function(){if(!T4SThemeSP.isTouch)try{e.play(),t(i.querySelector(".nav_bar_play")).removeClass("nav_bar_play").addClass("nav_bar_stop")}catch(t){}})),t(i).hasClass("is-selected")&&(e.play(),setTimeout((function(){t(i.querySelector(".nav_bar_play")).removeClass("nav_bar_play").addClass("nav_bar_stop")}),50));i.addEventListener("mediaHidden",(function(){e.stop(),t(i.querySelector(".nav_bar_stop")).removeClass("nav_bar_stop").addClass("nav_bar_play")}))}(c,e)};var c=t(e.querySelector(".t4s-threesixty")).ThreeSixty(a)}return{init:function(t,a){e?i(t,a):$script(T4Sconfigs.script12b,(function(){i(t,a),e=!0}))}}}(),k=function(){var e="[data-pickup-availability-popup-open]",i="[data-pickup-availability-popup-close]",a=d.mfp_close,n=d.mfp_loading,s={};function r(t,e){this.container=e,this.idPopup=this.container.dataset.idPopup,this.hasOnlyDefaultVariant="true"===this.container.dataset.hasOnlyDefaultVariant,this.rootUrl=this.container.dataset.rootUrl,this.variantId=this.container.dataset.variantId;var i=this;t.on("pickupAvailability:update",(function(t){i.updateContent(t.currentVariant.id)})),t.on("pickupAvailability:clear",(function(t){i.clearContent()}))}return r.prototype=Object.assign({},r.prototype,{updateContent:function(t=this.variantId){let i=this.rootUrl;i.endsWith("/")||(i+="/");var a=i+"variants/"+t+"/?section_id=pickup-availability",n=this,o=c+"pickup-availability"+t,r=n.container.querySelector(e);n.container.style.opacity=.5,r&&(r.disabled=!0,r.setAttribute("aria-busy",!0)),s[o]?n.updateResponse(s[o]):T4SThemeSP.getToFetchSection(null,"text",a).then((t=>{"NVT_94"!=t&&(s[o]=t,n.updateResponse(t))}))},updateResponse:function(t){if(""!==t.trim()){this.container.innerHTML=t,this.container.innerHTML=this.container.firstElementChild.innerHTML,this.container.style.opacity=1;var i=this.container.querySelector(e);i&&(this.container.querySelector("#pickupAvailabilityPopup").id=this.idPopup,i.addEventListener("click",this._onClickModalOpen.bind(this)))}},clearContent:function(){this.container.innerHTML=""},_onClickModalOpen:function(){var e=this;t.magnificPopupT4s.open({items:{src:`#${e.idPopup}`},type:"inline",removalDelay:500,tClose:a,tLoading:n,callbacks:{beforeOpen:function(){this.st.mainClass="mfp-move-horizontal t4s-pickup-availability_pp_wrapper"},open:function(){o.trigger("NTpopupInline:offClose"),o.trigger("currency:update");var t=document.querySelector(`#${e.idPopup} ${i}`);t&&(t.removeEventListener("click",e._onClickModalClose),t.addEventListener("click",e._onClickModalClose))},beforeClose:function(){},close:function(){},afterClose:function(){o.trigger("NTpopupInline:onClose")}}})},_onClickModalClose:function(){t.magnificPopupT4s.close()}}),r}(),I=function(){var a,s,r,l="is--shown",d="sticky-is--active",c="click.sticky",u=e.height(),f=i.height(),p=0,h=0,m="#t4s-backToTop",g="is--show";function v(i,n,u){if(!i[0])return;var f,p=this,h="2"==n;s=i.offset().top+i.outerHeight(),p._updateContent(),r=a.find("[data-action-info-close]"),_.ani(a.find("[data-action-atc]")),p._stickyAddToCartToggle(),h||(a.addClass(l),o.addClass(d)),e.scroll((function(){f&&clearTimeout(f),f=setTimeout((function(){p._stickyAddToCartToggle(h)}),30)}));var v=a.find("[data-quantity-wrapper] [data-quantity-value]"),b=i.find("[data-quantity-value");v.change((function(){b.val(this.value)})),b.change((function(){v.val(this.value)})),u||a.find("[data-sticky-v-title]").on(c,(function(e){e.preventDefault(),t("html, body").animate({scrollTop:i.offset().top-100},isBehaviorSmooth?0:500),r.trigger(c)}));let S=a.find(".t4s-sticky-atc__product"),w="data-action-atc",T="data-action-delay",$=a.find(`[${w}]`),C=!0;a.find(`[${w}][${T}]`).on(c,(function(i){!t(this)[0].hasAttribute(T)||e.width()>767||(i.preventDefault(),i.stopPropagation(),C=!0,S.slideDown({start:function(){t(this).css({display:"flex"}),$.removeAttr(T),t(m).removeClass(g)},complete:function(){y(),t(m).addClass(g)}}))})),r.on(c,(function(i){!C||e.width()>767||(i.preventDefault(),C=!1,S.slideUp({start:function(){$.attr(T,""),t(m).removeClass(g)},complete:function(){y(),t(m).addClass(g)}}))})),e.on("resize.sticky",y)}function y(){h=a.outerHeight(),p!=h&&(p=h,n.css({"--stickyATC-height":a.outerHeight()+"px"}))}return v.prototype=Object.assign({},v.prototype,{_updateContent:function(){T4SThemeSP.$appendComponent.after(t("#t4s-sticky-atc-temp").html()),a=t("[data-sticky-addtocart]")},_stickyAddToCartToggle:function(n){var p=e.scrollTop(),h=parseInt(p+u)+Math.max(t("#t4s-footer").outerHeight()/2,120);f=i.height(),s<p&&h!==f&&h<f?(a.addClass(l),o.addClass(d),y()):(h===f||h>f||s>p&&n)&&(a.removeClass(l),o.removeClass(d),a.find("[data-dropdown-open].is--clicked").click(),r.trigger(c))}}),v}();function x(e){var i=e.find(".t4s-product__media-item:not(.is--media-hide)"),a=i.length;(4==a||a>5)&&(a="normal"),e.attr("data-media-sizes",a),e.find(".t4s-product__media-item:not(.is--media-hide):last").addClass("is--media-last"),e.find(".t4s-product__media-item").attr("data-index",""),i.each((function(e){t(this).attr("data-index",e)}))}function P(t){return JSON.parse(t||"{}")}T4SThemeSP.Product=function(){var r={},l={},d=!n.hasClass("is-remove-unavai-0"),c="[data-swatch-option]",u="is--selected",f="."+u,p="is-pswp-disable",h="isotopet4s-enabled",m=".t4s-color-mode__variant_image .is--first-color",g="[data-swatch-item]:not("+f+")",v=T4Sconfigs,y=T4SProductStrings,b=v.nowTimestamp,S=v.new_day_int,M=v.use_sale_badge,O=v.label_sale_style,V=v.use_preorder_badge,A=v.use_new_badge,L=v.use_soldout_badge,E=v.use_custom_badge,D={sale:y.badgeSale,new:y.badgeNew,preOrder:y.badgepreOrder,soldout:y.badgeSoldout,SavePercent:y.badgeSavePercent},z={texts:D,saleStyle:O};function B(e){if(this.$container=t(e),this.$container.is("[data-product-options]"))this._itemQuickShopInline();else if(this.productConfigs=P(this.$container.attr("data-product-featured")),this.productID=this.productConfigs.id,this.container=e,this.$mainMedia=this.$container.find("[data-main-media]"),this.mainMedia=this.$mainMedia[0],this.sectionId=this.productConfigs.sectionId,this.disableSwatch=this.productConfigs.disableSwatch,this.isSticky=this.productConfigs.isSticky,this.isStickyMB=this.productConfigs.isStickyMB,this.stickyShow=this.productConfigs.stickyShow,this.useStickySelect=this.productConfigs.useStickySelect,this.$shortDes=this.$container.find("[data-des-height]"),this.eventHandlers={},this._createBadgesProduct(),this._initBootSales(),this._initSubmit(),this.productConfigs.id){this.$variantImg=this.$container.find(m),this.disableVariantImage=!this.$variantImg[0],this.$formSelectorId=this.$container.find(this.productConfigs.formID),this.$formSelectorIdLength=this.$formSelectorId.length,this.pickupAvailabilityContainer=this.$container.find("[data-pickup-availability-container]")[0],this.pickupAvailabilityContainer&&this.$formSelectorIdLength>0&&(this._initPickupAvailability(),this.disableSwatch&&this.pickupAvailability.updateContent()),this._initNotifyBackinStock(),s&&!this.isStickyMB&&(this.isSticky=!1),this.isSticky&&this._initStickyAddToCart(),!this.disableSwatch&&this.$formSelectorIdLength>0&&(this.$originalSelectorId=this.$formSelectorId.find('select[name="items[][id]"]').length?this.$formSelectorId.find('select[name="items[][id]"]'):this.$formSelectorId.find('select[name="id"]'),this.$options1=this.$formSelectorId.find('[data-swatch-option][data-id="0"]'),this.$options2=this.$formSelectorId.find('[data-swatch-option][data-id="1"]'),this.$options3=this.$formSelectorId.find('[data-swatch-option][data-id="2"]'),this.PrOptionsSize,this.disableVariantImage||(this.$variantImgItems=this.$variantImg.find("[data-swatch-item]"),this.colorOptionIndex=this.$variantImg.data("id"),this.swatchWidth=2*this.$variantImgItems.outerWidth()),this._initVariants(),this._swatchesEventListeners(),this._changeMediaSlider(),this.disableVariantImage||this._updateVariantImageSwatchFirst());var i=this;i.mainMedia&&i._initProductIsotope(),setTimeout((function(){i.mainMedia&&(i._initLoadContent(),setTimeout((function(){i._initProductVideo(),i._initModelViewerLibraries(),i._initShopifyXrLaunch(),i._init360ViewerLibraries();var t=i.container.querySelector(".t4s-product__info-container--sticky");t&&i.productConfigs.infoOverflowScroller&&(i.infoOverflowScroller=new T4SThemeSP.OverflowScroller(t,{offsetTop:109,offsetBottom:30,updateOffsetTop:!0}))}),100))}),1e3);var n=i.productConfigs.main_click;if("none"!=n&&i.mainMedia){if(T4SThemeSP.isTouch&&i.productConfigs.enable_zoom_click_mb||T4SThemeSP.isHover&&"pswp"==n){var o=this.$mainMedia.find("."+p);o.removeClass(p),T4SThemeSP.isTouch&&i.productConfigs.enable_zoom_click_mb&&a>1024&&document.addEventListener("theme:hover",(function(t){o.addClass(p)}))}this.$shortDes&&(this.$shortDes.each((function(e,i){t(i).parent().css("--full-h",t(i).height()+"px")})),this.$shortDes.on("click",(function(){t(this).parent().css("--full-h",t(this).height()+"px")})))}}}return B.prototype=Object.assign({},B.prototype,{_itemQuickShopInline:function(){var t=this;t.$qsInline=t.$container.find("[data-qs-inl]"),t.$formSelectorId=t.$qsInline.find("form"),t.$originalSelectorId=t.$formSelectorId.find('select[name="id"]')||t.$formSelectorId.find('select[name="items[][id]"]'),t.$options1=t.$formSelectorId.find('[data-swatch-option][data-id="0"]'),t.$options2=t.$formSelectorId.find('[data-swatch-option][data-id="1"]'),t.$options3=t.$formSelectorId.find('[data-swatch-option][data-id="2"]'),t.productConfigs=P(t.$originalSelectorId.attr("data-product-featured")),t.productID=t.productConfigs.id,t.$variantImg=t.$qsInline.find(m),t.disableVariantImage=!t.$variantImg[0],t.disableVariantImage||(t.$variantImgItems=t.$variantImg.find("[data-swatch-item]"),t.colorOptionIndex=t.$variantImg.data("id"),t.swatchWidth=2*t.$variantImgItems.outerWidth()),t._initVariants(),t._swatchesEventListeners(),t._initSubmit(),t.disableVariantImage||this._updateVariantImageSwatchFirst()},_initVariants:function(){var e,i,a,n=this.productConfigs;if(n.isGrouped&&(n.isGrouped=this.$container.find("form[data-groups-pr-form]").length>0),!n.isGrouped){if(r[this.productID])a=r[this.productID],i=l[this.productID],this.PrOptionsSize=i.length;else try{a=JSON.parse(this.$container.find(".pr_variants_json").html()),this.$originalSelectorId.find("> option").each((function(i){e=t(this),a[i].incoming=e.data("incoming"),a[i].next_incoming_date=e.data("nextincomingdate")||null,a[i].inventory_policy=e.data("inventorypolicy")||null,a[i].inventory_quantity=e.data("inventoryquantity")})),r[this.productID]=a,i=JSON.parse(this.$container.find(".pr_options_json").html()),l[this.productID]=i,this.PrOptionsSize=i.length}catch(t){return void console.log("not found pr json")}"boolean"!=typeof n.unQuickShopInline&&(n.unQuickShopInline=!0);var o={enableHistoryState:n.enableHistoryState||!1,$container:this.$container,formSelectorId:this.$formSelectorId,$originalSelectorId:this.$originalSelectorId,$options1:this.$options1,$options2:this.$options2,$options3:this.$options3,variants:a,productOptions:i,PrOptionsSize:this.PrOptionsSize,removeSoldout:n.removeSoldout,isNoPick:n.isNoPick,hasSoldoutUnavailable:n.hasSoldoutUnavailable,canMediaGroup:n.canMediaGroup,isMainProduct:n.isMainProduct,oldVariant:{},badgesConfigs:z,$variantImg:this.$variantImg,disableVariantImage:this.disableVariantImage,swatchWidth:this.swatchWidth,isSticky:this.isSticky,useStickySelect:this.useStickySelect,showFirstMedia:n.showFirstMedia,unQuickShopInline:n.unQuickShopInline,isQuickShopForm:n.isQuickShopForm};this.Variants=new w(o)}},_swatchesEventListeners:function(){if(this.PrOptionsSize){var e,i,a,n=!0,s=this.$formSelectorId.hasClass("is-form-t4spritem"),r=this;r.$formSelectorId.on("click",g,(function(l){l.preventDefault();var p=t(this);if(p.addClass(u).siblings().removeClass(u),p.closest(c).find("[data-current-value]").html(p.data("value")),s&&n&&(r.$formSelectorId.addClass("t4sproduct-swatched"),r.$formSelectorId.find('[data-swatch-option][data-id="0"] [data-swatch-name],[data-swatch-option][data-id="1"],[data-swatch-option][data-id="2"]').show(150),n=!1),r.Variants.clickedCurrentValue=p.data("value")+"",a=p.closest(c).data("id"),r.Variants.clickedCurrentIndex="option"+ ++a,r.$formSelectorId.find(f).length<r.PrOptionsSize&&r.Variants.isNoPick)return r.Variants._updateMediaFilterNoPick(),void r.Variants._updateMediaFirst(p);r.Variants.eventClickedSwatch=!0,r.Variants.clickedOptions=[],r.$formSelectorId.find(c+" "+f).each((function(e,i){r.Variants.clickedOptions.push({value:t(i).data("value")+"",index:"option"+ ++e})})),r.Variants.isNoPick||(r.Variants.oldVariant=r.Variants.currentVariant),r.Variants.isNoPick&&(o.trigger("hide.t4s.notices"),r.isSticky&&t("[data-sticky-addtocart]").removeAttr("hidden"),r.Variants.isNoPick=!1,r.$container.trigger("replace:btnAtc")),i=r.Variants._getVariantFromOptions(),r.$originalSelectorId.val(i.id),e=r.$originalSelectorId.val(),!d||null!==e&&""!==e||(i=r.Variants._getVariantFromSize(),r.$originalSelectorId.val(i.id),e=r.$originalSelectorId.val()),r.Variants.currentVariant=i,r.$originalSelectorId[0].dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0}))}))}},_changeMediaSlider:function(){if(this.PrOptionsSize&&this.productConfigs.changeVariantByImg&&0!=this.$container.find(".flickityt4s[data-main-media] .t4s-product__media-item--variant").length){var e,i,a,n,o=this;this.$container.find(".flickityt4s[data-main-media]").off("select.flickityt4s").on("select.flickityt4s",(function(s,r){t(this).trigger("select.carousel"),(n=t(this).find(".flickityt4s-slider>[data-main-slide]").eq(r)).hasClass("t4s-product__media-item--variant")&&!o.Variants.eventClickedSwatch&&(e=n.data("media-id"),a=o.$originalSelectorId.val(),void 0===(i=o.$originalSelectorId.find('option[data-mdid="'+e+'"]:not(:disabled)').val())||a==i||o.Variants.isNoPick||o.Variants.mediaID==e||(o.$originalSelectorId.val(i),o.$originalSelectorId[0].dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0}))))})),this.$container.find(".flickityt4s[data-main-media]").on("click",(function(t){o.Variants.eventClickedSwatch&&(o.Variants.eventClickedSwatch=!1)}))}},_initBootSales:function(){this.BootSales=new _.init(this.$container)},_initSubmit:function(){i.trigger({type:"submitAtc:t4s",$container:this.$container})},_initProductVideo:function(){var t=this.sectionId,e=this.mainMedia.querySelectorAll('[data-media-type="video"], [data-media-type="external_video"]');e.length<1||e.forEach((function(e){T.init(e,t)}))},_init360ViewerLibraries:function(){var t=this.mainMedia.querySelectorAll('[data-media-type="360"]');t.length<1||C.init(t,this.sectionId)},_initModelViewerLibraries:function(){var t=this.mainMedia.querySelectorAll('[data-media-type="model"]');t.length<1||$.init(t,this.sectionId)},_initShopifyXrLaunch:function(){this.eventHandlers.initShopifyXrLaunchHandler=this._initShopifyXrLaunchHandler.bind(this),document.addEventListener("shopify_xr_launch",this.eventHandlers.initShopifyXrLaunchHandler)},_initShopifyXrLaunchHandler:function(){this.mainMedia.querySelector("[data-product-single-media-wrapper]").dispatchEvent(new CustomEvent("xrLaunch",{bubbles:!0,cancelable:!0}))},loadContent:function(t){if(t.getAttribute("loaded"))return;const e=document.createElement("div"),i=t.querySelector("template");e.appendChild(i.content.firstElementChild.cloneNode(!0)),t.setAttribute("loaded",!0);t.appendChild(e.querySelector("video, model-viewer, iframe"));i.remove()},_initLoadContent:function(){var t=this;t.mainMedia.querySelectorAll("[data-deferred-media]").forEach((function(e){e.classList.add("is--adding"),t.loadContent(e.querySelector(".t4s-pr"))}))},_initProductIsotope:function(){var t=this;!s&&t.productConfigs.hasIsotope&&(x(this.$mainMedia),T4SThemeSP.Isotopet4s.init(this.$mainMedia),e.on("resize.prIstope",(function(){e.width()<768&&t.$mainMedia.hasClass(h)?t.$mainMedia.isotopet4s("destroy").removeClass(h):e.width()>=768&&!t.$mainMedia.hasClass(h)&&setTimeout((function(){T4SThemeSP.Isotopet4s.init(t.$mainMedia)}),500)})))},_initPickupAvailability:function(){this.pickupAvailability=new k(this.$container,this.pickupAvailabilityContainer)},_initNotifyBackinStock:function(){let e=this,i=this.$container.find(".t4s-product-notify-stock"),a=this.$container.find("[data-notify-stock-btn]");if(i[0]||a[0])if(i[0])this.$container.on("notifyBackinStock:show",(function(a){let n=t("#ContactFormNotifyStock"+e.productID);i.show();let o=`${a.currentVariant.name.replace("- ","( ")} ) ${e.productConfigs.orgUrl}?variants=${a.currentVariant.id}`;n.find('[name="contact[product]"]').text(o)})),this.$container.on("notifyBackinStock:hide",(function(t){i.hide()}));else{var n=this.$container.find("[data-notify-stock-btn]"),o=n.data("root-url"),s="";if(o.endsWith("/")||(o+="/"),s=`${o}variants/${n.data("variant-id")}/?section_id=back-in-stock`,n.attr("data-mfp-src",s).hide().removeClass("t4s-d-none"),!this.productConfigs.available&&this.productConfigs.disableSwatch)return void n.show();this.$container.on("notifyBackinStock:show",(function(t){s=`${o}variants/${t.currentVariant.id}/?section_id=back-in-stock`,n.attr({"data-mfp-src":s,"data-storageid":`notify-stock${t.currentVariant.id}`}).show()})),this.$container.on("notifyBackinStock:hide",(function(t){n.hide()}))}},_createBadgesProduct:function(){let t=this.$container.find("[data-product-single-badge]"),e=(t.attr("data-sort")||"").replace(/ /g,"").split(","),i=this.productConfigs,a="";if(0==e.length||0==t.length)return;let n=e.length;for(let t=0;t<n;t++)switch(e[t]){case"sale":if(!M)break;let n=i.compare_at_price,c=i.price;if(n<=c){a+='<span data-badge-sale class="t4s-badge-item t4s-badge-sale" hidden></span>';break}if("2"==O)var o=100*(n-c)/n,s=D.SavePercent.replace("[sale]",Math.round(o));else if("3"==O){var r=n-c;s=T4SThemeSP.Currency.formatMoney(r)}else s=D[e[t]];a+='<span data-badge-sale class="t4s-badge-item t4s-badge-sale">'+s+"</span>";break;case"preOrder":if(!V)break;a+=`<span data-badge-preorder class="t4s-badge-item t4s-badge-preorder"${i.isPreoder?"":" hidden"}>${D[e[t]]}</span>`;break;case"new":var l=b-i.dateStart,d=Math.floor(l/3600);if((d=Math.floor(d/24))>=S||!A)break;a+='<span class="t4s-badge-item t4s-badge-new">'+D[e[t]]+"</span>";break;case"soldout":if(!L)break;a+=`<span data-badge-soldout class="t4s-badge-item t4s-badge-soldout"${i.available?" hidden":""}>${D[e[t]]}</span>`;break;default:let u=i.customBadge;if(!u||!E)break;let f=u.length;for(let t=0;t<f;t++)a+='<span class="t4s-badge-item t4s-badge-custom t4s-badge-'+i.customBadgeHandle[t]+'">'+u[t]+"</span>"}t.html(a)},_updateVariantImageSwatchFirst:function(){let e=this,i=e.Variants.variants,a=i.length,n=this.colorOptionIndex;e.$variantImgItems.each((function(o){let s=t(this),r=function(t){for(let e=0;e<a;e++){let a=i[e];if(a.featured_image&&(a.options[n]+"").toLowerCase()==t)return a.featured_image}}((s.data("value")+"").toLowerCase());if(!r)return;let l=s.find("[data-img-el]");s=l[0]?l:s,s.attr("data-bg",T4SThemeSP.Images.getNewImageUrl(r.src,e.swatchWidth))}))},_initStickyAddToCart:function(){this.stickyAddToCart=new I(this.$formSelectorId,this.stickyShow,this.useStickySelect)}}),B}(),T4SThemeSP._initProducts=(f="initProducts__enabled",function(){t("[data-product-featured]:not(."+f+")").each((function(){t(this).addClass(f),new T4SThemeSP.Product(this)}))}),T4SThemeSP._initBundlePrs=function(){var e="initBundles__enabled",i="has--hover-pin",a="is--hover",n="is--trigger-hover";return function(){t("[data-product-bundles]:not(."+e+")").each((function(){let o=t(this);o.addClass(e),_.ani(o.find("[data-atc-form]")),function(e){if(T4SThemeSP.isTouch||0==e.length)return;let o=e.find("[data-bundles-pr-form]"),s=e.find("[data-bundle-image]");e.hoverIntent({selector:"[data-bundle-pin]",sensitivity:6,interval:40,timeout:40,over:function(e){s.addClass(i),o.addClass(i),t(this).addClass(a),t(t(this).data("trigger")).addClass(n)},out:function(){s.removeClass(i),o.removeClass(i),s.find("."+a).removeClass(a),o.find("."+n).removeClass(n)}})}(o)}))}}(),T4SThemeSP.Cookies=function(){var t;(t=navigator.cookieEnabled)||(document.cookie="testcookie",t=-1!==document.cookie.indexOf("testcookie")),t||n.addClass("not--cookies")},T4SThemeSP.isVisible=function(i,a,n,o,s){if(!(i.length<1)){o=o||"both";var r=e,l=i.length>1?i.eq(0):i,d=null!=s,c=d?t(s):r,u=d?c.position():0,f=l.get(0),p=c.outerWidth(),h=c.outerHeight(),m=!0!==n||f.offsetWidth*f.offsetHeight;if("function"==typeof f.getBoundingClientRect){var g=f.getBoundingClientRect(),v=d?g.top-u.top>=0&&g.top<h+u.top:g.top>=0&&g.top<h,y=d?g.bottom-u.top>0&&g.bottom<=h+u.top:g.bottom>0&&g.bottom<=h,b=d?g.left-u.left>=0&&g.left<p+u.left:g.left>=0&&g.left<p,S=d?g.right-u.left>0&&g.right<p+u.left:g.right>0&&g.right<=p,w=a?v||y:v&&y,_=a?b||S:b&&S;w=g.top<0&&g.bottom>h||w,_=g.left<0&&g.right>p||_;if("both"===o)return m&&w&&_;if("vertical"===o)return m&&w;if("horizontal"===o)return m&&_}else{var T=d?0:u,$=T+h,C=c.scrollLeft(),k=C+p,I=l.position(),x=I.top,P=x+l.height(),M=I.left,O=M+l.width(),V=!0===a?P:x,A=!0===a?x:P,L=!0===a?O:M,E=!0===a?M:O;if("both"===o)return!!m&&A<=$&&V>=T&&E<=k&&L>=C;if("vertical"===o)return!!m&&A<=$&&V>=T;if("horizontal"===o)return!!m&&E<=k&&L>=C}}},T4SThemeSP.Tabs=(v="t4s-tabs-enabled",y="t4s-tabs-simple-enabled",b="t4s-tabs-accordion-enabled",S="t4s-active",{Default:function(){0!=(h=t(`[data-t4s-tabs]:not(.${v})`)).length&&(h.addClass(v),h.on("click","[data-t4s-tab-ul] [data-t4s-tab-item]",(function(e){e.preventDefault();var i=t(this),a=i.closest("[data-t4s-tabs]"),n=i.attr("href")||i.data("id-tab"),o=a.find(n),s=o.find(".flickityt4s"),r=o.find(".isotopet4s");a.find("."+S).removeClass(S),a.find("[data-t4s-tab-content]").hide(),i.addClass(S),o.show().addClass(S),o.closest("[data-t4s-tab-wrapper]").addClass(S),clearTimeout(p),p=setTimeout((function(){s.hasClass("flickityt4s-enabled")?s.flickityt4s("resize"):r.hasClass("isotopet4s-enabled")&&r.isotopet4s("layout")}),200)})))},Simple:function(){0!=(m=t(`[data-t4s-tabs2]:not(.${y})`)).length&&(m.addClass(y),m.on("click","[data-t4s-tab-ul2] [data-t4s-tab-item]",(function(e){e.preventDefault();var i=t(this),a=i.closest("[data-t4s-tabs2]"),n=i.attr("href")||i.data("id-tab"),o=a.find(n);a.find("."+S).removeClass(S),i.addClass(S),o.addClass(S),o.closest("[data-t4s-tab-wrapper]").addClass(S),o.closest("[data-t4s-tabs2]").attr("data-tab-active",n.replace("#","")),i.is("[data-triger-btns-tab]")&&(o.hasClass("flickityt4s flickityt4s-enabled")?o.trigger("updateBtnTab.flickityt4s"):o.find(".flickityt4s.flickityt4s-enabled").trigger("updateBtnTab.flickityt4s"))})))},Accordion:function(){0!=(g=t(`[data-t4s-tabs]:not(.${b})`)).length&&(g.addClass(b),t(".t4s-type-accordion, [data-t4s-accordion-pr]").find("."+S).find("[data-t4s-tab-content]").css("display","block"),g.on("click","[data-t4s-tab-wrapper] [data-t4s-tab-item]",(function(e){e.preventDefault();var i=t(this),a=i.closest("[data-t4s-tabs]"),n=a.find("[data-t4s-tab-ul]"),o=a.find("[data-t4s-tab-wrapper]:not([data-no-auto-close])."+S),s=o.find("[data-t4s-tab-content]"),r=i.closest("[data-t4s-tab-wrapper]"),l=r.find("[data-t4s-tab-content]"),d=i.closest(".t4s-section"),c=l.find(".flickityt4s"),u=l.find(".isotopet4s");0==d.length&&(d=i.closest(".t4s-section,.shopify-section")),r.hasClass(S)?(n.find("."+S).removeClass(S),r.removeClass(S),l.slideUp(300).removeClass(S)):(o.removeClass(S),n.find("."+S).removeClass(S),r.addClass(S),n.find(`a[href="${i.attr("href")}"], [data-href="${i.attr("href")}"]`).addClass(S),s.slideUp(150).removeClass(S),l.stop(!0,!0).slideDown(300,(function(){if(c.hasClass("flickityt4s-enabled")?c.flickityt4s("resize"):u.hasClass("isotopet4s-enabled")&&u.isotopet4s("layout"),!T4SThemeSP.isVisible(i,!0)){var e=t(".t4s-section-header").height()||0,a=d.find(".t4s-tab-wrapper.t4s-active").offset().top-e-10;t("body,html").animate({scrollTop:a})}})).addClass(S))})))}}),T4SThemeSP.RenderRefresh=function(){function e(t){o.trigger("currency:update");let e=t.find(".flickityt4s"),i=t.find(".isotopet4s");t.find(".t4s-products").length>0&&"function"==typeof T4SThemeSP.reinitProductGridItem&&T4SThemeSP.reinitProductGridItem(),i.length>0&&T4SThemeSP.Isotopet4s.init(i),e.length>0&&(e[0].flickityt4s=new T4SThemeSP.Carousel(e[0])),T4SThemeSP.ProductItem.resizeObserver(),T4SThemeSP.initLoadMore&&T4SThemeSP.initLoadMore()}return function(){var i="[data-render-lazy-component]";0!=t(i).length&&(t(i+".lazyloadt4sed").each((function(){e(t(this))})),t(i+":not(.lazyloadt4sed)").one("lazyincluded",(function(i){var a=t(i.target)[0];e(t(a))})),t(i+":not(.lazyloadt4s)").addClass("lazyloadt4s"))}}(),T4SThemeSP.ParallaxInt=function(){var e=t("[data-parallax-t4strue]:not(.parallax_enabled)");0!=e.length&&e.each((function(){var e=t(this),i=e.attr("data-imgsl")||e.find(".t4s-parallax-img:visible")[0]||".t4s-parallax-img";(e.find(i).length>0||e.is(".t4s-parallax-bg.lazyloadt4sed"))&&e.addClass("parallax_enabled").t4sJarallax({speed:e.attr("data-speed")||.8,imgElement:i})}))},T4SThemeSP.Countdown=function(){var e=T4Sconfigs.timezone,i="t4_nt_guess";try{i=dayjs.tz.guess()}catch(t){}return function(){var a=t("[data-countdown-t4s]:not(.t4s-countdown-enabled)");0!=a.length&&a.each((function(){var a,n=t(this),o=t(n.attr("data-keyid")).html()||t.trim(n.html())||"%D days %H:%M:%S",s=n.is("[data-refresh-owl]"),r=n.data("loop"),l=n.data("date"),d=parseInt(n.data("dayl")),c=dayjs(),u=l.replace(/\//g,"").replace(/-/g,"")+"",f=parseInt(u),p=u.length<9?"YYYYMMDD":"YYYYMMDDHHmmss";if((f>parseInt(c.format(p))||d<1)&&(r=!1),r||"true"==r){var h=dayjs(l).format(" HH:mm:ss"),m=d-c.diff(l.replace(/\//g,"-"),"days")%d;l=(c=c.add(m,"day")).format("YYYY/MM/DD")+h,n.attr("data-dateloop",l)}(n.countdown(function(t){if(void 0!==t){var a=t.replace("24:00:00","23:59:59");if("not4"!=e&&i!=e)try{a=dayjs.tz(t.replace(/\//g,"-"),e).toDate()}catch(t){console.log("Timezone error: "+e)}else a=new Date(a);return a}}(l),{elapse:!0}).on("update.countdown",(function(t){if(t.elapsed)n.html("").addClass("expired_cdt4s").closest("[data-countdown-wrap]").html("").addClass("expired_cdt4s");else{var e=24*t.offset.totalDays+t.offset.hours;n.html(t.strftime(o.replace("[totalHours]",e)))}})).addClass("t4s-countdown-enabled").closest("[data-countdown-wrap]").addClass("t4s-countdown-enabled"),s)&&(clearTimeout(a),a=setTimeout((()=>{n.closest(".flickityt4s-enabled").flickityt4s("resize")}),600))}))}}();var M,O,V,A,L,E,D,z,B,j,H,N,F,q,Y=function(){const e={from:0,to:0,speed:1e3,refreshInterval:100,decimals:0,formatter:function(t,e){return t.toFixed(e.decimals)},onUpdate:null,onComplete:null};function i(i,a){const n="object"==typeof a?a:{};this.$element=t(i),this.options=Object.assign({},e,this._dataOptions(),n),this._init()}return i.prototype=Object.assign({},i.prototype,{_init:function(){this.value=this.options.from,this.loops=Math.ceil(this.options.speed/this.options.refreshInterval),this.loopCount=0,this.increment=(this.options.to-this.options.from)/this.loops,this._start()},_dataOptions:function(){var t={from:this.$element.data("from"),to:this.$element.data("to"),speed:this.$element.data("speed"),refreshInterval:this.$element.data("refresh-interval"),decimals:this.$element.data("decimals")},e=Object.keys(t);for(var i in e){var a=e[i];void 0===t[a]&&delete t[a]}return t},_update:function(){this.value+=this.increment,this.loopCount++,this._render(),"function"==typeof this.options.onUpdate&&this.options.onUpdate.call(this.$element,this.value),this.loopCount>=this.loops&&(clearInterval(this.interval),this.value=this.options.to,"function"==typeof this.options.onComplete&&this.options.onComplete.call(this.$element,this.value))},_render:function(){var t=this.options.formatter.call(this.$element,this.value,this.options);this.$element.text(t)},_start:function(){this._stop(),this._render(),this.interval=setInterval(this._update.bind(this),this.options.refreshInterval)},_stop:function(){this.interval&&clearInterval(this.interval)}}),i}();T4SThemeSP.AnimateOnScroll=(M=T4Sconfigs.timeani||200,O=new IntersectionObserver((function(e,i){e.forEach((function(e){var i=e.target;e.isIntersecting&&!i.classList.contains("t4s_animated")&&(setTimeout((function(){i.classList.add("t4s_animated"),t(i).is("[data-count-to]")&&(this.countTo=new Y(i))}),M),O.unobserve(i))}))})),function(){var e=t("[data-t4s-animate]:not(.t4s-animate-init)");0!=e.length&&window.IntersectionObserver&&e.each((function(e){O.observe(this),t(this).addClass("t4s-animate-init")}))}),T4SThemeSP.PopupMFP=(V=t.fn.magnificPopupT4s,A=d.mfp_close,L=d.mfp_loading,E=t("[data-open-mfp-inline]:not(.t4s-mfp-enabled)"),D=t("[data-open-mfp-iframe]:not(.t4s-mfp-enabled)"),z=t("[data-open-mfp-video]:not(.t4s-mfp-enabled)"),B=t("[data-open-mfp-ajax]:not(.t4s-mfp-enabled)"),j=t("[data-open-mfp]"),H="is-opening-mfp",function(){void 0!==V&&(0!=E.length&&E.magnificPopupT4s({type:"inline",removalDelay:500,tClose:A,tLoading:L,callbacks:{beforeOpen:function(){n.addClass(H),this.st.mainClass="mfp-move-horizontal t4s-inline-popup-wrapper t4s-rte "+t(this.st.el).data("id")||""},open:function(){o.trigger("NTpopupInline:offClose"),o.trigger("currency:update")},afterClose:function(){o.trigger("NTpopupInline:onClose"),n.removeClass(H)}}}).addClass("t4s-mfp-enabled"),0!=(D=t("[data-open-mfp-iframe]:not(.t4s-mfp-enabled)")).length&&D.magnificPopupT4s({type:"iframe",tClose:A,tLoading:L,iframe:{markup:'<div class="mfp-iframe-scaler t4s-pr t4s-mfp-iframe"><div class="mfp-close"></div><iframe class="mfp-iframe" allow="autoplay; encrypted-media" frameborder="0" allowfullscreen></iframe></div>',patterns:{youtube:{index:"youtube.com/",id:"v=",src:"//www.youtube.com/embed/%id%?enablejsapi=1&autoplay=0&rel=0&playlist=%id%&loop=1"},vimeo:{index:"vimeo.com/",id:"/",src:"//player.vimeo.com/video/%id%?autoplay=0&loop=1"},gmaps:{index:"//maps.google.",src:"%id%&output=embed"}},srcAction:"iframe_src"},callbacks:{beforeOpen:function(){n.addClass(H),this.st.mainClass="t4s-iframe-popup-wrapper "+t(this.st.el).data("id")||""},change:function(){},open:function(){var e=t(this.st.el),i=t(".t4s-mfp-iframe").find(".mfp-iframe"),a=i.attr("src");e.is("[data-autoplay-true]")&&(a=a.replace("autoplay=0","autoplay=1")),e.is("[data-loop-false]")&&(a=(a=a.split("&playlist=")[0]).replace("loop=1","loop=0")),i.attr("src",a)},close:function(){},afterClose:function(){n.removeClass(H)}}}).addClass("t4s-mfp-enabled"),0!=(z=t("[data-open-mfp-video]:not(.t4s-mfp-enabled)")).length&&z.on("click",(function(e){e.preventDefault();var i,a=t(this),o=JSON.parse(a.attr("data-options")||"{}"),s=o.type,r=o.vid,l=o.autoplay,d=o.loop,c='<iframe src="src_t4s" class="class_t4s" title="" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>',u={html5:"html5",youtube:"youtube",vimeo:"vimeo"};switch(s){case u.html5:let e=o.id,a="";a=e&&t(e)[0]?t(e).html():'<video class="mfp-video" src="'+o.srcDefault+'" preload="auto" controls '+(l?"autoplay":"")+(l?" loop":"")+" playsinline></video>",i=`<div class="mfp-video-scaler t4s-pr t4s-mfp-video">${a.replace("<video",'<video  class="mfp-video"')}</div>`;break;case u.youtube:i='<div class="mfp-iframe-scaler t4s-pr t4s-mfp-iframe">'+(c=c.replace("src_t4s","//www.youtube.com/embed/"+r+"?enablejsapi=1&showinfo=0&controls=1&modestbranding=1&autoplay="+ +l+"&rel=0"+(d?"&playlist="+r+"&loop=1":"")).replace("class_t4s","js-youtube"))+"</div>";break;case u.vimeo:i='<div class="mfp-iframe-scaler t4s-pr t4s-mfp-iframe">'+(c=c.replace("src_t4s","//player.vimeo.com/video/"+r+"?&portrait=0&byline=0&color="+o.accent_color+"&autoplay="+ +l+"&loop="+ +d).replace("class_t4s","js-vimeo"))+"</div>"}t.magnificPopupT4s.open({items:{src:i,type:"inline"},tClose:A,tLoading:L,callbacks:{beforeOpen:function(){n.addClass(H),this.st.mainClass="t4s-video-popup-wrapper mfp-video-holder "+a.data("id")||""},open:function(){a.addClass("t4s-mfp-enabled")},afterClose:function(){a.removeClass("t4s-mfp-enabled"),n.removeClass(H)}}})})),0!=(B=t("[data-open-mfp-ajax]:not(.t4s-mfp-enabled)")).length&&B.magnificPopupT4s({type:"ajax",removalDelay:500,tClose:A,tLoading:'<div class="t4s-loading-spin t4s-spin-centered t4s-spin-dark t4s-spin-medium"></div>',callbacks:{parseAjax:function(e){var i=t(this.st.el),a=i.data("id")||"",n=i.data("class")||"",o=i.data("style")||"",s=e.data;e.data=`<div class="mfp-with-anim t4s-mfp-popup t4s-rte ${n}" id="${a}" style="${o}">${s.split("[t4splitlz]")[1]||s}</div>`},ajaxContentAdded:function(){},beforeOpen:function(){n.addClass(H),this.st.mainClass="mfp-move-horizontal t4s-ajax-popup-wrapper"},open:function(){var e=t(this.st.el).data("custom"),i=t(this.st.el).data("phone"),a=t(".t4s-ajax-popup-wrapper:not(.mfp-bg) .mfp-content");if(o.trigger("NTpopupInline:offClose"),o.trigger("currency:update"),0==i&&(a.find("#t4s-ContactFormAsk__phone").remove(),setTimeout((function(){a.find("#t4s-ContactFormAsk__phone").remove()}),400)),e){var n=e.split("||");t.each(n,(function(t,e){var i=e.split("=>");a.find(i[0]).html(i[1])})),setTimeout((function(){t.each(n,(function(t,e){var i=e.split("=>");a.find(i[0]).html(i[1])}))}),400)}},afterClose:function(){o.trigger("NTpopupInline:onClose"),n.removeClass(H)}}}).addClass("t4s-mfp-enabled"),0!=j.length&&o.on("click","[data-open-mfp]",(function(e){e.preventDefault();var i=t(e.currentTarget),a=(t("html"),i.data()),o=a.opennt,s=a.color,r=a.bg,l=a.pos,d=a.ani||"has_ntcanvas",c=a.remove,u=a.class,f=a.close||!1,p=a.focuslast||!1,h=i.attr("data-focus"),m=window.pageYOffset,g=(window.height,t("#shopify-section-header_banner").outerHeight(),t(".ntheader_wrapper").outerHeight(),function(){m&&t("html, body").scrollTop(m)});i.addClass("current_clicked"),t.magnificPopupT4s.open({items:{src:o,type:"inline",tLoading:'<div class="loading-spin dark"></div>'},tClose:nt_settings.close,removalDelay:300,closeBtnInside:f,focus:h,autoFocusLast:p,callbacks:{beforeOpen:function(){this.st.mainClass=d+" "+s+" "+d+"_"+l,n.addClass(H)},open:function(){n.addClass(d),n.addClass(d+"_"+l),u&&t(".mfp-content").addClass(u),r&&t(".mfp-bg").addClass(r),body.on("click",".close_pp",(function(e){e.preventDefault(),t.magnificPopup.close()})),g()},beforeClose:function(){n.removeClass(d)},afterClose:function(){n.removeClass(d+"_"+l),t(".current_clicked").removeClass("current_clicked"),c&&t(o).removeClass("mfp-hide"),n.removeClass(H)}}})})))}),T4SThemeSP.NTpopupInline=function(){var e,i=t("#t4s_temp_modal").html(),a="modalt4s:trigger",s="modalt4s:opened",r="modalt4s:closed",l="modalt4s:destroy",d="t4s-modal--is-active",c="t4s-modal-opened",u=".t4s_qv",f="click"+u,p="keyup"+u,h="transitionend webkitTransitionEnd oTransitionEnd";function m(){return t("html").hasClass(c)}function g(t){27===t.keyCode&&o.trigger(r)}function v(e){t(e.target).parents().is(".t4s-modal__inner")||t(e.target).parents().is(".mfp-ready")||t(e.target).is("#t4s-notices__wrapper")||t(e.target).parents().is("#t4s-notices__wrapper")||(e.preventDefault(),o.trigger(r))}return function(u,y,b,S=null){e=S,o.off(a).on(a,(function(t){m()?o.trigger(r):o.trigger(s)})),o.off(s).on(s,(function(a){m()||(function(a){T4SThemeSP.$appendComponent.after(i),e&&n.addClass(e);t(".t4s-modal__content").html(a)}(u),function(e){n.addClass(c),n.css({"margin-right":T4SThemeSP.scrollbarWidth}),t(".t4s-modal").addClass(d),t(".t4s-modal").on(h,(function(){t(this).focus().off(h)})),setTimeout((function(){t(".t4s-modal").focus()}),500),e()}(b))})),o.off(r).on(r,(function(i){m()&&(t(".t4s-modal .flickityt4s-enabled").trigger("destroy.t4s"),t("html").css({"margin-right":""}),t("html").removeClass(c).addClass("t4s-modal-closing"),t(".t4s-modal").removeClass(d).addClass("t4s-modal--is-closing"),setTimeout((function(){t(".t4s-modal").remove(),t("html").removeClass("t4s-modal-closing"),e&&n.removeClass(e),T4SThemeSP.isEditCartReplace=!1}),500),o.trigger(l),o.trigger("t4s:hideTooltip"))})),o.off(l).on(l,(function(t){o.off(a).off(s).off(r).off(l).off(f).off(p)})),o.on(f,"[data-t4s-modal-close]",(function(t){t.preventDefault(),o.trigger(r)})),o.on(f,v),o.on(p,g),o.on("NTpopupInline:offClose",(function(t){o.off(p,g),o.off(f,v)})),o.on("NTpopupInline:onClose",(function(t){o.on(p,g),o.on(f,v)}))}}(),T4SThemeSP.Currency=(N=T4SThemeSP.settings&&T4SThemeSP.settings.superScriptPrice,F=window.T4Sconfigs.moneyFormat,q=function(t,e){return null==t||t!=t?e:t},{formatMoney:function(t,e){e||(e=F),"string"==typeof t&&(t=t.replace(".",""));var i="",a=/\{\{\s*(\w+)\s*\}\}/,n=e||"${{amount}}";function o(t,e,i,a){if(e=q(e,2),i=q(i,","),a=q(a,"."),isNaN(t)||null==t)return 0;var n=(t=(t/100).toFixed(e)).split(".");return n[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g,"$1"+i)+(n[1]?a+n[1]:"")}switch(n.match(a)[1]){case"amount":i=o(t,2),N&&i&&i.includes(".")&&(i=i.replace(".","<sup>")+"</sup>");break;case"amount_no_decimals":i=o(t,0);break;case"amount_with_comma_separator":i=o(t,2,".",","),N&&i&&i.includes(".")&&(i=i.replace(",","<sup>")+"</sup>");break;case"amount_no_decimals_with_comma_separator":i=o(t,0,".",",");break;case"amount_no_decimals_with_space_separator":i=o(t,0," ");break;case"amount_with_apostrophe_separator":i=o(t,2,"'")}return n.replace(a,i)},getBaseUnit:function(t){if(t&&t.unit_price_measurement&&t.unit_price_measurement.reference_value)return 1===t.unit_price_measurement.reference_value?t.unit_price_measurement.reference_unit:t.unit_price_measurement.reference_value+t.unit_price_measurement.reference_unit}}),T4SThemeSP.slate={};var W,R,U,Q,X,Z=T4SThemeSP.slate;Z.utils={getParameterByName:function(t,e){e||(e=window.location.href),t=t.replace(/[[\]]/g,"\\$&");var i=new RegExp("[?&]"+t+"(=([^&#]*)|&|#|$)").exec(e);return i?i[2]?decodeURIComponent(i[2].replace(/\+/g," ")):"":null},removeParameterByName:function(t,e){e||(e=window.location.href),t=t.replace(/[[\]]/g,"\\$&");var i=e.split("?")[0],a=[],n=-1!==e.indexOf("?")?e.split("?")[1]:"";if(""!==n){for(var o=(a=n.split("&")).length-1;o>=0;o-=1)a[o].split("=")[0]===t&&a.splice(o,1);a.length&&(i=i+"?"+a.join("&"))}return i},resizeSelects:function(e){e.each((function(){var e=t(this),i=e.find("option:selected").text(),a=t("<span>").html(i);a.appendTo("body");var n=a.width();a.remove(),e.width(n+10)}))},keyboardKeys:{TAB:9,ENTER:13,ESCAPE:27,LEFTARROW:37,RIGHTARROW:39}},Z.rte={wrapTable:function(t){t.$tables.wrap('<div class="'+t.tableWrapperClass+'"></div>')},wrapIframe:function(e){e.$iframes.each((function(){t(this).wrap('<div class="'+e.iframeWrapperClass+'"></div>'),this.src=this.src}))}},Z.a11y={pageLinkFocus:function(t){var e="js-focus-hidden";t.first().attr("tabIndex","-1").focus().addClass(e).one("blur",(function(){t.first().removeClass(e).removeAttr("tabindex")}))},focusHash:function(){var e=window.location.hash;e&&document.getElementById(e.slice(1))&&this.pageLinkFocus(t(e))},bindInPageLinks:function(){t("a[href*=#]").on("click",function(e){this.pageLinkFocus(t(e.currentTarget.hash))}.bind(this))},trapFocus:function(e){var i={focusin:e.namespace?"focusin."+e.namespace:"focusin",focusout:e.namespace?"focusout."+e.namespace:"focusout",keydown:e.namespace?"keydown."+e.namespace:"keydown.handleFocus"},a=e.$container.find(t('button, [href], input, select, textarea, [tabindex]:not([tabindex^="-"])').filter(":visible")),n=a[0],o=a[a.length-1];e.$elementToFocus||(e.$elementToFocus=e.$container),e.$container.attr("tabindex","-1"),e.$elementToFocus.focus(),t(document).off("focusin"),t(document).on(i.focusout,(function(){t(document).off(i.keydown)})),t(document).on(i.focusin,(function(e){e.target!==o&&e.target!==n||t(document).on(i.keydown,(function(t){!function(t){t.keyCode===Z.utils.keyboardKeys.TAB&&(t.target!==o||t.shiftKey||(t.preventDefault(),n.focus()),t.target===n&&t.shiftKey&&(t.preventDefault(),o.focus()))}(t)}))}))},removeTrapFocus:function(e){var i=e.namespace?"focusin."+e.namespace:"focusin";e.$container&&e.$container.length&&e.$container.removeAttr("tabindex"),t(document).off(i)},accessibleLinks:function(e){var i=document.querySelector("body"),a={newWindow:"a11y-new-window-message",external:"a11y-external-message",newWindowExternal:"a11y-new-window-external-message"};void 0!==e.$links&&e.$links.jquery||(e.$links=t("a[href]:not([aria-describedby])")),t.each(e.$links,(function(){var e=t(this),i=e.attr("target"),n=e.attr("rel"),o=function(t){var e=window.location.hostname;return t[0].hostname!==e}(e),s="_blank"===i;o&&e.attr("aria-describedby",a.external),s&&(void 0!==n&&-1!==n.indexOf("noopener")||e.attr("rel",(function(t,e){return(void 0===e?"":e+" ")+"noopener"})),e.attr("aria-describedby",a.newWindow)),o&&s&&e.attr("aria-describedby",a.newWindowExternal)})),function(e){"object"!=typeof e&&(e={});var n=t.extend({newWindow:"Opens in a new window.",external:"Opens external website.",newWindowExternal:"Opens external website in a new window."},e),o=document.createElement("ul"),s="";for(var r in n)s+="<li id="+a[r]+">"+n[r]+"</li>";o.setAttribute("hidden",!0),o.innerHTML=s,i.appendChild(o)}(e.messages)}},T4SThemeSP.LinkMyltiLang=(W=T4SThemeSP.root_url,R=window.location.hostname,U=T4Sroutes.root_url,Q="is--checked-link",X=R+U,function(){"/"!=W&&t(`a[href*="${R}"]:not(.${Q}):not([href*="@"])`).each((function(){let e=t(this),i=e.attr("href");e.addClass(Q),i.indexOf(R+W)>=0&&"/"!=i||i.indexOf("preview_theme_id=")>-1||("/"!=i?e.attr("href",i.replace(R,X)):e.attr("href",U))}))});var K,G,J,tt,et,it,at=function(){var e="is--active";function i(e){var i=this;if(i.$slider=t(e),i.slideWrap=i.$slider.closest("[data-slide-wrap]")[0]||e,!i.slideWrap)return;if(i.sliderOptions=JSON.parse(i.$slider.attr("data-options")||"{}"),i.slider=e,i.sliderItems=e.querySelectorAll(".t4s-slider__slide"),i.pageCount=i.slideWrap.querySelector(".t4s-slider-counter--current"),i.pageTotal=i.slideWrap.querySelector(".t4s-slider-counter--total"),i.prevButton=i.slideWrap.querySelector(".t4s-slider__slide-prev"),i.nextButton=i.slideWrap.querySelector(".t4s-slider__slide-next"),!i.slider||!i.nextButton)return;new ResizeObserver((t=>i._initPages())).observe(i.slider),i.slider.addEventListener("scroll",i._update.bind(i)),i.prevButton.addEventListener("click",i._onButtonClick.bind(i)),i.nextButton.addEventListener("click",i._onButtonClick.bind(i))}return i.prototype=Object.assign({},i.prototype,{_initPages:function(){this.slider.classList.remove(e),0!==!this.sliderItems.length&&(this.slider.classList.add(e),this.slidesPerPage=Math.floor(this.slider.clientWidth/this.sliderItems[0].clientWidth),this.totalPages=this.sliderItems.length-this.slidesPerPage+1,this._update())},_update:function(){this.pageCount&&this.pageTotal&&(this.currentPage=Math.round(this.slider.scrollLeft/this.sliderItems[0].clientWidth)+1,1===this.currentPage?this.prevButton.setAttribute("disabled",!0):this.prevButton.removeAttribute("disabled"),this.currentPage===this.totalPages?this.nextButton.setAttribute("disabled",!0):this.nextButton.removeAttribute("disabled"),this.pageCount.textContent=this.currentPage,this.pageTotal.textContent=this.totalPages)},_onButtonClick:function(t){t.preventDefault();const e="next"===t.currentTarget.name?this.slider.scrollLeft+this.sliderItems[0].clientWidth:this.slider.scrollLeft-this.sliderItems[0].clientWidth;this.slider.scrollTo({left:e})}}),i}();T4SThemeSP.SliderComponentInt=void(0!=(K=t(".t4s-slider:not(.t4s-enabled)")).length&&K.each((function(t){this.classList.add("t4s-enabled"),this.sliderComponent=new at(this)}))),T4SThemeSP.LibraryLoader=(tt={requested:"requested",loaded:"loaded"},it={youtubeSdk:{tagId:"youtube-sdk",src:"https://www.youtube.com/iframe_api",type:J="script"},vimeoSdk:{tagId:"vimeo-sdk",src:"https://player.vimeo.com/api/player.js",type:J},plyrShopifyStyles:{tagId:"plyr-shopify-styles",src:(et="https://cdn.shopify.com/shopifycloud/")+"plyr/v2.0/shopify-plyr.css",type:G="link"},modelViewerUiStyles:{tagId:"shopify-model-viewer-ui-styles",src:et+"model-viewer-ui/assets/v1.0/model-viewer-ui.css",type:G}},{load:function(t,e){var a=it[t];if(a){if(l&&"youtubeSdk"==t&&window.YT)return e(),void i.trigger("youtube:ready");if(a.status!==tt.requested)if(e=e||function(){},a.status!==tt.loaded){var n;switch(a.status=tt.requested,a.type){case J:n=function(t,e){var i=document.createElement("script");return i.src=t.src,i.addEventListener("load",(function(){t.status=tt.loaded,e()})),i}(a,e);break;case G:n=function(t,e){var i=document.createElement("link");return i.href=t.src,i.rel="stylesheet",i.type="text/css",i.addEventListener("load",(function(){t.status=tt.loaded,e()})),i}(a,e)}n.id=a.tagId,a.element=n;var o=document.getElementsByTagName(a.type)[0];o.parentNode.insertBefore(n,o)}else e()}}});var nt,ot=function(){var a="t4s-bgvideo-playing",n="html5",o="youtube",s="vimeo",r=!1,l=!1;function d(e){var i=this;switch(i.$video=t(e),i.video_options=JSON.parse(i.$video.attr("data-options")||"{}"),i.video_type=i.video_options.type,i.video_mute=i.video_options.mute,i.$videoInsert=i.$video.find("[data-bgvideo-insert]"),i.$elementToInsert=i.$videoInsert.length?i.$videoInsert:i.$video,i.elementToInsert=i.$elementToInsert[0],i.$video.attr("loaded",!0),i.video_type){case n:i._setupBgHtml5Video();break;case o:window.YT?i._setupBgYouTubeVideo():(i._triggerBgYouTubeVideo(),r||(T4SThemeSP.LibraryLoader.load("youtubeSdk"),r=!0));break;case s:window.Vimeo?i._setupBgVimeoVideo():(i._triggerBgVimeoVideo(),l||(T4SThemeSP.LibraryLoader.load("vimeoSdk",i._loadedVimeoSDK.bind(i)),l=!0))}}return d.prototype=Object.assign({},d.prototype,{_triggerBgYouTubeVideo:function(){var t=this;i.on("youtube:ready",(function(e){t._setupBgYouTubeVideo()}))},_loadedVimeoSDK:function(){i.trigger("vimeo:ready")},_triggerBgVimeoVideo:function(){var t=this;i.on("vimeo:ready",(function(e){t._setupBgVimeoVideo()}))},_setupBgHtml5Video:function(){var e=this,i=e.video_options.id;let n=i&&t(i)[0]?t(i).html():'<video class="t4s_bg_vid_html5" src="'+e.video_options.srcDefault+'" preload="auto" playsinline autoplay '+(e.video_mute?"muted ":" ")+"loop></video>";e.$elementToInsert.replaceWith(n),e.$video.find(".t4s_bg_vid_html5").on("playing",(function(t){e.$video.addClass(a)}))},_setupBgYouTubeVideo:function(){if(window.YT){var t=this;t.player=new YT.Player(t.elementToInsert,{videoId:t.video_options.vid,playerVars:{iv_load_policy:3,enablejsapi:1,disablekb:1,autoplay:0,controls:0,rel:0,loop:0,playsinline:1,modestbranding:1,autohide:1,branding:0,cc_load_policy:0,fs:0,quality:"hd1080",wmode:"transparent",height:"100%",width:"100%",origin:t.video_options.requestHost},events:{onReady:t.onPlayerReady.bind(this),onStateChange:t.onPlayerStateChange.bind(this)}}),t.resizeVideoBackground(),e.on("resize",T4SThemeSP.debounce(300,function(){t.resizeVideoBackground()}.bind(t)))}},onPlayerReady:function(t){this.video_mute&&this.player.mute(),this.player.playVideo()},onPlayerStateChange:function(t){t.data===YT.PlayerState.PLAYING?this.$video.addClass(a):t.data===YT.PlayerState.ENDED&&this.player.playVideo()},_setupBgVimeoVideo:function(){if(window.Vimeo){var t=this;t.player=new Vimeo.Player(t.elementToInsert.parentNode,{id:t.video_options.vid,autoplay:!0,autopause:!1,muted:!0,background:!0,loop:t.video_mute}),t.$videoInsert.remove(),t.resizeVideoBackground(),e.on("resize",T4SThemeSP.debounce(300,function(){t.resizeVideoBackground()}.bind(t))),t.player.on("play",(function(){t.$video.addClass(a)})),t.player.on("ended",(function(){}))}},resizeVideoBackground:function(){var t,e,i,a,n=this.$video,o=n.innerWidth(),s=n.innerHeight();o/s<16/9?(t=s*(16/9),e=s,i=-Math.round((t-o)/2)+"px",a=-Math.round((e-s)/2)+"px"):(e=(t=o)*(9/16),a=-Math.round((e-s)/2)+"px",i=-Math.round((t-o)/2)+"px"),t+="px",e+="px",n.find("iframe").css({maxWidth:"1000%",marginLeft:i,marginTop:a,width:t,height:e})}}),d}();T4SThemeSP.BgVideo=function(){var e=t('[data-video-background]:not([loaded="true"])');0!=e.length&&e.each((function(t){this.bgVideo=new ot(this)}))},T4SThemeSP.Footer=function(){var i={opened:"is--footer_opened"},a=200;function n(){t("[data-footer-open]").off("click").on("click",(function(){var e=t(this).parent(),n=e.find("> [data-footer-content]");e.hasClass(i.opened)?(e.removeClass(i.opened),n.stop().slideUp(a)):(e.addClass(i.opened),n.stop().slideDown(a))})),e.off("resize.FooterCollapse")}return function(){e.on("resize.FooterCollapse",n),e.width()<768&&t(".is--footer-collapse-true").length>0&&n()}}(),T4SThemeSP.Notices=function(){var e,i,a,s,r,l=t("#t4s-notices__tmp"),d=l.html(),c="is--show",u="is--active-notices",f="click.notices",p="hide.t4s.notices",h=!1,m=T4Sconfigs.autoHideNotices,g=T4Sconfigs.timeOutNotices;function v(){a.hide(),clearTimeout(r),e.removeClass(c),n.removeClass(u),e.off(f).off(p)}return l.remove(),function(l,y="warning"){h||(T4SThemeSP.$appendComponent.after(d),e=t("#t4s-notices__wrapper"),i=e.find(".t4s-notices__mess"),a=e.find(".t4s-notices__progressbar"),s=a.show().find(">span"),h=!0,d=null,s.css("animation-duration",`${g}ms`)),e.attr("data-notices-status",y),"object"==typeof l?(i.html(""),t.each(l,(function(t,e){i.append('<span class="t4s-d-block">'+e[0]+"</span>")}))):i.html(l),m?a.show():a.hide(),setTimeout((function(){e.addClass(c),n.addClass(u)}),200),m&&(r=setTimeout((function(){v()}),g+200)),e.on(f,(function(){v()})),o.on(p,(function(){v()}))}}(),T4SThemeSP.FormShopifyMessSuccess=function(){t(document).on("submit",'form[action^="/contact"]',(function(e){-1===t(this).attr("id").indexOf("captcha")&&localStorage.setItem("t4s-recentform",t(this).attr("id"))}));let e=location.href,i=localStorage.getItem("t4s-recentform")||"";e.indexOf("contact_posted=true")<0&&""!==i||(e.indexOf("contact_posted=true#ContactFormNotifyStock")>-1||i.includes("ContactFormNotifyStock")?T4SThemeSP.Notices(d.frm_notify_stock_success,"success"):e.indexOf("contact_posted=true#ContactFormAsk")>-1||i.includes("ContactFormAsk")?T4SThemeSP.Notices(d.frm_contact_ask_success,"success"):(e.indexOf("contact_posted=true#t4sNewsletterFormPopup")>-1||i.includes("t4sNewsletterFormPopup"))&&T4SThemeSP.Notices(d.frm_newsletter_popup_success,"success"))},T4SThemeSP.PreloadStylePopup=function(){var e=t("#t4s-style-popup");0!=e.length&&t("#t4s-assets-pre").html(e.html())},T4SThemeSP.BtnMore=function(){var e="is--enabled",i="[data-btn-toogle]",a="is--open";function n(i){this.el=i,this.$el=t(i),this.clickHandler(),this.$el.addClass(e),this.selector=this.$el.data("slector"),this.tMore=this.$el.data("tmore"),this.tLess=this.$el.data("tless"),this.hasIsotope=this.$el.hasClass("isotopet4s")}return n.prototype=Object.assign({},n.prototype,{clickHandler:function(){var e=this;e.$el.on("click.more",i,(function(i){i.preventDefault();let n=t(this);n.parent().find(e.selector).slideToggle(200),n.toggleClass(a),n.hasClass(a)?n.html(e.tLess):n.html(e.tMore),e.hasIsotope&&(e.$el.isotopet4s("layout"),setTimeout((function(){e.$el.isotopet4s("layout")}),219))}))}}),n}(),T4SThemeSP.initBtnMore=void(0!=(nt=t("[data-wrap-toogle]:not(.is--enabled)")).length&&nt.each((function(){this.btnMore=new T4SThemeSP.BtnMore(this)})))}(jQuery_T4NT),jQuery_T4NT(document).ready((function(t){let e;smoothscroll.polyfill(),T4SThemeSP.FormShopifyMessSuccess(),T4SThemeSP.BgVideo();const i=navigator.userAgent.toLowerCase().includes("firefox");e=setTimeout((()=>T4SThemeSP.ParallaxInt()),i?300:0),t(".t4s-parallax-bg:not(.lazyloadt4sed)").on("lazyloaded",(function(t){clearTimeout(e),e=setTimeout((()=>T4SThemeSP.ParallaxInt()),i?300:0)})),T4SThemeSP.Countdown(),T4SThemeSP.AnimateOnScroll(),T4SThemeSP._initProducts(),T4SThemeSP.slate.a11y.accessibleLinks({$links:t("a[href]:not([aria-describedby])")}),T4SThemeSP.Tabs.Default(),T4SThemeSP.Tabs.Simple(),T4SThemeSP.Tabs.Accordion(),T4SThemeSP.Footer(),T4SThemeSP.PopupMFP(),T4SThemeSP.Cookies(),$script([T4Sconfigs.script3,T4Sconfigs.script6]),setTimeout((function(){T4SThemeSP.LinkMyltiLang()}),500),setTimeout((function(){T4SThemeSP.PreloadStylePopup()}),1500)}));