window.lazySizesT4Config=window.lazySizesT4Config||{},window.lazySizesT4Config={init:!0,loadMode:!0,loadHidden:!1,hFac:.5,expFactor:2,ricTimeout:150,lazyClass:"lazyloadt4s",loadingClass:"lazyloadt4sing",loadedClass:"lazyloadt4sed",preloadClass:"lazypreloadt4s"},function(e,t){var n=function(){t(e.lazySizesT4),e.removeEventListener("lazyunveilread",n,!0)};t=t.bind(null,e,e.document),"object"==typeof module&&module.exports?t(require("lazySizesT4")):e.lazySizesT4?n():e.addEventListener("lazyunveilread",n,!0)}(window,function(e,t,n){"use strict";function a(e,n){if(!s[e]){var a=t.createElement(n?"link":"script"),i=t.getElementsByTagName("script")[0];n?(a.rel="stylesheet",a.href=e):a.src=e,s[e]=!0,s[a.src||a.href]=!0,i.parentNode.insertBefore(a,i)}}var i,r,s={};t.addEventListener&&(r=/\(|\)|\s|'/,i=function(e,n){var a=t.createElement("img");a.onload=function(){a.onload=null,a.onerror=null,a=null,n()},a.onerror=a.onload,a.src=e,a&&a.complete&&a.onload&&a.onload()},addEventListener("lazybeforeunveil",function(e){if(e.detail.instance==n){var t,s,o,l;if(!e.defaultPrevented){var d=e.target;if("none"==d.preload&&(d.preload="auto"),null!=d.getAttribute("data-autoplay"))if(d.getAttribute("data-expand")&&!d.autoplay)try{d.play()}catch(e){}else requestAnimationFrame(function(){d.setAttribute("data-expand","-10"),n.aC(d,n.cfg.lazyClass)});(t=d.getAttribute("data-link"))&&a(t,!0),(t=d.getAttribute("data-script"))&&a(t),(t=d.getAttribute("data-require"))&&(n.cfg.requireJs?n.cfg.requireJs([t]):a(t)),(o=d.getAttribute("data-bg"))&&(e.detail.firesLoad=!0,s=function(){d.style.backgroundImage="url("+(r.test(o)?JSON.stringify(o):o)+")",e.detail.firesLoad=!1,n.fire(d,"_lazyloaded",{},!0,!0)},i(o,s)),(l=d.getAttribute("data-poster"))&&(e.detail.firesLoad=!0,s=function(){d.poster=l,e.detail.firesLoad=!1,n.fire(d,"_lazyloaded",{},!0,!0)},i(l,s))}}},!1))}),function(e,t){var n=function(){t(e.lazySizesT4),e.removeEventListener("lazyunveilread",n,!0)};t=t.bind(null,e,e.document),"object"==typeof module&&module.exports?t(require("lazySizesT4")):"function"==typeof define&&define.amd?define(["lazySizesT4"],t):e.lazySizesT4?n():e.addEventListener("lazyunveilread",n,!0)}(window,function(e,t,n){"use strict";addEventListener("lazybeforeunveil",function(e){var t=e.target,n=t.getAttribute("data-bgset"),a="1"!=new URLSearchParams(n).get("width");if(!(e.defaultPrevented||!n||n.indexOf("_1x1.")<0||a)){var i,r,s=t.getAttribute("data-ratio")||0,o=t.hasAttribute("data-hash"),l=n,n="",d=(i=t.hasAttribute("data-widths")?JSON.parse(t.getAttribute("data-widths")):t.hasAttribute("data-wiis")?[180,360,540,720,900,1080]:[180,360,540,720,900,1080,1296,1512,1728,1950,2100,2260,2450,2700,3e3,3350,3750,4100]).length,u=a?l.split("_1x1."):l.split("width=1"),c=a?`${u[0]}x.`:`${u[0]}width=`,f=u[1];if(o&&s>0)for(r=0;r<d;r++)n+=c+"_"+i[r]+f+" "+i[r]+"w "+Math.round(i[r]*s)+"h, ";else for(r=0;r<d;r++)n+=c+"_"+i[r]+f+" "+i[r]+"w, ";n=n.slice(0,-2),t.setAttribute("data-bgset",n)}},!0)}),function(e,t){var n=function(){t(e.lazySizesT4),e.removeEventListener("lazyunveilread",n,!0)};t=t.bind(null,e,e.document),"object"==typeof module&&module.exports?t(require("lazySizesT4")):e.lazySizesT4?n():e.addEventListener("lazyunveilread",n,!0)}(window,function(e,t,n){"use strict";if(e.addEventListener){var a=n.cfg,i=/\s+/g,r=/\s*\|\s+|\s+\|\s*/g,s=/^(.+?)(?:\s+\[\s*(.+?)\s*\])(?:\s+\[\s*(.+?)\s*\])?$/,o=/^\s*\(*\s*type\s*:\s*(.+?)\s*\)*\s*$/,l=/\(|\)|'/,d={contain:1,cover:1},u=function(e,t){if(t){var n=t.match(o);n&&n[1]?e.setAttribute("type",n[1]):e.setAttribute("media",a.customMedia[t]||t)}},c=function(e){if(e.target._lazybgset){var t=e.target,a=t._lazybgset,i=t.currentSrc||t.src;if(i){var r=n.fire(a,"bgsetproxy",{src:i,useSrc:l.test(i)?JSON.stringify(i):i});r.defaultPrevented||(a.style.backgroundImage="url("+r.detail.useSrc+")")}t._lazybgsetLoading&&(n.fire(a,"_lazyloaded",{},!1,!0),delete t._lazybgsetLoading)}};addEventListener("lazybeforeunveil",function(e){var o,l,d;!e.defaultPrevented&&(o=e.target.getAttribute("data-bgset"))&&(d=e.target,(l=t.createElement("img")).alt="",l._lazybgsetLoading=!0,e.detail.firesLoad=!0,function(e,n,o){var l=t.createElement("picture"),d=n.getAttribute(a.sizesAttr),c=n.getAttribute("data-ratio"),f=n.getAttribute("data-optimumx"),y=n.getAttribute("data-sizes-scale");n._lazybgset&&n._lazybgset.parentNode==n&&n.removeChild(n._lazybgset),Object.defineProperty(o,"_lazybgset",{value:n,writable:!0}),Object.defineProperty(n,"_lazybgset",{value:l,writable:!0}),e=e.replace(i," ").split(r),l.style.display="none",o.className=a.lazyClass,1!=e.length||d||(d="auto"),e.forEach(function(e){var n,i=t.createElement("source");d&&"auto"!=d&&i.setAttribute("sizes",d),(n=e.match(s))?(i.setAttribute(a.srcsetAttr,n[1]),u(i,n[2]),u(i,n[3])):i.setAttribute(a.srcsetAttr,e),l.appendChild(i)}),d&&(o.setAttribute(a.sizesAttr,d),n.removeAttribute(a.sizesAttr),n.removeAttribute("sizes")),f&&o.setAttribute("data-optimumx",f),c&&o.setAttribute("data-ratio",c),y&&o.setAttribute("data-sizes-scale",y),l.appendChild(o),n.appendChild(l)}(o,d,l),setTimeout(function(){n.loader.unveil(l),n.rAF(function(){n.fire(l,"_lazyloaded",{},!0,!0),l.complete&&c({target:l})})}))}),t.addEventListener("load",c,!0),e.addEventListener("lazybeforesizes",function(e){if(e.detail.instance==n&&e.target._lazybgset&&e.detail.dataAttr){var t=e.target._lazybgset,a=t.dataset.parentFit||function(e){var t;return t=(getComputedStyle(e)||{getPropertyValue:function(){}}).getPropertyValue("background-size"),!d[t]&&d[e.style.backgroundSize]&&(t=e.style.backgroundSize),t}(t);d[a]&&(e.target._lazySizesT4ParentFit=a,n.rAF(function(){e.target.setAttribute("data-parent-fit",a),e.target._lazySizesT4ParentFit&&delete e.target._lazySizesT4ParentFit}))}},!0),t.documentElement.addEventListener("lazybeforesizes",function(e){!e.defaultPrevented&&e.target._lazybgset&&e.detail.instance==n&&(e.detail.width=function(e){var t=n.gW(e,e.parentNode);return(!e._lazySizesT4Width||t>e._lazySizesT4Width)&&(e._lazySizesT4Width=t),e._lazySizesT4Width}(e.target._lazybgset))})}}),function(e,t){var n=function(){t(e.lazySizesT4),e.removeEventListener("lazyunveilread",n,!0)};t=t.bind(null,e,e.document),"object"==typeof module&&module.exports?t(require("lazySizesT4")):"function"==typeof define&&define.amd?define(["lazySizesT4"],t):e.lazySizesT4?n():e.addEventListener("lazyunveilread",n,!0)}(window,function(e,t,n){"use strict";function a(e){var t;(t=e.match(A))?this.urls[t[1]]=v.map[t[2]]||t[2]:this.urls.include=v.map[e]||e}function i(e){var t,n,i;return e=e.trim(),e=v.map[e]||e,(n=e.match(E))?(i=n[1],t={condition:y.include.conditions[n[3]]||y.customMedia[n[3]]||n[2]||null,name:n[3]}):(i=e,t={condition:null,name:""}),t.urls={},(v.map[i]||i).split(b).forEach(a,t),!t.urls.include&&t.urls.amd&&(this.saved=!0,t.initial=this),t}function r(n,a){var i=!a.condition;return a.condition&&(!function(){var e;p||(g||(g=t.querySelector(v.contentElement)),g?(e=(S(g,":after").getPropertyValue("content")||"none").replace(w,""),p={},e&&(p[e]=1),(e=(S(g,":before").getPropertyValue("content")||"none").replace(w,""))&&(p[e]=1)):p={})}(),p[a.name]?i=!0:e.matchMedia&&"string"==typeof a.condition?i=(matchMedia(a.condition)||{}).matches:"function"==typeof a.condition&&(i=a.condition(n,a))),i}function s(e){var t,n,a=e.lazyInclude;if(a&&a.candidates)for(t=0;t<a.candidates.length&&(n=a.candidates[t],!r(e,n));t++);return n&&n!=a.current||(n=null),n}function o(e,n,a){if(h[e])a&&(!0===h[e]?setTimeout(a):h[e].push(a));else{var i=t.createElement(!0===n?"script":"link"),r=t.getElementsByTagName("script")[0];if(n?(i.src=e,i.async=!1):(i.rel="stylesheet",i.href=e),h[e]=[],h[i.href]=h[e],a){var s,o=function(t){if("readystatechange"!=t.type||m[t.target.readyState]){var n=h[e];for(i.removeEventListener("load",o),i.removeEventListener("error",o),i.removeEventListener("readystatechange",o),i.removeEventListener("loadcssdefined",o),s&&clearInterval(s),h[e]=!0,h[i.href]=!0;n.length;)n.shift()()}};h[i.href][0]=a,n||(s=setInterval(function(){(function(e){for(var n=!1,a=t.styleSheets,i=e.href,r=0,s=a.length;r<s;r++)if(a[r].href==i){n=!0;break}return n})(i)&&o({})},60)),i.addEventListener("load",o),i.addEventListener("error",o),i.addEventListener("readystatechange",o),i.addEventListener("loadcssdefined",o)}r.parentNode.insertBefore(i,r)}}function l(e){e&&"function"==typeof e.lazytransform&&e.lazytransform(this)}function d(e){e&&"function"==typeof e.lazyunload&&e.lazyunload(this)}function u(e){e&&"function"==typeof e.lazyload&&e.lazyload(this)}function c(e,t){var a,i,r,s,c,f=e.lazyInclude.current||null,y={candidate:t,openArgs:["GET",t.urls.include,!0],sendData:null,xhrModifier:null,content:t.content&&t.content.content||t.content,oldCandidate:f};if(n.fire(e,"lazyincludeload",y).defaultPrevented)T.d();else{if(c=function(){i&&r&&!s&&a()},a=function(){var a,s=i.status,o=i.content||i.responseText,c=!(null!=o||!f||!f.urls.include),y={candidate:t,content:o,text:i.responseText||i.content,response:i.response,xml:i.responseXML,isSuccess:!("status"in i)||(s>=200&&s<300||304===s),oldCandidate:f,insert:!0,resetHTML:c},v={target:e,details:y,detail:y};t.modules=r,f&&f.modules&&(f.modules.forEach(d,v),f.modules=null,y.resetHTML&&null==y.content&&t.initial&&t.initial.saved&&(y.content=t.initial.content)),r.forEach(l,v),a=n.fire(e,"lazyincludeloaded",y),y.insert&&y.isSuccess&&!a.defaultPrevented&&null!=y.content&&y.content!=e.innerHTML&&(e.innerHTML=y.content),T.d(),r.forEach(u,v),setTimeout(function(){n.fire(e,"lazyincluded",y)}),i=null,r=null},e.lazyInclude.current=t,e.setAttribute("data-currentrendert4s",t.name),t.urls.css&&(s=!0,function(e,t){var n=(e=e.split("|,|")).length-1;e.forEach(function(e,a){o(e,!1,a==n?t:null)})}(t.urls.css,function(){s=!1,c()})),null==y.content&&t.urls.include?function(e,t){var n=new t4sXMLHttpRequest;n.addEventListener("readystatechange",function(){var e=this.DONE||4;this.readyState===e&&(t(n),n=null)},!1),n.open.apply(n,e.openArgs),n.setRequestHeader("X-Requested-With","XMLHttpRequest"),e.xhrModifier&&e.xhrModifier(n,e.candidate),n.send(e.sendData)}(y,function(e){i=e,c()}):i=y,t.urls.amd||t.urls.module){var v=function(){r=Array.prototype.slice.call(arguments),c()};t.urls.amd?function(e,t){var a=(e=e.split("|,|")).length-1;n.cfg.requireJs?n.cfg.requireJs(e,t):e.forEach(function(e,n){o(e,n==a?t:null)})}(t.urls.amd,v):function(e,t){n.cfg.systemJs?n.cfg.systemJs(e,t):o(e,t)}(t.urls.module,v)}else r=[];c()}}function f(e){var t;if(function(e){var t,a=e.getAttribute("data-set4surl")||"";if(e.hasAttribute("data-qs-inl"))r=N+"products/"+(e.getAttribute("data-rendert4s")||"")+"/?section_id=qs_inline";else var r=(e.getAttribute("data-rendert4s")||"")+a;var s,o=e.lazyInclude;return o&&o.str==r&&!v.allowReload||(s={saved:!1,content:null},!(t=(o={str:r,candidates:(v.map[r]||r).split(z).map(i,s)}).candidates.length)||o.candidates[t-1].condition?(s.saved=!0,o.candidates.push({urls:{},condition:null,name:"initial",content:s})):s.saved&&1==o.candidates.length&&(s.saved=!1),o.initialContent=s,s.saved&&(s.content=e.innerHTML),e.lazyInclude=o,o.candidates.length>1?n.aC(e,"lazyconditionalinclude"):n.rC(e,"lazyconditionalinclude")),o}(e).candidates.length&&L.contains(e))return(t=s(e))&&c(e,t),!0}if(t.getElementsByClassName){var y,v,g,p,z=/\s*,+\s+/,m={complete:1,loaded:1},h={},b=/\s+/,A=/^(amd|css|module)\:(.+)/i,E=/(.+)\s+(\(\s*(.+)\s*\))/,w=/['"]/g,L=t.documentElement,C=t.getElementsByClassName("lazyconditionalinclude"),S=function(t,n){var a=t.ownerDocument.defaultView;return a.opener||(a=e),a.getComputedStyle(t,n||null)||{getPropertyValue:function(){},isNull:!0}},T=function(){var e=2,t=0,n=0,a=[],i=function(){var e,n=function(){a.length&&(t=0)};return function(){clearTimeout(e),e=setTimeout(n,999)}}();return{q:function(r){var s=null==r.getAttribute("data-lazyqueue");s&&(n++,e=3),t>e?a[s?"unshift":"push"](r):f(r)&&(t++,i())},d:function(){if(t&&t--,n>0&&(--n||(e=2)),!(t>e)){for(;a.length;)if(f(a.shift())){t++;break}i()}}}}(),_=function(){var e,t=function(){for(var e=0,t=C.length;e<t;e++)!n.hC(C[e],y.lazyClass)&&s(C[e])&&n.aC(C[e],y.lazyClass)};return function(n){clearTimeout(e),p=null,e=setTimeout(t,"resize"==n.type?31:0)}}();(y=n&&n.cfg).include||(y.include={}),(v=y.include).contentElement||(v.contentElement="html"),v.conditions||(v.conditions={}),v.map||(v.map={});var N=Shopify.routes.root;addEventListener("lazybeforeunveil",function(e){e.detail.instance==n&&!e.defaultPrevented&&e.target.getAttribute("data-rendert4s")&&(T.q(e.target),e.detail.firesLoad=!0)},!1),addEventListener("resize",_,!1),addEventListener("lazyrefreshincludes",_,!1)}}),function(e,t){var n;e&&(n=function(){t(e.lazySizesT4),e.removeEventListener("lazyunveilread",n,!0)},t=t.bind(null,e,e.document),"object"==typeof module&&module.exports?t(require("lazySizesT4")):"function"==typeof define&&define.amd?define(["lazySizesT4"],t):e.lazySizesT4?n():e.addEventListener("lazyunveilread",n,!0))}("undefined"!=typeof window?window:0,function(e,t,n){"use strict";var a=function(){function i(e){for(var t,a,i=0,r=e.length;i<r;i++)(a=(t=e[i]).target).getAttribute(t.attributeName)&&("source"==a.localName&&a.parentNode&&(a=a.parentNode.querySelector("img")),a&&v.test(a.className)&&function(e){n.rAF(function(){n.rC(e,f.loadedClass),f.unloadedClass&&n.rC(e,f.unloadedClass),n.aC(e,f.lazyClass),("none"==e.style.display||e.parentNode&&"none"==e.parentNode.style.display)&&setTimeout(function(){n.loader.unveil(e)},0)})}(a))}function r(){i(c),u=!(c=[])}var s,o,l,d,u,c,f=n.cfg,y={"data-bgset":1,"data-include":1,"data-poster":1,"data-bg":1,"data-script":1},v="(\\s|^)("+f.loadedClass,g=t.documentElement;f.unloadedClass&&(v+="|"+f.unloadedClass),v+="|"+f.loadingClass+")(\\s|$)",v=new RegExp(v),y[f.srcAttr]=1,y[f.srcsetAttr]=1,o=e.MutationObserver?(l=new MutationObserver(i),s=function(){d||(d=!0,l.observe(g,{subtree:!0,attributes:!0,attributeFilter:Object.keys(y)}))},function(){d&&(d=!1,l.disconnect())}):(g.addEventListener("DOMAttrModified",(c=[],function(e){d&&y[e.attrName]&&e.newValue&&(c.push({target:e.target,attributeName:e.attrName}),u||(setTimeout(r),u=!0))}),!0),s=function(){d=!0},function(){d=!1}),addEventListener("lazybeforeunveil",o,!0),addEventListener("lazybeforeunveil",s),addEventListener("lazybeforesizes",o,!0),addEventListener("lazybeforesizes",s),s(),removeEventListener("lazybeforeunveil",a)};addEventListener("lazybeforeunveil",a)}),function(e,t){var n;e&&(n=function(){t(e.lazySizesT4),e.removeEventListener("lazyunveilread",n,!0)},t=t.bind(null,e,e.document),"object"==typeof module&&module.exports?t(require("lazySizesT4")):"function"==typeof define&&define.amd?define(["lazySizesT4"],t):e.lazySizesT4?n():e.addEventListener("lazyunveilread",n,!0))}("undefined"!=typeof window?window:0,function(e,t,n){"use strict";var a,i,r,s,o,l;e.addEventListener&&(a=/\s+(\d+)(w|h)\s+(\d+)(w|h)/,i=/parent-fit["']*\s*:\s*["']*(contain|cover|width)/,r=/parent-container["']*\s*:\s*["']*(.+?)(?=(\s|$|,|'|"|;))/,s=/^picture$/i,o=n.cfg,l={getParent:function(t,n){var a=t,i=t.parentNode;return n&&"prev"!=n||!i||!s.test(i.nodeName||"")||(i=i.parentNode),"self"!=n&&(a="prev"==n?t.previousElementSibling:n&&(i.closest||e.jQuery)&&(i.closest?i.closest(n):jQuery(i).closest(n)[0])||i),a},getFit:function(e){var t,n,a=getComputedStyle(e,null)||{},s=a.content||a.fontFamily,o={fit:e._lazySizesT4ParentFit||e.getAttribute("data-parent-fit")};return!o.fit&&s&&(t=s.match(i))&&(o.fit=t[1]),o.fit?(!(n=e._lazySizesT4ParentContainer||e.getAttribute("data-parent-container"))&&s&&(t=s.match(r))&&(n=t[1]),o.parent=l.getParent(e,n)):o.fit=a.objectFit,o},getImageRatio:function(t){for(var n,i,r,l,d,u,c=t.parentNode,f=c&&s.test(c.nodeName||"")?c.querySelectorAll("source, img"):[t],y=0;y<f.length;y++)if(n=(t=f[y]).getAttribute(o.srcsetAttr)||t.getAttribute("srcset")||t.getAttribute("data-pfsrcset")||t.getAttribute("data-risrcset")||"",i=t._lsMedia||t.getAttribute("media"),i=o.customMedia[t.getAttribute("data-media")||i]||i,n&&(!i||(e.matchMedia&&matchMedia(i)||{}).matches)){(r=parseFloat(t.getAttribute("data-aspectratio")))||(u=(l=n.match(a))?"w"==l[2]?(d=l[1],l[3]):(d=l[3],l[1]):(d=t.getAttribute("width"),t.getAttribute("height")),r=d/u);break}return r},calculateSize:function(e,t){var n,a,i,r=this.getFit(e),s=r.fit,o=r.parent;return"width"==s||("contain"==s||"cover"==s)&&(a=this.getImageRatio(e))?(o?t=o.clientWidth:o=e,i=t,"width"==s?i=t:(n=t/o.clientHeight)&&("cover"==s&&n<a||"contain"==s&&a<n)&&(i=t*(a/n)),i):t}},n.parentFit=l,t.addEventListener("lazybeforesizes",function(e){var t;e.defaultPrevented||e.detail.instance!=n||(t=e.target,e.detail.width=l.calculateSize(t,e.detail.width))}))}),function(e,t){var n=function(){t(e.lazySizesT4),e.removeEventListener("lazyunveilread",n,!0)};t=t.bind(null,e,e.document),"object"==typeof module&&module.exports?t(require("lazySizesT4")):"function"==typeof define&&define.amd?define(["lazySizesT4"],t):e.lazySizesT4?n():e.addEventListener("lazyunveilread",n,!0)}(window,function(e,t,n){"use strict";function a(t,n,a){var i,r,s,l,d,f=e.getComputedStyle(t);if(a){d={};for(l in a)d[l]=a[l];a=d}else r=t.parentNode,a={isPicture:!(!r||!c.test(r.nodeName||""))};s=function(e,n){var i=t.getAttribute("data-"+e);if(!i){var r=f.getPropertyValue("--ls-"+e);r&&(i=r.trim())}if(i){if("true"==i)i=!0;else if("false"==i)i=!1;else if(u.test(i))i=parseFloat(i);else if("function"==typeof o[e])i=o[e](t,i);else if(g.test(i))try{i=JSON.parse(i)}catch(e){}a[e]=i}else e in o&&"function"!=typeof o[e]&&!a[e]?a[e]=o[e]:n&&"function"==typeof o[e]&&(a[e]=o[e](t,i))};for(i in o)s(i);return n.replace(v,function(e,t){t in a||s(t,!0)}),a}function i(e,n,a){var i=0,r=0,l=a;if(e){if("container"===n.ratio){for(i=l.scrollWidth,r=l.scrollHeight;!(i&&r||l===t);)i=(l=l.parentNode).scrollWidth,r=l.scrollHeight;i&&r&&(n.ratio=n.traditionalRatio?r/i:i/r)}(e=function(e,t){var n=[];return n.srcset=[],t.absUrl&&(z.setAttribute("href",e),e=z.href),e=((t.prefix||"")+e+(t.postfix||"")).replace(v,function(e,n){return d[typeof t[n]]?t[n]:e}),t.widths.forEach(function(a){var i=t.widthmap[a]||a,r=t.aspectratio||t.ratio,s=!t.aspectratio&&o.traditionalRatio,l={u:e.replace(f,i).replace(y,r?s?Math.round(a*r):Math.round(a/r):""),w:a};n.push(l),n.srcset.push(l.c=l.u+" "+a+"w")}),n}(e,n)).isPicture=n.isPicture,h&&"IMG"==a.nodeName.toUpperCase()?a.removeAttribute(s.srcsetAttr):a.setAttribute(s.srcsetAttr,e.srcset.join(", ")),Object.defineProperty(a,"_lazyrias",{value:e,writable:!0})}}function r(e){var t=e.getAttribute(e.getAttribute("data-srcattr")||o.srcAttr)||e.getAttribute(s.srcsetAttr)||e.getAttribute(s.srcAttr)||e.getAttribute("data-pfsrcset")||"";return"1"!=new URLSearchParams(t).get("width")?t.replace("_1x1.","_{width}x."):t.replace("width=1","width={width}")}var s,o,l=n.cfg,d={string:1,number:1},u=/^\-*\+*\d+\.*\d*$/,c=/^picture$/i,f=/\s*\{\s*width\s*\}\s*/i,y=/\s*\{\s*height\s*\}\s*/i,v=/\s*\{\s*([a-z0-9]+)\s*\}\s*/gi,g=/^\[.*\]|\{.*\}$/,p=/^(?:auto|\d+(px)?)$/,z=t.createElement("a"),m=t.createElement("img"),h="srcset"in m&&!("sizes"in m),b=!!e.HTMLPictureElement&&!h;!function(){var e,t={prefix:"",postfix:"",srcAttr:"data-src",absUrl:!1,modifyOptions:function(){},widthmap:{},ratio:!1,traditionalRatio:!1,aspectratio:!1};(s=n&&n.cfg).supportsType||(s.supportsType=function(e){return!e}),s.rias||(s.rias={}),"widths"in(o=s.rias)||(o.widths=[],function(e){for(var t,n=0;!t||t<3e3;)(n+=5)>30&&(n+=1),t=36*n,e.push(t)}(o.widths));for(e in t)e in o||(o[e]=t[e])}(),addEventListener("lazybeforesizes",function(e){if(e.detail.instance==n){var t,l,d,u,c,y,v,g,z,m,h,E,w;if(t=e.target,e.detail.dataAttr&&!e.defaultPrevented&&!o.disabled&&(z=t.getAttribute(s.sizesAttr)||t.getAttribute("sizes"))&&p.test(z)){if(l=r(t),d=function(e,t){var i=a(e,t);return o.modifyOptions.call(e,{target:e,details:i,detail:i}),n.fire(e,"lazyriasmodifyoptions",i),i}(t,l),h=f.test(d.prefix)||f.test(d.postfix),d.isPicture&&(u=t.parentNode))for(y=0,v=(c=u.getElementsByTagName("source")).length;y<v;y++)(h||f.test(g=r(c[y])))&&(i(g,a(c[y],g,d),c[y]),E=!0);h||f.test(l)?(i(l,d,t),E=!0):E&&((w=[]).srcset=[],w.isPicture=!0,Object.defineProperty(t,"_lazyrias",{value:w,writable:!0})),E&&(b?t.removeAttribute(s.srcAttr):"auto"!=z&&(m={width:parseInt(z,10)},A({target:t,detail:m})))}}},!0);var A=function(){var a=function(e,t){return e.w-t.w},i=function(e,t){var a;return!e._lazyrias&&n.pWS&&(a=n.pWS(e.getAttribute(s.srcsetAttr||""))).length&&(Object.defineProperty(e,"_lazyrias",{value:a,writable:!0}),t&&e.parentNode&&(a.isPicture="PICTURE"==e.parentNode.nodeName.toUpperCase())),e._lazyrias},r=function(o){if(o.detail.instance==n){var d,u=o.target;h||!(e.respimage||e.picturefill||l.pf)?("_lazyrias"in u||o.detail.dataAttr&&i(u,!0))&&(d=function(t,r){var s,o,l,d,u,c;if((u=t._lazyrias).isPicture&&e.matchMedia)for(o=0,l=(s=t.parentNode.getElementsByTagName("source")).length;o<l;o++)if(i(s[o])&&!s[o].getAttribute("type")&&(!(d=s[o].getAttribute("media"))||(matchMedia(d)||{}).matches)){u=s[o]._lazyrias;break}return(!u.w||u.w<r)&&(u.w=r,u.d=function(t){var a=e.devicePixelRatio||1,i=n.getX&&n.getX(t);return Math.min(i||a,2.4,a)}(t),c=function(e){var t,n,a=e.length,i=e[a-1],r=0;for(r;r<a;r++)if(i=e[r],i.d=i.w/e.w,i.d>=e.d){!i.cached&&(t=e[r-1])&&t.d>e.d-.13*Math.pow(e.d,2.2)&&(n=Math.pow(t.d-.6,1.6),t.cached&&(t.d+=.15*n),t.d+(i.d-e.d)*n>e.d&&(i=t));break}return i}(u.sort(a))),c}(u,o.detail.width))&&d.u&&u._lazyrias.cur!=d.u&&(u._lazyrias.cur=d.u,d.cached=!0,n.rAF(function(){u.setAttribute(s.srcAttr,d.u),u.setAttribute("src",d.u)})):t.removeEventListener("lazybeforesizes",r)}};return b?r=function(){}:addEventListener("lazybeforesizes",r),r}()}),function(e,t){var n=function(e,t,n){"use strict";var a,i;if(function(){var t,n={lazyClass:"lazyload",loadedClass:"lazyloaded",loadingClass:"lazyloading",preloadClass:"lazypreload",errorClass:"lazyerror",autosizesClass:"lazyautosizes",srcAttr:"data-src",srcsetAttr:"data-srcset",sizesAttr:"data-sizes",minSize:40,customMedia:{},init:!0,expFactor:1.5,hFac:.8,loadMode:2,loadHidden:!0,ricTimeout:0,throttleDelay:125};i=e.lazySizesT4Config||e.lazySizesT4Config||{};for(t in n)t in i||(i[t]=n[t])}(),!t||!t.getElementsByClassName)return{init:function(){},cfg:i,noSupport:!0};var r=t.documentElement,s=e.HTMLPictureElement,o=e.addEventListener.bind(e),l=e.setTimeout,d=e.requestAnimationFrame||l,u=e.requestIdleCallback,c=/^picture$/i,f=["load","error","lazyincluded","_lazyloaded"],y={},v=Array.prototype.forEach,g=function(e,t){return y[t]||(y[t]=new RegExp("(\\s|^)"+t+"(\\s|$)")),y[t].test(e.getAttribute("class")||"")&&y[t]},p=function(e,t){g(e,t)||e.setAttribute("class",(e.getAttribute("class")||"").trim()+" "+t)},z=function(e,t){var n;(n=g(e,t))&&e.setAttribute("class",(e.getAttribute("class")||"").replace(n," "))},m=function(e,t,n){var a=n?"addEventListener":"removeEventListener";n&&m(e,t),f.forEach(function(n){e[a](n,t)})},h=function(e,n,i,r,s){var o=t.createEvent("Event");return i||(i={}),i.instance=a,o.initEvent(n,!r,!s),o.detail=i,e.dispatchEvent(o),o},b=function(t,n){var a;!s&&(a=e.picturefill||i.pf)?(n&&n.src&&!t.getAttribute("srcset")&&t.setAttribute("srcset",n.src),a({reevaluate:!0,elements:[t]})):n&&n.src&&(t.src=n.src)},A=function(e,t){return(getComputedStyle(e,null)||{})[t]},E=function(e,t,n){for(n=n||e.offsetWidth;n<i.minSize&&t&&!e._lazySizesT4Width;)n=t.offsetWidth,t=t.parentNode;return n},w=function(){var e,n,a=[],i=[],r=a,s=function(){var t=r;for(r=a.length?i:a,e=!0,n=!1;t.length;)t.shift()();e=!1},o=function(a,i){e&&!i?a.apply(this,arguments):(r.push(a),n||(n=!0,(t.hidden?l:d)(s)))};return o._lsFlush=s,o}(),L=function(e,t){return t?function(){w(e)}:function(){var t=this,n=arguments;w(function(){e.apply(t,n)})}},C=function(e){var t,a,i=function(){t=null,e()},r=function(){var e=n.now()-a;e<99?l(r,99-e):(u||i)(i)};return function(){a=n.now(),t||(t=l(r,99))}},S=function(){var s,f,y,E,S,_,N,M,x,P,F,q,O=/^img$/i,R=/^iframe$/i,W="onscroll"in e&&!/(gle|ing)bot/.test(navigator.userAgent),k=0,j=0,H=-1,B=function(e){j--,(!e||j<0||!e.target)&&(j=0)},I=function(e){return null==q&&(q="hidden"==A(t.body,"visibility")),q||!("hidden"==A(e.parentNode,"visibility")&&"hidden"==A(e,"visibility"))},$=function(e,n){var a,i=e,s=I(e);for(M-=n,F+=n,x-=n,P+=n;s&&(i=i.offsetParent)&&i!=t.body&&i!=r;)(s=(A(i,"opacity")||1)>0)&&"visible"!=A(i,"overflow")&&(a=i.getBoundingClientRect(),s=P>a.left&&x<a.right&&F>a.top-1&&M<a.bottom+1);return s},D=function(){var e,n,o,l,d,u,c,y,v,g,p,z,m=a.elements;if((E=i.loadMode)&&j<8&&(e=m.length)){for(n=0,H++;n<e;n++)if(m[n]&&!m[n]._lazyRace)if(!W||a.prematureUnveil&&a.prematureUnveil(m[n]))K(m[n]);else if((y=m[n].getAttribute("data-expand"))&&(u=1*y)||(u=k),g||(g=!i.expand||i.expand<1?r.clientHeight>500&&r.clientWidth>500?500:370:i.expand,a._defEx=g,p=g*i.expFactor,z=i.hFac,q=null,k<p&&j<1&&H>2&&E>2&&!t.hidden?(k=p,H=0):k=E>1&&H>1&&j<6?g:0),v!==u&&(_=innerWidth+u*z,N=innerHeight+u,c=-1*u,v=u),o=m[n].getBoundingClientRect(),(F=o.bottom)>=c&&(M=o.top)<=N&&(P=o.right)>=c*z&&(x=o.left)<=_&&(F||P||x||M)&&(i.loadHidden||I(m[n]))&&(f&&j<3&&!y&&(E<3||H<4)||$(m[n],u))){if(K(m[n]),d=!0,j>9)break}else!d&&f&&!l&&j<4&&H<4&&E>2&&(s[0]||i.preloadAfterLoad)&&(s[0]||!y&&(F||P||x||M||"auto"!=m[n].getAttribute(i.sizesAttr)))&&(l=s[0]||m[n]);l&&!d&&K(l)}},J=function(e){var t,a=0,r=i.throttleDelay,s=i.ricTimeout,o=function(){t=!1,a=n.now(),e()},d=u&&s>49?function(){u(o,{timeout:s}),s!==i.ricTimeout&&(s=i.ricTimeout)}:L(function(){l(o)},!0);return function(e){var i;(e=!0===e)&&(s=33),t||(t=!0,(i=r-(n.now()-a))<0&&(i=0),e||i<9?d():l(d,i))}}(D),U=function(e){var t=e.target;t._lazyCache?delete t._lazyCache:(B(e),p(t,i.loadedClass),z(t,i.loadingClass),m(t,X),h(t,"lazyloaded"))},V=L(U),X=function(e){V({target:e.target})},G=function(e){var t,n=e.getAttribute(i.srcsetAttr);(t=i.customMedia[e.getAttribute("data-media")||e.getAttribute("media")])&&e.setAttribute("media",t),n&&e.setAttribute("srcset",n)},Q=L(function(e,t,n,a,r){var s,o,d,u,f,g;(f=h(e,"lazybeforeunveil",t)).defaultPrevented||(a&&(n?p(e,i.autosizesClass):e.setAttribute("sizes",a)),o=e.getAttribute(i.srcsetAttr),s=e.getAttribute(i.srcAttr),r&&(d=e.parentNode,u=d&&c.test(d.nodeName||"")),g=t.firesLoad||"src"in e&&(o||s||u),f={target:e},p(e,i.loadingClass),g&&(clearTimeout(y),y=l(B,2500),m(e,X,!0)),u&&v.call(d.getElementsByTagName("source"),G),o?e.setAttribute("srcset",o):s&&!u&&(R.test(e.nodeName)?function(e,t){try{e.contentWindow.location.replace(t)}catch(n){e.src=t}}(e,s):e.src=s),r&&(o||u)&&b(e,{src:s})),e._lazyRace&&delete e._lazyRace,z(e,i.lazyClass),w(function(){var t=e.complete&&e.naturalWidth>1;g&&!t||(t&&p(e,"ls-is-cached"),U(f),e._lazyCache=!0,l(function(){"_lazyCache"in e&&delete e._lazyCache},9)),"lazy"==e.loading&&j--},!0)}),K=function(e){if(!e._lazyRace){var t,n=O.test(e.nodeName),a=n&&(e.getAttribute(i.sizesAttr)||e.getAttribute("sizes")),r="auto"==a;(!r&&f||!n||!e.getAttribute("src")&&!e.srcset||e.complete||g(e,i.errorClass)||!g(e,i.lazyClass))&&(t=h(e,"lazyunveilread").detail,r&&T.updateElem(e,!0,e.offsetWidth),e._lazyRace=!0,j++,Q(e,t,r,a,n))}},Y=C(function(){i.loadMode=3,J()}),Z=function(){3==i.loadMode&&(i.loadMode=2),Y()},ee=function(){f||(n.now()-S<999?l(ee,999):(f=!0,i.loadMode=3,J(),o("scroll",Z,!0)))};return{_:function(){S=n.now(),a.elements=t.getElementsByClassName(i.lazyClass),s=t.getElementsByClassName(i.lazyClass+" "+i.preloadClass),o("scroll",J,!0),o("resize",J,!0),o("pageshow",function(e){if(e.persisted){var n=t.querySelectorAll("."+i.loadingClass);n.length&&n.forEach&&d(function(){n.forEach(function(e){e.complete&&K(e)})})}}),e.MutationObserver?new MutationObserver(J).observe(r,{childList:!0,subtree:!0,attributes:!0}):(r.addEventListener("DOMNodeInserted",J,!0),r.addEventListener("DOMAttrModified",J,!0),setInterval(J,999)),o("hashchange",J,!0),["focus","mouseover","click","load","transitionend","animationend"].forEach(function(e){t.addEventListener(e,J,!0)}),/d$|^c/.test(t.readyState)?ee():(o("load",ee),t.addEventListener("DOMContentLoaded",J),l(ee,2e4)),a.elements.length?(D(),w._lsFlush()):J()},checkElems:J,unveil:K,_aLSL:Z}}(),T=function(){var e,n=L(function(e,t,n,a){var i,r,s;if(a*=parseFloat(e.getAttribute("data-sizes-scale")||1),e._lazySizesT4Width=a,a+="px",e.setAttribute("sizes",a),c.test(t.nodeName||""))for(i=t.getElementsByTagName("source"),r=0,s=i.length;r<s;r++)i[r].setAttribute("sizes",a);n.detail.dataAttr||b(e,n.detail)}),a=function(e,t,a){var i,r=e.parentNode;r&&(a=E(e,r,a),(i=h(e,"lazybeforesizes",{width:a,dataAttr:!!t})).defaultPrevented||(a=i.detail.width)&&a!==e._lazySizesT4Width&&n(e,r,i,a))},r=C(function(){var t,n=e.length;if(n)for(t=0;t<n;t++)a(e[t])});return{_:function(){e=t.getElementsByClassName(i.autosizesClass),o("resize",r)},checkElems:r,updateElem:a}}(),_=function(){!_.i&&t.getElementsByClassName&&(_.i=!0,T._(),S._())};return l(function(){i.init&&_()}),a={cfg:i,autoSizer:T,loader:S,init:_,uP:b,aC:p,rC:z,hC:g,fire:h,gW:E,rAF:w}}(e,e.document,Date);e.lazySizesT4=n,"object"==typeof module&&module.exports&&(module.exports=n)}("undefined"!=typeof window?window:{}),document.addEventListener("lazyincludeloaded",function(e){if(e.detail.content&&e.detail.content.indexOf("[t4splitlz]")>-1){var t=e.detail.content.split("[t4splitlz]")[1];e.detail.content=t}}),document.dispatchEvent(new CustomEvent("lazysizet4s:loaded")),document.addEventListener("lazyloaded",function(e){let t=e.target.parentNode;t&&t.classList.contains("t4s-bg-11")&&t.classList.add("t4s-child-lazyloaded")});