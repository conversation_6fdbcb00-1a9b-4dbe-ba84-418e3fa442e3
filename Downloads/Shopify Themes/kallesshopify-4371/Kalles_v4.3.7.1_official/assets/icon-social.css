:root {
    --facebook-cl: rgb(59, 89, 152);
    --twitter-cl: rgb(29, 161, 242);
    --instagram-cl: rgb(224, 53, 102);
    --dribbble-cl: rgb(234, 76, 137);
    --linkedin-cl: rgb(0, 119, 181);
    --pinterest-cl: rgb(203, 32, 39);
    --tumblr-cl: rgb(55, 69, 92);
    --snapchat-cl: rgb(255, 221, 0);
    --youtube-cl: rgb(205, 32, 31);
    --vimeo-cl: rgb(26, 183, 234);
    --behance-cl: rgb(23, 106, 255);
    --soundcloud-cl: rgb(255, 119, 0);
    --tiktok-cl: #FE2C55;
    --email-cl: rgb(219, 68, 55);
    --telegram-cl: rgb(0, 136, 204);
}
.t4s-socials> a {
	text-align: center;
}
.t4s-socials a:last-child {
	margin-right: 0;
}
.t4s-socials a {
	display: inline-block;
	vertical-align: top;
	transition: all 0.5s ease;
	display: flex !important;
	justify-content: center;
	align-items: center;
	position: relative;
	overflow: hidden;
	color: var(--text-color);
	background-color: transparent;
	border-color: var(--text-color);
	border-radius: var(--bd-radius)
}
.t4s-socials.t4s-setts-color-true a {
	color: var(--cl);
	background-color: var(--bg-cl);
    border-color: var(--cl);
}
.t4s-socials a svg {
	fill: currentColor !important;
	display: inline-block;
	vertical-align: middle;
	border: none;
}
.t4s-socials a svg {
	height: 18px;
	width: 18px;
}
.t4s-socials.t4s-socials-style-1 a {
	width: auto;
	height: auto;
	background-color: transparent !important; 
}
.t4s-socials-size-extra-small.t4s-socials-style-1 a,
.t4s-socials-size-small.t4s-socials-style-1 a {
	min-height: 24px;
}
.t4s-socials-size-medium.t4s-socials-style-1 a {
	min-height: 30px;
}
.t4s-socials-size-large.t4s-socials-style-1 a {
	min-height: 35px;
}

.t4s-socials-style-2 a,
.t4s-footer .t4s-socials-style-2 a {
	color: var(--t4s-light-color);
	background-color: var(--text-color);
}
.t4s-socials-style-2.t4s-setts-color-true a {
	color: var(--bg-cl);
	background-color: var(--cl);
}

.t4s-socials-style-3 a {
	border:solid 1px var(--text-color);
	color: var(--text-color);
	background-color: transparent !important;
}
.t4s-socials-style-3.t4s-setts-color-true a {
	color: var(--cl);
	border-color: var(--cl);
}

.t4s-socials-style-4 a,
.t4s-footer .t4s-socials-style-4 a {
	border:solid 1px;
	color: var(--t4s-light-color);
	border-color: var(--border-color);
	background-color: var(--text-color);
}
.t4s-socials-style-4.t4s-setts-color-true a {
	background-color: var(--cl);
	border-color: var(--bg-cl);
	color: var(--bg-cl);
}
.t4s-socials.t4s-socials-size-extra-small:not(.t4s-socials-style-1) a {
	width: 28px;
	height: 28px;
}
.t4s-socials.t4s-socials-size-extra-small a svg {
	height: 14px;
	width: 14px;
}
.t4s-socials:not(.t4s-socials-style-1) a {
	width: 34px;
	height: 34px;
}
.t4s-socials.t4s-socials-size-medium:not(.t4s-socials-style-1) a {
	width: 40px;
	height: 40px;
	line-height: 40px;
  display: flex;
}
.t4s-socials.t4s-socials-size-medium a svg {
	height: 20px;
	width: 20px;
}
.t4s-socials.t4s-socials-size-large:not(.t4s-socials-style-1) a {
	width: 50px;
	height: 50px;
	line-height: 50px;
}
.t4s-socials.t4s-socials-size-large a svg {
	height: 22px;
	width: 22px;
}
.t4s-text-start .t4s-socials {
	justify-content: flex-start;
}
.t4s-text-center .t4s-socials {
	justify-content: center;
}
.t4s-text-end .t4s-socials {
	justify-content: flex-end;
}
@media(min-width: 768px) {
	.t4s-text-md-start .t4s-socials {
		justify-content: flex-start;
	}
	.t4s-text-md-center .t4s-socials {
		justify-content: center;
	}
	.t4s-text-md-end .t4s-socials {
		justify-content: flex-end;
	}
}
@media(min-width: 1025px) {
	.t4s-text-lg-start .t4s-socials {
		justify-content: flex-start;
	}
	.t4s-text-lg-center .t4s-socials {
		justify-content: center;
	}
	.t4s-text-lg-end .t4s-socials {
		justify-content: flex-end;
	}
}
@media (-moz-touch-enabled: 0), (hover: hover) and (min-width: 1025px){
	.t4s-socials.t4s-setts-color-true a:hover {
		color: var(--bg-cl) !important;
	}
	.t4s-socials a:hover {
		transform: translateY(-5px);
		background-color: transparent;
	}
	.t4s-socials-style-1 a:hover {
		background-color: transparent;
	}
	.t4s-socials.t4s-socials-style-1 a.facebook:hover {color: var(--facebook-cl);}
	.t4s-socials.t4s-socials-style-1 a.twitter:hover {color: var(--twitter-cl);}
	.t4s-socials.t4s-socials-style-1 a.instagram:hover {color: var(--instagram-cl);}
	.t4s-socials.t4s-socials-style-1 a.dribbble:hover {color: var(--dribbble-cl);}
	.t4s-socials.t4s-socials-style-1 a.linkedin:hover {color: var(--linkedin-cl);}
	.t4s-socials.t4s-socials-style-1 a.pinterest:hover {color: var(--pinterest-cl);}
	.t4s-socials.t4s-socials-style-1 a.tumblr:hover {color: var(--tumblr-cl);}
	.t4s-socials.t4s-socials-style-1 a.snapchat:hover {color: var(--snapchat-cl);}
	.t4s-socials.t4s-socials-style-1 a.youtube:hover {color: var(--youtube-cl);}
	.t4s-socials.t4s-socials-style-1 a.vimeo:hover {color: var(--vimeo-cl);}
	.t4s-socials.t4s-socials-style-1 a.behance:hover {color: var(--behance-cl);}
	.t4s-socials.t4s-socials-style-1 a.soundcloud:hover {color: var(--soundcloud-cl);}
	.t4s-socials.t4s-socials-style-1 a.tiktok:hover {color: var(--tiktok-cl);}
	.t4s-socials.t4s-socials-style-1 a.email:hover {color: var(--email-cl);}
	.t4s-socials.t4s-socials-style-1 a.telegram:hover {color: var(--telegram-cl);}

	.t4s-socials-style-2 a:hover {
		color: var(--t4s-light-color);
	}
	.t4s-socials-style-2.t4s-setts-color-true a:hover {
		color: var(--cl) !important; 
		background-color: var(--bg-cl) !important; 
		background: var(--bg-cl) !important; 
	}
	.t4s-socials.t4s-socials-style-2 a.facebook:hover {background-color: var(--facebook-cl);}
	.t4s-socials.t4s-socials-style-2 a.twitter:hover {background-color: var(--twitter-cl);}
	.t4s-socials.t4s-socials-style-2 a.instagram:hover {background: linear-gradient(#8a3ab9,#e95950,#fccc63);}
	.t4s-socials.t4s-socials-style-2 a.dribbble:hover {background-color: var(--dribbble-cl);}
	.t4s-socials.t4s-socials-style-2 a.linkedin:hover {background-color: var(--linkedin-cl);}
	.t4s-socials.t4s-socials-style-2 a.pinterest:hover {background-color: var(--pinterest-cl);}
	.t4s-socials.t4s-socials-style-2 a.tumblr:hover {background-color: var(--tumblr-cl);}
	.t4s-socials.t4s-socials-style-2 a.snapchat:hover {background-color: var(--snapchat-cl);}
	.t4s-socials.t4s-socials-style-2 a.youtube:hover {background-color: var(--youtube-cl);}
	.t4s-socials.t4s-socials-style-2 a.vimeo:hover {background-color: var(--vimeo-cl);}
	.t4s-socials.t4s-socials-style-2 a.behance:hover {background-color: var(--behance-cl);}
	.t4s-socials.t4s-socials-style-2 a.soundcloud:hover {background-color: var(--soundcloud-cl);}
	.t4s-socials.t4s-socials-style-2 a.tiktok:hover {background: linear-gradient(#25F4EE,#000000,#FE2C55);}
	.t4s-socials.t4s-socials-style-2 a.email:hover {background: linear-gradient(rgb(219, 68, 55),#EFA134,#E4D81B,#2DBF19);}
	.t4s-socials.t4s-socials-style-2 a.telegram:hover {background-color: var(--telegram-cl);}

	.t4s-socials-style-3.t4s-setts-color-true a:hover {
		color: var(--bg-cl) !important;
		border-color: var(--bg-cl) !important;
	}
	.t4s-socials.t4s-socials-style-3 a.facebook:hover {color: var(--facebook-cl);border-color: var(--facebook-cl);}
	.t4s-socials.t4s-socials-style-3 a.twitter:hover {color: var(--twitter-cl);border-color: var(--twitter-cl);}
	.t4s-socials.t4s-socials-style-3 a.instagram:hover {color: var(--instagram-cl);border-color: var(--instagram-cl);}
	.t4s-socials.t4s-socials-style-3 a.dribbble:hover {color: var(--dribbble-cl);border-color: var(--dribbble-cl);}
	.t4s-socials.t4s-socials-style-3 a.linkedin:hover {color: var(--linkedin-cl);border-color: var(--linkedin-cl);}
	.t4s-socials.t4s-socials-style-3 a.pinterest:hover {color: var(--pinterest-cl);border-color: var(--pinterest-cl);}
	.t4s-socials.t4s-socials-style-3 a.tumblr:hover {color: var(--tumblr-cl);border-color: var(--tumblr-cl);}
	.t4s-socials.t4s-socials-style-3 a.snapchat:hover {color: var(--snapchat-cl);border-color: var(--snapchat-cl);}
	.t4s-socials.t4s-socials-style-3 a.youtube:hover {color: var(--youtube-cl);border-color: var(--youtube-cl);}
	.t4s-socials.t4s-socials-style-3 a.vimeo:hover {color: var(--vimeo-cl);border-color: var(--vimeo-cl);}
	.t4s-socials.t4s-socials-style-3 a.behance:hover {color: var(--behance-cl);border-color: var(--behance-cl);}
	.t4s-socials.t4s-socials-style-3 a.soundcloud:hover {color: var(--soundcloud-cl);border-color: var(--soundcloud-cl);}
	.t4s-socials.t4s-socials-style-3 a.tiktok:hover {color: var(--tiktok-cl);border-color: var(--tiktok-cl);}
	.t4s-socials.t4s-socials-style-3 a.email:hover {color: var(--email-cl);border-color: var(--email-cl);}
	.t4s-socials.t4s-socials-style-3 a.telegram:hover {color: var(--telegram-cl);border-color: var(--telegram-cl);}

	.t4s-socials.t4s-socials-style-4 a:hover {
		color: var(--t4s-light-color);
	}

	.t4s-socials-style-4.t4s-setts-color-true a:hover {
		background-color: var(--bg-cl) !important;
		background: var(--bg-cl) !important;
		border-color: var(--cl) !important;
		color: var(--cl) !important;
	}
	.t4s-socials.t4s-socials-style-4 a.facebook:hover {border-color: var(--facebook-cl);background-color: var(--facebook-cl);}
	.t4s-socials.t4s-socials-style-4 a.twitter:hover {border-color: var(--twitter-cl);background-color: var(--twitter-cl);}
	.t4s-socials.t4s-socials-style-4 a.instagram:hover {border-color: var(--instagram-cl);background: linear-gradient(#8a3ab9,#e95950,#fccc63);}
	.t4s-socials.t4s-socials-style-4 a.dribbble:hover {border-color: var(--dribbble-cl);background-color: var(--dribbble-cl);}
	.t4s-socials.t4s-socials-style-4 a.linkedin:hover {border-color: var(--linkedin-cl);background-color: var(--linkedin-cl);}
	.t4s-socials.t4s-socials-style-4 a.pinterest:hover {border-color: var(--pinterest-cl);background-color: var(--pinterest-cl);}
	.t4s-socials.t4s-socials-style-4 a.tumblr:hover {border-color: var(--tumblr-cl);background-color: var(--tumblr-cl);}
	.t4s-socials.t4s-socials-style-4 a.snapchat:hover {border-color: var(--snapchat-cl);background-color: var(--snapchat-cl);}
	.t4s-socials.t4s-socials-style-4 a.youtube:hover {border-color: var(--youtube-cl);background-color: var(--youtube-cl);}
	.t4s-socials.t4s-socials-style-4 a.vimeo:hover {border-color: var(--vimeo-cl);background-color: var(--vimeo-cl);}
	.t4s-socials.t4s-socials-style-4 a.behance:hover {border-color: var(--behance-cl);background-color: var(--behance-cl);}
	.t4s-socials.t4s-socials-style-4 a.soundcloud:hover {border-color: var(--soundcloud-cl);background-color: var(--soundcloud-cl);}
	.t4s-socials.t4s-socials-style-4 a.tiktok:hover {border-color: var(--tiktok-cl);background: linear-gradient(#25F4EE,#000000,#FE2C55);}
	.t4s-socials.t4s-socials-style-4 a.email:hover {border-color: var(--email-cl);background: linear-gradient(rgb(219, 68, 55),#EFA134,#E4D81B,#2DBF19);}
	.t4s-socials.t4s-socials-style-4 a.telegram:hover {border-color: var(--telegram-cl);background-color: var(--telegram-cl);}
}
