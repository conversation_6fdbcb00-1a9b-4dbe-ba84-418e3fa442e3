.t4s-collection-item {
    margin-bottom: var(--mgb);
}
.t4s-collection-item:not(.t4s-coll-style-5):not(.t4s-coll-style-7):not(.t4s-coll-style-11):not(.t4s-coll-style-12):not(.t4s-coll-style-14):not(.t4s-coll-style-16) .t4s-cat-content {
   border-radius: var(--item-rd); 
}
.t4s-collection-item .t4s_ratio {
    --aspect-ratioapt: 4/3;
    overflow: hidden;
}
.t4s-collection-item .t4s_ratio {
    border-radius: var(--item-rd);
    overflow: hidden;
}
.t4s-collection-item .t4s-eff-border-run::before, 
.t4s-collection-item .t4s-eff-border-run::after,
.t4s-collection-item .t4s_ratio > svg,
.t4s-collection-item .t4s_ratio > img {
    border-radius: var(--item-rd);
}
.t4s-collection-item .t4s-source-icon .t4s-coll-img {
    display: inline-block;
    vertical-align: top;
}
.t4s-collection-item .t4s-source-icon .t4s-coll-icon {
    min-width: var(--icon-width,86px);
    min-height: var(--icon-width,86px);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-size: 40px;
    color: var(--border-cl);
}
.t4s-collection-item .t4s-source-icon .t4s-obj-eff {
    transition: 0.5s ease 0s;
}

.t4s-collection-item .t4s-cat-subtitle span.t4s-text {
    margin: 0 3px;
}
.t4s-collection-item .t4s-cate-wrapper {
    position: absolute; 
    z-index: 3;
    left: 50%;
    min-width: 150px;
    height: 44px;
    padding: 0;
    text-align: center;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    bottom: var(--space-bottom);
}
.t4s-collection-border-true .t4s-collection-item .t4s-cate-wrapper  {
    bottom: calc(var(--space-bottom) + 1px);
}
.t4s-coll-style-1 .t4s-cate-wrapper {
    box-shadow: 1px 1px 0 0 rgb(0 0 0 / 10%);
}
.t4s-collection-item .t4s-cat-title,
.t4s-collection-item .t4s-cat-subtitle {
    min-height: 24px;
    line-height: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.5s ease;
    font-size: 14px;
    font-weight: 600;
}
.t4s-collection-item .t4s-cat-title { 
    position: relative;
    z-index: 2;
    color: var(--color);
    background-color: var(--bg-color);
}
.t4s-collection-item .t4s-cat-subtitle {
    color: var(--subtitle-cl);
}
.t4s-collection-item .t4s-cat-subtitle {
    display: none;
}
.t4s-coll-style-1 .t4s-cat-title,
.t4s-coll-style-6 .t4s-cat-title,
.t4s-coll-style-9 .t4s-cat-title {
    width: 100%;
    height: 100%;
    padding: 5px 20px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}
.t4s-coll-style-2 .t4s-cate-wrapper, 
.t4s-coll-style-3 .t4s-cate-wrapper {
    position: absolute;
    text-align: center;
    top: 50%;
    left: 15px;
    right: 15px;
    bottom: auto !important;
    transform: translateY(-50%); 
    text-shadow: 0 0 4px rgb(0,0,0,0.4);
    hyphens: auto;
    z-index: 10;
    transition: .3s;
    box-shadow: none;
    min-height: 1px;
    height: auto;
    display: block;
}
.t4s-coll-style-2 .t4s-cat-title {
    width: auto;
    display: inline-flex;
    vertical-align: top;
    padding: 0;
}
.t4s-coll-style-3 .t4s-cat-title {
    transform: translateY(-15px);
    font-size: 23px;
    display: flex;
}
.t4s-coll-style-3 .t4s-cat-subtitle {
    opacity: 0;
    visibility: hidden;
    transform: translateY(15px);
    position: absolute;
    z-index: 1;
    display: inline-block;
    vertical-align: top;
    left: 0;
    right: 0;
    font-weight: 400;
}
.t4s-coll-style-4 .t4s-cat-title,
.t4s-coll-style-4 .t4s-cat-subtitle {
    width: 100%;
    height: 100%;
    padding: 5px 20px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}
.t4s-coll-style-4 .t4s-cat-subtitle {
    opacity: 0;
    visibility: hidden;
    transform: translateY(0);
    position: absolute;
    z-index: 3;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    margin: auto;
    color: var(--subtitle-cl2);
    background-color: var(--subtitle-cl);
}
.t4s-coll-style-5 .t4s-cate-wrapper  {
    position: static;
    bottom: 0;
    left: 0;
    right: 0;
    display: block;
    box-shadow: none;
    transform: translate(0);
    padding: var(--space-bottom) 0 0;
    height: auto;
}
.t4s-coll-style-5 .t4s-cat-title {
    font-size: 18px;
    font-weight: 500;
    font-family: var(--font-heading-family);
}
.t4s-coll-style-5 .t4s-cat-subtitle {
    display: flex;
    font-weight: 400;
    font-family: var(--font-family-2);
}
.t4s-coll-style-7 .t4s-cate-wrapper {
    position: static;
    bottom: 0;
    left: 0;
    right: 0;
    display: block;
    box-shadow: none;
    transform: translate(0);
}
.t4s-collection-border-true .t4s-coll-style-7 .t4s-cate-wrapper {
    margin-top: -1px;
    border: solid 1px var(--border-cl);
}
.t4s-coll-style-7 .t4s-cat-title {
    height: 100%;
}
.t4s-coll-style-8 .t4s-cate-wrapper {
    display: none;
}
.t4s-coll-style-9 .t4s-cate-wrapper {
    width: calc(100% - 30px);
    left: 15px;
    right: 15px;
    transform: none;
}
.t4s-coll-style-9 .t4s-cat-title {
    font-size: 17px;
}
.t4s-coll-style-10 span.t4s-count {
    margin-right: 5px;
}
.t4s-coll-style-10 .t4s-cate-wrapper {
    width: 100%;
    left: 0px;
    right: 0px;
    transform: none;
    flex-direction: column;
    font-weight: 400;
    box-shadow: 0 0 0;
    padding: 10px;
    background-color: var(--bg-color);
    height: auto;
    min-height: 44px;
}

.t4s-collection-border-true .t4s-coll-style-10 .t4s-cate-wrapper {
    width: calc(100% - 2px);
    left: 1px;
    right: 1px;
}
.t4s-coll-style-10 .t4s-cat-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--color);
    width: 100%;
    background-color: transparent;
}
.t4s-coll-style-10 .t4s-coll-img {
    padding-bottom: 30px;
}
.t4s-coll-style-10 .t4s-cat-subtitle {
    font-weight: 400;
    width: 100%;
    display: flex;
}
.t4s-coll-style-11 .t4s-cate-wrapper,
.t4s-coll-style-12 .t4s-cate-wrapper,
.t4s-coll-style-14 .t4s-cate-wrapper {
    position: static;
    -webkit-transform: none;
    transform: none;
    min-width: 1px;
    box-shadow: none;
    padding: 0;
    min-height: 1px;
    height: auto;
    font-weight: 400;
    padding: var(--space-bottom) 0 0;
}
.t4s-collection-border-true .t4s-coll-style-11 .t4s-img-wrap {
    border-width: 1.5px;
}
.t4s-coll-style-11 .t4s-cat-title,
.t4s-coll-style-12 .t4s-cat-title {
    line-height: 21px;
}
.t4s-coll-style-13 .t4s-cate-wrapper {
    height: 25px;
    min-width: 90px;
}
.t4s-coll-style-13 .t4s-cat-title {
    font-size: 12px;
    line-height: 18px;
    height: 100%;
    width: 100%;
    padding: 0 20px;
}
.t4s-collection-item.t4s-coll-style-14 {
    margin-bottom: 0;
}
.t4s-collection-item.t4s-coll-style-14 .t4s-cat-title {
    font-weight: 500;
    font-size: 18px;
    margin-top: -5px;
    display: block;
    line-height: 24px;
}
.t4s-collection-item.t4s-coll-style-14 .t4s-cat-title .t4s-count {
    display: inline-block !important;
    background-color: transparent;
    color: var(--count-cl-pri);
    vertical-align: middle;
    position: static;
    font-weight: 400;
    font-size: 18px;
    padding: 0;
}
.t4s-collection-item {
    --color: var(--title-cl-second);
    --bg-color: var(--title-cl-pri);
    --color-hover: var(--title-cl-second-hover);
    --bg-color-hover: var(--title-cl-pri-hover);
}
.t4s-coll-style-2,
.t4s-coll-style-3,
.t4s-coll-style-5,
.t4s-coll-style-11,
.t4s-coll-style-12,
.t4s-coll-style-14 {
    --color: var(--title-cl-pri);
    --bg-color: tranparent;
    --color-hover: var(--title-cl-pri-hover);
    --bg-color-hover: tranparent;
}
a.t4s-loadpreview {
  margin-bottom: 50px; 
}
.t4s-coll-style-15 .t4s-cate-wrapper {
    height: auto;
    min-width: 1px;
}
.t4s-coll-style-15 .t4s-cat-title {
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    min-height: 1px;
    color: var(--title-cl-pri);
    background-color: transparent;
}
.t4s-coll-style-15:hover {
  --border-cl: var(--title-cl-pri-hover);
}
.t4s-coll-style-15:hover .t4s-cat-content {
  background-color: var(--title-cl-pri-hover);
  color: var(--title-cl-second-hover);
}
.t4s-coll-style-15:hover .t4s-cat-title,
.t4s-coll-style-15:hover .t4s-cat-title:hover,
.t4s-coll-style-15 .t4s-cat-title:hover {
  color: var(--title-cl-second-hover);
}
.t4s-coll-style-15:hover .t4s-coll-img {
  filter: brightness(2);
}

.t4s-collection-border-true.t4s-has-collection16 .t4s-collection-item .t4s-img-wrap::before {
    display: none;
}
.t4s-collection-border-true.t4s-has-collection16 .t4s-collection-item {
    position: relative;
}
.t4s-collection-border-true.t4s-has-collection16 .t4s-collection-item::before {
    content: "";
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    border: solid 1px var(--border-cl);
    border-radius: var(--item-rd);
    pointer-events: none;
}
.t4s-coll-style-16 {
    --color: var(--title-cl-pri);
    --bg-color: transparent;
    --color-hover: var(--title-cl-pri-hover);
    --bg-color-hover: transparent;
}
.t4s-coll-style-16 .t4s-cat-content {
    padding: 20px;
}
.t4s-coll-style-16 .t4s-cate-wrapper {
    height: auto;
    min-width: 1px;
    position: static;
    transform: none;
}
.t4s-coll-style-16 .t4s-cat-title {
    font-weight: 500;
    font-size: 15px;
    line-height: 22.5px;
    min-height: 1px;
    color: var(--title-cl-pri);
    background-color: transparent;
    margin-bottom: 20px;
}
.t4s-coll-style-16 .t4s-cat-title:hover {
  color: var(--title-cl-pri-hover);
  background-color: transparent;
}
.t4s-has-collection16 .t4s-coll-style-16 .t4s-img-wrap {
    max-width: 100px;
    margin: 20px auto 10px;
}

@media(max-width: 1199px) and (min-width: 1025px) {
    .t4s-coll-layout-2.has-3-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-3.has-3-item .coll-item-3 .t4s_ratio {
        --aspect-ratioapt: 570/635 !important;
    }
    .t4s-coll-layout-4.has-3-item .coll-item-2 .t4s_ratio,
    .t4s-coll-layout-4.has-3-item .coll-item-3 .t4s_ratio,
    .t4s-coll-layout-5.has-3-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-5.has-3-item .coll-item-3 .t4s_ratio,
    .t4s-coll-layout-6.has-3-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-6.has-3-item .coll-item-2 .t4s_ratio,
    .t4s-coll-layout-4.has-4-item .coll-item-4 .t4s_ratio  {
        --aspect-ratioapt: 270/636 !important;
    }
    .t4s-coll-layout-3.has-4-item .coll-item-1 .t4s_ratio {
        --aspect-ratioapt: 570/633 !important;
    }
    .t4s-coll-layout-5.has-4-item .coll-item-4 .t4s_ratio {
        --aspect-ratioapt: 570/670 !important;
    }
    .t4s-coll-layout-6.has-4-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-6.has-4-item .coll-item-4 .t4s_ratio {
        --aspect-ratioapt: 400/640 !important;
    }
    .t4s-coll-layout-1.has-5-item .coll-item-4 .t4s_ratio,
    .t4s-coll-layout-1.has-5-item .coll-item-5 .t4s_ratio {
        --aspect-ratioapt: 570/318 !important;
    }
    .t4s-coll-layout-2.has-5-item .coll-item-3 .t4s_ratio {
        --aspect-ratioapt: 400/639 !important;   
    }
    .t4s-coll-layout-5.has-5-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-5.has-5-item .coll-item-5 .t4s_ratio {
        --aspect-ratioapt: 400/677 !important;   
    }
    .t4s-coll-layout-5.has-6-item .coll-item-1 .t4s_ratio {
        --aspect-ratioapt: 400/679 !important; 
    }
    .t4s-coll-layout-5.has-6-item .coll-item-2 .t4s_ratio,
    .t4s-coll-layout-5.has-6-item .coll-item-3 .t4s_ratio {
        --aspect-ratioapt: 400/320 !important;   
    }
    .t4s-coll-layout-1.has-7-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-1.has-7-item .coll-item-2 .t4s_ratio,
    .t4s-coll-layout-1.has-7-item .coll-item-6 .t4s_ratio,
    .t4s-coll-layout-1.has-7-item .coll-item-7 .t4s_ratio {
        --aspect-ratioapt: 400/319 !important;
    }
    .t4s-coll-layout-2.has-7-item .coll-item-6 .t4s_ratio {
        --aspect-ratioapt: 400/298 !important;
    }
}
@media(max-width: 1024px) and (min-width: 768px) {
    .t4s-collection-manual .t4s-collection-manual .t4s-collection-item .t4s_ratio {
       --aspect-ratioapt : var(--aspect-ratioapttb) !important;
    }
    .t4s-collection-item .t4s-cate-wrapper {
        bottom: var(--space-bottom-tb,15px);
    }
    .t4s-collection-border-true .t4s-collection-item .t4s-cate-wrapper  {
        bottom: calc(var(--space-bottom-tb) + 1px);
    }
}
@media(max-width: 991px) {
    .t4s-coll-layout-2.has-3-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-3.has-3-item .coll-item-3 .t4s_ratio,
    .t4s-coll-layout-5.has-3-item .coll-item-2 .t4s_ratio,
    .t4s-coll-layout-6.has-3-item .coll-item-3 .t4s_ratio,
    .t4s-coll-layout-3.has-4-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-7.has-4-item .coll-item-4 .t4s_ratio {
        --aspect-ratioapt: 570/400 !important;
    }
    .t4s-coll-layout-4.has-3-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-5.has-3-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-5.has-3-item .coll-item-3 .t4s_ratio,
    .t4s-coll-layout-6.has-3-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-6.has-3-item .coll-item-2 .t4s_ratio {
        --aspect-ratioapt: 570/300 !important;
    }
    .t4s-coll-layout-4.has-3-item .coll-item-2 .t4s_ratio,
    .t4s-coll-layout-4.has-3-item .coll-item-3 .t4s_ratio,
    .t4s-coll-layout-6.has-3-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-6.has-3-item .coll-item-2 .t4s_ratio {
        --aspect-ratioapt: 270/300 !important;
    }
    .t4s-coll-layout-4.has-4-item .coll-item-4 .t4s_ratio {
        --aspect-ratioapt: 570/627 !important;
    }
    .t4s-coll-layout-5.has-4-item .coll-item-4 .t4s_ratio {
        --aspect-ratioapt: 570/696 !important;
    }
    .t4s-coll-layout-6.has-4-item .coll-item-1 .t4s_ratio {
        --aspect-ratioapt: 400/633 !important;
    }
    .t4s-coll-layout-2.has-5-item .coll-item-3 .t4s_ratio {
        --aspect-ratioapt: 400/634 !important;   
    }
    .t4s-coll-layout-4.has-5-item .coll-item-3 .t4s_ratio {
        --aspect-ratioapt: 400/300 !important;   
    }
    .t4s-coll-layout-5.has-5-item .coll-item-1 .t4s_ratio {
        --aspect-ratioapt: 400/651 !important;
    }
    .t4s-coll-layout-5.has-6-item .coll-item-1 .t4s_ratio {
        --aspect-ratioapt: 400/664 !important; 
    }
    .t4s-coll-layout-2.has-7-item .coll-item-5 .t4s_ratio,
    .t4s-coll-layout-2.has-7-item .coll-item-7 .t4s_ratio {
        --aspect-ratioapt: 570/300 !important;
    }
}
@media(max-width: 767px) {
    .t4s-collection-item {
        margin-bottom: var(--mgb-mb);
    }
    .t4s-cate-wrapper {
        padding: 5px 10px;
        min-height: 30px;
    }
    .t4s-coll-layout-4.has-4-item .coll-item-3 .t4s_ratio,
    .t4s-coll-layout-5.has-4-item .coll-item-4 .t4s_ratio,
    .t4s-coll-layout-5.has-4-item .coll-item-4 .t4s_ratio,
    .t4s-coll-layout-6.has-4-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-6.has-4-item .coll-item-4 .t4s_ratio {
        --aspect-ratioapt: 570/400 !important;
    }
    .t4s-coll-layout-4.has-4-item .coll-item-4 .t4s_ratio {
        --aspect-ratioapt: 570/300 !important;
    }
    .t4s-coll-layout-2.has-5-item .coll-item-3 .t4s_ratio {
        --aspect-ratioapt: 400/250 !important;   
    }
    .t4s-coll-layout-5.has-5-item .coll-item-1 .t4s_ratio,
    .t4s-coll-layout-5.has-5-item .coll-item-5 .t4s_ratio {
        --aspect-ratioapt: 400/250 !important;
    }
    .t4s-coll-layout-5.has-6-item .coll-item-1 .t4s_ratio {
        --aspect-ratioapt: 400/300 !important; 
    }
    .t4s-collection-item .t4s-cate-wrapper {
        bottom: var(--space-bottom-mb);
    }
    .t4s-collection-border-true .t4s-collection-item .t4s-cate-wrapper  {
        bottom: calc(var(--space-bottom-mb) + 1px);
    }
}
@media(max-width: 480px) {
    .t4s-collection-item .t4s-cate-wrapper {
        max-width: 90%;
        margin: auto;
        min-width: 100px;
    }
    .t4s-coll-style-5 .t4s-cate-wrapper,
    .t4s-coll-style-7 .t4s-cate-wrapper,
    .t4s-coll-style-11 .t4s-cate-wrapper,
    .t4s-coll-style-12 .t4s-cate-wrapper  {
        padding: var(--space-bottom-mb) 0 0;
    }
}
.t4s-collection-item .t4s-coll-img {
    padding: var(--item-pd);
}
.t4s-collection-border-true .t4s-collection-item .t4s-coll-img {
    padding: 0;
}
.t4s-collection-border-true .t4s-collection-item .t4s-img-wrap {
    overflow: hidden;
    position: relative;
    border-radius: var(--item-rd);
    padding: var(--item-pd);
    margin-bottom: 1px;
}
.t4s-collection-border-true .t4s-collection-item .t4s-img-wrap::before {
    content: "";
    position: absolute;
    z-index: 3;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border: solid 1px var(--border-cl);
    border-radius: var(--item-rd);
}
.t4s-collection-item .t4s-coll-icon-svg {
    display: inline-flex;
}
.t4s-collection-item .t4s-coll-icon-svg svg {
    width: var(--icon-width,64px);
    height: var(--icon-width,64px);
    fill: currentColor;
}
.t4s-collection-border-true.t4s-gx-md-0 .t4s-collection-item {
  margin-inline-start: -1px;
}
.t4s-collection-border-true.t4s-gy-md-0 .t4s-collection-item {
  margin-bottom: -1px;
}
.t4s-collection-small .t4s-collection-item .t4s-coll-img {
    width: 86px;
}
.t4s-collection-medium .t4s-collection-item .t4s-coll-img {
    width: 102px;
}
.t4s-collection-large .t4s-collection-item .t4s-coll-img {
    width: 150px;
}
.t4s-collection-item .t4s-coll-img .t4s-count,
.t4s-collection-item .t4s-cat-title .t4s-count {
    background-color: var(--count-cl-pri);
    color: var(--count-cl-second);
    height: 20px;
    line-height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 26px;
    width: auto;
    padding: 0 5px;
    font-size: 11px;
    border-radius: 10px;
    position: absolute;
    z-index: 4;
    left: auto;
    right: 10%;
    top: 10%;
}
.t4s-collection-item .t4s-cat-title .t4s-count {
    right: auto;
    left: 100%;
    top: -5px;
}
.t4s-collection-item .t4s-source-icon .t4s-coll-img .t4s-count {
   right: -8px;
   top: 12px; 
}
.t4s-list-collections[style*="--item-rd:0%"] .t4s-collection-item .t4s-coll-img .t4s-count {
    top: 5px;
    right: 5px;
}
    
.t4s-collection-small .t4s-collection-item .t4s-coll-img .t4s-count {
    right: -10px;
    top: 12px;
}
.t4s-collection-medium .t4s-collection-item .t4s-coll-img .t4s-count {
    right: -10px;
    top: 12px;
}
.t4s-collection-large .t4s-collection-item .t4s-coll-img .t4s-count {
    right: 0;
    top: 20px;
}
.t4s-collection-item:not(.t4s-coll-style-12) .t4s-cat-title .t4s-count {
    display: none;
}
.t4s-prs-footer {
    margin-top: 40px;
}     
@media(max-width: 767px) {
  .t4s-collection-border-true.t4s-gx-0.t4s-gy-0 {
    border-top: solid 1px var(--border-cl);
    border-left: solid 1px var(--border-cl);
  }
  .t4s-collection-border-true.t4s-gx-0.t4s-gy-0 .t4s-collection-item .t4s-img-wrap::before {
    box-shadow: inset -1px -1px var(--border-cl), -1px -1px var(--border-cl);
    border: none;
  }
  .t4s-collection-border-true.t4s-gx-0.t4s-gy-0.t4s-has-collection16 {
    border: none;
  }
}
@media (-moz-touch-enabled: 0), (hover: hover) {
    .t4s-collection-item .t4s-cat-title:hover {
        color: var(--color-hover);
        background-color: var(--bg-color-hover);
    }
    .t4s-coll-style-3:hover .t4s-cat-title,
    .t4s-coll-style-3:hover .t4s-cat-subtitle {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    .t4s-coll-style-3:hover .t4s-cate-wrapper > a {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    .t4s-coll-style-4 .t4s-cate-wrapper:hover .t4s-cat-subtitle {
        opacity: 1;
        visibility: visible;
    }
    .t4s-coll-style-10.t4s-collection-item .t4s-cat-title:hover{
        color: var(--color);
        background-color: transparent;
    }
}
@media (max-width: 320px){
    .t4s-collection-item .t4s-cat-title, 
    .t4s-collection-item .t4s-cat-subtitle{
        font-size: 12px;
        font-weight: 500;
    }
}