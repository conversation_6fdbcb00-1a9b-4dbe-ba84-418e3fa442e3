var T4SThemeSP={},isStorageSpSession=!1,isStorageSpSessionAll=!1,isStorageSpdLocal=!1,isStorageSpdLocalAll=!1,T4Sconfigs=window.T4Sconfigs,IsDesignMode=window.T4Srequest.design_mode,isThemeRTL="rtl"==document.documentElement.getAttribute("dir"),jQuery_T4NT=(T4stt_var={HoverInterval:35,HoverTimeout:150,dragThreshold:10,prevOnHref:!1},!function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(t.document)return e(t);throw new Error("jQuery requires a window with a document")}:e(t)}("undefined"!=typeof window?window:this,function(w,j){"use strict";function v(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item}function g(t){return null!=t&&t===t.window}var e=[],W=Object.getPrototypeOf,a=e.slice,O=e.flat?function(t){return e.flat.call(t)}:function(t){return e.concat.apply([],t)},q=e.push,$=e.indexOf,H={},R=H.toString,B=H.hasOwnProperty,F=B.toString,U=F.call(Object),m={},T=w.document,V={type:!0,src:!0,nonce:!0,noModule:!0};function G(t,e,i){var n,o,s=(i=i||T).createElement("script");if(s.text=t,e)for(n in V)(o=e[n]||e.getAttribute&&e.getAttribute(n))&&s.setAttribute(n,o);i.head.appendChild(s).parentNode.removeChild(s)}function p(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?H[R.call(t)]||"object":typeof t}var t="3.6.0",C=function(t,e){return new C.fn.init(t,e)};function X(t){var e=!!t&&"length"in t&&t.length,i=p(t);return!v(t)&&!g(t)&&("array"===i||0===e||"number"==typeof e&&0<e&&e-1 in t)}C.fn=C.prototype={jquery:t,constructor:C,length:0,toArray:function(){return a.call(this)},get:function(t){return null==t?a.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){t=C.merge(this.constructor(),t);return t.prevObject=this,t},each:function(t){return C.each(this,t)},map:function(i){return this.pushStack(C.map(this,function(t,e){return i.call(t,e,t)}))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(C.grep(this,function(t,e){return(e+1)%2}))},odd:function(){return this.pushStack(C.grep(this,function(t,e){return e%2}))},eq:function(t){var e=this.length,t=+t+(t<0?e:0);return this.pushStack(0<=t&&t<e?[this[t]]:[])},end:function(){return this.prevObject||this.constructor()},push:q,sort:e.sort,splice:e.splice},C.extend=C.fn.extend=function(){var t,e,i,n,o,s=arguments[0]||{},r=1,a=arguments.length,l=!1;for("boolean"==typeof s&&(l=s,s=arguments[r]||{},r++),"object"==typeof s||v(s)||(s={}),r===a&&(s=this,r--);r<a;r++)if(null!=(t=arguments[r]))for(e in t)i=t[e],"__proto__"!==e&&s!==i&&(l&&i&&(C.isPlainObject(i)||(n=Array.isArray(i)))?(o=s[e],o=n&&!Array.isArray(o)?[]:n||C.isPlainObject(o)?o:{},n=!1,s[e]=C.extend(l,o,i)):void 0!==i&&(s[e]=i));return s},C.extend({expando:"jQuery"+(t+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){return!(!t||"[object Object]"!==R.call(t)||(t=W(t))&&("function"!=typeof(t=B.call(t,"constructor")&&t.constructor)||F.call(t)!==U))},isEmptyObject:function(t){for(var e in t)return!1;return!0},globalEval:function(t,e,i){G(t,{nonce:e&&e.nonce},i)},each:function(t,e){var i,n=0;if(X(t))for(i=t.length;n<i&&!1!==e.call(t[n],n,t[n]);n++);else for(n in t)if(!1===e.call(t[n],n,t[n]))break;return t},makeArray:function(t,e){e=e||[];return null!=t&&(X(Object(t))?C.merge(e,"string"==typeof t?[t]:t):q.call(e,t)),e},inArray:function(t,e,i){return null==e?-1:$.call(e,t,i)},merge:function(t,e){for(var i=+e.length,n=0,o=t.length;n<i;n++)t[o++]=e[n];return t.length=o,t},grep:function(t,e,i){for(var n=[],o=0,s=t.length,r=!i;o<s;o++)!e(t[o],o)!=r&&n.push(t[o]);return n},map:function(t,e,i){var n,o,s=0,r=[];if(X(t))for(n=t.length;s<n;s++)null!=(o=e(t[s],s,i))&&r.push(o);else for(s in t)null!=(o=e(t[s],s,i))&&r.push(o);return O(r)},guid:1,support:m}),"function"==typeof Symbol&&(C.fn[Symbol.iterator]=e[Symbol.iterator]),C.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){H["[object "+e+"]"]=e.toLowerCase()});function n(t,e,i){for(var n=[],o=void 0!==i;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(o&&C(t).is(i))break;n.push(t)}return n}function Y(t,e){for(var i=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&i.push(t);return i}var t=function(j){function h(t,e){return t="0x"+t.slice(1)-65536,e||(t<0?String.fromCharCode(65536+t):String.fromCharCode(t>>10|55296,1023&t|56320))}function W(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}function O(){w()}var t,d,x,s,q,f,$,H,S,l,c,w,T,i,C,p,n,o,g,E="sizzle"+ +new Date,u=j.document,_=0,R=0,B=L(),F=L(),U=L(),m=L(),V=function(t,e){return t===e&&(c=!0),0},G={}.hasOwnProperty,e=[],X=e.pop,Y=e.push,k=e.push,Q=e.slice,v=function(t,e){for(var i=0,n=t.length;i<n;i++)if(t[i]===e)return i;return-1},J="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",r="[\\x20\\t\\r\\n\\f]",a="(?:\\\\[\\da-fA-F]{1,6}"+r+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",K="\\["+r+"*("+a+")(?:"+r+"*([*^$|!~]?=)"+r+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+a+"))|)"+r+"*\\]",Z=":("+a+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+K+")*)|.*)\\)|)",tt=new RegExp(r+"+","g"),y=new RegExp("^"+r+"+|((?:^|[^\\\\])(?:\\\\.)*)"+r+"+$","g"),et=new RegExp("^"+r+"*,"+r+"*"),it=new RegExp("^"+r+"*([>+~]|"+r+")"+r+"*"),nt=new RegExp(r+"|>"),ot=new RegExp(Z),st=new RegExp("^"+a+"$"),b={ID:new RegExp("^#("+a+")"),CLASS:new RegExp("^\\.("+a+")"),TAG:new RegExp("^("+a+"|[*])"),ATTR:new RegExp("^"+K),PSEUDO:new RegExp("^"+Z),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+r+"*(even|odd|(([+-]|)(\\d*)n|)"+r+"*(?:([+-]|)"+r+"*(\\d+)|))"+r+"*\\)|)","i"),bool:new RegExp("^(?:"+J+")$","i"),needsContext:new RegExp("^"+r+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+r+"*((?:-\\d)?\\d*)"+r+"*\\)|)(?=[^-]|$)","i")},rt=/HTML$/i,at=/^(?:input|select|textarea|button)$/i,lt=/^h\d$/i,I=/^[^{]+\{\s*\[native \w/,ct=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ut=/[+~]/,P=new RegExp("\\\\[\\da-fA-F]{1,6}"+r+"?|\\\\([^\\r\\n\\f])","g"),ht=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,dt=vt(function(t){return!0===t.disabled&&"fieldset"===t.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{k.apply(e=Q.call(u.childNodes),u.childNodes),e[u.childNodes.length].nodeType}catch(t){k={apply:e.length?function(t,e){Y.apply(t,Q.call(e))}:function(t,e){for(var i=t.length,n=0;t[i++]=e[n++];);t.length=i-1}}}function D(t,e,i,n){var o,s,r,a,l,c,u=e&&e.ownerDocument,h=e?e.nodeType:9;if(i=i||[],"string"!=typeof t||!t||1!==h&&9!==h&&11!==h)return i;if(!n&&(w(e),e=e||T,C)){if(11!==h&&(a=ct.exec(t)))if(o=a[1]){if(9===h){if(!(c=e.getElementById(o)))return i;if(c.id===o)return i.push(c),i}else if(u&&(c=u.getElementById(o))&&g(e,c)&&c.id===o)return i.push(c),i}else{if(a[2])return k.apply(i,e.getElementsByTagName(t)),i;if((o=a[3])&&d.getElementsByClassName&&e.getElementsByClassName)return k.apply(i,e.getElementsByClassName(o)),i}if(d.qsa&&!m[t+" "]&&(!p||!p.test(t))&&(1!==h||"object"!==e.nodeName.toLowerCase())){if(c=t,u=e,1===h&&(nt.test(t)||it.test(t))){for((u=ut.test(t)&&mt(e.parentNode)||e)===e&&d.scope||((r=e.getAttribute("id"))?r=r.replace(ht,W):e.setAttribute("id",r=E)),s=(l=f(t)).length;s--;)l[s]=(r?"#"+r:":scope")+" "+M(l[s]);c=l.join(",")}try{return k.apply(i,u.querySelectorAll(c)),i}catch(e){m(t,!0)}finally{r===E&&e.removeAttribute("id")}}}return H(t.replace(y,"$1"),e,i,n)}function L(){var n=[];return function t(e,i){return n.push(e+" ")>x.cacheLength&&delete t[n.shift()],t[e+" "]=i}}function A(t){return t[E]=!0,t}function z(t){var e=T.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e)}}function ft(t,e){for(var i=t.split("|"),n=i.length;n--;)x.attrHandle[i[n]]=e}function pt(t,e){var i=e&&t,n=i&&1===t.nodeType&&1===e.nodeType&&t.sourceIndex-e.sourceIndex;if(n)return n;if(i)for(;i=i.nextSibling;)if(i===e)return-1;return t?1:-1}function gt(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&dt(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function N(r){return A(function(s){return s=+s,A(function(t,e){for(var i,n=r([],t.length,s),o=n.length;o--;)t[i=n[o]]&&(t[i]=!(e[i]=t[i]))})})}function mt(t){return t&&void 0!==t.getElementsByTagName&&t}for(t in d=D.support={},q=D.isXML=function(t){var e=t&&t.namespaceURI,t=t&&(t.ownerDocument||t).documentElement;return!rt.test(e||t&&t.nodeName||"HTML")},w=D.setDocument=function(t){var t=t?t.ownerDocument||t:u;return t!=T&&9===t.nodeType&&t.documentElement&&(i=(T=t).documentElement,C=!q(T),u!=T&&(t=T.defaultView)&&t.top!==t&&(t.addEventListener?t.addEventListener("unload",O,!1):t.attachEvent&&t.attachEvent("onunload",O)),d.scope=z(function(t){return i.appendChild(t).appendChild(T.createElement("div")),void 0!==t.querySelectorAll&&!t.querySelectorAll(":scope fieldset div").length}),d.attributes=z(function(t){return t.className="i",!t.getAttribute("className")}),d.getElementsByTagName=z(function(t){return t.appendChild(T.createComment("")),!t.getElementsByTagName("*").length}),d.getElementsByClassName=I.test(T.getElementsByClassName),d.getById=z(function(t){return i.appendChild(t).id=E,!T.getElementsByName||!T.getElementsByName(E).length}),d.getById?(x.filter.ID=function(t){var e=t.replace(P,h);return function(t){return t.getAttribute("id")===e}},x.find.ID=function(t,e){if(void 0!==e.getElementById&&C)return(e=e.getElementById(t))?[e]:[]}):(x.filter.ID=function(t){var e=t.replace(P,h);return function(t){t=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return t&&t.value===e}},x.find.ID=function(t,e){if(void 0!==e.getElementById&&C){var i,n,o,s=e.getElementById(t);if(s){if((i=s.getAttributeNode("id"))&&i.value===t)return[s];for(o=e.getElementsByName(t),n=0;s=o[n++];)if((i=s.getAttributeNode("id"))&&i.value===t)return[s]}return[]}}),x.find.TAG=d.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):d.qsa?e.querySelectorAll(t):void 0}:function(t,e){var i,n=[],o=0,s=e.getElementsByTagName(t);if("*"!==t)return s;for(;i=s[o++];)1===i.nodeType&&n.push(i);return n},x.find.CLASS=d.getElementsByClassName&&function(t,e){if(void 0!==e.getElementsByClassName&&C)return e.getElementsByClassName(t)},n=[],p=[],(d.qsa=I.test(T.querySelectorAll))&&(z(function(t){var e;i.appendChild(t).innerHTML="<a id='"+E+"'></a><select id='"+E+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&p.push("[*^$]="+r+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||p.push("\\["+r+"*(?:value|"+J+")"),t.querySelectorAll("[id~="+E+"-]").length||p.push("~="),(e=T.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||p.push("\\["+r+"*name"+r+"*="+r+"*(?:''|\"\")"),t.querySelectorAll(":checked").length||p.push(":checked"),t.querySelectorAll("a#"+E+"+*").length||p.push(".#.+[+~]"),t.querySelectorAll("\\\f"),p.push("[\\r\\n\\f]")}),z(function(t){t.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var e=T.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&p.push("name"+r+"*[*^$|!~]?="),2!==t.querySelectorAll(":enabled").length&&p.push(":enabled",":disabled"),i.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&p.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),p.push(",.*:")})),(d.matchesSelector=I.test(o=i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.oMatchesSelector||i.msMatchesSelector))&&z(function(t){d.disconnectedMatch=o.call(t,"*"),o.call(t,"[s!='']:x"),n.push("!=",Z)}),p=p.length&&new RegExp(p.join("|")),n=n.length&&new RegExp(n.join("|")),t=I.test(i.compareDocumentPosition),g=t||I.test(i.contains)?function(t,e){var i=9===t.nodeType?t.documentElement:t,e=e&&e.parentNode;return t===e||!(!e||1!==e.nodeType||!(i.contains?i.contains(e):t.compareDocumentPosition&&16&t.compareDocumentPosition(e)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},V=t?function(t,e){var i;return t===e?(c=!0,0):!t.compareDocumentPosition-!e.compareDocumentPosition||(1&(i=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!d.sortDetached&&e.compareDocumentPosition(t)===i?t==T||t.ownerDocument==u&&g(u,t)?-1:e==T||e.ownerDocument==u&&g(u,e)?1:l?v(l,t)-v(l,e):0:4&i?-1:1)}:function(t,e){if(t===e)return c=!0,0;var i,n=0,o=t.parentNode,s=e.parentNode,r=[t],a=[e];if(!o||!s)return t==T?-1:e==T?1:o?-1:s?1:l?v(l,t)-v(l,e):0;if(o===s)return pt(t,e);for(i=t;i=i.parentNode;)r.unshift(i);for(i=e;i=i.parentNode;)a.unshift(i);for(;r[n]===a[n];)n++;return n?pt(r[n],a[n]):r[n]==u?-1:a[n]==u?1:0}),T},D.matches=function(t,e){return D(t,null,null,e)},D.matchesSelector=function(t,e){if(w(t),d.matchesSelector&&C&&!m[e+" "]&&(!n||!n.test(e))&&(!p||!p.test(e)))try{var i=o.call(t,e);if(i||d.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(t){m(e,!0)}return 0<D(e,T,null,[t]).length},D.contains=function(t,e){return(t.ownerDocument||t)!=T&&w(t),g(t,e)},D.attr=function(t,e){(t.ownerDocument||t)!=T&&w(t);var i=x.attrHandle[e.toLowerCase()],i=i&&G.call(x.attrHandle,e.toLowerCase())?i(t,e,!C):void 0;return void 0!==i?i:d.attributes||!C?t.getAttribute(e):(i=t.getAttributeNode(e))&&i.specified?i.value:null},D.escape=function(t){return(t+"").replace(ht,W)},D.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},D.uniqueSort=function(t){var e,i=[],n=0,o=0;if(c=!d.detectDuplicates,l=!d.sortStable&&t.slice(0),t.sort(V),c){for(;e=t[o++];)e===t[o]&&(n=i.push(o));for(;n--;)t.splice(i[n],1)}return l=null,t},s=D.getText=function(t){var e,i="",n=0,o=t.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)i+=s(t)}else if(3===o||4===o)return t.nodeValue}else for(;e=t[n++];)i+=s(e);return i},(x=D.selectors={cacheLength:50,createPseudo:A,match:b,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(P,h),t[3]=(t[3]||t[4]||t[5]||"").replace(P,h),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||D.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&D.error(t[0]),t},PSEUDO:function(t){var e,i=!t[6]&&t[2];return b.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":i&&ot.test(i)&&(e=(e=f(i,!0))&&i.indexOf(")",i.length-e)-i.length)&&(t[0]=t[0].slice(0,e),t[2]=i.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(P,h).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=B[t+" "];return e||(e=new RegExp("(^|"+r+")"+t+"("+r+"|$)"))&&B(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(e,i,n){return function(t){t=D.attr(t,e);return null==t?"!="===i:!i||(t+="","="===i?t===n:"!="===i?t!==n:"^="===i?n&&0===t.indexOf(n):"*="===i?n&&-1<t.indexOf(n):"$="===i?n&&t.slice(-n.length)===n:"~="===i?-1<(" "+t.replace(tt," ")+" ").indexOf(n):"|="===i&&(t===n||t.slice(0,n.length+1)===n+"-"))}},CHILD:function(p,t,e,g,m){var y="nth"!==p.slice(0,3),v="last"!==p.slice(-4),b="of-type"===t;return 1===g&&0===m?function(t){return!!t.parentNode}:function(t,e,i){var n,o,s,r,a,l,c=y!=v?"nextSibling":"previousSibling",u=t.parentNode,h=b&&t.nodeName.toLowerCase(),d=!i&&!b,f=!1;if(u){if(y){for(;c;){for(r=t;r=r[c];)if(b?r.nodeName.toLowerCase()===h:1===r.nodeType)return!1;l=c="only"===p&&!l&&"nextSibling"}return!0}if(l=[v?u.firstChild:u.lastChild],v&&d){for(f=(a=(n=(o=(s=(r=u)[E]||(r[E]={}))[r.uniqueID]||(s[r.uniqueID]={}))[p]||[])[0]===_&&n[1])&&n[2],r=a&&u.childNodes[a];r=++a&&r&&r[c]||(f=a=0,l.pop());)if(1===r.nodeType&&++f&&r===t){o[p]=[_,a,f];break}}else if(!1===(f=d?a=(n=(o=(s=(r=t)[E]||(r[E]={}))[r.uniqueID]||(s[r.uniqueID]={}))[p]||[])[0]===_&&n[1]:f))for(;(r=++a&&r&&r[c]||(f=a=0,l.pop()))&&((b?r.nodeName.toLowerCase()!==h:1!==r.nodeType)||!++f||(d&&((o=(s=r[E]||(r[E]={}))[r.uniqueID]||(s[r.uniqueID]={}))[p]=[_,f]),r!==t)););return(f-=m)===g||f%g==0&&0<=f/g}}},PSEUDO:function(t,s){var e,r=x.pseudos[t]||x.setFilters[t.toLowerCase()]||D.error("unsupported pseudo: "+t);return r[E]?r(s):1<r.length?(e=[t,t,"",s],x.setFilters.hasOwnProperty(t.toLowerCase())?A(function(t,e){for(var i,n=r(t,s),o=n.length;o--;)t[i=v(t,n[o])]=!(e[i]=n[o])}):function(t){return r(t,0,e)}):r}},pseudos:{not:A(function(t){var n=[],o=[],a=$(t.replace(y,"$1"));return a[E]?A(function(t,e,i,n){for(var o,s=a(t,null,n,[]),r=t.length;r--;)(o=s[r])&&(t[r]=!(e[r]=o))}):function(t,e,i){return n[0]=t,a(n,null,i,o),n[0]=null,!o.pop()}}),has:A(function(e){return function(t){return 0<D(e,t).length}}),contains:A(function(e){return e=e.replace(P,h),function(t){return-1<(t.textContent||s(t)).indexOf(e)}}),lang:A(function(i){return st.test(i||"")||D.error("unsupported lang: "+i),i=i.replace(P,h).toLowerCase(),function(t){var e;do{if(e=C?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(e=e.toLowerCase())===i||0===e.indexOf(i+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var e=j.location&&j.location.hash;return e&&e.slice(1)===t.id},root:function(t){return t===i},focus:function(t){return t===T.activeElement&&(!T.hasFocus||T.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:gt(!1),disabled:gt(!0),checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!x.pseudos.empty(t)},header:function(t){return lt.test(t.nodeName)},input:function(t){return at.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(t=t.getAttribute("type"))||"text"===t.toLowerCase())},first:N(function(){return[0]}),last:N(function(t,e){return[e-1]}),eq:N(function(t,e,i){return[i<0?i+e:i]}),even:N(function(t,e){for(var i=0;i<e;i+=2)t.push(i);return t}),odd:N(function(t,e){for(var i=1;i<e;i+=2)t.push(i);return t}),lt:N(function(t,e,i){for(var n=i<0?i+e:e<i?e:i;0<=--n;)t.push(n);return t}),gt:N(function(t,e,i){for(var n=i<0?i+e:i;++n<e;)t.push(n);return t})}}).pseudos.nth=x.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})x.pseudos[t]=function(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}(t);for(t in{submit:!0,reset:!0})x.pseudos[t]=function(i){return function(t){var e=t.nodeName.toLowerCase();return("input"===e||"button"===e)&&t.type===i}}(t);function yt(){}function M(t){for(var e=0,i=t.length,n="";e<i;e++)n+=t[e].value;return n}function vt(r,t,e){var a=t.dir,l=t.next,c=l||a,u=e&&"parentNode"===c,h=R++;return t.first?function(t,e,i){for(;t=t[a];)if(1===t.nodeType||u)return r(t,e,i);return!1}:function(t,e,i){var n,o,s=[_,h];if(i){for(;t=t[a];)if((1===t.nodeType||u)&&r(t,e,i))return!0}else for(;t=t[a];)if(1===t.nodeType||u)if(o=(o=t[E]||(t[E]={}))[t.uniqueID]||(o[t.uniqueID]={}),l&&l===t.nodeName.toLowerCase())t=t[a]||t;else{if((n=o[c])&&n[0]===_&&n[1]===h)return s[2]=n[2];if((o[c]=s)[2]=r(t,e,i))return!0}return!1}}function bt(o){return 1<o.length?function(t,e,i){for(var n=o.length;n--;)if(!o[n](t,e,i))return!1;return!0}:o[0]}function xt(t,e,i,n,o){for(var s,r=[],a=0,l=t.length,c=null!=e;a<l;a++)!(s=t[a])||i&&!i(s,n,o)||(r.push(s),c&&e.push(a));return r}function St(t){for(var n,e,i,o=t.length,s=x.relative[t[0].type],r=s||x.relative[" "],a=s?1:0,l=vt(function(t){return t===n},r,!0),c=vt(function(t){return-1<v(n,t)},r,!0),u=[function(t,e,i){t=!s&&(i||e!==S)||((n=e).nodeType?l:c)(t,e,i);return n=null,t}];a<o;a++)if(e=x.relative[t[a].type])u=[vt(bt(u),e)];else{if((e=x.filter[t[a].type].apply(null,t[a].matches))[E]){for(i=++a;i<o&&!x.relative[t[i].type];i++);return function t(f,p,g,m,y,e){return m&&!m[E]&&(m=t(m)),y&&!y[E]&&(y=t(y,e)),A(function(t,e,i,n){var o,s,r,a=[],l=[],c=e.length,u=t||function(t,e,i){for(var n=0,o=e.length;n<o;n++)D(t,e[n],i);return i}(p||"*",i.nodeType?[i]:i,[]),h=!f||!t&&p?u:xt(u,a,f,i,n),d=g?y||(t?f:c||m)?[]:e:h;if(g&&g(h,d,i,n),m)for(o=xt(d,l),m(o,[],i,n),s=o.length;s--;)(r=o[s])&&(d[l[s]]=!(h[l[s]]=r));if(t){if(y||f){if(y){for(o=[],s=d.length;s--;)(r=d[s])&&o.push(h[s]=r);y(null,d=[],o,n)}for(s=d.length;s--;)(r=d[s])&&-1<(o=y?v(t,r):a[s])&&(t[o]=!(e[o]=r))}}else d=xt(d===e?d.splice(c,d.length):d),y?y(null,e,d,n):k.apply(e,d)})}(1<a&&bt(u),1<a&&M(t.slice(0,a-1).concat({value:" "===t[a-2].type?"*":""})).replace(y,"$1"),e,a<i&&St(t.slice(a,i)),i<o&&St(t=t.slice(i)),i<o&&M(t))}u.push(e)}return bt(u)}return yt.prototype=x.filters=x.pseudos,x.setFilters=new yt,f=D.tokenize=function(t,e){var i,n,o,s,r,a,l,c=F[t+" "];if(c)return e?0:c.slice(0);for(r=t,a=[],l=x.preFilter;r;){for(s in i&&!(n=et.exec(r))||(n&&(r=r.slice(n[0].length)||r),a.push(o=[])),i=!1,(n=it.exec(r))&&(i=n.shift(),o.push({value:i,type:n[0].replace(y," ")}),r=r.slice(i.length)),x.filter)!(n=b[s].exec(r))||l[s]&&!(n=l[s](n))||(i=n.shift(),o.push({value:i,type:s,matches:n}),r=r.slice(i.length));if(!i)break}return e?r.length:r?D.error(t):F(t,a).slice(0)},$=D.compile=function(t,e){var i,m,y,v,b,n,o=[],s=[],r=U[t+" "];if(!r){for(i=(e=e||f(t)).length;i--;)((r=St(e[i]))[E]?o:s).push(r);(r=U(t,(v=0<(y=o).length,b=0<(m=s).length,n=function(t,e,i,n,o){var s,r,a,l=0,c="0",u=t&&[],h=[],d=S,f=t||b&&x.find.TAG("*",o),p=_+=null==d?1:Math.random()||.1,g=f.length;for(o&&(S=e==T||e||o);c!==g&&null!=(s=f[c]);c++){if(b&&s){for(r=0,e||s.ownerDocument==T||(w(s),i=!C);a=m[r++];)if(a(s,e||T,i)){n.push(s);break}o&&(_=p)}v&&((s=!a&&s)&&l--,t)&&u.push(s)}if(l+=c,v&&c!==l){for(r=0;a=y[r++];)a(u,h,e,i);if(t){if(0<l)for(;c--;)u[c]||h[c]||(h[c]=X.call(n));h=xt(h)}k.apply(n,h),o&&!t&&0<h.length&&1<l+y.length&&D.uniqueSort(n)}return o&&(_=p,S=d),u},v?A(n):n))).selector=t}return r},H=D.select=function(t,e,i,n){var o,s,r,a,l,c="function"==typeof t&&t,u=!n&&f(t=c.selector||t);if(i=i||[],1===u.length){if(2<(s=u[0]=u[0].slice(0)).length&&"ID"===(r=s[0]).type&&9===e.nodeType&&C&&x.relative[s[1].type]){if(!(e=(x.find.ID(r.matches[0].replace(P,h),e)||[])[0]))return i;c&&(e=e.parentNode),t=t.slice(s.shift().value.length)}for(o=b.needsContext.test(t)?0:s.length;o--&&(r=s[o],!x.relative[a=r.type]);)if((l=x.find[a])&&(n=l(r.matches[0].replace(P,h),ut.test(s[0].type)&&mt(e.parentNode)||e))){if(s.splice(o,1),t=n.length&&M(s))break;return k.apply(i,n),i}}return(c||$(t,u))(n,e,!C,i,!e||ut.test(t)&&mt(e.parentNode)||e),i},d.sortStable=E.split("").sort(V).join("")===E,d.detectDuplicates=!!c,w(),d.sortDetached=z(function(t){return 1&t.compareDocumentPosition(T.createElement("fieldset"))}),z(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||ft("type|href|height|width",function(t,e,i){if(!i)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),d.attributes&&z(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||ft("value",function(t,e,i){if(!i&&"input"===t.nodeName.toLowerCase())return t.defaultValue}),z(function(t){return null==t.getAttribute("disabled")})||ft(J,function(t,e,i){if(!i)return!0===t[e]?e.toLowerCase():(i=t.getAttributeNode(e))&&i.specified?i.value:null}),D}(w),Q=(C.find=t,C.expr=t.selectors,C.expr[":"]=C.expr.pseudos,C.uniqueSort=C.unique=t.uniqueSort,C.text=t.getText,C.isXMLDoc=t.isXML,C.contains=t.contains,C.escapeSelector=t.escape,C.expr.match.needsContext);function l(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}var J=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function K(t,i,n){return v(i)?C.grep(t,function(t,e){return!!i.call(t,e,t)!==n}):i.nodeType?C.grep(t,function(t){return t===i!==n}):"string"!=typeof i?C.grep(t,function(t){return-1<$.call(i,t)!==n}):C.filter(i,t,n)}C.filter=function(t,e,i){var n=e[0];return i&&(t=":not("+t+")"),1===e.length&&1===n.nodeType?C.find.matchesSelector(n,t)?[n]:[]:C.find.matches(t,C.grep(e,function(t){return 1===t.nodeType}))},C.fn.extend({find:function(t){var e,i,n=this.length,o=this;if("string"!=typeof t)return this.pushStack(C(t).filter(function(){for(e=0;e<n;e++)if(C.contains(o[e],this))return!0}));for(i=this.pushStack([]),e=0;e<n;e++)C.find(t,o[e],i);return 1<n?C.uniqueSort(i):i},filter:function(t){return this.pushStack(K(this,t||[],!1))},not:function(t){return this.pushStack(K(this,t||[],!0))},is:function(t){return!!K(this,"string"==typeof t&&Q.test(t)?C(t):t||[],!1).length}});var Z,tt=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,et=((C.fn.init=function(t,e,i){if(t){if(i=i||Z,"string"!=typeof t)return t.nodeType?(this[0]=t,this.length=1,this):v(t)?void 0!==i.ready?i.ready(t):t(C):C.makeArray(t,this);if(!(n="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:tt.exec(t))||!n[1]&&e)return(!e||e.jquery?e||i:this.constructor(e)).find(t);if(n[1]){if(e=e instanceof C?e[0]:e,C.merge(this,C.parseHTML(n[1],e&&e.nodeType?e.ownerDocument||e:T,!0)),J.test(n[1])&&C.isPlainObject(e))for(var n in e)v(this[n])?this[n](e[n]):this.attr(n,e[n])}else(i=T.getElementById(n[2]))&&(this[0]=i,this.length=1)}return this}).prototype=C.fn,Z=C(T),/^(?:parents|prev(?:Until|All))/),it={children:!0,contents:!0,next:!0,prev:!0};function nt(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}C.fn.extend({has:function(t){var e=C(t,this),i=e.length;return this.filter(function(){for(var t=0;t<i;t++)if(C.contains(this,e[t]))return!0})},closest:function(t,e){var i,n=0,o=this.length,s=[],r="string"!=typeof t&&C(t);if(!Q.test(t))for(;n<o;n++)for(i=this[n];i&&i!==e;i=i.parentNode)if(i.nodeType<11&&(r?-1<r.index(i):1===i.nodeType&&C.find.matchesSelector(i,t))){s.push(i);break}return this.pushStack(1<s.length?C.uniqueSort(s):s)},index:function(t){return t?"string"==typeof t?$.call(C(t),this[0]):$.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(C.uniqueSort(C.merge(this.get(),C(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),C.each({parent:function(t){t=t.parentNode;return t&&11!==t.nodeType?t:null},parents:function(t){return n(t,"parentNode")},parentsUntil:function(t,e,i){return n(t,"parentNode",i)},next:function(t){return nt(t,"nextSibling")},prev:function(t){return nt(t,"previousSibling")},nextAll:function(t){return n(t,"nextSibling")},prevAll:function(t){return n(t,"previousSibling")},nextUntil:function(t,e,i){return n(t,"nextSibling",i)},prevUntil:function(t,e,i){return n(t,"previousSibling",i)},siblings:function(t){return Y((t.parentNode||{}).firstChild,t)},children:function(t){return Y(t.firstChild)},contents:function(t){return null!=t.contentDocument&&W(t.contentDocument)?t.contentDocument:(l(t,"template")&&(t=t.content||t),C.merge([],t.childNodes))}},function(n,o){C.fn[n]=function(t,e){var i=C.map(this,o,t);return(e="Until"!==n.slice(-5)?t:e)&&"string"==typeof e&&(i=C.filter(e,i)),1<this.length&&(it[n]||C.uniqueSort(i),et.test(n))&&i.reverse(),this.pushStack(i)}});var E=/[^\x20\t\r\n\f]+/g;function u(t){return t}function ot(t){throw t}function st(t,e,i,n){var o;try{t&&v(o=t.promise)?o.call(t).done(e).fail(i):t&&v(o=t.then)?o.call(t,e,i):e.apply(void 0,[t].slice(n))}catch(t){i.apply(void 0,[t])}}C.Callbacks=function(n){var t,i;n="string"==typeof n?(t=n,i={},C.each(t.match(E)||[],function(t,e){i[e]=!0}),i):C.extend({},n);function o(){for(a=a||n.once,r=s=!0;c.length;u=-1)for(e=c.shift();++u<l.length;)!1===l[u].apply(e[0],e[1])&&n.stopOnFalse&&(u=l.length,e=!1);n.memory||(e=!1),s=!1,a&&(l=e?[]:"")}var s,e,r,a,l=[],c=[],u=-1,h={add:function(){return l&&(e&&!s&&(u=l.length-1,c.push(e)),function i(t){C.each(t,function(t,e){v(e)?n.unique&&h.has(e)||l.push(e):e&&e.length&&"string"!==p(e)&&i(e)})}(arguments),e)&&!s&&o(),this},remove:function(){return C.each(arguments,function(t,e){for(var i;-1<(i=C.inArray(e,l,i));)l.splice(i,1),i<=u&&u--}),this},has:function(t){return t?-1<C.inArray(t,l):0<l.length},empty:function(){return l=l&&[],this},disable:function(){return a=c=[],l=e="",this},disabled:function(){return!l},lock:function(){return a=c=[],e||s||(l=e=""),this},locked:function(){return!!a},fireWith:function(t,e){return a||(e=[t,(e=e||[]).slice?e.slice():e],c.push(e),s)||o(),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!r}};return h},C.extend({Deferred:function(t){var s=[["notify","progress",C.Callbacks("memory"),C.Callbacks("memory"),2],["resolve","done",C.Callbacks("once memory"),C.Callbacks("once memory"),0,"resolved"],["reject","fail",C.Callbacks("once memory"),C.Callbacks("once memory"),1,"rejected"]],o="pending",r={state:function(){return o},always:function(){return a.done(arguments).fail(arguments),this},catch:function(t){return r.then(null,t)},pipe:function(){var o=arguments;return C.Deferred(function(n){C.each(s,function(t,e){var i=v(o[e[4]])&&o[e[4]];a[e[1]](function(){var t=i&&i.apply(this,arguments);t&&v(t.promise)?t.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[e[0]+"With"](this,i?[t]:arguments)})}),o=null}).promise()},then:function(e,i,n){var l=0;function c(o,s,r,a){return function(){function t(){var t,e;if(!(o<l)){if((t=r.apply(i,n))===s.promise())throw new TypeError("Thenable self-resolution");e=t&&("object"==typeof t||"function"==typeof t)&&t.then,v(e)?a?e.call(t,c(l,s,u,a),c(l,s,ot,a)):(l++,e.call(t,c(l,s,u,a),c(l,s,ot,a),c(l,s,u,s.notifyWith))):(r!==u&&(i=void 0,n=[t]),(a||s.resolveWith)(i,n))}}var i=this,n=arguments,e=a?t:function(){try{t()}catch(t){C.Deferred.exceptionHook&&C.Deferred.exceptionHook(t,e.stackTrace),l<=o+1&&(r!==ot&&(i=void 0,n=[t]),s.rejectWith(i,n))}};o?e():(C.Deferred.getStackHook&&(e.stackTrace=C.Deferred.getStackHook()),w.setTimeout(e))}}return C.Deferred(function(t){s[0][3].add(c(0,t,v(n)?n:u,t.notifyWith)),s[1][3].add(c(0,t,v(e)?e:u)),s[2][3].add(c(0,t,v(i)?i:ot))}).promise()},promise:function(t){return null!=t?C.extend(t,r):r}},a={};return C.each(s,function(t,e){var i=e[2],n=e[5];r[e[1]]=i.add,n&&i.add(function(){o=n},s[3-t][2].disable,s[3-t][3].disable,s[0][2].lock,s[0][3].lock),i.add(e[3].fire),a[e[0]]=function(){return a[e[0]+"With"](this===a?void 0:this,arguments),this},a[e[0]+"With"]=i.fireWith}),r.promise(a),t&&t.call(a,a),a},when:function(t){function e(e){return function(t){o[e]=this,s[e]=1<arguments.length?a.call(arguments):t,--i||r.resolveWith(o,s)}}var i=arguments.length,n=i,o=Array(n),s=a.call(arguments),r=C.Deferred();if(i<=1&&(st(t,r.done(e(n)).resolve,r.reject,!i),"pending"===r.state()||v(s[n]&&s[n].then)))return r.then();for(;n--;)st(s[n],e(n),r.reject);return r.promise()}});var rt=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/,at=(C.Deferred.exceptionHook=function(t,e){w.console&&w.console.warn&&t&&rt.test(t.name)&&w.console.warn("jQuery.Deferred exception: "+t.message,t.stack,e)},C.readyException=function(t){w.setTimeout(function(){throw t})},C.Deferred());function lt(){T.removeEventListener("DOMContentLoaded",lt),w.removeEventListener("load",lt),C.ready()}C.fn.ready=function(t){return at.then(t).catch(function(t){C.readyException(t)}),this},C.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--C.readyWait:C.isReady)||(C.isReady=!0)!==t&&0<--C.readyWait||at.resolveWith(T,[C])}}),C.ready.then=at.then,"complete"===T.readyState||"loading"!==T.readyState&&!T.documentElement.doScroll?w.setTimeout(C.ready):(T.addEventListener("DOMContentLoaded",lt),w.addEventListener("load",lt));function h(t,e,i,n,o,s,r){var a=0,l=t.length,c=null==i;if("object"===p(i))for(a in o=!0,i)h(t,e,a,i[a],!0,s,r);else if(void 0!==n&&(o=!0,v(n)||(r=!0),e=c?r?(e.call(t,n),null):(c=e,function(t,e,i){return c.call(C(t),i)}):e))for(;a<l;a++)e(t[a],i,r?n:n.call(t[a],a,e(t[a],i)));return o?t:c?e.call(t):l?e(t[0],i):s}var ct=/^-ms-/,ut=/-([a-z])/g;function ht(t,e){return e.toUpperCase()}function b(t){return t.replace(ct,"ms-").replace(ut,ht)}function y(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType}function dt(){this.expando=C.expando+dt.uid++}dt.uid=1,dt.prototype={cache:function(t){var e=t[this.expando];return e||(e={},y(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,i){var n,o=this.cache(t);if("string"==typeof e)o[b(e)]=i;else for(n in e)o[b(n)]=e[n];return o},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][b(e)]},access:function(t,e,i){return void 0===e||e&&"string"==typeof e&&void 0===i?this.get(t,e):(this.set(t,e,i),void 0!==i?i:e)},remove:function(t,e){var i,n=t[this.expando];if(void 0!==n){if(void 0!==e){i=(e=Array.isArray(e)?e.map(b):(e=b(e))in n?[e]:e.match(E)||[]).length;for(;i--;)delete n[e[i]]}void 0!==e&&!C.isEmptyObject(n)||(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){t=t[this.expando];return void 0!==t&&!C.isEmptyObject(t)}};var x=new dt,c=new dt,ft=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,pt=/[A-Z]/g;function gt(t,e,i){var n,o;if(void 0===i&&1===t.nodeType)if(n="data-"+e.replace(pt,"-$&").toLowerCase(),"string"==typeof(i=t.getAttribute(n))){try{i="true"===(o=i)||"false"!==o&&("null"===o?null:o===+o+""?+o:ft.test(o)?JSON.parse(o):o)}catch(t){}c.set(t,e,i)}else i=void 0;return i}C.extend({hasData:function(t){return c.hasData(t)||x.hasData(t)},data:function(t,e,i){return c.access(t,e,i)},removeData:function(t,e){c.remove(t,e)},_data:function(t,e,i){return x.access(t,e,i)},_removeData:function(t,e){x.remove(t,e)}}),C.fn.extend({data:function(i,t){var e,n,o,s=this[0],r=s&&s.attributes;if(void 0!==i)return"object"==typeof i?this.each(function(){c.set(this,i)}):h(this,function(t){var e;if(s&&void 0===t)return void 0!==(e=c.get(s,i))||void 0!==(e=gt(s,i))?e:void 0;this.each(function(){c.set(this,i,t)})},null,t,1<arguments.length,null,!0);if(this.length&&(o=c.get(s),1===s.nodeType)&&!x.get(s,"hasDataAttrs")){for(e=r.length;e--;)r[e]&&0===(n=r[e].name).indexOf("data-")&&(n=b(n.slice(5)),gt(s,n,o[n]));x.set(s,"hasDataAttrs",!0)}return o},removeData:function(t){return this.each(function(){c.remove(this,t)})}}),C.extend({queue:function(t,e,i){var n;if(t)return n=x.get(t,e=(e||"fx")+"queue"),i&&(!n||Array.isArray(i)?n=x.access(t,e,C.makeArray(i)):n.push(i)),n||[]},dequeue:function(t,e){e=e||"fx";var i=C.queue(t,e),n=i.length,o=i.shift(),s=C._queueHooks(t,e);"inprogress"===o&&(o=i.shift(),n--),o&&("fx"===e&&i.unshift("inprogress"),delete s.stop,o.call(t,function(){C.dequeue(t,e)},s)),!n&&s&&s.empty.fire()},_queueHooks:function(t,e){var i=e+"queueHooks";return x.get(t,i)||x.access(t,i,{empty:C.Callbacks("once memory").add(function(){x.remove(t,[e+"queue",i])})})}}),C.fn.extend({queue:function(e,i){var t=2;return"string"!=typeof e&&(i=e,e="fx",t--),arguments.length<t?C.queue(this[0],e):void 0===i?this:this.each(function(){var t=C.queue(this,e,i);C._queueHooks(this,e),"fx"===e&&"inprogress"!==t[0]&&C.dequeue(this,e)})},dequeue:function(t){return this.each(function(){C.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){function i(){--o||s.resolveWith(r,[r])}var n,o=1,s=C.Deferred(),r=this,a=this.length;for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";a--;)(n=x.get(r[a],t+"queueHooks"))&&n.empty&&(o++,n.empty.add(i));return i(),s.promise(e)}});function mt(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&_(t)&&"none"===C.css(t,"display")}var t=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,yt=new RegExp("^(?:([+-])=|)("+t+")([a-z%]*)$","i"),d=["Top","Right","Bottom","Left"],S=T.documentElement,_=function(t){return C.contains(t.ownerDocument,t)},vt={composed:!0};S.getRootNode&&(_=function(t){return C.contains(t.ownerDocument,t)||t.getRootNode(vt)===t.ownerDocument});function bt(t,e,i,n){var o,s,r=20,a=n?function(){return n.cur()}:function(){return C.css(t,e,"")},l=a(),c=i&&i[3]||(C.cssNumber[e]?"":"px"),u=t.nodeType&&(C.cssNumber[e]||"px"!==c&&+l)&&yt.exec(C.css(t,e));if(u&&u[3]!==c){for(c=c||u[3],u=+(l/=2)||1;r--;)C.style(t,e,u+c),(1-s)*(1-(s=a()/l||.5))<=0&&(r=0),u/=s;C.style(t,e,(u*=2)+c),i=i||[]}return i&&(u=+u||+l||0,o=i[1]?u+(i[1]+1)*i[2]:+i[2],n)&&(n.unit=c,n.start=u,n.end=o),o}var xt={};function k(t,e){for(var i,n,o,s,r,a,l=[],c=0,u=t.length;c<u;c++)(n=t[c]).style&&(i=n.style.display,e?("none"===i&&(l[c]=x.get(n,"display")||null,l[c]||(n.style.display="")),""===n.style.display&&mt(n)&&(l[c]=(a=s=o=void 0,s=n.ownerDocument,(a=xt[r=n.nodeName])||(o=s.body.appendChild(s.createElement(r)),a=C.css(o,"display"),o.parentNode.removeChild(o),xt[r]=a="none"===a?"block":a)))):"none"!==i&&(l[c]="none",x.set(n,"display",i)));for(c=0;c<u;c++)null!=l[c]&&(t[c].style.display=l[c]);return t}C.fn.extend({show:function(){return k(this,!0)},hide:function(){return k(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){mt(this)?C(this).show():C(this).hide()})}});var St=/^(?:checkbox|radio)$/i,wt=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Tt=/^$|^module$|\/(?:java|ecma)script/i,i=T.createDocumentFragment().appendChild(T.createElement("div")),I=((A=T.createElement("input")).setAttribute("type","radio"),A.setAttribute("checked","checked"),A.setAttribute("name","t"),i.appendChild(A),m.checkClone=i.cloneNode(!0).cloneNode(!0).lastChild.checked,i.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!i.cloneNode(!0).lastChild.defaultValue,i.innerHTML="<option></option>",m.option=!!i.lastChild,{thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]});function P(t,e){var i=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[];return void 0===e||e&&l(t,e)?C.merge([t],i):i}function Ct(t,e){for(var i=0,n=t.length;i<n;i++)x.set(t[i],"globalEval",!e||x.get(e[i],"globalEval"))}I.tbody=I.tfoot=I.colgroup=I.caption=I.thead,I.th=I.td,m.option||(I.optgroup=I.option=[1,"<select multiple='multiple'>","</select>"]);var Et=/<|&#?\w+;/;function _t(t,e,i,n,o){for(var s,r,a,l,c,u=e.createDocumentFragment(),h=[],d=0,f=t.length;d<f;d++)if((s=t[d])||0===s)if("object"===p(s))C.merge(h,s.nodeType?[s]:s);else if(Et.test(s)){for(r=r||u.appendChild(e.createElement("div")),a=(wt.exec(s)||["",""])[1].toLowerCase(),a=I[a]||I._default,r.innerHTML=a[1]+C.htmlPrefilter(s)+a[2],c=a[0];c--;)r=r.lastChild;C.merge(h,r.childNodes),(r=u.firstChild).textContent=""}else h.push(e.createTextNode(s));for(u.textContent="",d=0;s=h[d++];)if(n&&-1<C.inArray(s,n))o&&o.push(s);else if(l=_(s),r=P(u.appendChild(s),"script"),l&&Ct(r),i)for(c=0;s=r[c++];)Tt.test(s.type||"")&&i.push(s);return u}var kt=/^([^.]*)(?:\.(.+)|)/;function r(){return!0}function f(){return!1}function It(t,e){return t===function(){try{return T.activeElement}catch(t){}}()==("focus"===e)}function Pt(t,e,i,n,o,s){var r,a;if("object"==typeof e){for(a in"string"!=typeof i&&(n=n||i,i=void 0),e)Pt(t,a,i,n,e[a],s);return t}if(null==n&&null==o?(o=i,n=i=void 0):null==o&&("string"==typeof i?(o=n,n=void 0):(o=n,n=i,i=void 0)),!1===o)o=f;else if(!o)return t;return 1===s&&(r=o,(o=function(t){return C().off(t),r.apply(this,arguments)}).guid=r.guid||(r.guid=C.guid++)),t.each(function(){C.event.add(this,e,o,n,i)})}function Dt(t,o,s){s?(x.set(t,o,!1),C.event.add(t,o,{namespace:!1,handler:function(t){var e,i,n=x.get(this,o);if(1&t.isTrigger&&this[o]){if(n.length)(C.event.special[o]||{}).delegateType&&t.stopPropagation();else if(n=a.call(arguments),x.set(this,o,n),e=s(this,o),this[o](),n!==(i=x.get(this,o))||e?x.set(this,o,!1):i={},n!==i)return t.stopImmediatePropagation(),t.preventDefault(),i&&i.value}else n.length&&(x.set(this,o,{value:C.event.trigger(C.extend(n[0],C.Event.prototype),n.slice(1),this)}),t.stopImmediatePropagation())}})):void 0===x.get(t,o)&&C.event.add(t,o,r)}C.event={global:{},add:function(e,t,i,n,o){var s,r,a,l,c,u,h,d,f,p=x.get(e);if(y(e))for(i.handler&&(i=(s=i).handler,o=s.selector),o&&C.find.matchesSelector(S,o),i.guid||(i.guid=C.guid++),a=(a=p.events)||(p.events=Object.create(null)),r=(r=p.handle)||(p.handle=function(t){return void 0!==C&&C.event.triggered!==t.type?C.event.dispatch.apply(e,arguments):void 0}),l=(t=(t||"").match(E)||[""]).length;l--;)h=f=(d=kt.exec(t[l])||[])[1],d=(d[2]||"").split(".").sort(),h&&(c=C.event.special[h]||{},h=(o?c.delegateType:c.bindType)||h,c=C.event.special[h]||{},f=C.extend({type:h,origType:f,data:n,handler:i,guid:i.guid,selector:o,needsContext:o&&C.expr.match.needsContext.test(o),namespace:d.join(".")},s),(u=a[h])||((u=a[h]=[]).delegateCount=0,c.setup&&!1!==c.setup.call(e,n,d,r))||e.addEventListener&&e.addEventListener(h,r),c.add&&(c.add.call(e,f),f.handler.guid||(f.handler.guid=i.guid)),o?u.splice(u.delegateCount++,0,f):u.push(f),C.event.global[h]=!0)},remove:function(t,e,i,n,o){var s,r,a,l,c,u,h,d,f,p,g,m=x.hasData(t)&&x.get(t);if(m&&(l=m.events)){for(c=(e=(e||"").match(E)||[""]).length;c--;)if(f=g=(a=kt.exec(e[c])||[])[1],p=(a[2]||"").split(".").sort(),f){for(h=C.event.special[f]||{},d=l[f=(n?h.delegateType:h.bindType)||f]||[],a=a[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),r=s=d.length;s--;)u=d[s],!o&&g!==u.origType||i&&i.guid!==u.guid||a&&!a.test(u.namespace)||n&&n!==u.selector&&("**"!==n||!u.selector)||(d.splice(s,1),u.selector&&d.delegateCount--,h.remove&&h.remove.call(t,u));r&&!d.length&&(h.teardown&&!1!==h.teardown.call(t,p,m.handle)||C.removeEvent(t,f,m.handle),delete l[f])}else for(f in l)C.event.remove(t,f+e[c],i,n,!0);C.isEmptyObject(l)&&x.remove(t,"handle events")}},dispatch:function(t){var e,i,n,o,s,r=new Array(arguments.length),a=C.event.fix(t),t=(x.get(this,"events")||Object.create(null))[a.type]||[],l=C.event.special[a.type]||{};for(r[0]=a,e=1;e<arguments.length;e++)r[e]=arguments[e];if(a.delegateTarget=this,!l.preDispatch||!1!==l.preDispatch.call(this,a)){for(s=C.event.handlers.call(this,a,t),e=0;(n=s[e++])&&!a.isPropagationStopped();)for(a.currentTarget=n.elem,i=0;(o=n.handlers[i++])&&!a.isImmediatePropagationStopped();)a.rnamespace&&!1!==o.namespace&&!a.rnamespace.test(o.namespace)||(a.handleObj=o,a.data=o.data,void 0!==(o=((C.event.special[o.origType]||{}).handle||o.handler).apply(n.elem,r))&&!1===(a.result=o)&&(a.preventDefault(),a.stopPropagation()));return l.postDispatch&&l.postDispatch.call(this,a),a.result}},handlers:function(t,e){var i,n,o,s,r,a=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&1<=t.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(s=[],r={},i=0;i<l;i++)void 0===r[o=(n=e[i]).selector+" "]&&(r[o]=n.needsContext?-1<C(o,this).index(c):C.find(o,this,null,[c]).length),r[o]&&s.push(n);s.length&&a.push({elem:c,handlers:s})}return c=this,l<e.length&&a.push({elem:c,handlers:e.slice(l)}),a},addProp:function(e,t){Object.defineProperty(C.Event.prototype,e,{enumerable:!0,configurable:!0,get:v(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(t){return t[C.expando]?t:new C.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){t=this||t;return St.test(t.type)&&t.click&&l(t,"input")&&Dt(t,"click",r),!1},trigger:function(t){t=this||t;return St.test(t.type)&&t.click&&l(t,"input")&&Dt(t,"click"),!0},_default:function(t){t=t.target;return St.test(t.type)&&t.click&&l(t,"input")&&x.get(t,"click")||l(t,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},C.removeEvent=function(t,e,i){t.removeEventListener&&t.removeEventListener(e,i)},C.Event=function(t,e){if(!(this instanceof C.Event))return new C.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?r:f,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&C.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[C.expando]=!0},C.Event.prototype={constructor:C.Event,isDefaultPrevented:f,isPropagationStopped:f,isImmediatePropagationStopped:f,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=r,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=r,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=r,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},C.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},C.event.addProp),C.each({focus:"focusin",blur:"focusout"},function(t,e){C.event.special[t]={setup:function(){return Dt(this,t,It),!1},trigger:function(){return Dt(this,t),!0},_default:function(){return!0},delegateType:e}}),C.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,o){C.event.special[t]={delegateType:o,bindType:o,handle:function(t){var e,i=t.relatedTarget,n=t.handleObj;return i&&(i===this||C.contains(this,i))||(t.type=n.origType,e=n.handler.apply(this,arguments),t.type=o),e}}}),C.fn.extend({on:function(t,e,i,n){return Pt(this,t,e,i,n)},one:function(t,e,i,n){return Pt(this,t,e,i,n,1)},off:function(t,e,i){var n,o;if(t&&t.preventDefault&&t.handleObj)n=t.handleObj,C(t.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler);else{if("object"!=typeof t)return!1!==e&&"function"!=typeof e||(i=e,e=void 0),!1===i&&(i=f),this.each(function(){C.event.remove(this,t,i,e)});for(o in t)this.off(o,e,t[o])}return this}});var Lt=/<script|<style|<link/i,At=/checked\s*(?:[^=]|=\s*.checked.)/i,zt=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Nt(t,e){return l(t,"table")&&l(11!==e.nodeType?e:e.firstChild,"tr")&&C(t).children("tbody")[0]||t}function Mt(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function jt(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function Wt(t,e){var i,n,o,s;if(1===e.nodeType){if(x.hasData(t)&&(s=x.get(t).events))for(o in x.remove(e,"handle events"),s)for(i=0,n=s[o].length;i<n;i++)C.event.add(e,o,s[o][i]);c.hasData(t)&&(t=c.access(t),t=C.extend({},t),c.set(e,t))}}function D(i,n,o,s){n=O(n);var t,e,r,a,l,c,u=0,h=i.length,d=h-1,f=n[0],p=v(f);if(p||1<h&&"string"==typeof f&&!m.checkClone&&At.test(f))return i.each(function(t){var e=i.eq(t);p&&(n[0]=f.call(this,t,e.html())),D(e,n,o,s)});if(h&&(e=(t=_t(n,i[0].ownerDocument,!1,i,s)).firstChild,1===t.childNodes.length&&(t=e),e||s)){for(a=(r=C.map(P(t,"script"),Mt)).length;u<h;u++)l=t,u!==d&&(l=C.clone(l,!0,!0),a)&&C.merge(r,P(l,"script")),o.call(i[u],l,u);if(a)for(c=r[r.length-1].ownerDocument,C.map(r,jt),u=0;u<a;u++)l=r[u],Tt.test(l.type||"")&&!x.access(l,"globalEval")&&C.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?C._evalUrl&&!l.noModule&&C._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},c):G(l.textContent.replace(zt,""),l,c))}return i}function Ot(t,e,i){for(var n,o=e?C.filter(e,t):t,s=0;null!=(n=o[s]);s++)i||1!==n.nodeType||C.cleanData(P(n)),n.parentNode&&(i&&_(n)&&Ct(P(n,"script")),n.parentNode.removeChild(n));return t}C.extend({htmlPrefilter:function(t){return t},clone:function(t,e,i){var n,o,s,r,a,l,c,u=t.cloneNode(!0),h=_(t);if(!(m.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||C.isXMLDoc(t)))for(r=P(u),n=0,o=(s=P(t)).length;n<o;n++)a=s[n],"input"===(c=(l=r[n]).nodeName.toLowerCase())&&St.test(a.type)?l.checked=a.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=a.defaultValue);if(e)if(i)for(s=s||P(t),r=r||P(u),n=0,o=s.length;n<o;n++)Wt(s[n],r[n]);else Wt(t,u);return 0<(r=P(u,"script")).length&&Ct(r,!h&&P(t,"script")),u},cleanData:function(t){for(var e,i,n,o=C.event.special,s=0;void 0!==(i=t[s]);s++)if(y(i)){if(e=i[x.expando]){if(e.events)for(n in e.events)o[n]?C.event.remove(i,n):C.removeEvent(i,n,e.handle);i[x.expando]=void 0}i[c.expando]&&(i[c.expando]=void 0)}}}),C.fn.extend({detach:function(t){return Ot(this,t,!0)},remove:function(t){return Ot(this,t)},text:function(t){return h(this,function(t){return void 0===t?C.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return D(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Nt(this,t).appendChild(t)})},prepend:function(){return D(this,arguments,function(t){var e;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(e=Nt(this,t)).insertBefore(t,e.firstChild)})},before:function(){return D(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return D(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(C.cleanData(P(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return C.clone(this,t,e)})},html:function(t){return h(this,function(t){var e=this[0]||{},i=0,n=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!Lt.test(t)&&!I[(wt.exec(t)||["",""])[1].toLowerCase()]){t=C.htmlPrefilter(t);try{for(;i<n;i++)1===(e=this[i]||{}).nodeType&&(C.cleanData(P(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var i=[];return D(this,arguments,function(t){var e=this.parentNode;C.inArray(this,i)<0&&(C.cleanData(P(this)),e)&&e.replaceChild(t,this)},i)}}),C.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,r){C.fn[t]=function(t){for(var e,i=[],n=C(t),o=n.length-1,s=0;s<=o;s++)e=s===o?this:this.clone(!0),C(n[s])[r](e),q.apply(i,e.get());return this.pushStack(i)}});function qt(t){var e=t.ownerDocument.defaultView;return(e=e&&e.opener?e:w).getComputedStyle(t)}function $t(t,e,i){var n,o={};for(n in e)o[n]=t.style[n],t.style[n]=e[n];for(n in i=i.call(t),e)t.style[n]=o[n];return i}var Ht,Rt,Bt,Ft,Ut,Vt,Gt,o,Xt=new RegExp("^("+t+")(?!px)[a-z%]+$","i"),Yt=new RegExp(d.join("|"),"i");function Qt(t,e,i){var n,o,s=t.style;return(i=i||qt(t))&&(""!==(o=i.getPropertyValue(e)||i[e])||_(t)||(o=C.style(t,e)),!m.pixelBoxStyles())&&Xt.test(o)&&Yt.test(e)&&(t=s.width,e=s.minWidth,n=s.maxWidth,s.minWidth=s.maxWidth=s.width=o,o=i.width,s.width=t,s.minWidth=e,s.maxWidth=n),void 0!==o?o+"":o}function Jt(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}function Kt(){var t;o&&(Gt.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",o.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",S.appendChild(Gt).appendChild(o),t=w.getComputedStyle(o),Ht="1%"!==t.top,Vt=12===Zt(t.marginLeft),o.style.right="60%",Ft=36===Zt(t.right),Rt=36===Zt(t.width),o.style.position="absolute",Bt=12===Zt(o.offsetWidth/3),S.removeChild(Gt),o=null)}function Zt(t){return Math.round(parseFloat(t))}Gt=T.createElement("div"),(o=T.createElement("div")).style&&(o.style.backgroundClip="content-box",o.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===o.style.backgroundClip,C.extend(m,{boxSizingReliable:function(){return Kt(),Rt},pixelBoxStyles:function(){return Kt(),Ft},pixelPosition:function(){return Kt(),Ht},reliableMarginLeft:function(){return Kt(),Vt},scrollboxSize:function(){return Kt(),Bt},reliableTrDimensions:function(){var t,e,i;return null==Ut&&(t=T.createElement("table"),e=T.createElement("tr"),i=T.createElement("div"),t.style.cssText="position:absolute;left:-11111px;border-collapse:separate",e.style.cssText="border:1px solid",e.style.height="1px",i.style.height="9px",i.style.display="block",S.appendChild(t).appendChild(e).appendChild(i),i=w.getComputedStyle(e),Ut=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===e.offsetHeight,S.removeChild(t)),Ut}}));var te=["Webkit","Moz","ms"],ee=T.createElement("div").style,ie={};function ne(t){return C.cssProps[t]||ie[t]||(t in ee?t:ie[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),i=te.length;i--;)if((t=te[i]+e)in ee)return t}(t)||t)}var oe=/^(none|table(?!-c[ea]).+)/,se=/^--/,re={position:"absolute",visibility:"hidden",display:"block"},ae={letterSpacing:"0",fontWeight:"400"};function le(t,e,i){var n=yt.exec(e);return n?Math.max(0,n[2]-(i||0))+(n[3]||"px"):e}function ce(t,e,i,n,o,s){var r="width"===e?1:0,a=0,l=0;if(i===(n?"border":"content"))return 0;for(;r<4;r+=2)"margin"===i&&(l+=C.css(t,i+d[r],!0,o)),n?("content"===i&&(l-=C.css(t,"padding"+d[r],!0,o)),"margin"!==i&&(l-=C.css(t,"border"+d[r]+"Width",!0,o))):(l+=C.css(t,"padding"+d[r],!0,o),"padding"!==i?l+=C.css(t,"border"+d[r]+"Width",!0,o):a+=C.css(t,"border"+d[r]+"Width",!0,o));return!n&&0<=s&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-s-l-a-.5))||0),l}function ue(t,e,i){var n=qt(t),o=(!m.boxSizingReliable()||i)&&"border-box"===C.css(t,"boxSizing",!1,n),s=o,r=Qt(t,e,n),a="offset"+e[0].toUpperCase()+e.slice(1);if(Xt.test(r)){if(!i)return r;r="auto"}return(!m.boxSizingReliable()&&o||!m.reliableTrDimensions()&&l(t,"tr")||"auto"===r||!parseFloat(r)&&"inline"===C.css(t,"display",!1,n))&&t.getClientRects().length&&(o="border-box"===C.css(t,"boxSizing",!1,n),s=a in t)&&(r=t[a]),(r=parseFloat(r)||0)+ce(t,e,i||(o?"border":"content"),s,n,r)+"px"}function s(t,e,i,n,o){return new s.prototype.init(t,e,i,n,o)}C.extend({cssHooks:{opacity:{get:function(t,e){if(e)return""===(e=Qt(t,"opacity"))?"1":e}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(t,e,i,n){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var o,s,r,a=b(e),l=se.test(e),c=t.style;if(l||(e=ne(a)),r=C.cssHooks[e]||C.cssHooks[a],void 0===i)return r&&"get"in r&&void 0!==(o=r.get(t,!1,n))?o:c[e];"string"==(s=typeof i)&&(o=yt.exec(i))&&o[1]&&(i=bt(t,e,o),s="number"),null!=i&&i==i&&("number"!==s||l||(i+=o&&o[3]||(C.cssNumber[a]?"":"px")),m.clearCloneStyle||""!==i||0!==e.indexOf("background")||(c[e]="inherit"),r&&"set"in r&&void 0===(i=r.set(t,i,n))||(l?c.setProperty(e,i):c[e]=i))}},css:function(t,e,i,n){var o,s=b(e);return se.test(e)||(e=ne(s)),"normal"===(o=void 0===(o=(s=C.cssHooks[e]||C.cssHooks[s])&&"get"in s?s.get(t,!0,i):o)?Qt(t,e,n):o)&&e in ae&&(o=ae[e]),(""===i||i)&&(s=parseFloat(o),!0===i||isFinite(s))?s||0:o}}),C.each(["height","width"],function(t,r){C.cssHooks[r]={get:function(t,e,i){if(e)return!oe.test(C.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?ue(t,r,i):$t(t,re,function(){return ue(t,r,i)})},set:function(t,e,i){var n=qt(t),o=!m.scrollboxSize()&&"absolute"===n.position,s=(o||i)&&"border-box"===C.css(t,"boxSizing",!1,n),i=i?ce(t,r,i,s,n):0;return s&&o&&(i-=Math.ceil(t["offset"+r[0].toUpperCase()+r.slice(1)]-parseFloat(n[r])-ce(t,r,"border",!1,n)-.5)),i&&(s=yt.exec(e))&&"px"!==(s[3]||"px")&&(t.style[r]=e,e=C.css(t,r)),le(0,e,i)}}}),C.cssHooks.marginLeft=Jt(m.reliableMarginLeft,function(t,e){if(e)return(parseFloat(Qt(t,"marginLeft"))||t.getBoundingClientRect().left-$t(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),C.each({margin:"",padding:"",border:"Width"},function(o,s){C.cssHooks[o+s]={expand:function(t){for(var e=0,i={},n="string"==typeof t?t.split(" "):[t];e<4;e++)i[o+d[e]+s]=n[e]||n[e-2]||n[0];return i}},"margin"!==o&&(C.cssHooks[o+s].set=le)}),C.fn.extend({css:function(t,e){return h(this,function(t,e,i){var n,o,s={},r=0;if(Array.isArray(e)){for(n=qt(t),o=e.length;r<o;r++)s[e[r]]=C.css(t,e[r],!1,n);return s}return void 0!==i?C.style(t,e,i):C.css(t,e)},t,e,1<arguments.length)}}),((C.Tween=s).prototype={constructor:s,init:function(t,e,i,n,o,s){this.elem=t,this.prop=i,this.easing=o||C.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=n,this.unit=s||(C.cssNumber[i]?"":"px")},cur:function(){var t=s.propHooks[this.prop];return(t&&t.get?t:s.propHooks._default).get(this)},run:function(t){var e,i=s.propHooks[this.prop];return this.options.duration?this.pos=e=C.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(i&&i.set?i:s.propHooks._default).set(this),this}}).init.prototype=s.prototype,(s.propHooks={_default:{get:function(t){return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(t=C.css(t.elem,t.prop,""))&&"auto"!==t?t:0},set:function(t){C.fx.step[t.prop]?C.fx.step[t.prop](t):1!==t.elem.nodeType||!C.cssHooks[t.prop]&&null==t.elem.style[ne(t.prop)]?t.elem[t.prop]=t.now:C.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=s.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},C.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},C.fx=s.prototype.init,C.fx.step={};var L,he,A,de=/^(?:toggle|show|hide)$/,fe=/queueHooks$/;function pe(){he&&(!1===T.hidden&&w.requestAnimationFrame?w.requestAnimationFrame(pe):w.setTimeout(pe,C.fx.interval),C.fx.tick())}function ge(){return w.setTimeout(function(){L=void 0}),L=Date.now()}function me(t,e){var i,n=0,o={height:t};for(e=e?1:0;n<4;n+=2-e)o["margin"+(i=d[n])]=o["padding"+i]=t;return e&&(o.opacity=o.width=t),o}function ye(t,e,i){for(var n,o=(z.tweeners[e]||[]).concat(z.tweeners["*"]),s=0,r=o.length;s<r;s++)if(n=o[s].call(i,e,t))return n}function z(o,t,e){var i,s,n,r,a,l,c,u=0,h=z.prefilters.length,d=C.Deferred().always(function(){delete f.elem}),f=function(){if(s)return!1;for(var t=L||ge(),t=Math.max(0,p.startTime+p.duration-t),e=1-(t/p.duration||0),i=0,n=p.tweens.length;i<n;i++)p.tweens[i].run(e);return d.notifyWith(o,[p,e,t]),e<1&&n?t:(n||d.notifyWith(o,[p,1,0]),d.resolveWith(o,[p]),!1)},p=d.promise({elem:o,props:C.extend({},t),opts:C.extend(!0,{specialEasing:{},easing:C.easing._default},e),originalProperties:t,originalOptions:e,startTime:L||ge(),duration:e.duration,tweens:[],createTween:function(t,e){e=C.Tween(o,p.opts,t,e,p.opts.specialEasing[t]||p.opts.easing);return p.tweens.push(e),e},stop:function(t){var e=0,i=t?p.tweens.length:0;if(!s){for(s=!0;e<i;e++)p.tweens[e].run(1);t?(d.notifyWith(o,[p,1,0]),d.resolveWith(o,[p,t])):d.rejectWith(o,[p,t])}return this}}),g=p.props,m=g,y=p.opts.specialEasing;for(n in m)if(a=y[r=b(n)],l=m[n],Array.isArray(l)&&(a=l[1],l=m[n]=l[0]),n!==r&&(m[r]=l,delete m[n]),(c=C.cssHooks[r])&&"expand"in c)for(n in l=c.expand(l),delete m[r],l)n in m||(m[n]=l[n],y[n]=a);else y[r]=a;for(;u<h;u++)if(i=z.prefilters[u].call(p,o,g,p.opts))return v(i.stop)&&(C._queueHooks(p.elem,p.opts.queue).stop=i.stop.bind(i)),i;return C.map(g,ye,p),v(p.opts.start)&&p.opts.start.call(o,p),p.progress(p.opts.progress).done(p.opts.done,p.opts.complete).fail(p.opts.fail).always(p.opts.always),C.fx.timer(C.extend(f,{elem:o,anim:p,queue:p.opts.queue})),p}C.Animation=C.extend(z,{tweeners:{"*":[function(t,e){var i=this.createTween(t,e);return bt(i.elem,t,yt.exec(e),i),i}]},tweener:function(t,e){for(var i,n=0,o=(t=v(t)?(e=t,["*"]):t.match(E)).length;n<o;n++)i=t[n],z.tweeners[i]=z.tweeners[i]||[],z.tweeners[i].unshift(e)},prefilters:[function(t,e,i){var n,o,s,r,a,l,c,u="width"in e||"height"in e,h=this,d={},f=t.style,p=t.nodeType&&mt(t),g=x.get(t,"fxshow");for(n in i.queue||(null==(r=C._queueHooks(t,"fx")).unqueued&&(r.unqueued=0,a=r.empty.fire,r.empty.fire=function(){r.unqueued||a()}),r.unqueued++,h.always(function(){h.always(function(){r.unqueued--,C.queue(t,"fx").length||r.empty.fire()})})),e)if(o=e[n],de.test(o)){if(delete e[n],s=s||"toggle"===o,o===(p?"hide":"show")){if("show"!==o||!g||void 0===g[n])continue;p=!0}d[n]=g&&g[n]||C.style(t,n)}if((l=!C.isEmptyObject(e))||!C.isEmptyObject(d))for(n in u&&1===t.nodeType&&(i.overflow=[f.overflow,f.overflowX,f.overflowY],null==(c=g&&g.display)&&(c=x.get(t,"display")),"none"===(u=C.css(t,"display"))&&(c?u=c:(k([t],!0),c=t.style.display||c,u=C.css(t,"display"),k([t]))),"inline"===u||"inline-block"===u&&null!=c)&&"none"===C.css(t,"float")&&(l||(h.done(function(){f.display=c}),null==c&&(u=f.display,c="none"===u?"":u)),f.display="inline-block"),i.overflow&&(f.overflow="hidden",h.always(function(){f.overflow=i.overflow[0],f.overflowX=i.overflow[1],f.overflowY=i.overflow[2]})),l=!1,d)l||(g?"hidden"in g&&(p=g.hidden):g=x.access(t,"fxshow",{display:c}),s&&(g.hidden=!p),p&&k([t],!0),h.done(function(){for(n in p||k([t]),x.remove(t,"fxshow"),d)C.style(t,n,d[n])})),l=ye(p?g[n]:0,n,h),n in g||(g[n]=l.start,p&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?z.prefilters.unshift(t):z.prefilters.push(t)}}),C.speed=function(t,e,i){var n=t&&"object"==typeof t?C.extend({},t):{complete:i||!i&&e||v(t)&&t,duration:t,easing:i&&e||e&&!v(e)&&e};return C.fx.off?n.duration=0:"number"!=typeof n.duration&&(n.duration in C.fx.speeds?n.duration=C.fx.speeds[n.duration]:n.duration=C.fx.speeds._default),null!=n.queue&&!0!==n.queue||(n.queue="fx"),n.old=n.complete,n.complete=function(){v(n.old)&&n.old.call(this),n.queue&&C.dequeue(this,n.queue)},n},C.fn.extend({fadeTo:function(t,e,i,n){return this.filter(mt).css("opacity",0).show().end().animate({opacity:e},t,i,n)},animate:function(e,t,i,n){function o(){var t=z(this,C.extend({},e),r);(s||x.get(this,"finish"))&&t.stop(!0)}var s=C.isEmptyObject(e),r=C.speed(t,i,n);return o.finish=o,s||!1===r.queue?this.each(o):this.queue(r.queue,o)},stop:function(o,t,s){function r(t){var e=t.stop;delete t.stop,e(s)}return"string"!=typeof o&&(s=t,t=o,o=void 0),t&&this.queue(o||"fx",[]),this.each(function(){var t=!0,e=null!=o&&o+"queueHooks",i=C.timers,n=x.get(this);if(e)n[e]&&n[e].stop&&r(n[e]);else for(e in n)n[e]&&n[e].stop&&fe.test(e)&&r(n[e]);for(e=i.length;e--;)i[e].elem!==this||null!=o&&i[e].queue!==o||(i[e].anim.stop(s),t=!1,i.splice(e,1));!t&&s||C.dequeue(this,o)})},finish:function(r){return!1!==r&&(r=r||"fx"),this.each(function(){var t,e=x.get(this),i=e[r+"queue"],n=e[r+"queueHooks"],o=C.timers,s=i?i.length:0;for(e.finish=!0,C.queue(this,r,[]),n&&n.stop&&n.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===r&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<s;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete e.finish})}}),C.each(["toggle","show","hide"],function(t,n){var o=C.fn[n];C.fn[n]=function(t,e,i){return null==t||"boolean"==typeof t?o.apply(this,arguments):this.animate(me(n,!0),t,e,i)}}),C.each({slideDown:me("show"),slideUp:me("hide"),slideToggle:me("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,n){C.fn[t]=function(t,e,i){return this.animate(n,t,e,i)}}),C.timers=[],C.fx.tick=function(){var t,e=0,i=C.timers;for(L=Date.now();e<i.length;e++)(t=i[e])()||i[e]!==t||i.splice(e--,1);i.length||C.fx.stop(),L=void 0},C.fx.timer=function(t){C.timers.push(t),C.fx.start()},C.fx.interval=13,C.fx.start=function(){he||(he=!0,pe())},C.fx.stop=function(){he=null},C.fx.speeds={slow:600,fast:200,_default:400},C.fn.delay=function(n,t){return n=C.fx&&C.fx.speeds[n]||n,this.queue(t=t||"fx",function(t,e){var i=w.setTimeout(t,n);e.stop=function(){w.clearTimeout(i)}})},A=T.createElement("input"),i=T.createElement("select").appendChild(T.createElement("option")),A.type="checkbox",m.checkOn=""!==A.value,m.optSelected=i.selected,(A=T.createElement("input")).value="t",A.type="radio",m.radioValue="t"===A.value;var ve,be=C.expr.attrHandle,xe=(C.fn.extend({attr:function(t,e){return h(this,C.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each(function(){C.removeAttr(this,t)})}}),C.extend({attr:function(t,e,i){var n,o,s=t.nodeType;if(3!==s&&8!==s&&2!==s)return void 0===t.getAttribute?C.prop(t,e,i):(1===s&&C.isXMLDoc(t)||(o=C.attrHooks[e.toLowerCase()]||(C.expr.match.bool.test(e)?ve:void 0)),void 0!==i?null===i?void C.removeAttr(t,e):o&&"set"in o&&void 0!==(n=o.set(t,i,e))?n:(t.setAttribute(e,i+""),i):!(o&&"get"in o&&null!==(n=o.get(t,e)))&&null==(n=C.find.attr(t,e))?void 0:n)},attrHooks:{type:{set:function(t,e){var i;if(!m.radioValue&&"radio"===e&&l(t,"input"))return i=t.value,t.setAttribute("type",e),i&&(t.value=i),e}}},removeAttr:function(t,e){var i,n=0,o=e&&e.match(E);if(o&&1===t.nodeType)for(;i=o[n++];)t.removeAttribute(i)}}),ve={set:function(t,e,i){return!1===e?C.removeAttr(t,i):t.setAttribute(i,i),i}},C.each(C.expr.match.bool.source.match(/\w+/g),function(t,e){var r=be[e]||C.find.attr;be[e]=function(t,e,i){var n,o,s=e.toLowerCase();return i||(o=be[s],be[s]=n,n=null!=r(t,e,i)?s:null,be[s]=o),n}}),/^(?:input|select|textarea|button)$/i),Se=/^(?:a|area)$/i;function N(t){return(t.match(E)||[]).join(" ")}function M(t){return t.getAttribute&&t.getAttribute("class")||""}function we(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(E)||[]}C.fn.extend({prop:function(t,e){return h(this,C.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each(function(){delete this[C.propFix[t]||t]})}}),C.extend({prop:function(t,e,i){var n,o,s=t.nodeType;if(3!==s&&8!==s&&2!==s)return 1===s&&C.isXMLDoc(t)||(e=C.propFix[e]||e,o=C.propHooks[e]),void 0!==i?o&&"set"in o&&void 0!==(n=o.set(t,i,e))?n:t[e]=i:o&&"get"in o&&null!==(n=o.get(t,e))?n:t[e]},propHooks:{tabIndex:{get:function(t){var e=C.find.attr(t,"tabindex");return e?parseInt(e,10):xe.test(t.nodeName)||Se.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.optSelected||(C.propHooks.selected={get:function(t){t=t.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(t){t=t.parentNode;t&&(t.selectedIndex,t.parentNode)&&t.parentNode.selectedIndex}}),C.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){C.propFix[this.toLowerCase()]=this}),C.fn.extend({addClass:function(e){var t,i,n,o,s,r,a=0;if(v(e))return this.each(function(t){C(this).addClass(e.call(this,t,M(this)))});if((t=we(e)).length)for(;i=this[a++];)if(r=M(i),n=1===i.nodeType&&" "+N(r)+" "){for(s=0;o=t[s++];)n.indexOf(" "+o+" ")<0&&(n+=o+" ");r!==(r=N(n))&&i.setAttribute("class",r)}return this},removeClass:function(e){var t,i,n,o,s,r,a=0;if(v(e))return this.each(function(t){C(this).removeClass(e.call(this,t,M(this)))});if(!arguments.length)return this.attr("class","");if((t=we(e)).length)for(;i=this[a++];)if(r=M(i),n=1===i.nodeType&&" "+N(r)+" "){for(s=0;o=t[s++];)for(;-1<n.indexOf(" "+o+" ");)n=n.replace(" "+o+" "," ");r!==(r=N(n))&&i.setAttribute("class",r)}return this},toggleClass:function(o,e){var s=typeof o,r="string"==s||Array.isArray(o);return"boolean"==typeof e&&r?e?this.addClass(o):this.removeClass(o):v(o)?this.each(function(t){C(this).toggleClass(o.call(this,t,M(this),e),e)}):this.each(function(){var t,e,i,n;if(r)for(e=0,i=C(this),n=we(o);t=n[e++];)i.hasClass(t)?i.removeClass(t):i.addClass(t);else void 0!==o&&"boolean"!=s||((t=M(this))&&x.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",!t&&!1!==o&&x.get(this,"__className__")||""))})},hasClass:function(t){for(var e,i=0,n=" "+t+" ";e=this[i++];)if(1===e.nodeType&&-1<(" "+N(M(e))+" ").indexOf(n))return!0;return!1}});function Te(t){t.stopPropagation()}var Ce=/\r/g,Ee=(C.fn.extend({val:function(e){var i,t,n,o=this[0];return arguments.length?(n=v(e),this.each(function(t){1===this.nodeType&&(null==(t=n?e.call(this,t,C(this).val()):e)?t="":"number"==typeof t?t+="":Array.isArray(t)&&(t=C.map(t,function(t){return null==t?"":t+""})),(i=C.valHooks[this.type]||C.valHooks[this.nodeName.toLowerCase()])&&"set"in i&&void 0!==i.set(this,t,"value")||(this.value=t))})):o?(i=C.valHooks[o.type]||C.valHooks[o.nodeName.toLowerCase()])&&"get"in i&&void 0!==(t=i.get(o,"value"))?t:"string"==typeof(t=o.value)?t.replace(Ce,""):null==t?"":t:void 0}}),C.extend({valHooks:{option:{get:function(t){var e=C.find.attr(t,"value");return null!=e?e:N(C.text(t))}},select:{get:function(t){for(var e,i=t.options,n=t.selectedIndex,o="select-one"===t.type,s=o?null:[],r=o?n+1:i.length,a=n<0?r:o?n:0;a<r;a++)if(((e=i[a]).selected||a===n)&&!e.disabled&&(!e.parentNode.disabled||!l(e.parentNode,"optgroup"))){if(e=C(e).val(),o)return e;s.push(e)}return s},set:function(t,e){for(var i,n,o=t.options,s=C.makeArray(e),r=o.length;r--;)((n=o[r]).selected=-1<C.inArray(C.valHooks.option.get(n),s))&&(i=!0);return i||(t.selectedIndex=-1),s}}}}),C.each(["radio","checkbox"],function(){C.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=-1<C.inArray(C(t).val(),e)}},m.checkOn||(C.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}),m.focusin="onfocusin"in w,/^(?:focusinfocus|focusoutblur)$/),_e=(C.extend(C.event,{trigger:function(t,e,i,n){var o,s,r,a,l,c,u,h=[i||T],d=B.call(t,"type")?t.type:t,f=B.call(t,"namespace")?t.namespace.split("."):[],p=u=s=i=i||T;if(3!==i.nodeType&&8!==i.nodeType&&!Ee.test(d+C.event.triggered)&&(-1<d.indexOf(".")&&(d=(f=d.split(".")).shift(),f.sort()),a=d.indexOf(":")<0&&"on"+d,(t=t[C.expando]?t:new C.Event(d,"object"==typeof t&&t)).isTrigger=n?2:3,t.namespace=f.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=i),e=null==e?[t]:C.makeArray(e,[t]),c=C.event.special[d]||{},n||!c.trigger||!1!==c.trigger.apply(i,e))){if(!n&&!c.noBubble&&!g(i)){for(r=c.delegateType||d,Ee.test(r+d)||(p=p.parentNode);p;p=p.parentNode)h.push(p),s=p;s===(i.ownerDocument||T)&&h.push(s.defaultView||s.parentWindow||w)}for(o=0;(p=h[o++])&&!t.isPropagationStopped();)u=p,t.type=1<o?r:c.bindType||d,(l=(x.get(p,"events")||Object.create(null))[t.type]&&x.get(p,"handle"))&&l.apply(p,e),(l=a&&p[a])&&l.apply&&y(p)&&(t.result=l.apply(p,e),!1===t.result)&&t.preventDefault();return t.type=d,n||t.isDefaultPrevented()||c._default&&!1!==c._default.apply(h.pop(),e)||!y(i)||a&&v(i[d])&&!g(i)&&((s=i[a])&&(i[a]=null),C.event.triggered=d,t.isPropagationStopped()&&u.addEventListener(d,Te),i[d](),t.isPropagationStopped()&&u.removeEventListener(d,Te),C.event.triggered=void 0,s)&&(i[a]=s),t.result}},simulate:function(t,e,i){i=C.extend(new C.Event,i,{type:t,isSimulated:!0});C.event.trigger(i,null,e)}}),C.fn.extend({trigger:function(t,e){return this.each(function(){C.event.trigger(t,e,this)})},triggerHandler:function(t,e){var i=this[0];if(i)return C.event.trigger(t,e,i,!0)}}),m.focusin||C.each({focus:"focusin",blur:"focusout"},function(i,n){function o(t){C.event.simulate(n,t.target,C.event.fix(t))}C.event.special[n]={setup:function(){var t=this.ownerDocument||this.document||this,e=x.access(t,n);e||t.addEventListener(i,o,!0),x.access(t,n,(e||0)+1)},teardown:function(){var t=this.ownerDocument||this.document||this,e=x.access(t,n)-1;e?x.access(t,n,e):(t.removeEventListener(i,o,!0),x.remove(t,n))}}}),w.location),ke={guid:Date.now()},Ie=/\?/,Pe=(C.parseXML=function(t){var e,i;if(!t||"string"!=typeof t)return null;try{e=(new w.DOMParser).parseFromString(t,"text/xml")}catch(t){}return i=e&&e.getElementsByTagName("parsererror")[0],e&&!i||C.error("Invalid XML: "+(i?C.map(i.childNodes,function(t){return t.textContent}).join("\n"):t)),e},/\[\]$/),De=/\r?\n/g,Le=/^(?:submit|button|image|reset|file)$/i,Ae=/^(?:input|select|textarea|keygen)/i;C.param=function(t,e){function i(t,e){e=v(e)?e():e,o[o.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==e?"":e)}var n,o=[];if(null==t)return"";if(Array.isArray(t)||t.jquery&&!C.isPlainObject(t))C.each(t,function(){i(this.name,this.value)});else for(n in t)!function i(n,t,o,s){if(Array.isArray(t))C.each(t,function(t,e){o||Pe.test(n)?s(n,e):i(n+"["+("object"==typeof e&&null!=e?t:"")+"]",e,o,s)});else if(o||"object"!==p(t))s(n,t);else for(var e in t)i(n+"["+e+"]",t[e],o,s)}(n,t[n],e,i);return o.join("&")},C.fn.extend({serialize:function(){return C.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=C.prop(this,"elements");return t?C.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!C(this).is(":disabled")&&Ae.test(this.nodeName)&&!Le.test(t)&&(this.checked||!St.test(t))}).map(function(t,e){var i=C(this).val();return null==i?null:Array.isArray(i)?C.map(i,function(t){return{name:e.name,value:t.replace(De,"\r\n")}}):{name:e.name,value:i.replace(De,"\r\n")}}).get()}});var ze=/%20/g,Ne=/#.*$/,Me=/([?&])_=[^&]*/,je=/^(.*?):[ \t]*([^\r\n]*)$/gm,We=/^(?:GET|HEAD)$/,Oe=/^\/\//,qe={},$e={},He="*/".concat("*"),Re=T.createElement("a");function Be(s){return function(t,e){"string"!=typeof t&&(e=t,t="*");var i,n=0,o=t.toLowerCase().match(E)||[];if(v(e))for(;i=o[n++];)"+"===i[0]?(i=i.slice(1)||"*",(s[i]=s[i]||[]).unshift(e)):(s[i]=s[i]||[]).push(e)}}function Fe(e,n,o,s){var r={},a=e===$e;function l(t){var i;return r[t]=!0,C.each(e[t]||[],function(t,e){e=e(n,o,s);return"string"!=typeof e||a||r[e]?a?!(i=e):void 0:(n.dataTypes.unshift(e),l(e),!1)}),i}return l(n.dataTypes[0])||!r["*"]&&l("*")}function Ue(t,e){var i,n,o=C.ajaxSettings.flatOptions||{};for(i in e)void 0!==e[i]&&((o[i]?t:n=n||{})[i]=e[i]);return n&&C.extend(!0,t,n),t}Re.href=_e.href,C.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:_e.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(_e.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":He,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":C.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Ue(Ue(t,C.ajaxSettings),e):Ue(C.ajaxSettings,t)},ajaxPrefilter:Be(qe),ajaxTransport:Be($e),ajax:function(t,e){"object"==typeof t&&(e=t,t=void 0);var l,c,u,i,h,d,f,n,o,p=C.ajaxSetup({},e=e||{}),g=p.context||p,m=p.context&&(g.nodeType||g.jquery)?C(g):C.event,y=C.Deferred(),v=C.Callbacks("once memory"),b=p.statusCode||{},s={},r={},a="canceled",x={readyState:0,getResponseHeader:function(t){var e;if(d){if(!i)for(i={};e=je.exec(u);)i[e[1].toLowerCase()+" "]=(i[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=i[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return d?u:null},setRequestHeader:function(t,e){return null==d&&(t=r[t.toLowerCase()]=r[t.toLowerCase()]||t,s[t]=e),this},overrideMimeType:function(t){return null==d&&(p.mimeType=t),this},statusCode:function(t){if(t)if(d)x.always(t[x.status]);else for(var e in t)b[e]=[b[e],t[e]];return this},abort:function(t){t=t||a;return l&&l.abort(t),S(0,t),this}};if(y.promise(x),p.url=((t||p.url||_e.href)+"").replace(Oe,_e.protocol+"//"),p.type=e.method||e.type||p.method||p.type,p.dataTypes=(p.dataType||"*").toLowerCase().match(E)||[""],null==p.crossDomain){o=T.createElement("a");try{o.href=p.url,o.href=o.href,p.crossDomain=Re.protocol+"//"+Re.host!=o.protocol+"//"+o.host}catch(t){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!=typeof p.data&&(p.data=C.param(p.data,p.traditional)),Fe(qe,p,e,x),!d){for(n in(f=C.event&&p.global)&&0==C.active++&&C.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!We.test(p.type),c=p.url.replace(Ne,""),p.hasContent?p.data&&p.processData&&0===(p.contentType||"").indexOf("application/x-www-form-urlencoded")&&(p.data=p.data.replace(ze,"+")):(o=p.url.slice(c.length),p.data&&(p.processData||"string"==typeof p.data)&&(c+=(Ie.test(c)?"&":"?")+p.data,delete p.data),!1===p.cache&&(c=c.replace(Me,"$1"),o=(Ie.test(c)?"&":"?")+"_="+ke.guid+++o),p.url=c+o),p.ifModified&&(C.lastModified[c]&&x.setRequestHeader("If-Modified-Since",C.lastModified[c]),C.etag[c])&&x.setRequestHeader("If-None-Match",C.etag[c]),(p.data&&p.hasContent&&!1!==p.contentType||e.contentType)&&x.setRequestHeader("Content-Type",p.contentType),x.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+He+"; q=0.01":""):p.accepts["*"]),p.headers)x.setRequestHeader(n,p.headers[n]);if(p.beforeSend&&(!1===p.beforeSend.call(g,x,p)||d))return x.abort();if(a="abort",v.add(p.complete),x.done(p.success),x.fail(p.error),l=Fe($e,p,e,x)){if(x.readyState=1,f&&m.trigger("ajaxSend",[x,p]),d)return x;p.async&&0<p.timeout&&(h=w.setTimeout(function(){x.abort("timeout")},p.timeout));try{d=!1,l.send(s,S)}catch(t){if(d)throw t;S(-1,t)}}else S(-1,"No Transport")}return x;function S(t,e,i,n){var o,s,r,a=e;d||(d=!0,h&&w.clearTimeout(h),l=void 0,u=n||"",x.readyState=0<t?4:0,n=200<=t&&t<300||304===t,i&&(r=function(t,e,i){for(var n,o,s,r,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===n&&(n=t.mimeType||e.getResponseHeader("Content-Type"));if(n)for(o in a)if(a[o]&&a[o].test(n)){l.unshift(o);break}if(l[0]in i)s=l[0];else{for(o in i){if(!l[0]||t.converters[o+" "+l[0]]){s=o;break}r=r||o}s=s||r}if(s)return s!==l[0]&&l.unshift(s),i[s]}(p,x,i)),!n&&-1<C.inArray("script",p.dataTypes)&&C.inArray("json",p.dataTypes)<0&&(p.converters["text script"]=function(){}),r=function(t,e,i,n){var o,s,r,a,l,c={},u=t.dataTypes.slice();if(u[1])for(r in t.converters)c[r.toLowerCase()]=t.converters[r];for(s=u.shift();s;)if(t.responseFields[s]&&(i[t.responseFields[s]]=e),!l&&n&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=s,s=u.shift())if("*"===s)s=l;else if("*"!==l&&l!==s){if(!(r=c[l+" "+s]||c["* "+s]))for(o in c)if((a=o.split(" "))[1]===s&&(r=c[l+" "+a[0]]||c["* "+a[0]])){!0===r?r=c[o]:!0!==c[o]&&(s=a[0],u.unshift(a[1]));break}if(!0!==r)if(r&&t.throws)e=r(e);else try{e=r(e)}catch(t){return{state:"parsererror",error:r?t:"No conversion from "+l+" to "+s}}}return{state:"success",data:e}}(p,r,x,n),n?(p.ifModified&&((i=x.getResponseHeader("Last-Modified"))&&(C.lastModified[c]=i),i=x.getResponseHeader("etag"))&&(C.etag[c]=i),204===t||"HEAD"===p.type?a="nocontent":304===t?a="notmodified":(a=r.state,o=r.data,n=!(s=r.error))):(s=a,!t&&a||(a="error",t<0&&(t=0))),x.status=t,x.statusText=(e||a)+"",n?y.resolveWith(g,[o,a,x]):y.rejectWith(g,[x,a,s]),x.statusCode(b),b=void 0,f&&m.trigger(n?"ajaxSuccess":"ajaxError",[x,p,n?o:s]),v.fireWith(g,[x,a]),f&&(m.trigger("ajaxComplete",[x,p]),--C.active||C.event.trigger("ajaxStop")))}},getJSON:function(t,e,i){return C.get(t,e,i,"json")},getScript:function(t,e){return C.get(t,void 0,e,"script")}}),C.each(["get","post"],function(t,o){C[o]=function(t,e,i,n){return v(e)&&(n=n||i,i=e,e=void 0),C.ajax(C.extend({url:t,type:o,dataType:n,data:e,success:i},C.isPlainObject(t)&&t))}}),C.ajaxPrefilter(function(t){for(var e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")}),C._evalUrl=function(t,e,i){return C.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){C.globalEval(t,e,i)}})},C.fn.extend({wrapAll:function(t){return this[0]&&(v(t)&&(t=t.call(this[0])),t=C(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(i){return v(i)?this.each(function(t){C(this).wrapInner(i.call(this,t))}):this.each(function(){var t=C(this),e=t.contents();e.length?e.wrapAll(i):t.append(i)})},wrap:function(e){var i=v(e);return this.each(function(t){C(this).wrapAll(i?e.call(this,t):e)})},unwrap:function(t){return this.parent(t).not("body").each(function(){C(this).replaceWith(this.childNodes)}),this}}),C.expr.pseudos.hidden=function(t){return!C.expr.pseudos.visible(t)},C.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},C.ajaxSettings.xhr=function(){try{return new w.XMLHttpRequest}catch(t){}};var Ve={0:200,1223:204},Ge=C.ajaxSettings.xhr();m.cors=!!Ge&&"withCredentials"in Ge,m.ajax=Ge=!!Ge,C.ajaxTransport(function(o){var s,r;if(m.cors||Ge&&!o.crossDomain)return{send:function(t,e){var i,n=o.xhr();if(n.open(o.type,o.url,o.async,o.username,o.password),o.xhrFields)for(i in o.xhrFields)n[i]=o.xhrFields[i];for(i in o.mimeType&&n.overrideMimeType&&n.overrideMimeType(o.mimeType),o.crossDomain||t["X-Requested-With"]||(t["X-Requested-With"]="XMLHttpRequest"),t)n.setRequestHeader(i,t[i]);s=function(t){return function(){s&&(s=r=n.onload=n.onerror=n.onabort=n.ontimeout=n.onreadystatechange=null,"abort"===t?n.abort():"error"===t?"number"!=typeof n.status?e(0,"error"):e(n.status,n.statusText):e(Ve[n.status]||n.status,n.statusText,"text"!==(n.responseType||"text")||"string"!=typeof n.responseText?{binary:n.response}:{text:n.responseText},n.getAllResponseHeaders()))}},n.onload=s(),r=n.onerror=n.ontimeout=s("error"),void 0!==n.onabort?n.onabort=r:n.onreadystatechange=function(){4===n.readyState&&w.setTimeout(function(){s&&r()})},s=s("abort");try{n.send(o.hasContent&&o.data||null)}catch(t){if(s)throw t}},abort:function(){s&&s()}}}),C.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),C.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return C.globalEval(t),t}}}),C.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),C.ajaxTransport("script",function(i){var n,o;if(i.crossDomain||i.scriptAttrs)return{send:function(t,e){n=C("<script>").attr(i.scriptAttrs||{}).prop({charset:i.scriptCharset,src:i.url}).on("load error",o=function(t){n.remove(),o=null,t&&e("error"===t.type?404:200,t.type)}),T.head.appendChild(n[0])},abort:function(){o&&o()}}});var Xe=[],Ye=/(=)\?(?=&|$)|\?\?/,Qe=(C.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Xe.pop()||C.expando+"_"+ke.guid++;return this[t]=!0,t}}),C.ajaxPrefilter("json jsonp",function(t,e,i){var n,o,s,r=!1!==t.jsonp&&(Ye.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&Ye.test(t.data)&&"data");if(r||"jsonp"===t.dataTypes[0])return n=t.jsonpCallback=v(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,r?t[r]=t[r].replace(Ye,"$1"+n):!1!==t.jsonp&&(t.url+=(Ie.test(t.url)?"&":"?")+t.jsonp+"="+n),t.converters["script json"]=function(){return s||C.error(n+" was not called"),s[0]},t.dataTypes[0]="json",o=w[n],w[n]=function(){s=arguments},i.always(function(){void 0===o?C(w).removeProp(n):w[n]=o,t[n]&&(t.jsonpCallback=e.jsonpCallback,Xe.push(n)),s&&v(o)&&o(s[0]),s=o=void 0}),"script"}),m.createHTMLDocument=((t=T.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===t.childNodes.length),C.parseHTML=function(t,e,i){return"string"!=typeof t?[]:("boolean"==typeof e&&(i=e,e=!1),e||(m.createHTMLDocument?((n=(e=T.implementation.createHTMLDocument("")).createElement("base")).href=T.location.href,e.head.appendChild(n)):e=T),n=!i&&[],(i=J.exec(t))?[e.createElement(i[1])]:(i=_t([t],e,n),n&&n.length&&C(n).remove(),C.merge([],i.childNodes)));var n},C.fn.load=function(t,e,i){var n,o,s,r=this,a=t.indexOf(" ");return-1<a&&(n=N(t.slice(a)),t=t.slice(0,a)),v(e)?(i=e,e=void 0):e&&"object"==typeof e&&(o="POST"),0<r.length&&C.ajax({url:t,type:o||"GET",dataType:"html",data:e}).done(function(t){s=arguments,r.html(n?C("<div>").append(C.parseHTML(t)).find(n):t)}).always(i&&function(t,e){r.each(function(){i.apply(this,s||[t.responseText,e,t])})}),this},C.expr.pseudos.animated=function(e){return C.grep(C.timers,function(t){return e===t.elem}).length},C.offset={setOffset:function(t,e,i){var n,o,s,r,a=C.css(t,"position"),l=C(t),c={};"static"===a&&(t.style.position="relative"),s=l.offset(),n=C.css(t,"top"),r=C.css(t,"left"),a=("absolute"===a||"fixed"===a)&&-1<(n+r).indexOf("auto")?(o=(a=l.position()).top,a.left):(o=parseFloat(n)||0,parseFloat(r)||0),null!=(e=v(e)?e.call(t,i,C.extend({},s)):e).top&&(c.top=e.top-s.top+o),null!=e.left&&(c.left=e.left-s.left+a),"using"in e?e.using.call(t,c):l.css(c)}},C.fn.extend({offset:function(e){var t,i;return arguments.length?void 0===e?this:this.each(function(t){C.offset.setOffset(this,e,t)}):(i=this[0])?i.getClientRects().length?(t=i.getBoundingClientRect(),i=i.ownerDocument.defaultView,{top:t.top+i.pageYOffset,left:t.left+i.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,i,n=this[0],o={top:0,left:0};if("fixed"===C.css(n,"position"))e=n.getBoundingClientRect();else{for(e=this.offset(),i=n.ownerDocument,t=n.offsetParent||i.documentElement;t&&(t===i.body||t===i.documentElement)&&"static"===C.css(t,"position");)t=t.parentNode;t&&t!==n&&1===t.nodeType&&((o=C(t).offset()).top+=C.css(t,"borderTopWidth",!0),o.left+=C.css(t,"borderLeftWidth",!0))}return{top:e.top-o.top-C.css(n,"marginTop",!0),left:e.left-o.left-C.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===C.css(t,"position");)t=t.offsetParent;return t||S})}}),C.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,o){var s="pageYOffset"===o;C.fn[e]=function(t){return h(this,function(t,e,i){var n;if(g(t)?n=t:9===t.nodeType&&(n=t.defaultView),void 0===i)return n?n[o]:t[e];n?n.scrollTo(s?n.pageXOffset:i,s?i:n.pageYOffset):t[e]=i},e,t,arguments.length)}}),C.each(["top","left"],function(t,i){C.cssHooks[i]=Jt(m.pixelPosition,function(t,e){if(e)return e=Qt(t,i),Xt.test(e)?C(t).position()[i]+"px":e})}),C.each({Height:"height",Width:"width"},function(r,a){C.each({padding:"inner"+r,content:a,"":"outer"+r},function(n,s){C.fn[s]=function(t,e){var i=arguments.length&&(n||"boolean"!=typeof t),o=n||(!0===t||!0===e?"margin":"border");return h(this,function(t,e,i){var n;return g(t)?0===s.indexOf("outer")?t["inner"+r]:t.document.documentElement["client"+r]:9===t.nodeType?(n=t.documentElement,Math.max(t.body["scroll"+r],n["scroll"+r],t.body["offset"+r],n["offset"+r],n["client"+r])):void 0===i?C.css(t,e,o):C.style(t,e,i,o)},a,i?t:void 0,i)}})}),C.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){C.fn[e]=function(t){return this.on(e,t)}}),C.fn.extend({bind:function(t,e,i){return this.on(t,null,e,i)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,i,n){return this.on(e,t,i,n)},undelegate:function(t,e,i){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",i)},hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}}),C.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,i){C.fn[i]=function(t,e){return 0<arguments.length?this.on(i,null,t,e):this.trigger(i)}}),/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g),Je=(C.proxy=function(t,e){var i,n;if("string"==typeof e&&(n=t[e],e=t,t=n),v(t))return i=a.call(arguments,2),(n=function(){return t.apply(e||this,i.concat(a.call(arguments)))}).guid=t.guid=t.guid||C.guid++,n},C.holdReady=function(t){t?C.readyWait++:C.ready(!0)},C.isArray=Array.isArray,C.parseJSON=JSON.parse,C.nodeName=l,C.isFunction=v,C.isWindow=g,C.camelCase=b,C.type=p,C.now=Date.now,C.isNumeric=function(t){var e=C.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},C.trim=function(t){return null==t?"":(t+"").replace(Qe,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return C}),w.jQuery),Ke=w.$;return C.noConflict=function(t){return w.$===C&&(w.$=Ke),t&&w.jQuery===C&&(w.jQuery=Je),C},void 0===j&&(w.jQuery=w.$=C),C}),jQuery),jsBdT4s=(!function(t){var e,i="",n=(screen.width&&(width=screen.width||"",height=screen.height||"",i+=width+" x "+height),navigator.appVersion),o=navigator.userAgent,s=navigator.appName,r=""+parseFloat(navigator.appVersion),a=parseInt(navigator.appVersion,10),l=(-1!=(l=o.indexOf("Opera"))&&(s="Opera",r=o.substring(l+6),-1!=(l=o.indexOf("Version")))&&(r=o.substring(l+8)),-1!=(l=o.indexOf("OPR"))?(s="Opera",r=o.substring(l+4)):-1!=(l=o.indexOf("Edge"))?(s="Edge",r=o.substring(l+5)):-1!=(l=o.indexOf("MSIE"))?(s="Internet",r=o.substring(l+5)):-1!=(l=o.indexOf("Chrome"))?(s="Chrome",r=o.substring(l+7)):-1!=(l=o.indexOf("Safari"))?(s="Safari",r=o.substring(l+7),-1!=(l=o.indexOf("Version"))&&(r=o.substring(l+8))):-1!=(l=o.indexOf("Firefox"))?(s="Firefox",r=o.substring(l+8)):-1!=o.indexOf("Trident/")?(s="Internet",r=o.substring(o.indexOf("rv:")+3)):(d=o.lastIndexOf(" ")+1)<(l=o.lastIndexOf("/"))&&(s=o.substring(d,l),r=o.substring(l+1),s.toLowerCase()==s.toUpperCase())&&(s=navigator.appName),-1!=(d=(r=-1!=(d=(r=-1!=(d=r.indexOf(";"))?r.substring(0,d):r).indexOf(" "))?r.substring(0,d):r).indexOf(")"))&&(r=r.substring(0,d)),a=parseInt(""+r,10),isNaN(a)&&(r=""+parseFloat(navigator.appVersion),a=parseInt(navigator.appVersion,10)),/Mobile|mini|Fennec|Android|iP(ad|od|hone)/.test(n)),c="-",u=[{s:"Windows 10",r:/(Windows 10.0|Windows NT 10.0)/},{s:"Windows 8.1",r:/(Windows 8.1|Windows NT 6.3)/},{s:"Windows 8",r:/(Windows 8|Windows NT 6.2)/},{s:"Windows 7",r:/(Windows 7|Windows NT 6.1)/},{s:"Windows Vista",r:/Windows NT 6.0/},{s:"Windows Server 2003",r:/Windows NT 5.2/},{s:"Windows XP",r:/(Windows NT 5.1|Windows XP)/},{s:"Windows 2000",r:/(Windows NT 5.0|Windows 2000)/},{s:"Windows ME",r:/(Win 9x 4.90|Windows ME)/},{s:"Windows 98",r:/(Windows 98|Win98)/},{s:"Windows 95",r:/(Windows 95|Win95|Windows_95)/},{s:"Windows NT 4.0",r:/(Windows NT 4.0|WinNT4.0|WinNT|Windows NT)/},{s:"Windows CE",r:/Windows CE/},{s:"Windows 3.11",r:/Win16/},{s:"Android",r:/Android/},{s:"Open BSD",r:/OpenBSD/},{s:"Sun OS",r:/SunOS/},{s:"Linux",r:/(Linux|X11)/},{s:"iOS",r:/(iPhone|iPad|iPod)/},{s:"Mac OS X",r:/Mac OS X/},{s:"Mac OS",r:/(MacPPC|MacIntel|Mac_PowerPC|Macintosh)/},{s:"QNX",r:/QNX/},{s:"UNIX",r:/UNIX/},{s:"BeOS",r:/BeOS/},{s:"OS/2",r:/OS\/2/},{s:"Search Bot",r:/(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask Jeeves\/Teoma|ia_archiver)/}];for(e in u){var h=u[e];if(h.r.test(o)){c=h.s;break}}c=/Windows/.test(c)?"Windows":c;var d="no check";"undefined"!=typeof swfobject&&(d=0<(n=swfobject.getFlashPlayerVersion()).major?n.major+"."+n.minor+" r"+n.release:"-"),t.jscd={screen:i,browser:s,browserVersion:r,browserMajorVersion:a,mobile:l,os:c,osVersion:"-",flashVersion:d}}(this),jQuery_T4NT("html").addClass("t4s-browser-"+jscd.browser+" t4s-platform-"+jscd.os),document.getElementsByTagName("HTML")[0]),RtlT4s=jsBdT4s.classList.contains("rtl_true"),LtrT4s=!RtlT4s,CookiesT4=(!function(e,i){"function"==typeof define&&define.amd?define("jQuery_T4NT-bridget/jQuery_T4NT-bridget",["jQuery_T4NT"],function(t){return i(e,t)}):"object"==typeof module&&module.exports?module.exports=i(e,require("jQuery_T4NT")):e.jQuery_T4NTBridget=i(e,e.jQuery_T4NT)}(window,function(t,e){"use strict";function i(l,c,u){(u=u||e||t.jQuery_T4NT)&&(c.prototype.option||(c.prototype.option=function(t){u.isPlainObject(t)&&(this.options=u.extend(!0,this.options,t))}),u.fn[l]=function(t){return"string"==typeof t?(e=this,o=t,s=h.call(arguments,1),a="$()."+l+'("'+o+'")',e.each(function(t,e){var i,e=u.data(e,l);e?(i=e[o])&&"_"!=o.charAt(0)?(i=i.apply(e,s),r=void 0===r?i:r):d(a+" is not a valid method"):d(l+" not initialized. Cannot call methods, i.e. "+a)}),void 0!==r?r:e):(n=t,this.each(function(t,e){var i=u.data(e,l);i?(i.option(n),i._init()):(i=new c(e,n),u.data(e,l,i))}),this);var n,e,o,s,r,a},n(u))}function n(t){t&&!t.bridget&&(t.bridget=i)}var h=Array.prototype.slice,o=t.console,d=void 0===o?function(){}:function(t){o.error(t)};return n(e||t.jQuery_T4NT),i}),function(t,e){"function"==typeof define&&define.amd?define("ev-emitter/ev-emitter",e):"object"==typeof module&&module.exports?module.exports=e():t.EvEmitter=e()}("undefined"!=typeof window?window:this,function(){function t(){}var e=t.prototype;return e.on=function(t,e){var i;if(t&&e)return-1==(i=(i=this._events=this._events||{})[t]=i[t]||[]).indexOf(e)&&i.push(e),this},e.once=function(t,e){var i;if(t&&e)return this.on(t,e),((i=this._onceEvents=this._onceEvents||{})[t]=i[t]||{})[e]=!0,this},e.off=function(t,e){t=this._events&&this._events[t];if(t&&t.length)return-1!=(e=t.indexOf(e))&&t.splice(e,1),this},e.emitEvent=function(t,e){var i=this._events&&this._events[t];if(i&&i.length){i=i.slice(0),e=e||[];for(var n=this._onceEvents&&this._onceEvents[t],o=0;o<i.length;o++){var s=i[o];n&&n[s]&&(this.off(t,s),delete n[s]),s.apply(this,e)}return this}},e.allOff=function(){delete this._events,delete this._onceEvents},t}),function(t,e){"function"==typeof define&&define.amd?define("get-size/get-size",e):"object"==typeof module&&module.exports?module.exports=e():t.getSize=e()}(window,function(){"use strict";function m(t){var e=parseFloat(t);return-1==t.indexOf("%")&&!isNaN(e)&&e}function y(t){t=getComputedStyle(t);return t||e("Style returned "+t+". Are you running this code in a hidden iframe on Firefox? See https://bit.ly/getsizebug1"),t}function v(t){if(w||(w=!0,(u=document.createElement("div")).style.width="200px",u.style.padding="1px 2px 3px 4px",u.style.borderStyle="solid",u.style.borderWidth="1px 2px 3px 4px",u.style.boxSizing="border-box",(c=document.body||document.documentElement).appendChild(u),s=y(u),b=200==Math.round(m(s.width)),v.isBoxSizeOuter=b,c.removeChild(u)),(t="string"==typeof t?document.querySelector(t):t)&&"object"==typeof t&&t.nodeType){var e=y(t);if("none"==e.display){for(var i={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},n=0;n<S;n++)i[x[n]]=0;return i}var o={};o.width=t.offsetWidth,o.height=t.offsetHeight;for(var s=o.isBorderBox="border-box"==e.boxSizing,r=0;r<S;r++){var a=x[r],l=e[a],l=parseFloat(l);o[a]=isNaN(l)?0:l}var c=o.paddingLeft+o.paddingRight,u=o.paddingTop+o.paddingBottom,t=o.marginLeft+o.marginRight,h=o.marginTop+o.marginBottom,d=o.borderLeftWidth+o.borderRightWidth,f=o.borderTopWidth+o.borderBottomWidth,p=s&&b,g=m(e.width),g=(!1!==g&&(o.width=g+(p?0:c+d)),m(e.height));return!1!==g&&(o.height=g+(p?0:u+f)),o.innerWidth=o.width-(c+d),o.innerHeight=o.height-(u+f),o.outerWidth=o.width+t,o.outerHeight=o.height+h,o}var u,c,s}var b,e="undefined"==typeof console?function(){}:function(t){console.error(t)},x=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],S=x.length,w=!1;return v}),function(t,e){"use strict";"function"==typeof define&&define.amd?define("desandro-matches-selector/matches-selector",e):"object"==typeof module&&module.exports?module.exports=e():t.matchesSelector=e()}(window,function(){"use strict";var i=function(){var t=window.Element.prototype;if(t.matches)return"matches";if(t.matchesSelector)return"matchesSelector";for(var e=["webkit","moz","ms","o"],i=0;i<e.length;i++){var n=e[i]+"MatchesSelector";if(t[n])return n}}();return function(t,e){return t[i](e)}}),function(e,i){"function"==typeof define&&define.amd?define("fizzy-ui-utils/utils",["desandro-matches-selector/matches-selector"],function(t){return i(e,t)}):"object"==typeof module&&module.exports?module.exports=i(e,require("desandro-matches-selector")):e.fizzyUIUtils=i(e,e.matchesSelector)}(window,function(i,s){var l={extend:function(t,e){for(var i in e)t[i]=e[i];return t},modulo:function(t,e){return(t%e+e)%e}},e=Array.prototype.slice,c=(l.makeArray=function(t){return Array.isArray(t)?t:null==t?[]:"object"==typeof t&&"number"==typeof t.length?e.call(t):[t]},l.removeFrom=function(t,e){e=t.indexOf(e);-1!=e&&t.splice(e,1)},l.getParent=function(t,e){for(;t.parentNode&&t!=document.body;)if(s(t=t.parentNode,e))return t},l.getQueryElement=function(t){return"string"==typeof t?document.querySelector(t):t},l.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},l.filterFindElements=function(t,n){t=l.makeArray(t);var o=[];return t.forEach(function(t){if(t instanceof HTMLElement)if(n){s(t,n)&&o.push(t);for(var e=t.querySelectorAll(n),i=0;i<e.length;i++)o.push(e[i])}else o.push(t)}),o},l.debounceMethod=function(t,e,n){n=n||100;var o=t.prototype[e],s=e+"Timeout";t.prototype[e]=function(){var t=this[s],e=(clearTimeout(t),arguments),i=this;this[s]=setTimeout(function(){o.apply(i,e),delete i[s]},n)}},l.docReady=function(t){var e=document.readyState;"complete"==e||"interactive"==e?setTimeout(t):document.addEventListener("DOMContentLoaded",t)},l.toDashed=function(t){return t.replace(/(.)([A-Z])/g,function(t,e,i){return e+"-"+i}).toLowerCase()},i.console);return l.htmlInit=function(r,a){l.docReady(function(){var t=l.toDashed(a),n="data-"+t,e=document.querySelectorAll("["+n+"]"),t=document.querySelectorAll(".js-"+t),e=l.makeArray(e).concat(l.makeArray(t)),o=n+"-options",s=i.jQuery_T4NT;e.forEach(function(e){var t,i=e.getAttribute(n)||e.getAttribute(o);try{t=i&&JSON.parse(i)}catch(t){return void(c&&c.error("Error parsing "+n+" on "+e.className+": "+t))}i=new r(e,t);s&&s.data(e,a,i)})})},l}),function(t,e){"function"==typeof define&&define.amd?define("outlayer/item",["ev-emitter/ev-emitter","get-size/get-size"],e):"object"==typeof module&&module.exports?module.exports=e(require("ev-emitter"),require("get-size")):(t.Outlayer={},t.Outlayer.Item=e(t.EvEmitter,t.getSize))}(window,function(t,e){"use strict";function i(t,e){t&&(this.element=t,this.layout=e,this.position={x:0,y:0},this._create())}var n=document.documentElement.style,o="string"==typeof n.transition?"transition":"WebkitTransition",n="string"==typeof n.transform?"transform":"WebkitTransform",s={WebkitTransition:"webkitTransitionEnd",transition:"transitionend"}[o],r={transform:n,transition:o,transitionDuration:o+"Duration",transitionProperty:o+"Property",transitionDelay:o+"Delay"},t=i.prototype=Object.create(t.prototype),a=(t.constructor=i,t._create=function(){this._transn={ingProperties:{},clean:{},onEnd:{}},this.css({position:"absolute"})},t.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},t.getSize=function(){this.size=e(this.element)},t.css=function(t){var e,i=this.element.style;for(e in t)i[r[e]||e]=t[e]},t.getPosition=function(){var t=getComputedStyle(this.element),e=LtrT4s,i=this.layout._getOption("originTop"),n=t[e?"left":"right"],t=t[i?"top":"bottom"],o=parseFloat(n),s=parseFloat(t),r=this.layout.size;-1!=n.indexOf("%")&&(o=o/100*r.width),-1!=t.indexOf("%")&&(s=s/100*r.height),o=isNaN(o)?0:o,s=isNaN(s)?0:s,o-=e?r.paddingLeft:r.paddingRight,s-=i?r.paddingTop:r.paddingBottom,this.position.x=o,this.position.y=s},t.layoutPosition=function(){var t=this.layout.size,e={},i=LtrT4s,n=this.layout._getOption("originTop"),o=i?"right":"left",s=this.position.x+t[i?"paddingLeft":"paddingRight"],i=(e[i?"left":"right"]=this.getXValue(s),e[o]="",n?"paddingTop":"paddingBottom"),s=n?"bottom":"top",o=this.position.y+t[i];e[n?"top":"bottom"]=this.getYValue(o),e[s]="",this.css(e),this.emitEvent("layout",[this])},t.getXValue=function(t){var e=this.layout._getOption("horizontal");return this.layout.options.percentPosition&&!e?t/this.layout.size.width*100+"%":t+"px"},t.getYValue=function(t){var e=this.layout._getOption("horizontal");return this.layout.options.percentPosition&&e?t/this.layout.size.height*100+"%":t+"px"},t._transitionTo=function(t,e){this.getPosition();var i=this.position.x,n=this.position.y,o=t==this.position.x&&e==this.position.y;this.setPosition(t,e),!o||this.isTransitioning?((o={}).transform=this.getTranslate(t-i,e-n),this.transition({to:o,onTransitionEnd:{transform:this.layoutPosition},isCleaning:!0})):this.layoutPosition()},t.getTranslate=function(t,e){return"translate3d("+(t=LtrT4s?t:-t)+"px, "+(this.layout._getOption("originTop")?e:-e)+"px, 0)"},t.goTo=function(t,e){this.setPosition(t,e),this.layoutPosition()},t.moveTo=t._transitionTo,t.setPosition=function(t,e){this.position.x=parseFloat(t),this.position.y=parseFloat(e)},t._nonTransition=function(t){for(var e in this.css(t.to),t.isCleaning&&this._removeStyles(t.to),t.onTransitionEnd)t.onTransitionEnd[e].call(this)},t.transition=function(t){if(parseFloat(this.layout.options.transitionDuration)){var e,i=this._transn;for(e in t.onTransitionEnd)i.onEnd[e]=t.onTransitionEnd[e];for(e in t.to)i.ingProperties[e]=!0,t.isCleaning&&(i.clean[e]=!0);t.from&&(this.css(t.from),this.element.offsetHeight),this.enableTransition(t.to),this.css(t.to),this.isTransitioning=!0}else this._nonTransition(t)},"opacity,"+n.replace(/([A-Z])/g,function(t){return"-"+t.toLowerCase()})),l=(t.enableTransition=function(){var t;this.isTransitioning||(t=this.layout.options.transitionDuration,this.css({transitionProperty:a,transitionDuration:t="number"==typeof t?t+"ms":t,transitionDelay:this.staggerDelay||0}),this.element.addEventListener(s,this,!1))},t.onwebkitTransitionEnd=function(t){this.ontransitionend(t)},t.onotransitionend=function(t){this.ontransitionend(t)},{"-webkit-transform":"transform"}),c=(t.ontransitionend=function(t){var e,i;t.target===this.element&&(delete(e=this._transn).ingProperties[i=l[t.propertyName]||t.propertyName],function(){for(var t in e.ingProperties)return;return 1}()&&this.disableTransition(),i in e.clean&&(this.element.style[t.propertyName]="",delete e.clean[i]),i in e.onEnd&&(e.onEnd[i].call(this),delete e.onEnd[i]),this.emitEvent("transitionEnd",[this]))},t.disableTransition=function(){this.removeTransitionStyles(),this.element.removeEventListener(s,this,!1),this.isTransitioning=!1},t._removeStyles=function(t){var e,i={};for(e in t)i[e]="";this.css(i)},{transitionProperty:"",transitionDuration:"",transitionDelay:""});return t.removeTransitionStyles=function(){this.css(c)},t.stagger=function(t){t=isNaN(t)?0:t,this.staggerDelay=t+"ms"},t.removeElem=function(){this.element.parentNode.removeChild(this.element),this.css({display:""}),this.emitEvent("remove",[this])},t.remove=function(){return o&&parseFloat(this.layout.options.transitionDuration)?(this.once("transitionEnd",function(){this.removeElem()}),void this.hide()):void this.removeElem()},t.reveal=function(){delete this.isHidden,this.css({display:""});var t=this.layout.options,e={};e[this.getHideRevealTransitionEndProperty("visibleStyle")]=this.onRevealTransitionEnd,this.transition({from:t.hiddenStyle,to:t.visibleStyle,isCleaning:!0,onTransitionEnd:e})},t.onRevealTransitionEnd=function(){this.isHidden||this.emitEvent("reveal")},t.getHideRevealTransitionEndProperty=function(t){var e,t=this.layout.options[t];if(t.opacity)return"opacity";for(e in t)return e},t.hide=function(){this.isHidden=!0,this.css({display:""});var t=this.layout.options,e={};e[this.getHideRevealTransitionEndProperty("hiddenStyle")]=this.onHideTransitionEnd,this.transition({from:t.visibleStyle,to:t.hiddenStyle,isCleaning:!0,onTransitionEnd:e})},t.onHideTransitionEnd=function(){this.isHidden&&(this.css({display:"none"}),this.emitEvent("hide"))},t.destroy=function(){this.css({position:"",left:"",right:"",top:"",bottom:"",transition:"",transform:""})},i}),function(o,s){"use strict";"function"==typeof define&&define.amd?define("outlayer/outlayer",["ev-emitter/ev-emitter","get-size/get-size","fizzy-ui-utils/utils","./item"],function(t,e,i,n){return s(o,t,e,i,n)}):"object"==typeof module&&module.exports?module.exports=s(o,require("ev-emitter"),require("get-size"),require("fizzy-ui-utils"),require("./item")):o.Outlayer=s(o,o.EvEmitter,o.getSize,o.fizzyUIUtils,o.Outlayer.Item)}(window,function(t,e,o,n,s){"use strict";function r(t,e){var i=n.getQueryElement(t);i?(this.element=i,c&&(this.$element=c(this.element)),this.options=n.extend({},this.constructor.defaults),this.option(e),e=++u,this.element.outlayerGUID=e,(h[e]=this)._create(),this._getOption("initLayout")&&this.layout()):l&&l.error("Bad element for "+this.constructor.namespace+": "+(i||t))}function a(t){function e(){t.apply(this,arguments)}return(e.prototype=Object.create(t.prototype)).constructor=e}function i(){}var l=t.console,c=t.jQuery_T4NT,u=0,h={},d=(r.namespace="outlayer",r.Item=s,r.defaults={containerStyle:{position:"relative"},initLayout:!0,originLeft:!0,originTop:!0,resize:!0,resizeContainer:!0,transitionDuration:"0.4s",hiddenStyle:{opacity:0,transform:"scale(0.001)"},visibleStyle:{opacity:1,transform:"scale(1)"}},r.prototype),f=(n.extend(d,e.prototype),d.option=function(t){n.extend(this.options,t)},d._getOption=function(t){var e=this.constructor.compatOptions[t];return e&&void 0!==this.options[e]?this.options[e]:this.options[t]},r.compatOptions={initLayout:"isInitLayout",horizontal:"isHorizontal",layoutInstant:"isLayoutInstant",originLeft:"isOriginLeft",originTop:"isOriginTop",resize:"isResizeBound",resizeContainer:"isResizingContainer"},d._create=function(){this.reloadItems(),this.stamps=[],this.stamp(this.options.stamp),n.extend(this.element.style,this.options.containerStyle),this._getOption("resize")&&this.bindResize()},d.reloadItems=function(){this.items=this._itemize(this.element.children)},d._itemize=function(t){for(var e=this._filterFindItemElements(t),i=this.constructor.Item,n=[],o=0;o<e.length;o++){var s=new i(e[o],this);n.push(s)}return n},d._filterFindItemElements=function(t){return n.filterFindElements(t,this.options.itemSelector)},d.getItemElements=function(){return this.items.map(function(t){return t.element})},d.layout=function(){this._resetLayout(),this._manageStamps();var t=this._getOption("layoutInstant"),t=void 0!==t?t:!this._isLayoutInited;this.layoutItems(this.items,t),this._isLayoutInited=!0},d._init=d.layout,d._resetLayout=function(){this.getSize()},d.getSize=function(){this.size=o(this.element)},d._getMeasurement=function(t,e){var i,n=this.options[t];n?("string"==typeof n?i=this.element.querySelector(n):n instanceof HTMLElement&&(i=n),this[t]=i?o(i)[e]:n):this[t]=0},d.layoutItems=function(t,e){t=this._getItemsForLayout(t),this._layoutItems(t,e),this._postLayout()},d._getItemsForLayout=function(t){return t.filter(function(t){return!t.isIgnored})},d._layoutItems=function(t,i){var n;this._emitCompleteOnItems("layout",t),t&&t.length&&(n=[],t.forEach(function(t){var e=this._getItemLayoutPosition(t);e.item=t,e.isInstant=i||t.isLayoutInstant,n.push(e)},this),this._processLayoutQueue(n))},d._getItemLayoutPosition=function(){return{x:0,y:0}},d._processLayoutQueue=function(t){this.updateStagger(),t.forEach(function(t,e){this._positionItem(t.item,t.x,t.y,t.isInstant,e)},this)},d.updateStagger=function(){var t,e=this.options.stagger;return null==e?void(this.stagger=0):(this.stagger="number"==typeof e?e:(t=(e=e.match(/(^\d*\.?\d*)(\w*)/))&&e[1],e=e&&e[2],t.length?(t=parseFloat(t))*(f[e]||1):0),this.stagger)},d._positionItem=function(t,e,i,n,o){n?t.goTo(e,i):(t.stagger(o*this.stagger),t.moveTo(e,i))},d._postLayout=function(){this.resizeContainer()},d.resizeContainer=function(){var t;this._getOption("resizeContainer")&&(t=this._getContainerSize())&&(this._setContainerMeasure(t.width,!0),this._setContainerMeasure(t.height,!1))},d._getContainerSize=i,d._setContainerMeasure=function(t,e){var i;void 0!==t&&((i=this.size).isBorderBox&&(t+=e?i.paddingLeft+i.paddingRight+i.borderLeftWidth+i.borderRightWidth:i.paddingBottom+i.paddingTop+i.borderTopWidth+i.borderBottomWidth),t=Math.max(t,0),this.element.style[e?"width":"height"]=t+"px")},d._emitCompleteOnItems=function(e,t){function i(){s.dispatchEvent(e+"Complete",null,[t])}function n(){++o==r&&i()}var o,s=this,r=t.length;t&&r?(o=0,t.forEach(function(t){t.once(e,n)})):i()},d.dispatchEvent=function(t,e,i){var n=e?[e].concat(i):i;this.emitEvent(t,n),c&&(this.$element=this.$element||c(this.element),e?((n=c.Event(e)).type=t,this.$element.trigger(n,i)):this.$element.trigger(t,i))},d.ignore=function(t){t=this.getItem(t);t&&(t.isIgnored=!0)},d.unignore=function(t){t=this.getItem(t);t&&delete t.isIgnored},d.stamp=function(t){(t=this._find(t))&&(this.stamps=this.stamps.concat(t),t.forEach(this.ignore,this))},d.unstamp=function(t){(t=this._find(t))&&t.forEach(function(t){n.removeFrom(this.stamps,t),this.unignore(t)},this)},d._find=function(t){if(t)return"string"==typeof t&&(t=this.element.querySelectorAll(t)),n.makeArray(t)},d._manageStamps=function(){this.stamps&&this.stamps.length&&(this._getBoundingRect(),this.stamps.forEach(this._manageStamp,this))},d._getBoundingRect=function(){var t=this.element.getBoundingClientRect(),e=this.size;this._boundingRect={left:t.left+e.paddingLeft+e.borderLeftWidth,top:t.top+e.paddingTop+e.borderTopWidth,right:t.right-(e.paddingRight+e.borderRightWidth),bottom:t.bottom-(e.paddingBottom+e.borderBottomWidth)}},d._manageStamp=i,d._getElementOffset=function(t){var e=t.getBoundingClientRect(),i=this._boundingRect,t=o(t);return{left:e.left-i.left-t.marginLeft,top:e.top-i.top-t.marginTop,right:i.right-e.right-t.marginRight,bottom:i.bottom-e.bottom-t.marginBottom}},d.handleEvent=n.handleEvent,d.bindResize=function(){t.addEventListener("resize",this),this.isResizeBound=!0},d.unbindResize=function(){t.removeEventListener("resize",this),this.isResizeBound=!1},d.onresize=function(){this.resize()},n.debounceMethod(r,"onresize",100),d.resize=function(){this.isResizeBound&&this.needsResizeLayout()&&this.layout()},d.needsResizeLayout=function(){var t=o(this.element);return this.size&&t&&t.innerWidth!==this.size.innerWidth},d.addItems=function(t){t=this._itemize(t);return t.length&&(this.items=this.items.concat(t)),t},d.appended=function(t){t=this.addItems(t);t.length&&(this.layoutItems(t,!0),this.reveal(t))},d.prepended=function(t){var e,t=this._itemize(t);t.length&&(e=this.items.slice(0),this.items=t.concat(e),this._resetLayout(),this._manageStamps(),this.layoutItems(t,!0),this.reveal(t),this.layoutItems(e))},d.reveal=function(t){var i;this._emitCompleteOnItems("reveal",t),t&&t.length&&(i=this.updateStagger(),t.forEach(function(t,e){t.stagger(e*i),t.reveal()}))},d.hide=function(t){var i;this._emitCompleteOnItems("hide",t),t&&t.length&&(i=this.updateStagger(),t.forEach(function(t,e){t.stagger(e*i),t.hide()}))},d.revealItemElements=function(t){t=this.getItems(t);this.reveal(t)},d.hideItemElements=function(t){t=this.getItems(t);this.hide(t)},d.getItem=function(t){for(var e=0;e<this.items.length;e++){var i=this.items[e];if(i.element==t)return i}},d.getItems=function(t){t=n.makeArray(t);var e=[];return t.forEach(function(t){t=this.getItem(t);t&&e.push(t)},this),e},d.remove=function(t){t=this.getItems(t);this._emitCompleteOnItems("remove",t),t&&t.length&&t.forEach(function(t){t.remove(),n.removeFrom(this.items,t)},this)},d.destroy=function(){var t=this.element.style,t=(t.height="",t.position="",t.width="",this.items.forEach(function(t){t.destroy()}),this.unbindResize(),this.element.outlayerGUID);delete h[t],delete this.element.outlayerGUID,c&&c.removeData(this.element,this.constructor.namespace)},r.data=function(t){t=(t=n.getQueryElement(t))&&t.outlayerGUID;return t&&h[t]},r.create=function(t,e){var i=a(r);return i.defaults=n.extend({},r.defaults),n.extend(i.defaults,e),i.compatOptions=n.extend({},r.compatOptions),i.namespace=t,i.data=r.data,i.Item=a(s),n.htmlInit(i,t),c&&c.bridget&&c.bridget(t,i),i},{ms:1,s:1e3});return r.Item=s,r}),function(t,e){"function"==typeof define&&define.amd?define("isotopet4s-layout/js/item",["outlayer/outlayer"],e):"object"==typeof module&&module.exports?module.exports=e(require("outlayer")):(t.isotopet4s=t.isotopet4s||{},t.isotopet4s.Item=e(t.Outlayer))}(window,function(t){"use strict";function e(){t.Item.apply(this,arguments)}var i=e.prototype=Object.create(t.Item.prototype),n=i._create,o=(i._create=function(){this.id=this.layout.itemGUID++,n.call(this),this.sortData={}},i.updateSortData=function(){if(!this.isIgnored){this.sortData.id=this.id,this.sortData["original-order"]=this.id,this.sortData.random=Math.random();var t,e=this.layout.options.getSortData,i=this.layout._sorters;for(t in e){var n=i[t];this.sortData[t]=n(this.element,this)}}},i.destroy);return i.destroy=function(){o.apply(this,arguments),this.css({display:""})},e}),function(t,e){"function"==typeof define&&define.amd?define("isotopet4s-layout/js/layout-mode",["get-size/get-size","outlayer/outlayer"],e):"object"==typeof module&&module.exports?module.exports=e(require("get-size"),require("outlayer")):(t.isotopet4s=t.isotopet4s||{},t.isotopet4s.LayoutMode=e(t.getSize,t.Outlayer))}(window,function(e,i){"use strict";function n(t){(this.isotopet4s=t)&&(this.options=t.options[this.namespace],this.element=t.element,this.items=t.filteredItems,this.size=t.size)}var o=n.prototype;return["_resetLayout","_getItemLayoutPosition","_manageStamp","_getContainerSize","_getElementOffset","needsResizeLayout","_getOption"].forEach(function(t){o[t]=function(){return i.prototype[t].apply(this.isotopet4s,arguments)}}),o.needsVerticalResizeLayout=function(){var t=e(this.isotopet4s.element);return this.isotopet4s.size&&t&&t.innerHeight!=this.isotopet4s.size.innerHeight},o._getMeasurement=function(){this.isotopet4s._getMeasurement.apply(this,arguments)},o.getColumnWidth=function(){this.getSegmentSize("column","Width")},o.getRowHeight=function(){this.getSegmentSize("row","Height")},o.getSegmentSize=function(t,e){var i,t=t+e,n="outer"+e;this._getMeasurement(t,n),this[t]||(i=this.getFirstItemSize(),this[t]=i&&i[n]||this.isotopet4s.size["inner"+e])},o.getFirstItemSize=function(){var t=this.isotopet4s.filteredItems[0];return t&&t.element&&e(t.element)},o.layout=function(){this.isotopet4s.layout.apply(this.isotopet4s,arguments)},o.getSize=function(){this.isotopet4s.getSize(),this.size=this.isotopet4s.size},n.modes={},n.create=function(t,e){function i(){n.apply(this,arguments)}return(i.prototype=Object.create(o)).constructor=i,e&&(i.options=e),n.modes[i.prototype.namespace=t]=i},n}),function(t,e){"function"==typeof define&&define.amd?define("masonry-layout/masonry",["outlayer/outlayer","get-size/get-size"],e):"object"==typeof module&&module.exports?module.exports=e(require("outlayer"),require("get-size")):t.Masonry=e(t.Outlayer,t.getSize)}(window,function(t,a){var t=t.create("masonry"),e=(t.compatOptions.fitWidth="isFitWidth",t.prototype);return e._resetLayout=function(){this.getSize(),this._getMeasurement("columnWidth","outerWidth"),this._getMeasurement("gutter","outerWidth"),this.measureColumns(),this.colYs=[];for(var t=0;t<this.cols;t++)this.colYs.push(0);this.maxY=0,this.horizontalColIndex=0},e.measureColumns=function(){this.getContainerWidth(),this.columnWidth||(t=(t=this.items[0])&&t.element,this.columnWidth=t&&a(t).outerWidth||this.containerWidth);var t=this.columnWidth+=this.gutter,e=this.containerWidth+this.gutter,i=e/t,e=t-e%t,i=Math[e&&e<1?"round":"floor"](i);this.cols=Math.max(i,1)},e.getContainerWidth=function(){var t=this._getOption("fitWidth")?this.element.parentNode:this.element,t=a(t);this.containerWidth=t&&t.innerWidth},e._getItemLayoutPosition=function(t){t.getSize();for(var e=t.size.outerWidth%this.columnWidth,e=Math[e&&e<1?"round":"ceil"](t.size.outerWidth/this.columnWidth),e=Math.min(e,this.cols),i=this[this.options.horizontalOrder?"_getHorizontalColPosition":"_getTopColPosition"](e,t),n={x:this.columnWidth*i.col,y:i.y},o=i.y+t.size.outerHeight,s=e+i.col,r=i.col;r<s;r++)this.colYs[r]=o;return n},e._getTopColPosition=function(t){var t=this._getTopColGroup(t),e=Math.min.apply(Math,t);return{col:t.indexOf(e),y:e}},e._getTopColGroup=function(t){if(t<2)return this.colYs;for(var e=[],i=this.cols+1-t,n=0;n<i;n++)e[n]=this._getColGroupY(n,t);return e},e._getColGroupY=function(t,e){return e<2?this.colYs[t]:(t=this.colYs.slice(t,t+e),Math.max.apply(Math,t))},e._getHorizontalColPosition=function(t,e){var i=this.horizontalColIndex%this.cols,i=1<t&&i+t>this.cols?0:i,e=e.size.outerWidth&&e.size.outerHeight;return this.horizontalColIndex=e?i+t:this.horizontalColIndex,{col:i,y:this._getColGroupY(i,t)}},e._manageStamp=function(t){var e=a(t),t=this._getElementOffset(t),i=this._getOption("originLeft")?t.left:t.right,n=i+e.outerWidth,i=Math.floor(i/this.columnWidth),i=Math.max(0,i),o=Math.floor(n/this.columnWidth);o-=n%this.columnWidth?0:1;for(var o=Math.min(this.cols-1,o),s=(this._getOption("originTop")?t.top:t.bottom)+e.outerHeight,r=i;r<=o;r++)this.colYs[r]=Math.max(s,this.colYs[r])},e._getContainerSize=function(){this.maxY=Math.max.apply(Math,this.colYs);var t={height:this.maxY};return this._getOption("fitWidth")&&(t.width=this._getContainerFitWidth()),t},e._getContainerFitWidth=function(){for(var t=0,e=this.cols;--e&&0===this.colYs[e];)t++;return(this.cols-t)*this.columnWidth-this.gutter},e.needsResizeLayout=function(){var t=this.containerWidth;return this.getContainerWidth(),t!=this.containerWidth},t}),function(t,e){"function"==typeof define&&define.amd?define("isotopet4s-layout/js/layout-modes/masonry",["../layout-mode","masonry-layout/masonry"],e):"object"==typeof module&&module.exports?module.exports=e(require("../layout-mode"),require("masonry-layout")):e(t.isotopet4s.LayoutMode,t.Masonry)}(window,function(t,e){"use strict";var i,t=t.create("masonry"),n=t.prototype,o={_getElementOffset:!0,layout:!0,_getMeasurement:!0};for(i in e.prototype)o[i]||(n[i]=e.prototype[i]);var s=n.measureColumns,r=(n.measureColumns=function(){this.items=this.isotopet4s.filteredItems,s.call(this)},n._getOption);return n._getOption=function(t){return"fitWidth"==t?void 0!==this.options.isFitWidth?this.options.isFitWidth:this.options.fitWidth:r.apply(this.isotopet4s,arguments)},t}),function(t,e){"function"==typeof define&&define.amd?define("isotopet4s-layout/js/layout-modes/fit-rows",["../layout-mode"],e):"object"==typeof exports?module.exports=e(require("../layout-mode")):e(t.isotopet4s.LayoutMode)}(window,function(t){"use strict";var t=t.create("fitRows"),e=t.prototype;return e._resetLayout=function(){this.x=0,this.y=0,this.maxY=0,this._getMeasurement("gutter","outerWidth")},e._getItemLayoutPosition=function(t){t.getSize();var e=t.size.outerWidth+this.gutter,i=this.isotopet4s.size.innerWidth+this.gutter,i=(0!==this.x&&e+this.x>i&&(this.x=0,this.y=this.maxY),{x:this.x,y:this.y});return this.maxY=Math.max(this.maxY,this.y+t.size.outerHeight),this.x+=e,i},e._getContainerSize=function(){return{height:this.maxY}},t}),function(t,e){"function"==typeof define&&define.amd?define("isotopet4s-layout/js/layout-modes/vertical",["../layout-mode"],e):"object"==typeof module&&module.exports?module.exports=e(require("../layout-mode")):e(t.isotopet4s.LayoutMode)}(window,function(t){"use strict";var t=t.create("vertical",{horizontalAlignment:0}),e=t.prototype;return e._resetLayout=function(){this.y=0},e._getItemLayoutPosition=function(t){t.getSize();var e=(this.isotopet4s.size.innerWidth-t.size.outerWidth)*this.options.horizontalAlignment,i=this.y;return this.y+=t.size.outerHeight,{x:e,y:i}},e._getContainerSize=function(){return{height:this.y}},t}),function(r,a){"function"==typeof define&&define.amd?define(["outlayer/outlayer","get-size/get-size","desandro-matches-selector/matches-selector","fizzy-ui-utils/utils","isotopet4s-layout/js/item","isotopet4s-layout/js/layout-mode","isotopet4s-layout/js/layout-modes/masonry","isotopet4s-layout/js/layout-modes/fit-rows","isotopet4s-layout/js/layout-modes/vertical"],function(t,e,i,n,o,s){return a(r,t,0,i,n,o,s)}):"object"==typeof module&&module.exports?module.exports=a(r,require("outlayer"),require("get-size"),require("desandro-matches-selector"),require("fizzy-ui-utils"),require("isotopet4s-layout/js/item"),require("isotopet4s-layout/js/layout-mode"),require("isotopet4s-layout/js/layout-modes/masonry"),require("isotopet4s-layout/js/layout-modes/fit-rows"),require("isotopet4s-layout/js/layout-modes/vertical")):r.isotopet4s=a(r,r.Outlayer,0,r.matchesSelector,r.fizzyUIUtils,r.isotopet4s.Item,r.isotopet4s.LayoutMode)}(window,function(t,i,e,n,s,o,r){var a=t.jQuery_T4NT,l=String.prototype.trim?function(t){return t.trim()}:function(t){return t.replace(/^\s+|\s+$/g,"")},c=i.create("isotopet4s",{layoutMode:"masonry",isjQuery_T4NTFiltering:!0,sortAscending:!0}),t=(c.Item=o,c.LayoutMode=r,c.prototype),u=(t._create=function(){for(var t in this.itemGUID=0,this._sorters={},this._getSorters(),i.prototype._create.call(this),this.modes={},this.filteredItems=this.items,this.sortHistory=["original-order"],r.modes)this._initLayoutMode(t)},t.reloadItems=function(){this.itemGUID=0,i.prototype.reloadItems.call(this)},t._itemize=function(){for(var t=i.prototype._itemize.apply(this,arguments),e=0;e<t.length;e++)t[e].id=this.itemGUID++;return this._updateItemsSortData(t),t},t._initLayoutMode=function(t){var e=r.modes[t],i=this.options[t]||{};this.options[t]=e.options?s.extend(e.options,i):i,this.modes[t]=new e(this)},t.layout=function(){return!this._isLayoutInited&&this._getOption("initLayout")?void this.arrange():void this._layout()},t._layout=function(){var t=this._getIsInstant();this._resetLayout(),this._manageStamps(),this.layoutItems(this.filteredItems,t),this._isLayoutInited=!0},t.arrange=function(t){this.option(t),this._getIsInstant();t=this._filter(this.items);this.filteredItems=t.matches,this._bindArrangeComplete(),this._isInstant?this._noTransition(this._hideReveal,[t]):this._hideReveal(t),this._sort(),this._layout()},t._init=t.arrange,t._hideReveal=function(t){this.reveal(t.needReveal),this.hide(t.needHide)},t._getIsInstant=function(){var t=this._getOption("layoutInstant"),t=void 0!==t?t:!this._isLayoutInited;return this._isInstant=t},t._bindArrangeComplete=function(){function t(){e&&i&&n&&o.dispatchEvent("arrangeComplete",null,[o.filteredItems])}var e,i,n,o=this;this.once("layoutComplete",function(){e=!0,t()}),this.once("hideComplete",function(){i=!0,t()}),this.once("revealComplete",function(){n=!0,t()})},t._filter=function(t){for(var e=this.options.filter,i=[],n=[],o=[],s=this._getFilterTest(e||"*"),r=0;r<t.length;r++){var a,l=t[r];l.isIgnored||((a=s(l))&&i.push(l),a&&l.isHidden?n.push(l):a||l.isHidden||o.push(l))}return{matches:i,needReveal:n,needHide:o}},t._getFilterTest=function(e){return a&&this.options.isjQuery_T4NTFiltering?function(t){return a(t.element).is(e)}:"function"==typeof e?function(t){return e(t.element)}:function(t){return n(t.element,e)}},t.updateSortData=function(t){t=t?(t=s.makeArray(t),this.getItems(t)):this.items;this._getSorters(),this._updateItemsSortData(t)},t._getSorters=function(){var t,e=this.options.getSortData;for(t in e){var i=e[t];this._sorters[t]=u(i)}},t._updateItemsSortData=function(t){for(var e=t&&t.length,i=0;e&&i<e;i++)t[i].updateSortData()},function(t){var e,i,n,o,s,r;return"string"!=typeof t?t:(i=(e=(t=l(t).split(" "))[0]).match(/^\[(.+)\]$/),s=i&&i[1],r=e,n=s?function(t){return t.getAttribute(s)}:function(t){t=t.querySelector(r);return t&&t.textContent},(o=c.sortDataParsers[t[1]])?function(t){return t&&o(n(t))}:function(t){return t&&n(t)})}),h=(c.sortDataParsers={parseInt:function(t){return parseInt(t,10)},parseFloat:function(t){return parseFloat(t)}},t._sort=function(){var t,r,a;this.options.sortBy&&(t=s.makeArray(this.options.sortBy),this._getIsSameSortBy(t)||(this.sortHistory=t.concat(this.sortHistory)),r=this.sortHistory,a=this.options.sortAscending,this.filteredItems.sort(function(t,e){for(var i=0;i<r.length;i++){var n=r[i],o=t.sortData[n],s=e.sortData[n];if(s<o||o<s)return(s<o?1:-1)*((void 0!==a[n]?a[n]:a)?1:-1)}return 0}))},t._getIsSameSortBy=function(t){for(var e=0;e<t.length;e++)if(t[e]!=this.sortHistory[e])return!1;return!0},t._mode=function(){var t=this.options.layoutMode,e=this.modes[t];if(e)return e.options=this.options[t],e;throw new Error("No layout mode: "+t)},t._resetLayout=function(){i.prototype._resetLayout.call(this),this._mode()._resetLayout()},t._getItemLayoutPosition=function(t){return this._mode()._getItemLayoutPosition(t)},t._manageStamp=function(t){this._mode()._manageStamp(t)},t._getContainerSize=function(){return this._mode()._getContainerSize()},t.needsResizeLayout=function(){return this._mode().needsResizeLayout()},t.appended=function(t){var t=this.addItems(t);t.length&&(t=this._filterRevealAdded(t),this.filteredItems=this.filteredItems.concat(t))},t.prepended=function(t){var e,t=this._itemize(t);t.length&&(this._resetLayout(),this._manageStamps(),e=this._filterRevealAdded(t),this.layoutItems(this.filteredItems),this.filteredItems=e.concat(this.filteredItems),this.items=t.concat(this.items))},t._filterRevealAdded=function(t){t=this._filter(t);return this.hide(t.needHide),this.reveal(t.matches),this.layoutItems(t.matches,!0),t.matches},t.insert=function(t){var e=this.addItems(t);if(e.length){for(var i,n=e.length,o=0;o<n;o++)i=e[o],this.element.appendChild(i.element);t=this._filter(e).matches;for(o=0;o<n;o++)e[o].isLayoutInstant=!0;for(this.arrange(),o=0;o<n;o++)delete e[o].isLayoutInstant;this.reveal(t)}},t.remove);return t.remove=function(t){t=s.makeArray(t);var e=this.getItems(t);h.call(this,t);for(var i=e&&e.length,n=0;i&&n<i;n++){var o=e[n];s.removeFrom(this.filteredItems,o)}},t.shuffle=function(){for(var t=0;t<this.items.length;t++)this.items[t].sortData.random=Math.random();this.options.sortBy="random",this._sort(),this._layout()},t._noTransition=function(t,e){var i=this.options.transitionDuration,t=(this.options.transitionDuration=0,t.apply(this,e));return this.options.transitionDuration=i,t},t.getFilteredItemElements=function(){return this.filteredItems.map(function(t){return t.element})},c}),function(t,e){"function"==typeof define&&define.amd?define("packery/js/rect",e):"object"==typeof module&&module.exports?module.exports=e():(t.Packery=t.Packery||{},t.Packery.Rect=e())}(window,function(){function a(t){for(var e in a.defaults)this[e]=a.defaults[e];for(e in t)this[e]=t[e]}a.defaults={x:0,y:0,width:0,height:0};var t=a.prototype;return t.contains=function(t){var e=t.width||0;return this.x<=t.x&&this.y<=t.y&&this.x+this.width>=t.x+e&&this.y+this.height>=t.y+(t.height||0)},t.overlaps=function(t){var e=this.x+this.width,i=this.y+this.height,n=t.x+t.width;return this.x<n&&t.x<e&&this.y<t.y+t.height&&t.y<i},t.getMaximalFreeRects=function(t){var e,i,n,o,s,r;return!!this.overlaps(t)&&(i=[],n=this.x+this.width,o=this.y+this.height,s=t.x+t.width,r=t.y+t.height,this.y<t.y&&(e=new a({x:this.x,y:this.y,width:this.width,height:t.y-this.y}),i.push(e)),s<n&&(e=new a({x:s,y:this.y,width:n-s,height:this.height}),i.push(e)),r<o&&(e=new a({x:this.x,y:r,width:this.width,height:o-r}),i.push(e)),this.x<t.x&&(e=new a({x:this.x,y:this.y,width:t.x-this.x,height:this.height}),i.push(e)),i)},t.canFit=function(t){return this.width>=t.width&&this.height>=t.height},a}),function(t,e){"function"==typeof define&&define.amd?define("packery/js/packer",["./rect"],e):"object"==typeof module&&module.exports?module.exports=e(require("./rect")):(t=t.Packery=t.Packery||{}).Packer=e(t.Rect)}(window,function(e){function t(t,e,i){this.width=t||0,this.height=e||0,this.sortDirection=i||"downwardLeftToRight",this.reset()}var i=t.prototype,n=(i.reset=function(){this.spaces=[];var t=new e({x:0,y:0,width:this.width,height:this.height});this.spaces.push(t),this.sorter=n[this.sortDirection]||n.downwardLeftToRight},i.pack=function(t){for(var e=0;e<this.spaces.length;e++){var i=this.spaces[e];if(i.canFit(t)){this.placeInSpace(t,i);break}}},i.columnPack=function(t){for(var e=0;e<this.spaces.length;e++){var i=this.spaces[e];if(i.x<=t.x&&t.x+t.width<=i.x+i.width&&t.height-.01<=i.height){t.y=i.y,this.placed(t);break}}},i.rowPack=function(t){for(var e=0;e<this.spaces.length;e++){var i=this.spaces[e];if(i.y<=t.y&&t.y+t.height<=i.y+i.height&&t.width-.01<=i.width){t.x=i.x,this.placed(t);break}}},i.placeInSpace=function(t,e){t.x=e.x,t.y=e.y,this.placed(t)},i.placed=function(t){for(var e=[],i=0;i<this.spaces.length;i++){var n=this.spaces[i],o=n.getMaximalFreeRects(t);o?e.push.apply(e,o):e.push(n)}this.spaces=e,this.mergeSortSpaces()},i.mergeSortSpaces=function(){t.mergeRects(this.spaces),this.spaces.sort(this.sorter)},i.addSpace=function(t){this.spaces.push(t),this.mergeSortSpaces()},t.mergeRects=function(t){var e=0,i=t[e];t:for(;i;){for(var n=0,o=t[e+n];o;){if(o==i)n++;else{if(o.contains(i)){t.splice(e,1),i=t[e];continue t}i.contains(o)?t.splice(e+n,1):n++}o=t[e+n]}i=t[++e]}return t},{downwardLeftToRight:function(t,e){return t.y-e.y||t.x-e.x},rightwardTopToBottom:function(t,e){return t.x-e.x||t.y-e.y}});return t}),function(t,e){"function"==typeof define&&define.amd?define("packery/js/item",["outlayer/outlayer","./rect"],e):"object"==typeof module&&module.exports?module.exports=e(require("outlayer"),require("./rect")):t.Packery.Item=e(t.Outlayer,t.Packery.Rect)}(window,function(t,e){function i(){t.Item.apply(this,arguments)}var n="string"==typeof document.documentElement.style.transform?"transform":"WebkitTransform",o=i.prototype=Object.create(t.Item.prototype),s=o._create,r=(o._create=function(){s.call(this),this.rect=new e},o.moveTo);return o.moveTo=function(t,e){var i=Math.abs(this.position.x-t),n=Math.abs(this.position.y-e);return this.layout.dragItemCount&&!this.isPlacing&&!this.isTransitioning&&i<1&&n<1?void this.goTo(t,e):void r.apply(this,arguments)},o.enablePlacing=function(){this.removeTransitionStyles(),this.isTransitioning&&n&&(this.element.style[n]="none"),this.isTransitioning=!1,this.getSize(),this.layout._setRectSize(this.element,this.rect),this.isPlacing=!0},o.disablePlacing=function(){this.isPlacing=!1},o.removeElem=function(){this.element.parentNode.removeChild(this.element),this.layout.packer.addSpace(this.rect),this.emitEvent("remove",[this])},o.showDropPlaceholder=function(){var t=this.dropPlaceholder;t||((t=this.dropPlaceholder=document.createElement("div")).className="packery-drop-placeholder",t.style.position="absolute"),t.style.width=this.size.width+"px",t.style.height=this.size.height+"px",this.positionDropPlaceholder(),this.layout.element.appendChild(t)},o.positionDropPlaceholder=function(){this.dropPlaceholder.style[n]="translate("+this.rect.x+"px, "+this.rect.y+"px)"},o.hideDropPlaceholder=function(){this.layout.element.removeChild(this.dropPlaceholder)},i}),function(t,e){"function"==typeof define&&define.amd?define("packery/js/packery",["get-size/get-size","outlayer/outlayer","./rect","./packer","./item"],e):"object"==typeof module&&module.exports?module.exports=e(require("get-size"),require("outlayer"),require("./rect"),require("./packer"),require("./item")):t.Packery=e(t.getSize,t.Outlayer,t.Packery.Rect,t.Packery.Packer,t.Packery.Item)}(window,function(c,t,r,e,i){function n(t,e){return t.position.y-e.position.y||t.position.x-e.position.x}function o(t,e){return t.position.x-e.position.x||t.position.y-e.position.y}r.prototype.canFit=function(t){return this.width>=t.width-1&&this.height>=t.height-1};var s=t.create("packery"),i=(s.Item=i,s.prototype),a=(i._create=function(){t.prototype._create.call(this),this.packer=new e,this.shiftPacker=new e,this.isEnabled=!0,this.dragItemCount=0;var i=this;this.handleDraggabilly={dragStart:function(){i.itemDragStart(this.element)},dragMove:function(){i.itemDragMove(this.element,this.position.x,this.position.y)},dragEnd:function(){i.itemDragEnd(this.element)}},this.handleUIDraggable={start:function(t,e){e&&i.itemDragStart(t.currentTarget)},drag:function(t,e){e&&i.itemDragMove(t.currentTarget,e.position.left,e.position.top)},stop:function(t,e){e&&i.itemDragEnd(t.currentTarget)}}},i._resetLayout=function(){var t,e,i;this.getSize(),this._getMeasurements(),i=this._getOption("horizontal")?(t=1/0,e=this.size.innerHeight+this.gutter,"rightwardTopToBottom"):(t=this.size.innerWidth+this.gutter,e=1/0,"downwardLeftToRight"),this.packer.width=this.shiftPacker.width=t,this.packer.height=this.shiftPacker.height=e,this.packer.sortDirection=this.shiftPacker.sortDirection=i,this.packer.reset(),this.maxY=0,this.maxX=0},i._getMeasurements=function(){this._getMeasurement("columnWidth","width"),this._getMeasurement("rowHeight","height"),this._getMeasurement("gutter","width")},i._getItemLayoutPosition=function(t){var e;return this._setRectSize(t.element,t.rect),this.isShifting||0<this.dragItemCount?(e=this._getPackMethod(),this.packer[e](t.rect)):this.packer.pack(t.rect),this._setMaxXY(t.rect),t.rect},i.shiftLayout=function(){this.isShifting=!0,this.layout(),delete this.isShifting},i._getPackMethod=function(){return this._getOption("horizontal")?"rowPack":"columnPack"},i._setMaxXY=function(t){this.maxX=Math.max(t.x+t.width,this.maxX),this.maxY=Math.max(t.y+t.height,this.maxY)},i._setRectSize=function(t,e){var t=c(t),i=t.outerWidth,t=t.outerHeight;(i||t)&&(i=this._applyGridGutter(i,this.columnWidth),t=this._applyGridGutter(t,this.rowHeight)),e.width=Math.min(i,this.packer.width),e.height=Math.min(t,this.packer.height)},i._applyGridGutter=function(t,e){var i;return e?(i=t%(e+=this.gutter),Math[i&&i<1?"round":"ceil"](t/e)*e):t+this.gutter},i._getContainerSize=function(){return this._getOption("horizontal")?{width:this.maxX-this.gutter}:{height:this.maxY-this.gutter}},i._manageStamp=function(t){var e=this.getItem(t);e=e&&e.isPlacing?e.rect:(e=this._getElementOffset(t),new r({x:this._getOption("originLeft")?e.left:e.right,y:this._getOption("originTop")?e.top:e.bottom})),this._setRectSize(t,e),this.packer.placed(e),this._setMaxXY(e)},i.sortItemsByPosition=function(){var t=this._getOption("horizontal")?o:n;this.items.sort(t)},i.fit=function(t,e,i){t=this.getItem(t);t&&(this.stamp(t.element),t.enablePlacing(),this.updateShiftTargets(t),e=void 0===e?t.rect.x:e,i=void 0===i?t.rect.y:i,this.shift(t,e,i),this._bindFitEvents(t),t.moveTo(t.rect.x,t.rect.y),this.shiftLayout(),this.unstamp(t.element),this.sortItemsByPosition(),t.disablePlacing())},i._bindFitEvents=function(t){function e(){2==++n&&i.dispatchEvent("fitComplete",null,[t])}var i=this,n=0;t.once("layout",e),this.once("layoutComplete",e)},i.resize=function(){this.isResizeBound&&this.needsResizeLayout()&&(this.options.shiftPercentResize?this.resizeShiftPercentLayout():this.layout())},i.needsResizeLayout=function(){var t=c(this.element),e=this._getOption("horizontal")?"innerHeight":"innerWidth";return t[e]!=this.size[e]},i.resizeShiftPercentLayout=function(){var i,e,n,t=this._getItemsForLayout(this.items),o=this._getOption("horizontal"),s=o?"y":"x",r=o?"height":"width",a=o?"rowHeight":"columnWidth",o=o?"innerHeight":"innerWidth",l=this[a];(l=l&&l+this.gutter)?(this._getMeasurements(),i=this[a]+this.gutter,t.forEach(function(t){var e=Math.round(t.rect[s]/l);t.rect[s]=e*i})):(e=c(this.element)[o]+this.gutter,n=this.packer[r],t.forEach(function(t){t.rect[s]=t.rect[s]/n*e})),this.shiftLayout()},i.itemDragStart=function(t){this.isEnabled&&(this.stamp(t),t=this.getItem(t))&&(t.enablePlacing(),t.showDropPlaceholder(),this.dragItemCount++,this.updateShiftTargets(t))},i.updateShiftTargets=function(t){this.shiftPacker.reset(),this._getBoundingRect();var i=this._getOption("originLeft"),n=this._getOption("originTop"),l=(this.stamps.forEach(function(t){var e=this.getItem(t);e&&e.isPlacing||(e=this._getElementOffset(t),e=new r({x:i?e.left:e.right,y:n?e.top:e.bottom}),this._setRectSize(t,e),this.shiftPacker.placed(e))},this),this._getOption("horizontal")),e=l?"rowHeight":"columnWidth",c=l?"height":"width";this.shiftTargetKeys=[],this.shiftTargets=[];var u=this[e];if(u=u&&u+this.gutter)for(var e=Math.ceil(t.rect[c]/u),o=Math.floor((this.shiftPacker[c]+this.gutter)/u),h=(o-e)*u,s=0;s<o;s++)this._addShiftTarget(s*u,0,h);else h=this.shiftPacker[c]+this.gutter-t.rect[c],this._addShiftTarget(0,0,h);var e=this._getItemsForLayout(this.items),d=this._getPackMethod();e.forEach(function(t){var e=t.rect,i=(this._setRectSize(t.element,e),this.shiftPacker[d](e),this._addShiftTarget(e.x,e.y,h),l?e.x+e.width:e.x),n=l?e.y:e.y+e.height;if(this._addShiftTarget(i,n,h),u)for(var o=Math.round(e[c]/u),s=1;s<o;s++){var r=l?i:e.x+u*s,a=l?e.y+u*s:n;this._addShiftTarget(r,a,h)}},this)},i._addShiftTarget=function(t,e,i){var n=this._getOption("horizontal")?e:t;0!==n&&i<n||-1==this.shiftTargetKeys.indexOf(i=t+","+e)&&(this.shiftTargetKeys.push(i),this.shiftTargets.push({x:t,y:e}))},i.shift=function(t,e,i){var n,o=1/0,s={x:e,y:i};this.shiftTargets.forEach(function(t){i=s.x-t.x,e=s.y-t.y;var e,i=Math.sqrt(i*i+e*e);i<o&&(n=t,o=i)}),t.rect.x=n.x,t.rect.y=n.y},i.itemDragMove=function(t,e,i){function n(){o.shift(s,e,i),s.positionDropPlaceholder(),o.layout()}var o,s=this.isEnabled&&this.getItem(t);s&&(e-=this.size.paddingLeft,i-=this.size.paddingTop,o=this,t=new Date,this._itemDragTime&&t-this._itemDragTime<120?(clearTimeout(this.dragTimeout),this.dragTimeout=setTimeout(n,120)):(n(),this._itemDragTime=t))},i.itemDragEnd=function(t){function e(){2==++i&&(o.element.classList.remove("is-positioning-post-drag"),o.hideDropPlaceholder(),n.dispatchEvent("dragItemPositioned",null,[o]))}var i,n,o=this.isEnabled&&this.getItem(t);o&&(clearTimeout(this.dragTimeout),o.element.classList.add("is-positioning-post-drag"),i=0,n=this,o.once("layout",e),this.once("layoutComplete",e),o.moveTo(o.rect.x,o.rect.y),this.layout(),this.dragItemCount=Math.max(0,this.dragItemCount-1),this.sortItemsByPosition(),o.disablePlacing(),this.unstamp(o.element))},i.bindDraggabillyEvents=function(t){this._bindDraggabillyEvents(t,"on")},i.unbindDraggabillyEvents=function(t){this._bindDraggabillyEvents(t,"off")},i._bindDraggabillyEvents=function(t,e){var i=this.handleDraggabilly;t[e]("dragStart",i.dragStart),t[e]("dragMove",i.dragMove),t[e]("dragEnd",i.dragEnd)},i.bindUIDraggableEvents=function(t){this._bindUIDraggableEvents(t,"on")},i.unbindUIDraggableEvents=function(t){this._bindUIDraggableEvents(t,"off")},i._bindUIDraggableEvents=function(t,e){var i=this.handleUIDraggable;t[e]("dragstart",i.start)[e]("drag",i.drag)[e]("dragstop",i.stop)},i.destroy);return i.destroy=function(){a.apply(this,arguments),this.isEnabled=!1},s.Rect=r,s.Packer=e,s}),function(t,e){"function"==typeof define&&define.amd?define(["isotopet4s-layout/js/layout-mode","packery/js/packery"],e):"object"==typeof module&&module.exports?module.exports=e(require("isotopet4s-layout/js/layout-mode"),require("packery")):e(t.isotopet4s.LayoutMode,t.Packery)}(window,function(t,e){var i,t=t.create("packery"),n=t.prototype,o={_getElementOffset:!0,_getMeasurement:!0};for(i in e.prototype)o[i]||(n[i]=e.prototype[i]);var s=n._resetLayout,r=(n._resetLayout=function(){this.packer=this.packer||new e.Packer,this.shiftPacker=this.shiftPacker||new e.Packer,s.apply(this,arguments)},n._getItemLayoutPosition),a=(n._getItemLayoutPosition=function(t){return t.rect=t.rect||new e.Rect,r.call(this,t)},n.needsResizeLayout),l=(n.needsResizeLayout=function(){return this._getOption("horizontal")?this.needsVerticalResizeLayout():a.call(this)},n._getOption);return n._getOption=function(t){return"horizontal"==t?void 0!==this.options.isHorizontal?this.options.isHorizontal:this.options.horizontal:l.apply(this.isotopet4s,arguments)},t}),function(e,i){"function"==typeof define&&define.amd?define("jquery-bridget/jquery-bridget",["jquery"],function(t){return i(e,t)}):"object"==typeof module&&module.exports?module.exports=i(e,require("jquery")):e.jQuery_T4NTBridget=i(e,e.jQuery_T4NT)}(window,function(t,e){"use strict";var i=Array.prototype.slice,n=t.console,h=void 0===n?function(){}:function(t){n.error(t)};function o(l,c,u){(u=u||e||t.jQuery_T4NT)&&(c.prototype.option||(c.prototype.option=function(t){u.isPlainObject(t)&&(this.options=u.extend(!0,this.options,t))}),u.fn[l]=function(t){var n,e,o,s,r,a;return"string"==typeof t?(e=this,o=t,s=i.call(arguments,1),a="$()."+l+'("'+o+'")',e.each(function(t,e){var i,e=u.data(e,l);e?(i=e[o])&&"_"!=o.charAt(0)?(i=i.apply(e,s),r=void 0===r?i:r):h(a+" is not a valid method"):h(l+" not initialized. Cannot call methods, i.e. "+a)}),void 0!==r?r:e):(n=t,this.each(function(t,e){var i=u.data(e,l);i?(i.option(n),i._init()):(i=new c(e,n),u.data(e,l,i))}),this)},s(u))}function s(t){t&&!t.bridget&&(t.bridget=o)}return s(e||t.jQuery_T4NT),o}),function(t,e){"function"==typeof define&&define.amd?define("ev-emitter/ev-emitter",e):"object"==typeof module&&module.exports?module.exports=e():t.EvEmitter=e()}("undefined"!=typeof window?window:this,function(){function t(){}var e=t.prototype;return e.on=function(t,e){var i;if(t&&e)return-1==(i=(i=this._events=this._events||{})[t]=i[t]||[]).indexOf(e)&&i.push(e),this},e.once=function(t,e){var i;if(t&&e)return this.on(t,e),((i=this._onceEvents=this._onceEvents||{})[t]=i[t]||{})[e]=!0,this},e.off=function(t,e){t=this._events&&this._events[t];if(t&&t.length)return-1!=(e=t.indexOf(e))&&t.splice(e,1),this},e.emitEvent=function(t,e){var i=this._events&&this._events[t];if(i&&i.length){i=i.slice(0),e=e||[];for(var n=this._onceEvents&&this._onceEvents[t],o=0;o<i.length;o++){var s=i[o];n&&n[s]&&(this.off(t,s),delete n[s]),s.apply(this,e)}return this}},e.allOff=function(){delete this._events,delete this._onceEvents},t}),function(t,e){"function"==typeof define&&define.amd?define("get-size/get-size",e):"object"==typeof module&&module.exports?module.exports=e():t.getSize=e()}(window,function(){"use strict";function y(t){var e=parseFloat(t);return-1==t.indexOf("%")&&!isNaN(e)&&e}var e="undefined"==typeof console?function(){}:function(t){console.error(t)},v=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],b=v.length;function x(t){t=getComputedStyle(t);return t||e("Style returned "+t+". Are you running this code in a hidden iframe on Firefox? See https://bit.ly/getsizebug1"),t}var S,w=!1;return function t(e){if(w||(w=!0,(h=document.createElement("div")).style.width="200px",h.style.padding="1px 2px 3px 4px",h.style.borderStyle="solid",h.style.borderWidth="1px 2px 3px 4px",h.style.boxSizing="border-box",(u=document.body||document.documentElement).appendChild(h),r=x(h),S=200==Math.round(y(r.width)),t.isBoxSizeOuter=S,u.removeChild(h)),(e="string"==typeof e?document.querySelector(e):e)&&"object"==typeof e&&e.nodeType){var i=x(e);if("none"==i.display){for(var n={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},o=0;o<b;o++)n[v[o]]=0;return n}var s={};s.width=e.offsetWidth,s.height=e.offsetHeight;for(var r=s.isBorderBox="border-box"==i.boxSizing,a=0;a<b;a++){var l=v[a],c=i[l],c=parseFloat(c);s[l]=isNaN(c)?0:c}var u=s.paddingLeft+s.paddingRight,h=s.paddingTop+s.paddingBottom,e=s.marginLeft+s.marginRight,d=s.marginTop+s.marginBottom,f=s.borderLeftWidth+s.borderRightWidth,p=s.borderTopWidth+s.borderBottomWidth,g=r&&S,m=y(i.width),m=(!1!==m&&(s.width=m+(g?0:u+f)),y(i.height));return!1!==m&&(s.height=m+(g?0:h+p)),s.innerWidth=s.width-(u+f),s.innerHeight=s.height-(h+p),s.outerWidth=s.width+e,s.outerHeight=s.height+d,s}var h,u,r}}),function(t,e){"use strict";"function"==typeof define&&define.amd?define("desandro-matches-selector/matches-selector",e):"object"==typeof module&&module.exports?module.exports=e():t.matchesSelector=e()}(window,function(){"use strict";var i=function(){var t=window.Element.prototype;if(t.matches)return"matches";if(t.matchesSelector)return"matchesSelector";for(var e=["webkit","moz","ms","o"],i=0;i<e.length;i++){var n=e[i]+"MatchesSelector";if(t[n])return n}}();return function(t,e){return t[i](e)}}),function(e,i){"function"==typeof define&&define.amd?define("fizzy-ui-utils/utils",["desandro-matches-selector/matches-selector"],function(t){return i(e,t)}):"object"==typeof module&&module.exports?module.exports=i(e,require("desandro-matches-selector")):e.fizzyUIUtils=i(e,e.matchesSelector)}(window,function(i,s){var l={extend:function(t,e){for(var i in e)t[i]=e[i];return t},modulo:function(t,e){return(t%e+e)%e}},e=Array.prototype.slice,c=(l.makeArray=function(t){return Array.isArray(t)?t:null==t?[]:"object"==typeof t&&"number"==typeof t.length?e.call(t):[t]},l.removeFrom=function(t,e){e=t.indexOf(e);-1!=e&&t.splice(e,1)},l.getParent=function(t,e){for(;t.parentNode&&t!=document.body;)if(s(t=t.parentNode,e))return t},l.getQueryElement=function(t){return"string"==typeof t?document.querySelector(t):t},l.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},l.filterFindElements=function(t,n){t=l.makeArray(t);var o=[];return t.forEach(function(t){if(t instanceof HTMLElement)if(n){s(t,n)&&o.push(t);for(var e=t.querySelectorAll(n),i=0;i<e.length;i++)o.push(e[i])}else o.push(t)}),o},l.debounceMethod=function(t,e,n){n=n||100;var o=t.prototype[e],s=e+"Timeout";t.prototype[e]=function(){var t=this[s],e=(clearTimeout(t),arguments),i=this;this[s]=setTimeout(function(){o.apply(i,e),delete i[s]},n)}},l.docReady=function(t){var e=document.readyState;"complete"==e||"interactive"==e?setTimeout(t):document.addEventListener("DOMContentLoaded",t)},l.toDashed=function(t){return t.replace(/(.)([A-Z])/g,function(t,e,i){return e+"-"+i}).toLowerCase()},i.console);return l.htmlInit=function(r,a){l.docReady(function(){var t=l.toDashed(a),n="data-"+t,e=document.querySelectorAll("["+n+"]"),t=document.querySelectorAll(".js-"+t),e=l.makeArray(e).concat(l.makeArray(t)),o=n+"-options",s=i.jQuery_T4NT;e.forEach(function(e){var t,i=e.getAttribute(n)||e.getAttribute(o);try{t=i&&JSON.parse(i)}catch(t){return void(c&&c.error("Error parsing "+n+" on "+e.className+": "+t))}i=new r(e,t);s&&s.data(e,a,i)})})},l}),function(t,e){"function"==typeof define&&define.amd?define("flickityt4s/js/cell",["get-size/get-size"],function(t){return e(0,t)}):"object"==typeof module&&module.exports?module.exports=e(0,require("get-size")):(t.Flickityt4s=t.Flickityt4s||{},t.Flickityt4s.Cell=e(0,t.getSize))}(window,function(t,e){function i(t,e){this.element=t,this.parent=e,this.create()}var n=i.prototype;return n.create=function(){this.element.style.position="absolute",this.element.setAttribute("aria-hidden","true"),this.x=0,this.shift=0,this.element.style[this.parent.originSide]=0},n.destroy=function(){this.unselect(),this.element.style.position="";var t=this.parent.originSide;this.element.style[t]="",this.element.style.transform="",this.element.removeAttribute("aria-hidden")},n.getSize=function(){this.size=e(this.element)},n.setPosition=function(t){this.x=t,this.updateTarget(),this.renderPosition(t)},n.updateTarget=n.setDefaultTarget=function(){var t="left"==this.parent.originSide?"marginLeft":"marginRight";this.target=this.x+this.size[t]+this.size.width*this.parent.cellAlign},n.renderPosition=function(t){var e="left"===this.parent.originSide?1:-1,t=this.parent.options.percentPosition?t*e*(this.parent.size.innerWidth/this.size.width):t*e;this.element.style.transform="translateX("+this.parent.getPositionValue(t)+")"},n.select=function(){this.element.classList.add("is-selected"),this.element.removeAttribute("aria-hidden")},n.unselect=function(){this.element.classList.remove("is-selected"),this.element.setAttribute("aria-hidden","true")},n.wrapShift=function(t){this.shift=t,this.renderPosition(this.x+this.parent.slideableWidth*t)},n.remove=function(){this.element.parentNode.removeChild(this.element)},i}),function(t,e){"function"==typeof define&&define.amd?define("flickityt4s/js/slide",e):"object"==typeof module&&module.exports?module.exports=e():(t.Flickityt4s=t.Flickityt4s||{},t.Flickityt4s.Slide=e())}(window,function(){"use strict";function t(t){this.parent=t,this.isOriginLeft="left"==t.originSide,this.cells=[],this.outerWidth=0,this.height=0}var e=t.prototype;return e.addCell=function(t){var e;this.cells.push(t),this.outerWidth+=t.size.outerWidth,this.height=Math.max(t.size.outerHeight,this.height),1==this.cells.length&&(this.x=t.x,e=this.isOriginLeft?"marginLeft":"marginRight",this.firstMargin=t.size[e])},e.updateTarget=function(){var t=this.isOriginLeft?"marginRight":"marginLeft",e=this.getLastCell(),e=e?e.size[t]:0,t=this.outerWidth-(this.firstMargin+e);this.target=this.x+this.firstMargin+t*this.parent.cellAlign},e.getLastCell=function(){return this.cells[this.cells.length-1]},e.select=function(){this.cells.forEach(function(t){t.select()})},e.unselect=function(){this.cells.forEach(function(t){t.unselect()})},e.getCellElements=function(){return this.cells.map(function(t){return t.element})},t}),function(t,e){"function"==typeof define&&define.amd?define("flickityt4s/js/animate",["fizzy-ui-utils/utils"],function(t){return e(0,t)}):"object"==typeof module&&module.exports?module.exports=e(0,require("fizzy-ui-utils")):(t.Flickityt4s=t.Flickityt4s||{},t.Flickityt4s.animatePrototype=e(0,t.fizzyUIUtils))}(window,function(t,e){return{startAnimation:function(){this.isAnimating||(this.isAnimating=!0,this.restingFrames=0,this.animate())},animate:function(){this.applyDragForce(),this.applySelectedAttraction();var t,e=this.x;this.integratePhysics(),this.positionSlider(),this.settle(e),this.isAnimating&&(t=this,requestAnimationFrame(function(){t.animate()}))},positionSlider:function(){var t=this.x;this.options.wrapAround&&1<this.cells.length&&(t=e.modulo(t,this.slideableWidth),t-=this.slideableWidth,this.shiftWrapCells(t)),this.setTranslateX(t,this.isAnimating),this.dispatchScrollEvent()},setTranslateX:function(t,e){t+=this.cursorPosition;t=this.getPositionValue(t=RtlT4s?-t:t);this.slider.style.transform=e?"translate3d("+t+",0,0)":"translateX("+t+")"},dispatchScrollEvent:function(){var t,e=this.slides[0];e&&(t=(e=-this.x-e.target)/this.slidesWidth,this.dispatchEvent("scroll",null,[t,e]))},positionSliderAtSelected:function(){this.cells.length&&(this.x=-this.selectedSlide.target,this.velocity=0,this.positionSlider())},getPositionValue:function(t){return this.options.percentPosition?.01*Math.round(t/this.size.innerWidth*1e4)+"%":Math.round(t)+"px"},settle:function(t){this.isPointerDown||Math.round(100*this.x)!=Math.round(100*t)||this.restingFrames++,2<this.restingFrames&&(this.isAnimating=!1,delete this.isFreeScrolling,this.positionSlider(),this.dispatchEvent("settle",null,[this.selectedIndex]))},shiftWrapCells:function(t){var e=this.cursorPosition+t,e=(this._shiftCells(this.beforeShiftCells,e,-1),this.size.innerWidth-(t+this.slideableWidth+this.cursorPosition));this._shiftCells(this.afterShiftCells,e,1)},_shiftCells:function(t,e,i){for(var n=0;n<t.length;n++){var o=t[n];o.wrapShift(0<e?i:0),e-=o.size.outerWidth}this._checkVisibility()},_unshiftCells:function(t){if(t&&t.length)for(var e=0;e<t.length;e++)t[e].wrapShift(0)},integratePhysics:function(){this.x+=this.velocity,this.velocity*=this.getFrictionFactor()},applyForce:function(t){this.velocity+=t},getFrictionFactor:function(){return 1-this.options[this.isFreeScrolling?"freeScrollFriction":"friction"]},getRestingPosition:function(){return this.x+this.velocity/(1-this.getFrictionFactor())},applyDragForce:function(){var t;this.isDraggable&&this.isPointerDown&&(t=this.dragX-this.x-this.velocity,this.applyForce(t))},applySelectedAttraction:function(){var t;this.isDraggable&&this.isPointerDown||this.isFreeScrolling||!this.slides.length||(t=(-1*this.selectedSlide.target-this.x)*this.options.selectedAttraction,this.applyForce(t))}}}),function(r,a){var t;"function"==typeof define&&define.amd?define("flickityt4s/js/flickityt4s",["ev-emitter/ev-emitter","get-size/get-size","fizzy-ui-utils/utils","./cell","./slide","./animate"],function(t,e,i,n,o,s){return a(r,t,e,i,n,o,s)}):"object"==typeof module&&module.exports?module.exports=a(r,require("ev-emitter"),require("get-size"),require("fizzy-ui-utils"),require("./cell"),require("./slide"),require("./animate")):(t=r.Flickityt4s,r.Flickityt4s=a(r,r.EvEmitter,r.getSize,r.fizzyUIUtils,t.Cell,t.Slide,t.animatePrototype))}(window,function(n,t,e,r,i,a,o){var s=n.jQuery_T4NT,l=n.getComputedStyle,c=n.console;function u(t,e){for(t=r.makeArray(t);t.length;)e.appendChild(t.shift())}var h=0,d={};function f(t,e){var i,n=r.getQueryElement(t);if(n){if(this.element=n,this.element.flickityt4sGUID)return(i=d[this.element.flickityt4sGUID])&&i.option(e),i;switch(s&&(this.$element=s(this.element)),this.options=r.extend({},this.constructor.defaults),e.originwrapAround=e.wrapAround,e.rightToLeft="rtl"==document.documentElement.getAttribute("dir"),e.arrowIcon){case"1":e.arrowShape="M 10,50 L 60,100 L 70,90 L 30,50  L 70,10 L 60,0 Z";break;case"2":e.arrowShape="M 10,50 L 60,100 L 65,95 L 20,50  L 65,5 L 60,0 Z";break;case"3":e.arrowShape="M 0,50 L 60,00 L 50,30 L 80,30 L 80,70 L 50,70 L 60,100 Z"}this.option(e),this._create()}else c&&c.error("Bad element for Flickityt4s: "+(n||t))}f.defaults={accessibility:!0,cellAlign:"center",freeScrollFriction:.075,friction:.28,namespaceJQueryEvents:!0,percentPosition:!0,resize:!0,selectedAttraction:.025,setGallerySize:!0,setPrevNextButtons:!1,checkVisibility:!1,sync:!1},f.createMethods=[];var p=f.prototype,g=(r.extend(p,t.prototype),p._create=function(){var t,e=this.guid=++h;for(t in this.element.flickityt4sGUID=e,(d[e]=this).selectedIndex=0,this.restingFrames=0,this.x=0,this.velocity=0,this.originSide=RtlT4s?"right":"left",this.viewport=document.createElement("div"),this.viewport.className="flickityt4s-viewport",this._createSlider(),(this.options.resize||this.options.watchCSS)&&n.addEventListener("resize",this),this.options.on){var i=this.options.on[t];this.on(t,i)}f.createMethods.forEach(function(t){this[t]()},this),this.options.watchCSS?this.watchCSS():this.activate()},p.option=function(t){r.extend(this.options,t)},p.activate=function(){this.isActive||(this.isActive=!0,this.element.classList.add("flickityt4s-enabled"),RtlT4s&&this.element.classList.add("flickityt4s-rtl"),this.getSize(),u(this._filterFindCellElements(this.element.children),this.slider),this.viewport.appendChild(this.slider),this.element.appendChild(this.viewport),this.reloadCells(),this.options.accessibility&&(this.element.tabIndex=0,this.element.addEventListener("keydown",this)),this.emitEvent("activate"),this.selectInitialIndex(),this.isInitActivated=!0,this.dispatchEvent("ready"))},p._createSlider=function(){var t=document.createElement("div");t.className="flickityt4s-slider",t.style[this.originSide]=0,this.slider=t},p._filterFindCellElements=function(t){return r.filterFindElements(t,this.options.cellSelector)},p.reloadCells=function(){this.cells=this._makeCells(this.slider.children),this.positionCells(),this._getWrapShiftCells(),this.setGallerySize(),this.setPrevNextButtons()},p._makeCells=function(t){return this._filterFindCellElements(t).map(function(t){return new i(t,this)},this)},p.getLastCell=function(){return this.cells[this.cells.length-1]},p.getLastSlide=function(){return this.slides[this.slides.length-1]},p.positionCells=function(){this._sizeCells(this.cells),this._positionCells(0)},p._positionCells=function(t){this.maxCellHeight=(t=t||0)&&this.maxCellHeight||0;var e,i=0;0<t&&(i=(e=this.cells[t-1]).x+e.size.outerWidth);for(var n=this.cells.length,o=t;o<n;o++){var s=this.cells[o];s.setPosition(i),i+=s.size.outerWidth,this.maxCellHeight=Math.max(s.size.outerHeight,this.maxCellHeight)}this.slideableWidth=i,this.updateSlides(),this._containSlides(),this.slidesWidth=n?this.getLastSlide().target-this.slides[0].target:0,this.maxVisibilityHeight=0},p._sizeCells=function(t){t.forEach(function(t){t.getSize()})},p.updateSlides=function(){var n,o,s;this.slides=[],this.cells.length&&(n=new a(this),this.slides.push(n),o="left"==this.originSide?"marginRight":"marginLeft",s=this._getCanCellFit(),this.cells.forEach(function(t,e){var i;n.cells.length&&(i=n.outerWidth-n.firstMargin+(t.size.outerWidth-t.size[o]),s.call(this,e,i)||(n.updateTarget(),n=new a(this),this.slides.push(n))),n.addCell(t)},this),n.updateTarget(),this.updateSelectedSlide())},p._getCanCellFit=function(){var e,i,t=this.options.groupCells;return t?"number"==typeof t?(e=parseInt(t,10),function(t){return t%e!=0}):(t="string"==typeof t&&t.match(/^(\d+)%$/),i=t?parseInt(t[1],10)/100:1,function(t,e){return e<=(this.size.innerWidth+1)*i}):function(){return!1}},p._init=p.reposition=function(){this.positionCells(),this.positionSliderAtSelected()},p.getSize=function(){this.size=e(this.element),this.setCellAlign(),this.cursorPosition=this.size.innerWidth*this.cellAlign},{center:{left:.5,right:.5},left:{left:0,right:1},right:{right:0,left:1}});return p.setCellAlign=function(){var t=g[this.options.cellAlign];this.cellAlign=t?t[this.originSide]:this.options.cellAlign},p.setGallerySize=function(){var t;this.options.setGallerySize&&(t=this.options.adaptiveHeight&&this.selectedSlide?this.selectedSlide.height:this.maxCellHeight,t=this.maxVisibilityHeight&&this.maxVisibilityHeight>t?this.maxVisibilityHeight:t,this.viewport.style.height=t+"px")},p.setPrevNextButtons=function(){var t;this.options.setPrevNextButtons&&null!==(t=this.viewport.querySelector(".is-selected [data-cacl-slide]"))&&(t=t.offsetHeight/2,this.element.style.setProperty("--prev-next-top",t+"px"))},p._checkVisibility=function(){if(this.options.checkVisibility&&this.options.adaptiveHeight)for(var t=this.viewport.getBoundingClientRect().x,e=this.viewport.offsetWidth,i=this.cells.length,n=0;n<i;n++){var o=this.cells[n],s=o.element.getBoundingClientRect().x-t;s+o.size.innerWidth>t&&s+o.size.innerWidth<e||t<s&&s<e?(this.maxVisibilityHeight=Math.max(o.size.outerHeight,this.maxVisibilityHeight),o.element.classList.add("is-t4s-visible"),o.element.removeAttribute("aria-hidden")):(o.element.classList.remove("is-t4s-visible"),o.element.setAttribute("aria-hidden",!0))}},p._getWrapShiftCells=function(){var t,e;this.options.originwrapAround&&(this.slides.length<2?this.options.wrapAround=!1:(this.options.wrapAround=!0,this._unshiftCells(this.beforeShiftCells),this._unshiftCells(this.afterShiftCells),t=this.cursorPosition,e=this.cells.length-1,this.beforeShiftCells=this._getGapCells(t,e,-1),t=this.size.innerWidth-this.cursorPosition,this.afterShiftCells=this._getGapCells(t,0,1)))},p._getGapCells=function(t,e,i){for(var n=[];0<t;){var o=this.cells[e];if(!o)break;n.push(o),e+=i,t-=o.size.outerWidth}return n},p._containSlides=function(){var t,e,i,n,o,s;this.options.contain&&!this.options.wrapAround&&this.cells.length&&(t=RtlT4s?"marginRight":"marginLeft",e=RtlT4s?"marginLeft":"marginRight",i=this.slideableWidth-this.getLastCell().size[e],n=i<this.size.innerWidth,o=this.cursorPosition+this.cells[0].size[t],s=i-this.size.innerWidth*(1-this.cellAlign),this.slides.forEach(function(t){n?t.target=i*this.cellAlign:(t.target=Math.max(t.target,o),t.target=Math.min(t.target,s))},this))},p.dispatchEvent=function(t,e,i){var n=e?[e].concat(i):i;this.emitEvent(t,n),s&&this.$element&&(n=t+=this.options.namespaceJQueryEvents?".flickityt4s":"",e&&((e=new s.Event(e)).type=t,n=e),this.$element.trigger(n,i))},p.select=function(t,e,i){this.isActive&&(t=parseInt(t,10),this._wrapSelect(t),(this.options.wrapAround||e)&&(t=r.modulo(t,this.slides.length)),this.slides[t])&&(e=this.selectedIndex,this.selectedIndex=t,this.updateSelectedSlide(),i?this.positionSliderAtSelected():this.startAnimation(),this.options.adaptiveHeight&&this.setGallerySize(),this.setPrevNextButtons(),this.dispatchEvent("select",null,[t]),t!=e&&this.dispatchEvent("change",null,[t]),this.dispatchEvent("cellSelect"))},p._wrapSelect=function(t){var e=this.slides.length;if(!(this.options.wrapAround&&1<e))return t;var i=r.modulo(t,e),n=Math.abs(i-this.selectedIndex),o=Math.abs(i+e-this.selectedIndex),i=Math.abs(i-e-this.selectedIndex);!this.isDragSelect&&o<n?t+=e:!this.isDragSelect&&i<n&&(t-=e),t<0?this.x-=this.slideableWidth:e<=t&&(this.x+=this.slideableWidth)},p.previous=function(t,e){this.select(this.selectedIndex-1,t,e)},p.next=function(t,e){this.select(this.selectedIndex+1,t,e)},p.updateSelectedSlide=function(){var t=this.slides[this.selectedIndex];t&&(this.unselectSelectedSlide(),(this.selectedSlide=t).select(),this.selectedCells=t.cells,this.selectedElements=t.getCellElements(),this.selectedCell=t.cells[0],this.selectedElement=this.selectedElements[0])},p.unselectSelectedSlide=function(){this.selectedSlide&&this.selectedSlide.unselect()},p.selectInitialIndex=function(){var t,e=this.options.initialIndex;this.isInitActivated?this.select(this.selectedIndex,!1,!0):e&&"string"==typeof e&&this.queryCell(e)?this.selectCell(e,!1,!0):(t=0,e&&this.slides[e]&&(t=e),this.select(t,!1,!0))},p.selectCell=function(t,e,i){var t=this.queryCell(t);t&&(t=this.getCellSlideIndex(t),this.select(t,e,i))},p.getCellSlideIndex=function(t){for(var e=0;e<this.slides.length;e++)if(-1!=this.slides[e].cells.indexOf(t))return e},p.getCell=function(t){for(var e=0;e<this.cells.length;e++){var i=this.cells[e];if(i.element==t)return i}},p.getCells=function(t){t=r.makeArray(t);var e=[];return t.forEach(function(t){t=this.getCell(t);t&&e.push(t)},this),e},p.getCellElements=function(){return this.cells.map(function(t){return t.element})},p.getParentCell=function(t){return this.getCell(t)||(t=r.getParent(t,".flickityt4s-slider > *"),this.getCell(t))},p.getAdjacentCellElements=function(t,e){if(!t)return this.selectedSlide.getCellElements();e=void 0===e?this.selectedIndex:e;var i=this.slides.length;if(i<=1+2*t)return this.getCellElements();for(var n=[],o=e-t;o<=e+t;o++){var s=this.options.wrapAround?r.modulo(o,i):o,s=this.slides[s];s&&(n=n.concat(s.getCellElements()))}return n},p.queryCell=function(t){if("number"==typeof t)return this.cells[t];if("string"==typeof t){if(t.match(/^[#.]?[\d/]/))return;t=this.element.querySelector(t)}return this.getCell(t)},p.uiChange=function(){this.emitEvent("uiChange")},p.childUIPointerDown=function(t){"touchstart"!=t.type&&t.preventDefault(),this.focus()},p.onresize=function(){this.watchCSS(),this.resize()},r.debounceMethod(f,"onresize",150),p.resize=function(){var t;!this.isActive||this.isAnimating||this.isDragging||(this.getSize(),this.options.wrapAround&&(this.x=r.modulo(this.x,this.slideableWidth)),this.positionCells(),this._getWrapShiftCells(),this.setGallerySize(),this.setPrevNextButtons(),this.emitEvent("resize"),t=this.selectedElements&&this.selectedElements[0],this.selectCell(t,!1,!0))},p.watchCSS=function(){this.options.watchCSS&&(-1!=l(this.element,":after").content.indexOf("flickityt4s")?this.activate():this.deactivate())},p.onkeydown=function(t){var e=document.activeElement&&document.activeElement!=this.element;this.options.accessibility&&!e&&(e=f.keyboardHandlers[t.keyCode])&&e.call(this)},f.keyboardHandlers={37:function(){var t=RtlT4s?"next":"previous";this.uiChange(),this[t]()},39:function(){var t=RtlT4s?"previous":"next";this.uiChange(),this[t]()}},p.focus=function(){var t=n.pageYOffset;this.element.focus({preventScroll:!0}),n.pageYOffset!=t&&n.scrollTo(n.pageXOffset,t)},p.deactivate=function(){this.isActive&&(this.element.classList.remove("flickityt4s-enabled"),this.element.classList.remove("flickityt4s-rtl"),this.unselectSelectedSlide(),this.cells.forEach(function(t){t.destroy()}),this.element.removeChild(this.viewport),u(this.slider.children,this.element),this.options.accessibility&&(this.element.removeAttribute("tabIndex"),this.element.removeEventListener("keydown",this)),this.isActive=!1,this.emitEvent("deactivate"))},p.destroy=function(){this.deactivate(),n.removeEventListener("resize",this),this.allOff(),this.emitEvent("destroy"),s&&this.$element&&s.removeData(this.element,"flickityt4s"),delete this.element.flickityt4sGUID,delete d[this.guid]},r.extend(p,o),f.data=function(t){t=(t=r.getQueryElement(t))&&t.flickityt4sGUID;return t&&d[t]},r.htmlInit(f,"flickityt4s"),s&&s.bridget&&s.bridget("flickityt4s",f),f.setJQuery=function(t){s=t},f.Cell=i,f.Slide=a,f}),function(e,i){"function"==typeof define&&define.amd?define("unipointer/unipointer",["ev-emitter/ev-emitter"],function(t){return i(e,t)}):"object"==typeof module&&module.exports?module.exports=i(e,require("ev-emitter")):e.Unipointer=i(e,e.EvEmitter)}(window,function(n,t){function e(){}var t=e.prototype=Object.create(t.prototype),i=(t.bindStartEvent=function(t){this._bindStartEvent(t,!0)},t.unbindStartEvent=function(t){this._bindStartEvent(t,!1)},t._bindStartEvent=function(t,e){var e=(e=void 0===e||e)?"addEventListener":"removeEventListener",i="mousedown";"ontouchstart"in n?i="touchstart":n.PointerEvent&&(i="pointerdown"),t[e](i,this)},t.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},t.getTouch=function(t){for(var e=0;e<t.length;e++){var i=t[e];if(i.identifier==this.pointerIdentifier)return i}},t.onmousedown=function(t){var e=t.button;e&&0!==e&&1!==e||this._pointerDown(t,t)},t.ontouchstart=function(t){this._pointerDown(t,t.changedTouches[0])},t.onpointerdown=function(t){this._pointerDown(t,t)},t._pointerDown=function(t,e){t.button||this.isPointerDown||(this.isPointerDown=!0,this.pointerIdentifier=void 0!==e.pointerId?e.pointerId:e.identifier,this.pointerDown(t,e))},t.pointerDown=function(t,e){this._bindPostStartEvents(t),this.emitEvent("pointerDown",[t,e])},{mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"]});return t._bindPostStartEvents=function(t){t&&((t=i[t.type]).forEach(function(t){n.addEventListener(t,this)},this),this._boundPointerEvents=t)},t._unbindPostStartEvents=function(){this._boundPointerEvents&&(this._boundPointerEvents.forEach(function(t){n.removeEventListener(t,this)},this),delete this._boundPointerEvents)},t.onmousemove=function(t){this._pointerMove(t,t)},t.onpointermove=function(t){t.pointerId==this.pointerIdentifier&&this._pointerMove(t,t)},t.ontouchmove=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerMove(t,e)},t._pointerMove=function(t,e){this.pointerMove(t,e)},t.pointerMove=function(t,e){this.emitEvent("pointerMove",[t,e])},t.onmouseup=function(t){this._pointerUp(t,t)},t.onpointerup=function(t){t.pointerId==this.pointerIdentifier&&this._pointerUp(t,t)},t.ontouchend=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerUp(t,e)},t._pointerUp=function(t,e){this._pointerDone(),this.pointerUp(t,e)},t.pointerUp=function(t,e){this.emitEvent("pointerUp",[t,e])},t._pointerDone=function(){this._pointerReset(),this._unbindPostStartEvents(),this.pointerDone()},t._pointerReset=function(){this.isPointerDown=!1,delete this.pointerIdentifier},t.pointerDone=function(){},t.onpointercancel=function(t){t.pointerId==this.pointerIdentifier&&this._pointerCancel(t,t)},t.ontouchcancel=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerCancel(t,e)},t._pointerCancel=function(t,e){this._pointerDone(),this.pointerCancel(t,e)},t.pointerCancel=function(t,e){this.emitEvent("pointerCancel",[t,e])},e.getPointerPoint=function(t){return{x:t.pageX,y:t.pageY}},e}),function(e,i){"function"==typeof define&&define.amd?define("unidragger/unidragger",["unipointer/unipointer"],function(t){return i(e,t)}):"object"==typeof module&&module.exports?module.exports=i(e,require("unipointer")):e.Unidragger=i(e,e.Unipointer)}(window,function(s,t){function e(){}var i=e.prototype=Object.create(t.prototype),n=(i.bindHandles=function(){this._bindHandles(!0)},i.unbindHandles=function(){this._bindHandles(!1)},i._bindHandles=function(t){for(var e=(t=void 0===t||t)?"addEventListener":"removeEventListener",i=t?this._touchActionValue:"",n=0;n<this.handles.length;n++){var o=this.handles[n];this._bindStartEvent(o,t),o[e]("click",this),s.PointerEvent&&(o.style.touchAction=i)}},i._touchActionValue="none",i.pointerDown=function(t,e){this.okayPointerDown(t)&&(this.pointerDownPointer={pageX:e.pageX,pageY:e.pageY},t.preventDefault(),this.pointerDownBlur(),this._bindPostStartEvents(t),this.emitEvent("pointerDown",[t,e]))},{TEXTAREA:!0,INPUT:!0,SELECT:!0,OPTION:!0}),o={radio:!0,checkbox:!0,button:!0,submit:!0,image:!0,file:!0};return i.okayPointerDown=function(t){var e=n[t.target.nodeName],t=o[t.target.type],e=!e||t;return e||this._pointerReset(),e},i.pointerDownBlur=function(){var t=document.activeElement;t&&t.blur&&t!=document.body&&t.blur()},i.pointerMove=function(t,e){var i=this._dragPointerMove(t,e);this.emitEvent("pointerMove",[t,e,i]),this._dragMove(t,e,i)},i._dragPointerMove=function(t,e){var i={x:e.pageX-this.pointerDownPointer.pageX,y:e.pageY-this.pointerDownPointer.pageY};return!this.isDragging&&this.hasDragStarted(i)&&this._dragStart(t,e),i},i.hasDragStarted=function(t){return 3<Math.abs(t.x)||3<Math.abs(t.y)},i.pointerUp=function(t,e){this.emitEvent("pointerUp",[t,e]),this._dragPointerUp(t,e)},i._dragPointerUp=function(t,e){this.isDragging?this._dragEnd(t,e):this._staticClick(t,e)},i._dragStart=function(t,e){this.isDragging=!0,this.isPreventingClicks=!0,this.dragStart(t,e)},i.dragStart=function(t,e){this.emitEvent("dragStart",[t,e])},i._dragMove=function(t,e,i){this.isDragging&&this.dragMove(t,e,i)},i.dragMove=function(t,e,i){t.preventDefault(),this.emitEvent("dragMove",[t,e,i])},i._dragEnd=function(t,e){this.isDragging=!1,setTimeout(function(){delete this.isPreventingClicks}.bind(this)),this.dragEnd(t,e)},i.dragEnd=function(t,e){this.emitEvent("dragEnd",[t,e])},i.onclick=function(t){this.isPreventingClicks&&t.preventDefault()},i._staticClick=function(t,e){this.isIgnoringMouseUp&&"mouseup"==t.type||(this.staticClick(t,e),"mouseup"!=t.type&&(this.isIgnoringMouseUp=!0,setTimeout(function(){delete this.isIgnoringMouseUp}.bind(this),400)))},i.staticClick=function(t,e){this.emitEvent("staticClick",[t,e])},e.getPointerPoint=t.getPointerPoint,e}),function(n,o){"function"==typeof define&&define.amd?define("flickityt4s/js/drag",["./flickityt4s","unidragger/unidragger","fizzy-ui-utils/utils"],function(t,e,i){return o(n,t,e,i)}):"object"==typeof module&&module.exports?module.exports=o(n,require("./flickityt4s"),require("unidragger"),require("fizzy-ui-utils")):n.Flickityt4s=o(n,n.Flickityt4s,n.Unidragger,n.fizzyUIUtils)}(window,function(i,t,e,s){s.extend(t.defaults,{draggable:">1",dragThreshold:3}),t.createMethods.push("_createDrag");var n=t.prototype,o=(s.extend(n,e.prototype),n._touchActionValue="pan-y",n._createDrag=function(){this.on("activate",this.onActivateDrag),this.on("uiChange",this._uiChangeDrag),this.on("deactivate",this.onDeactivateDrag),this.on("cellChange",this.updateDraggable)},n.onActivateDrag=function(){this.handles=[this.viewport],this.bindHandles(),this.updateDraggable()},n.onDeactivateDrag=function(){this.unbindHandles(),this.element.classList.remove("is-draggable")},n.updateDraggable=function(){">1"==this.options.draggable?this.isDraggable=1<this.slides.length:"smart"==this.options.draggable?(this.viewport,this.isDraggable=this.viewport.scrollWidth>this.viewport.offsetWidth):this.isDraggable=this.options.draggable,this.isDraggable?this.element.classList.add("is-draggable"):this.element.classList.remove("is-draggable")},n.bindDrag=function(){this.options.draggable=!0,this.updateDraggable()},n.unbindDrag=function(){this.options.draggable=!1,this.updateDraggable()},n._uiChangeDrag=function(){delete this.isFreeScrolling},n.pointerDown=function(t,e){this.isDraggable?this.okayPointerDown(t)&&(this._pointerDownPreventDefault(t),this.pointerDownFocus(t),document.activeElement!=this.element&&this.pointerDownBlur(),this.dragX=this.x,this.viewport.classList.add("is-pointer-down"),this.pointerDownScroll=r(),i.addEventListener("scroll",this),this._pointerDownDefault(t,e)):this._pointerDownDefault(t,e)},n._pointerDownDefault=function(t,e){this.pointerDownPointer={pageX:e.pageX,pageY:e.pageY},this._bindPostStartEvents(t),this.dispatchEvent("pointerDown",t,[e])},{INPUT:!0,TEXTAREA:!0,SELECT:!0});function r(){return{x:i.pageXOffset,y:i.pageYOffset}}return n.pointerDownFocus=function(t){o[t.target.nodeName]||this.focus()},n._pointerDownPreventDefault=function(t){var e="touchstart"==t.type,i=o[t.target.nodeName];e||"touch"==t.pointerType||i||t.preventDefault()},n.hasDragStarted=function(t){return Math.abs(t.x)>this.options.dragThreshold},n.pointerUp=function(t,e){delete this.isTouchScrolling,this.viewport.classList.remove("is-pointer-down"),this.dispatchEvent("pointerUp",t,[e]),this._dragPointerUp(t,e)},n.pointerDone=function(){i.removeEventListener("scroll",this),delete this.pointerDownScroll},n.dragStart=function(t,e){this.isDraggable&&(this.dragStartPosition=this.x,this.startAnimation(),i.removeEventListener("scroll",this),this.dispatchEvent("dragStart",t,[e]))},n.pointerMove=function(t,e){var i=this._dragPointerMove(t,e);this.dispatchEvent("pointerMove",t,[e,i]),this._dragMove(t,e,i)},n.dragMove=function(t,e,i){var n,o;this.isDraggable&&(t.preventDefault(),this.previousDragX=this.dragX,n=RtlT4s?-1:1,this.options.wrapAround&&(i.x%=this.slideableWidth),n=this.dragStartPosition+i.x*n,!this.options.wrapAround&&this.slides.length&&(n=(n=(o=Math.max(-this.slides[0].target,this.dragStartPosition))<n?.5*(n+o):n)<(o=Math.min(-this.getLastSlide().target,this.dragStartPosition))?.5*(n+o):n),this.dragX=n,this.dragMoveTime=new Date,this.dispatchEvent("dragMove",t,[e,i]))},n.dragEnd=function(t,e){var i,n;this.isDraggable&&(this.options.freeScroll&&(this.isFreeScrolling=!0),i=this.dragEndRestingSelect(),this.options.freeScroll&&!this.options.wrapAround?(n=this.getRestingPosition(),this.isFreeScrolling=-n>this.slides[0].target&&-n<this.getLastSlide().target):this.options.freeScroll||i!=this.selectedIndex||(i+=this.dragEndBoostSelect()),delete this.previousDragX,this.isDragSelect=this.options.wrapAround,this.select(i),delete this.isDragSelect,this.dispatchEvent("dragEnd",t,[e]))},n.dragEndRestingSelect=function(){var t=this.getRestingPosition(),e=Math.abs(this.getSlideDistance(-t,this.selectedIndex)),i=this._getClosestResting(t,e,1),t=this._getClosestResting(t,e,-1);return(i.distance<t.distance?i:t).index},n._getClosestResting=function(t,e,i){for(var n=this.selectedIndex,o=1/0,s=this.options.contain&&!this.options.wrapAround?function(t,e){return t<=e}:function(t,e){return t<e};s(e,o)&&(o=e,null!==(e=this.getSlideDistance(-t,n+=i)));)e=Math.abs(e);return{distance:o,index:n-i}},n.getSlideDistance=function(t,e){var i=this.slides.length,n=this.options.wrapAround&&1<i,o=n?s.modulo(e,i):e,o=this.slides[o];return o?(n=n?this.slideableWidth*Math.floor(e/i):0,t-(o.target+n)):null},n.dragEndBoostSelect=function(){var t,e;return void 0===this.previousDragX||!this.dragMoveTime||100<new Date-this.dragMoveTime?0:(t=this.getSlideDistance(-this.dragX,this.selectedIndex),e=this.previousDragX-this.dragX,0<t&&0<e?1:t<0&&e<0?-1:0)},n.staticClick=function(t,e){var i=this.getParentCell(t.target),n=i&&i.element,i=i&&this.cells.indexOf(i);this.dispatchEvent("staticClick",t,[e,n,i])},n.onscroll=function(){var t=r(),e=this.pointerDownScroll.x-t.x,t=this.pointerDownScroll.y-t.y;(3<Math.abs(e)||3<Math.abs(t))&&this._pointerDone()},t}),function(t,n){"function"==typeof define&&define.amd?define("flickityt4s/js/prev-next-button",["./flickityt4s","unipointer/unipointer","fizzy-ui-utils/utils"],function(t,e,i){return n(0,t,e,i)}):"object"==typeof module&&module.exports?module.exports=n(0,require("./flickityt4s"),require("unipointer"),require("fizzy-ui-utils")):n(0,t.Flickityt4s,t.Unipointer,t.fizzyUIUtils)}(window,function(t,e,i,n){"use strict";var o="http://www.w3.org/2000/svg";function s(t,e){this.direction=t,this.parent=e,this._create()}(s.prototype=Object.create(i.prototype))._create=function(){this.isEnabled=!0,this.isPrevious=-1==this.direction;var t=this.parent.options.rightToLeft?1:-1,t=(this.isLeft=this.direction==t,this.element=document.createElement("button")),e=(t.className="flickityt4s-button flickityt4s-prev-next-button",t.className+=this.isPrevious?" previous":" next",t.setAttribute("type","button"),this.disable(),t.setAttribute("aria-label",this.isPrevious?"Previous":"Next"),this.createSVG());t.appendChild(e),this.parent.on("select",this.update.bind(this)),this.on("pointerDown",this.parent.childUIPointerDown.bind(this.parent))},s.prototype.activate=function(){this.bindStartEvent(this.element),this.element.addEventListener("click",this),this.parent.element.appendChild(this.element)},s.prototype.deactivate=function(){this.parent.element.removeChild(this.element),this.unbindStartEvent(this.element),this.element.removeEventListener("click",this)},s.prototype.createSVG=function(){var t=document.createElementNS(o,"svg"),e=(t.setAttribute("class","flickityt4s-button-icon"),t.setAttribute("viewBox","0 0 100 100"),document.createElementNS(o,"path")),i="string"==typeof(i=this.parent.options.arrowShape)?i:"M "+i.x0+",50 L "+i.x1+","+(i.y1+50)+" L "+i.x2+","+(i.y2+50)+" L "+i.x3+",50  L "+i.x2+","+(50-i.y2)+" L "+i.x1+","+(50-i.y1)+" Z";return e.setAttribute("d",i),e.setAttribute("class","arrow"),this.isLeft||e.setAttribute("transform","translate(100, 100) rotate(180) "),t.appendChild(e),t},s.prototype.handleEvent=n.handleEvent,s.prototype.onclick=function(){var t;this.isEnabled&&(this.parent.uiChange(),t=this.isPrevious?"previous":"next",this.parent[t]())},s.prototype.enable=function(){this.isEnabled||(this.element.disabled=!1,this.isEnabled=!0)},s.prototype.disable=function(){this.isEnabled&&(this.element.disabled=!0,this.isEnabled=!1)},s.prototype.update=function(){var t=this.isPrevious?"prev_":"next_",e=(this.parent.element.classList.remove("flickityt4s_"+t+"disable","flickityt4s_"+t+"enable"),this.parent.slides);this.parent.options.wrapAround&&1<e.length?this.enable():(e=e.length?e.length-1:0,e=this.isPrevious?0:e,this[e=this.parent.selectedIndex==e?"disable":"enable"](),this.parent.element.classList.add("flickityt4s_"+t+e))},s.prototype.destroy=function(){this.deactivate(),this.allOff()},n.extend(e.defaults,{prevNextButtons:!0,arrowShape:{x0:10,x1:60,y1:50,x2:70,y2:40,x3:30}}),e.createMethods.push("_createPrevNextButtons");i=e.prototype;return i._createPrevNextButtons=function(){this.options.prevNextButtons&&(this.prevButton=new s(-1,this),this.nextButton=new s(1,this),this.on("activate",this.activatePrevNextButtons))},i.activatePrevNextButtons=function(){this.prevButton.activate(),this.nextButton.activate(),this.on("deactivate",this.deactivatePrevNextButtons)},i.deactivatePrevNextButtons=function(){this.prevButton.deactivate(),this.nextButton.deactivate(),this.off("deactivate",this.deactivatePrevNextButtons)},e.PrevNextButton=s,e}),function(t,n){"function"==typeof define&&define.amd?define("flickityt4s/js/page-dots",["./flickityt4s","unipointer/unipointer","fizzy-ui-utils/utils"],function(t,e,i){return n(0,t,e,i)}):"object"==typeof module&&module.exports?module.exports=n(0,require("./flickityt4s"),require("unipointer"),require("fizzy-ui-utils")):n(0,t.Flickityt4s,t.Unipointer,t.fizzyUIUtils)}(window,function(t,e,i,n){function o(t){this.parent=t,this._create()}(o.prototype=Object.create(i.prototype))._create=function(){this.holder=document.createElement("ol"),this.holder.className="flickityt4s-page-dots",this.dots=[],this.handleClick=this.onClick.bind(this),this.on("pointerDown",this.parent.childUIPointerDown.bind(this.parent))},o.prototype.activate=function(){this.setDots(),this.holder.addEventListener("click",this.handleClick),this.bindStartEvent(this.holder),this.parent.element.appendChild(this.holder)},o.prototype.deactivate=function(){this.holder.removeEventListener("click",this.handleClick),this.unbindStartEvent(this.holder),this.parent.element.removeChild(this.holder)},o.prototype.setDots=function(){var t=this.parent.slides.length-this.dots.length;0<t?this.addDots(t):t<0&&this.removeDots(-t)},o.prototype.addDots=function(t){for(var e=document.createDocumentFragment(),i=[],n=this.dots.length,o=n+t,s=n;s<o;s++){var r=document.createElement("li");r.className="dot",r.setAttribute("aria-label","Page dot "+(s+1)),e.appendChild(r),i.push(r)}this.holder.appendChild(e),this.dots=this.dots.concat(i)},o.prototype.removeDots=function(t){this.dots.splice(this.dots.length-t,t).forEach(function(t){this.holder.removeChild(t)},this)},o.prototype.updateSelected=function(){this.selectedDot&&(this.selectedDot.className="dot",this.selectedDot.removeAttribute("aria-current")),this.dots.length&&(this.selectedDot=this.dots[this.parent.selectedIndex],this.selectedDot.className="dot is-selected",this.selectedDot.setAttribute("aria-current","step"))},o.prototype.onTap=o.prototype.onClick=function(t){var t=t.target;"LI"==t.nodeName&&(this.parent.uiChange(),t=this.dots.indexOf(t),this.parent.select(t))},o.prototype.destroy=function(){this.deactivate(),this.allOff()},e.PageDots=o,n.extend(e.defaults,{pageDots:!0}),e.createMethods.push("_createPageDots");i=e.prototype;return i._createPageDots=function(){this.options.pageDots&&(this.pageDots=new o(this),this.on("activate",this.activatePageDots),this.on("select",this.updateSelectedPageDots),this.on("cellChange",this.updatePageDots),this.on("resize",this.updatePageDots),this.on("deactivate",this.deactivatePageDots))},i.activatePageDots=function(){this.pageDots.activate()},i.updateSelectedPageDots=function(){this.pageDots.updateSelected()},i.updatePageDots=function(){this.pageDots.setDots()},i.deactivatePageDots=function(){this.pageDots.deactivate()},e.PageDots=o,e}),function(t,e){"function"==typeof define&&define.amd?define("flickityt4s/js/player",["ev-emitter/ev-emitter","fizzy-ui-utils/utils","./flickityt4s"],e):"object"==typeof module&&module.exports?module.exports=e(require("ev-emitter"),require("fizzy-ui-utils"),require("./flickityt4s")):e(t.EvEmitter,t.fizzyUIUtils,t.Flickityt4s)}(window,function(t,e,i){function n(t){this.parent=t,this.state="stopped",this.onVisibilityChange=this.visibilityChange.bind(this),this.onVisibilityPlay=this.visibilityPlay.bind(this)}(n.prototype=Object.create(t.prototype)).play=function(){"playing"!=this.state&&(document.hidden?document.addEventListener("visibilitychange",this.onVisibilityPlay):(this.state="playing",document.addEventListener("visibilitychange",this.onVisibilityChange),this.tick()))},n.prototype.tick=function(){var t,e;"playing"==this.state&&(t="number"==typeof(t=this.parent.options.autoPlay)?t:3e3,(e=this).clear(),this.timeout=setTimeout(function(){e.parent.next(!0),e.tick()},t))},n.prototype.stop=function(){this.state="stopped",this.clear(),document.removeEventListener("visibilitychange",this.onVisibilityChange)},n.prototype.clear=function(){clearTimeout(this.timeout)},n.prototype.pause=function(){"playing"==this.state&&(this.state="paused",this.clear())},n.prototype.unpause=function(){"paused"==this.state&&this.play()},n.prototype.visibilityChange=function(){this[document.hidden?"pause":"unpause"]()},n.prototype.visibilityPlay=function(){this.play(),document.removeEventListener("visibilitychange",this.onVisibilityPlay)},e.extend(i.defaults,{pauseAutoPlayOnHover:!0}),i.createMethods.push("_createPlayer");t=i.prototype;return t._createPlayer=function(){this.player=new n(this),this.on("activate",this.activatePlayer),this.on("uiChange",this.stopPlayer),this.on("pointerDown",this.stopPlayer),this.on("deactivate",this.deactivatePlayer)},t.activatePlayer=function(){this.options.autoPlay&&(this.player.play(),this.element.addEventListener("mouseenter",this))},t.playPlayer=function(){this.player.play()},t.stopPlayer=function(){this.player.stop()},t.pausePlayer=function(){this.player.pause()},t.unpausePlayer=function(){this.player.unpause()},t.deactivatePlayer=function(){this.player.stop(),this.element.removeEventListener("mouseenter",this)},t.onmouseenter=function(){this.options.pauseAutoPlayOnHover&&(this.player.pause(),this.element.addEventListener("mouseleave",this))},t.onmouseleave=function(){this.player.unpause(),this.element.removeEventListener("mouseleave",this)},i.Player=n,i}),function(t,i){"function"==typeof define&&define.amd?define("flickityt4s/js/add-remove-cell",["./flickityt4s","fizzy-ui-utils/utils"],function(t,e){return i(0,t,e)}):"object"==typeof module&&module.exports?module.exports=i(0,require("./flickityt4s"),require("fizzy-ui-utils")):i(0,t.Flickityt4s,t.fizzyUIUtils)}(window,function(t,e,n){var i=e.prototype;return i.insert=function(t,e){var i,n,o,s,r,t=this._makeCells(t);t&&t.length&&(i=this.cells.length,e=void 0===e?i:e,s=t,r=document.createDocumentFragment(),s.forEach(function(t){r.appendChild(t.element)}),s=r,(n=e==i)?this.slider.appendChild(s):(o=this.cells[e].element,this.slider.insertBefore(s,o)),0===e?this.cells=t.concat(this.cells):n?this.cells=this.cells.concat(t):(s=this.cells.splice(e,i-e),this.cells=this.cells.concat(t).concat(s)),this._sizeCells(t),this.cellChange(e,!0))},i.append=function(t){this.insert(t,this.cells.length)},i.prepend=function(t){this.insert(t,0)},i.remove=function(t){var i,t=this.getCells(t);t&&t.length&&(i=this.cells.length-1,t.forEach(function(t){t.remove();var e=this.cells.indexOf(t);i=Math.min(e,i),n.removeFrom(this.cells,t)},this),this.cellChange(i,!0))},i.cellSizeChange=function(t){var t=this.getCell(t);t&&(t.getSize(),t=this.cells.indexOf(t),this.cellChange(t))},i.cellChange=function(t,e){var i=this.selectedElement,i=(this._positionCells(t),this._getWrapShiftCells(),this.setGallerySize(),this.setPrevNextButtons(),this.getCell(i));i&&(this.selectedIndex=this.getCellSlideIndex(i)),this.selectedIndex=Math.min(this.slides.length-1,this.selectedIndex),this.emitEvent("cellChange",[t]),this.select(this.selectedIndex),e&&this.positionSliderAtSelected()},e}),function(t){"function"==typeof define&&define.amd?define("flickityt4s/js/index",["./flickityt4s","./drag","./prev-next-button","./page-dots","./player","./add-remove-cell","./lazyload"],t):"object"==typeof module&&module.exports&&(module.exports=t(require("./flickityt4s"),require("./drag"),require("./prev-next-button"),require("./page-dots"),require("./player"),require("./add-remove-cell"),require("./lazyload")))}((window,function(t){return t})),function(t,e){"function"==typeof define&&define.amd?define("flickityt4s-as-nav-for/as-nav-for",["flickityt4s/js/index","fizzy-ui-utils/utils"],e):"object"==typeof module&&module.exports?module.exports=e(require("flickityt4s"),require("fizzy-ui-utils")):t.Flickityt4s=e(t.Flickityt4s,t.fizzyUIUtils)}(window,function(i,n){i.createMethods.push("_createAsNavFor");var t=i.prototype;return t._createAsNavFor=function(){this.on("activate",this.activateAsNavFor),this.on("deactivate",this.deactivateAsNavFor),this.on("destroy",this.destroyAsNavFor);var t,e=this.options.asNavFor;e&&(t=this,setTimeout(function(){t.setNavCompanion(e)}))},t.setNavCompanion=function(t){t=n.getQueryElement(t);var e,t=i.data(t);t&&t!=this&&(this.navCompanion=t,(e=this).onNavCompanionSelect=function(){e.navCompanionSelect()},t.on("select",this.onNavCompanionSelect),this.on("staticClick",this.onNavStaticClick),this.navCompanionSelect(!0))},t.navCompanionSelect=function(t){var e,i,n=this.navCompanion&&this.navCompanion.selectedCells;n&&(e=n[0],n=(e=this.navCompanion.cells.indexOf(e))+n.length-1,i=Math.floor((n-e)*this.navCompanion.cellAlign+e),this.selectCell(i,!1,t),this.removeNavSelectedElements(),i>=this.cells.length||(t=this.cells.slice(e,1+n),this.navSelectedElements=t.map(function(t){return t.element}),this.changeNavSelectedClass("add")))},t.changeNavSelectedClass=function(e){this.navSelectedElements.forEach(function(t){t.classList[e]("is-nav-selected")})},t.activateAsNavFor=function(){this.navCompanionSelect(!0)},t.removeNavSelectedElements=function(){this.navSelectedElements&&(this.changeNavSelectedClass("remove"),delete this.navSelectedElements)},t.onNavStaticClick=function(t,e,i,n){"number"==typeof n&&this.navCompanion.selectCell(n)},t.deactivateAsNavFor=function(){this.removeNavSelectedElements()},t.destroyAsNavFor=function(){this.navCompanion&&(this.navCompanion.off("select",this.onNavCompanionSelect),this.off("staticClick",this.onNavStaticClick),delete this.navCompanion)},i}),function(t,e){"function"==typeof define&&define.amd?define(["flickityt4s/js/index","fizzy-ui-utils/utils"],e):"object"==typeof module&&module.exports?module.exports=e(require("flickityt4s"),require("fizzy-ui-utils")):e(t.Flickityt4s,t.fizzyUIUtils)}(this,function(t,n){var e=t.Slide,o=e.prototype.updateTarget,e=(e.prototype.updateTarget=function(){var i,n;o.apply(this,arguments),this.parent.options.fade&&(i=this.target-this.x,n=this.cells[0].x,this.cells.forEach(function(t){var e=t.x-n-i;t.renderPosition(e)}))},e.prototype.setOpacity=function(e){this.cells.forEach(function(t){t.element.style.opacity=e})},t.prototype),i=(t.createMethods.push("_createFade"),e._createFade=function(){this.fadeIndex=this.selectedIndex,this.prevSelectedIndex=this.selectedIndex,this.on("select",this.onSelectFade),this.on("dragEnd",this.onDragEndFade),this.on("settle",this.onSettleFade),this.on("activate",this.onActivateFade),this.on("deactivate",this.onDeactivateFade)},e.updateSlides),s=(e.updateSlides=function(){i.apply(this,arguments),this.options.fade},e.onSelectFade=function(){this.fadeIndex=Math.min(this.prevSelectedIndex,this.slides.length-1),this.prevSelectedIndex=this.selectedIndex},e.onSettleFade=function(){delete this.didDragEnd,this.options.fade},e.onDragEndFade=function(){this.didDragEnd=!0},e.onActivateFade=function(){this.options.fade&&this.element.classList.add("is-fade")},e.onDeactivateFade=function(){this.options.fade&&(this.element.classList.remove("is-fade"),this.slides.forEach(function(t){t.setOpacity("")}))},e.positionSlider),r=(e.positionSlider=function(){this.options.fade?(this.fadeSlides(),this.dispatchScrollEvent()):s.apply(this,arguments)},e.positionSliderAtSelected),a=(e.positionSliderAtSelected=function(){this.options.fade&&this.setTranslateX(0),r.apply(this,arguments)},e.fadeSlides=function(){this.slides.length},e.getFadeIndexes=function(){return this.isDragging||this.didDragEnd?this.options.wrapAround?this.getFadeDragWrapIndexes():this.getFadeDragLimitIndexes():{a:this.fadeIndex,b:this.selectedIndex}},e.getFadeDragWrapIndexes=function(){var t=this.slides.map(function(t,e){return this.getSlideDistance(-this.x,e)},this),e=t.map(function(t){return Math.abs(t)}),i=Math.min.apply(Math,e),e=e.indexOf(i),i=t[e],t=this.slides.length;return{a:e,b:n.modulo(e+(0<=i?1:-1),t)}},e.getFadeDragLimitIndexes=function(){for(var t=0,e=0;e<this.slides.length-1;e++){var i=this.slides[e];if(-this.x<i.target)break;t=e}return{a:t,b:t+1}},e.wrapDifference=function(t,e){var i,e=e-t;return e=this.options.wrapAround&&(t=e+this.slideableWidth,i=e-this.slideableWidth,Math.abs(t)<Math.abs(e)&&(e=t),Math.abs(i)<Math.abs(e))?i:e},e._getWrapShiftCells),l=(e._getWrapShiftCells=function(){this.options.fade||a.apply(this,arguments)},e.shiftWrapCells);return e.shiftWrapCells=function(){this.options.fade||l.apply(this,arguments)},t}),function(t,e){"function"==typeof define&&define.amd?define(["flickityt4s/js/index","fizzy-ui-utils/utils"],e):"object"==typeof module&&module.exports?module.exports=e(require("flickityt4s"),require("fizzy-ui-utils")):t.Flickityt4s=e(t.Flickityt4s,t.fizzyUIUtils)}(window,function(e,i){"use strict";return e.createMethods.push("_createSync"),e.prototype._createSync=function(){this.syncers={};var t,e=this.options.sync;this.on("destroy",this.unsyncAll),e&&(t=this,setTimeout(function(){t.sync(e)}))},e.prototype.sync=function(t){t=i.getQueryElement(t);t=e.data(t);t&&(this._syncCompanion(t),t._syncCompanion(this))},e.prototype._syncCompanion=function(e){var i=this;function t(){var t=i.selectedIndex;e.selectedIndex!=t&&e.select(t)}this.on("select",t),this.syncers[e.guid]={flickityt4s:e,listener:t}},e.prototype.unsync=function(t){t=i.getQueryElement(t);t=e.data(t);this._unsync(t)},e.prototype._unsync=function(t){t&&(this._unsyncCompanion(t),t._unsyncCompanion(this))},e.prototype._unsyncCompanion=function(t){var t=t.guid,e=this.syncers[t];this.off("select",e.listener),delete this.syncers[t]},e.prototype.unsyncAll=function(){for(var t in this.syncers){t=this.syncers[t];this._unsync(t.flickityt4s)}},e}),!function(t){"undefined"!=typeof module&&module.exports?module.exports=t():"function"==typeof define&&define.amd?define(t):this.$script=t()}(function(){function c(t,e){for(var i=0,n=t.length;i<n;++i)if(!e(t[i]))return a;return 1}function u(t,e){c(t,function(t){return e(t),1})}function h(t,e,i){function n(t){return t.call?t():m[t]}function o(){if(!--l)for(var t in m[a]=1,r&&r(),y)c(t.split("|"),n)&&(u(y[t],n),y[t]=[])}t=t[p]?t:[t];var s=e&&e.call,r=s?e:i,a=s?t.join(""):e,l=t.length;return setTimeout(function(){u(t,function t(e,i){return null===e?o():(i||/^https?:\/\//.test(e)||!f||(e=-1===e.indexOf(".js")?f+e+".js":f+e),v[e]?(a&&(a,0),2==v[e]?o():setTimeout(function(){t(e,!0)},0)):(v[e]=1,a&&(a,0),void d(e,o)))})},0),h}function d(t,e){var i,n=s.createElement("script");n.onload=n.onerror=n[g]=function(){n[l]&&!/^c|loade/.test(n[l])||i||(n.onload=n[g]=null,i=1,v[t]=2,e())},n.async=1,n.src=o?t+(-1===t.indexOf("?")?"?":"&")+o:t,r.insertBefore(n,r.lastChild)}var f,o,s=document,r=s.getElementsByTagName("head")[0],a=!1,p="push",l="readyState",g="onreadystatechange",m={},y={},v={};return h.get=d,h.order=function(i,n,o){!function t(e){e=i.shift(),i.length?h(e,t):h(e,n,o)}()},h.path=function(t){f=t},h.urlArgs=function(t){o=t},h.ready=function(t,e,i){t=t[p]?t:[t];var n=[];return u(t,function(t){m[t]||n[p](t)}),c(t,function(t){return m[t]})?e():(t=t.join("|"),y[t]=y[t]||[],y[t][p](e),i&&i(n)),h},h.done=function(t){h([null],t)},h}),!function(t,e){var i,n;"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self,i=t.Cookies,(n=t.Cookies=e()).noConflict=function(){return t.Cookies=i,n})}(this,function(){"use strict";function r(t){for(var e=1;e<arguments.length;e++){var i,n=arguments[e];for(i in n)t[i]=n[i]}return t}return function e(a,s){function i(t,e,i){if("undefined"!=typeof document){"number"==typeof(i=r({},s,i)).expires&&(i.expires=new Date(Date.now()+864e5*i.expires)),i.expires&&(i.expires=i.expires.toUTCString()),t=encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var n,o="";for(n in i)i[n]&&(o+="; "+n,!0!==i[n])&&(o+="="+i[n].split(";")[0]);return document.cookie=t+"="+a.write(e,t)+o}}return Object.create({set:i,get:function(t){if("undefined"!=typeof document&&(!arguments.length||t)){for(var e=document.cookie?document.cookie.split("; "):[],i={},n=0;n<e.length;n++){var o=e[n].split("="),s=o.slice(1).join("=");try{var r=decodeURIComponent(o[0]);if(i[r]=a.read(s,r),t===r)break}catch(t){}}return t?i[t]:i}},remove:function(t,e){i(t,"",r({},e,{expires:-1}))},withAttributes:function(t){return e(this.converter,r({},this.attributes,t))},withConverter:function(t){return e(r({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(s)},converter:{value:Object.freeze(a)}})}({read:function(t){return(t='"'===t[0]?t.slice(1,-1):t).replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(t){return encodeURIComponent(t).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})}),Cookies.noConflict());if(!function(w){"use strict";var i,n,o,s,T=w(window),t=w(document),r=w("body"),e=window.T4Sroutes.root_url,a=T.width(),l=T4Sconfigs.cacheName+T4Sconfigs.cartCurrency+Shopify.country+e,C=a<768,E=a<1025;function c(t,e=!0){if(IsDesignMode&&e)return!1;e="session"===t?window.sessionStorage:window.localStorage;try{return e.setItem("t4s","test"),e.removeItem("t4s"),!0}catch(t){return!1}}function u(t){t.removeClass("isotopet4s-later");var e=t.attr("data-isotopet4s-js")||"{}",i=t.isotopet4s(JSON.parse(e)).addClass("isotopet4s-enabled");T.on("resize",T4SThemeSP.debounce(555,function(){t.hasClass("isotopet4s-enabled")&&i.isotopet4s("layout")}))}function h(){var n,o="[data-isotopet4s-filter]",t=w(o);0!=t.length&&(n="is--active",(IsDesignMode?r:t).on("click",IsDesignMode?o+">button":">button",function(){var t=w(this),e=t.closest(o),i=e.data("grid"),i=i?w(i):e.next();e.find("."+n).removeClass(n),t.addClass(n),i.isotopet4s({filter:t.attr("data-filter")})}))}function d(t){this.el=t,this.$el=w(t),this.width=0,this.UID=T4SThemeSP.getUID(),i="resize.marquee"+this.UID,this.marquee3kItem=t.querySelector(".t4s-marquee__item"),IsDesignMode?setTimeout(this.resizeHandler.bind(this),100):this.resizeHandler(),this.$el.addClass(n);var e=this;T.on(i,T4SThemeSP.debounce(300,function(){e.resizeHandler()}.bind(this)))}isStorageSpSession=c("session"),isStorageSpSessionAll=c("session",!1),isStorageSpdLocal=c("local"),isStorageSpdLocalAll=c("local",!1),"development"==Shopify.theme.role&&isStorageSpSessionAll&&sessionStorage.clear(),T4SThemeSP.$appendComponent=w("#t4s-append-component"),T4SThemeSP.cacheNameFirst=l,T4SThemeSP.root_url="/"!=e?e+"/":"/",void 0===window.Shopify&&(window.Shopify={}),Shopify.bind=function(t,e){return function(){return t.apply(e,arguments)}},Shopify.setSelectorByValue=function(t,e){for(var i=0,n=t.options.length;i<n;i++){var o=t.options[i];if(e==o.value||e==o.innerHTML)return t.selectedIndex=i}},Shopify.addListener=function(t,e,i){t.addEventListener?t.addEventListener(e,i,!1):t.attachEvent("on"+e,i)},Shopify.postLink=function(t,e){var i,n=(e=e||{}).method||"post",o=e.parameters||{},s=document.createElement("form");for(i in s.setAttribute("method",n),s.setAttribute("action",t),o){var r=document.createElement("input");r.setAttribute("type","hidden"),r.setAttribute("name",i),r.setAttribute("value",o[i]),s.appendChild(r)}document.body.appendChild(s),s.submit(),document.body.removeChild(s)},Shopify.CountryProvinceSelector=function(t,e,i){this.countryEl=document.getElementById(t),this.provinceEl=document.getElementById(e),this.provinceContainer=document.getElementById(i.hideElement||e),Shopify.addListener(this.countryEl,"change",Shopify.bind(this.countryHandler,this)),this.initCountry(),this.initProvince()},Shopify.CountryProvinceSelector.prototype={initCountry:function(){var t=this.countryEl.getAttribute("data-default");Shopify.setSelectorByValue(this.countryEl,t),this.countryHandler()},initProvince:function(){var t=this.provinceEl.getAttribute("data-default");t&&0<this.provinceEl.options.length&&Shopify.setSelectorByValue(this.provinceEl,t)},countryHandler:function(t){var e=(n=this.countryEl.options[this.countryEl.selectedIndex]).getAttribute("data-provinces"),i=JSON.parse(e);if(this.clearOptions(this.provinceEl),i&&0==i.length)this.provinceContainer.style.display="none";else{for(var n,o=0;o<i.length;o++)(n=document.createElement("option")).value=i[o][0],n.innerHTML=i[o][1],this.provinceEl.appendChild(n);this.provinceContainer.style.display=""}},clearOptions:function(t){for(;t.firstChild;)t.removeChild(t.firstChild)},setOptions:function(t,e){var i=0;for(e.length;i<e.length;i++){var n=document.createElement("option");n.value=e[i],n.innerHTML=e[i],t.appendChild(n)}}},T4SThemeSP.resizeEventT4=function(t,e,i){try{window.dispatchEvent(new Event("resize"))}catch(t){var n=window.document.createEvent("UIEvents");n.initUIEvent("resize",!0,!1,window,0),window.dispatchEvent(n)}},T4SThemeSP.debounce=function(n,o,s){var r;return function(){var t=this,e=arguments,i=s&&!r;clearTimeout(r),r=setTimeout(function(){r=null,s||o.apply(t,e)},n),i&&o.apply(t,e)}},T4SThemeSP.storageCurrency=function(){return isStorageSpdLocal?localStorage.getItem("T4Currency"):null},T4SThemeSP.fullHeightFirtSe=function(){var t,e,i=w("#MainContent >.t4s-section:first").find(".t4s_ratio_fh");0!=i.length&&(t=T.height(),(e=i.offset().top)<t)&&i.css("--t4s-aspect-ratio-fh",100-e/(t/100)+"vh")},T4SThemeSP.handle=function(t){var e=(t=(t+"").toLowerCase()).replace(/'|"|\(|\)|\[|\]/g,"").replace(/[\s\x21-\x2f\x3a-\x40\x7b-\x7f^`\\[\]]+/g,"-").replace(/\W+/g,"-").replace(/^-+|-+$/g,"");return""==e?t:e},T4SThemeSP._handle=function(t){var e=(t=(t+"").toLowerCase()).replace(/'|"|\(|\)|\[|\]/g,"").replace(/[\s\x21-\x2f\x3a-\x40\x7b-\x7f^`\\[\]]+/g,"_").replace(/\W+/g,"_").replace(/^-+|-+$/g,"");return""==e?t:e},T4SThemeSP.escapeHtml=function(t){return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")},T4SThemeSP.descapeHtml=function(t){return t.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/&amp;/g,"&")},T4SThemeSP.Images={preloadImages:function(t){w(t).each(function(){w("<img/>")[0].src=this})},getNewImageUrl:function(t,e=0,i=0){return e||i?(e&&(t=t+"&width="+e),this.removeProtocol(t=i?t+"&height="+i:t)):null},removeProtocol:function(t){return t.replace(/http(s)?:/,"")},lazyloadImagePath:function(t){return this.removeProtocol(t+"&width=1")}},T4SThemeSP.getUID=function(t){for(;t+=~~(1e6*Math.random()),document.getElementById(t););return t},T4SThemeSP.Carousel=function(){var r,n="is--playing",o="is--paused",s="is--active",a="is-nav-selected",e="is-shopify-xr__showing",c="t4s-carousel__nav-item",l="is-selected",u={currentSlide:"."+l,wrapper:".t4s-carousel-wrapper",pauseButton:".t4s-carousel__pause",productMediaWrapper:"[data-product-single-media-wrapper]",mediaGroup:"[data-product-single-media-group]",dataMediaPlay:"data-is-mediaPlay",productMediaPlay:"[data-is-mediaPlay]"},h={adaptiveHeight:!1,autoPlay:!1,avoidReflow:!1,thumbNav:!1,thumbVertical:!1,navUI:!1,dotUI:!1,parallax:!1,status:!1,isMedia:!1,t4sid:"19041994",t4sidTab:"19041994",selectWithSelector:!1,scrollbar:!1,scrollbarDraggable:!1,fullwidthSide:!1,centerSlide:!1,isSimple:!1,minWidthLG:19041994,cellAlign:"center",cellAlignLG:"left",btnSmartTab:!1,activeTab:!1,customIcon:0,viewBox:"0 0 100 100",checkVisibility:!0,autoPlayT4:!1,dragThreshold:7,fade:!1,friction:.8,initialIndex:0,pageDots:!1,pauseAutoPlayOnHover:!1,prevNextButtons:!1,selectedAttraction:.14},d="select.carousel",f="click.navt4s",p="click.dott4s",t="click.thumbt4s",g="is--media-hide",m={video:'<svg viewBox="0 0 384 512"><use href="#icon-thumb-video"/></svg>',external_videoyoutube:'<svg viewBox="0 0 576 512"><use href="#icon-external-youtube"/></svg>',external_videovimeo:'<svg viewBox="0 0 448 512"><use href="#icon-external-vimeo"/></svg>',model:'<svg viewBox="0 0 512 512"><use href="#icon-thumb-model"/></svg>',360:'<svg viewBox="0 0 640 512"><use href="#icon-thumb-360"/></svg>'},y="destroy.t4s",v="",b=!1,x=window.CSS.supports("scroll-behavior","smooth");const S={start:"left",end:"right"};function i(t){this.el=t,this.$el=w(t),this.UID=T4SThemeSP.getUID(),r="resize.carousel"+this.UID;var e=JSON.parse(this.$el.attr("data-flickityt4s-js")||"{}"),e=(this.args=Object.assign({},h,e),this.args.cellAlign),i=this.args.cellAlignLG;if(this.args.cellAlign=S[e]||e,this.args.cellAlignLG=S[i]||i,this.IdSlider=this.args.t4sid,this.args.fade=this.$el.hasClass("t4s-slide-eff-fade")||this.args.fade,this.args.rightToLeft=isThemeRTL,this.$deferredMedia=this.$el.find(u.productMediaWrapper),this.args.isMedia&&(this.isMedia=0<this.$el.find("[data-deferred-media]").length||0<this.$el.find('[data-media-type="360"]').length,this.isMedia)&&(this.$groupBtn=w(`[ data-t4s-group-btns="${this.args.t4sid}"]`),this.$mediaGroup=this.$el.closest(u.mediaGroup)),this.args.wrapAround&&E&&(this.args.dragThreshold=55),this.args.on={ready:this.init.bind(this),change:this.slideChange.bind(this),select:this.slideSelect.bind(this),settle:this.afterChange.bind(this)},this.args.thumbNav&&this._initCarouselNav(),this.args.avoidReflow){e=t;if(e.id){for(var n=e.firstChild;null!=n&&3==n.nodeType;)n=n.nextSibling;i=document.createElement("style");i.innerHTML=`#${e.id} .flickity-viewport{height:${n.offsetHeight}px}`,document.head.appendChild(i)}}this.$wrapper=this.$el.closest(u.wrapper),this.wrapper=this.$wrapper[0],this.pauseBtn=this.wrapper?this.wrapper.querySelector(u.pauseButton):null,this.$pauseBtn=w(this.pauseBtn),this.isPlaying=this.args.autoPlay||this.args.autoPlayT4,this.args.cellAlignOriginnal=this.args.cellAlign,this.hasMWLG=19041994!=this.args.minWidthLG,this.hasMWLG&&this.args.minWidthLG<=T.width()&&(this.args.cellAlign=this.args.cellAlignLG),this.args.centerSlide&&this.args.wrapAround&&(this.args.cellAlign="center",this.args.cellAlignOriginnal="center"),this.$carousel=this.$el.flickityt4s(this.args);var o=this,s=(this.flkty=this.$carousel.data("flickityt4s"),this.selectedIndex=this.flkty.selectedIndex,setTimeout(o.actionsAPI("resize"),0),setTimeout(function(){o.$el.addClass("t4s-enabled"),o.args.isSimple&&(o.actionsAPI("resize"),setTimeout(o.actionsAPI("resize"),150))},100),this._selectChange(),this.isPlaying&&this.wrapper&&this.pauseBtn&&this.pauseBtn.addEventListener("click",this._togglePause.bind(this)),this.args.navUI&&this._customNavUI(),this.args.dotUI&&this._customDotUI(),this.args.parallax&&this._parallaxEffect(),this.args.status&&this._status(),this.args.isFilter&&this._updateCarousel(),this.args.btnSmartTab&&this._updateBtnTab(),this.args.prevNextButtons&&this._customIcon(this.$carousel.find(".flickityt4s-button.previous"),this.$carousel.find(".flickityt4s-button.next")),this.args.selectWithSelector&&this._selectWithSelector(),this.args.scrollbar&&!this.args.scrollbarDraggable&&this._scrollbarCarousel(),this.args.scrollbar&&this.args.scrollbarDraggable&&this._scrollbarDraggableCarousel(),this.args.thumbVertical);T.width()<1025&&s&&(this.args.thumbVertical=!1),T.on(r,T4SThemeSP.debounce(300,function(){o.hasMWLG&&(o.args.minWidthLG<=T.width()?o.flkty.options.cellAlign=o.args.cellAlignLG:o.flkty.options.cellAlign=o.args.cellAlignOriginnal),o.actionsAPI("resize"),T.width()<1025&&s?o.args.thumbVertical=!1:1024<T.width()&&s&&(o.args.thumbVertical=!0)}.bind(this))),o.$el.on(y,o.destroy.bind(o))}return i.prototype=Object.assign({},i.prototype,{init:function(t){var e=this;e.currentSlide=e.el.querySelector(u.currentSlide),e.args.autoPlayT4&&e.autoPlayT4(),e.$pauseBtn.addClass(n),e.args.callbacks&&e.args.callbacks.onInit&&"function"==typeof e.args.callbacks.onInit&&e.args.callbacks.onInit(e.currentSlide)},slideChange:function(t){var e=this;e.args.thumbNav&&e.thumbnailsGoto(t),e.args.callbacks&&e.args.callbacks.onChange&&"function"==typeof e.args.callbacks.onChange&&e.args.callbacks.onChange(t),e.$carouselNavPrev&&e.$carouselNavPrev.length&&(e.$carouselNavPrev[0].classList.toggle("is--hide",0===t),e.$carouselNavNext[0].classList.toggle("is--hide",t===e.$carouselNavLinks.length-1)),e.isMedia&&e._switchMedia(t),e.args.autoPlayT4&&e.autoPlayT4(),e.$carousel&&((e=this).flkty.prevButton&&w(e.flkty.prevButton.element).attr("data-imgkey",e.flkty.selectedIndex-1<0?e.flkty.cells.length-1:e.flkty.selectedIndex-1),e.flkty.nextButton)&&w(e.flkty.nextButton.element).attr("data-imgkey",e.flkty.selectedIndex+1>=e.flkty.cells.length?0:e.flkty.selectedIndex+1)},autoPlayT4:function(){var t=this;t.time||(t.wrapper&&t.wrapper.style.setProperty("--play-carousel-speed",t.args.autoPlayT4+"ms"),t.time={}),t.time.START=(new Date).getTime(),t.time.END=t.time.START+t.args.autoPlayT4,t.$pauseBtn.removeClass(n),t.isPlaying&&(clearTimeout(t.stayTimeout),t.stayTimeout=setTimeout(function(){t.actionsAPI("next",!0)},t.args.autoPlayT4),clearTimeout(t.pauseBtnTimeout),t.pauseBtnTimeout=setTimeout(function(){t.$pauseBtn.addClass(n)},20)),t.time.REMAINING=t.args.autoPlayT4},slideSelect:function(t){if(this.$carousel&&0<this.$carousel.find("video[autoplay]").length){var e=this.flkty.getCellElements(),i=(e.forEach(t=>{t.querySelector("video")?.pause()}),this.flkty.selectedElements.forEach(t=>{t.querySelector("video")?.play()}),window.matchMedia("(max-width: 767px)").matches?this.flkty.options.cellsLength:window.matchMedia("(max-width: 1024px)").matches?this.flkty.options.cellsLengthMD:this.flkty.options.cellsLengthLG);if(this.flkty.options.wrapAround){var n=e.indexOf(this.flkty.selectedElements[0]),o=e.indexOf(this.flkty.selectedElements[this.flkty.selectedElements.length-1]);++o,n=--n<0?e.length-1:n,o%=e.length,e[n]?.querySelector("video")?.play(),e[o]?.querySelector("video")?.play()}else if(this.flkty.selectedElements.length<i)for(var n=e.indexOf(this.flkty.selectedElements[0]),s=i-this.flkty.selectedElements.length,r=0;r<s;r++)e[--n]?.querySelector("video")?.play()}},afterChange:function(t){this.args.thumbNav&&this.thumbnailsGoto(this.flkty.selectedIndex)},destroy:function(){this.$carouselNav&&this.$carouselNav.find("."+a).removeClass(a),this.actionsAPI("destroy"),this.$el.off(y),T.off(r)},_togglePause:function(){var t=this,e=t.$pauseBtn.data("pause-title"),i=t.$pauseBtn.data("play-title");t.pauseBtn.classList.contains(o)?(t.pauseBtn.classList.remove(o),t.wrapper.classList.remove(o),w(".t4s-tooltip .tooltip-inner").text(e),t.pauseBtn.setAttribute("data-original-title",e),t.isPlaying=!0,t.args.autoPlayT4&&(t.time.END=(new Date).getTime()+t.time.REMAINING,t.stayTimeout=setTimeout(function(){t.actionsAPI("next",!0)},t.time.REMAINING))):(t.wrapper.classList.add(o),t.pauseBtn.classList.add(o),w(".t4s-tooltip .tooltip-inner").text(i),t.pauseBtn.setAttribute("data-original-title",i),t.isPlaying=!1,t.args.autoPlayT4&&(clearTimeout(t.stayTimeout),t.time.REMAINING=t.time.END-(new Date).getTime())),t.isPlaying&&t.$pauseBtn.addClass(n)},actionsAPI:function(t,e=!1){this.$carousel.flickityt4s(t,e)},_selectChange:function(t){var i=this;i.$carousel.on("select.flickityt4s",function(t,e){i.$carousel.trigger(d)})},_customNavUI:function(t,e){var i=this,n=i.args.wrapAround||!1;t=t||w(".btn__prev--"+i.IdSlider),0!=(e=e||w(".btn__next--"+i.IdSlider)).length&&(i._customIcon(t,e),t.off(f).on(f,function(){i.actionsAPI("previous")}),e.off(f).on(f,function(){i.actionsAPI("next")}),i._setButtonStatus(n,t,e),i.$carousel.on(d,function(){i._setButtonStatus(n,t,e)}))},_setButtonStatus:function(t,e,i){var n=i.closest("[data-tab-active]"),o=this.flkty.selectedCell.target;n.addClass("prev_next_added"),n.removeClass("tab_prev_next_disable"),this.flkty.slides.length<2?(n.addClass("tab_prev_next_disable"),e.attr("disabled","disabled"),i.attr("disabled","disabled")):(o!=this.flkty.cells[0].target||t?o!=this.flkty.getLastCell().target||t?(e.removeAttr("disabled"),i):(i.attr("disabled","disabled"),e):(e.attr("disabled","disabled"),i)).removeAttr("disabled")},_customDotUI:function(){var i=this,t=w(".btn_group--cells"+i.IdSlider);if(t.data("build")){let e="";for(let t=0;t<i.flkty.slides.length;t++)e+='<li class="dot btn_dott4s" aria-label="'+(t+1)+'"></li>';t.html(e)}var e=t.find(".btn_dott4s");0!=e.length&&(e.eq(i.flkty.selectedIndex).addClass(l),t.on(p,".btn_dott4s",function(){i.$carousel.flickityt4s("select",w(this).index())}),i.$carousel.on(d,function(){e.filter("."+l).removeClass(l),e.eq(i.flkty.selectedIndex).addClass(l)}))},_parallaxEffect:function(){var i,n=this;n.$carousel.hasClass("slide-eff-parallax")&&0!=(i=n.$carousel.find(C?".t4s-slide .t4s-img-as-bg.t4s-d-md-none":".t4s-slide .t4s-img-as-bg.t4s-d-md-block")).length&&n.$carousel.on("scroll.flickityt4s",function(t,e){n.flkty.slides.forEach(function(t,e){e=i[e],t=-1*(t.target+n.flkty.x)/3;e.style.transform="translateX( "+t+"px)"})})},_status:function(){var t,e,i,n,o,s=this,r=w(".carousel--status"+s.IdSlider);function a(){null!=s.flkty.slides&&(n=s.flkty.selectedIndex+1,o=s.flkty.slides.length,i&&(n=l(n,2),o=l(o,2)),t.text(n),e.text(o))}function l(t,e){return(t=t.toString()).length<e?l("0"+t,e):t}0!=r.length&&(t=r.find("[data-current-slide]"),e=r.find("[data-total-number]"),i=s.args.pad||!1,o=n=0,a(),s.$carousel.on(d,a))},_initCarouselNav:function(){var i=this;i.$carouselNav=w(".carousel__nav--"+i.IdSlider),0!=i.$carouselNav.length&&(b||i.addThumbIcons(),i.thumbnailsMarkup(),i.$carouselNavLinks.eq([i.args.initialIndex]).addClass(a),i.$carouselNavPrev=w(`[data-thumb-btn__prev="${i.IdSlider}"]`),i.$carouselNavNext=w(`[data-thumb-btn__next="${i.IdSlider}"]`),(this.$carouselNavPrev.length||this.$carouselNavNext.length)&&(i.$carouselNavPrev.on(t,function(){i.actionsAPI("previous")}),i.$carouselNavNext.on(t,function(){i.actionsAPI("next")})),i.args.isFilter?i.$carouselNav.on(t,"."+c,function(t){var e=i.$carouselNav.find(`.${c}:visible`).index(w(this));i.$carousel.flickityt4s("select",e)}):i.$carouselNav.on(t,"."+c,function(t){i.$carousel.flickityt4s("select",w(this).index())}))},addThumbIcons:function(){var t=w("template[data-icons-thumb]");0!=t.length&&(T4SThemeSP.$appendComponent.after(t.html()),b=!0)},thumbnailsMarkup:function(t){var e=this,t=(v="",t||(e.$el.find("[data-main-slide]").each(function(){var t=w(this),e=t.hasClass(g)?g:"",i=t.data("media-type"),n=t.data("vhost")||"",o=t.data("grname")||"",s=t.data("grpvl")||"",r=t.find(".t4s_ratio").attr("style"),t=t.find("img"),a=T4SThemeSP.Images.lazyloadImagePath(t.attr("data-master")||t.attr("data-src")),l=m[i+n]||"";v+=`<div class="t4s-col-item ${c} ${e}" data-grname="${o}" data-grpvl="${s}" data-mdtype="${i}" data-vhost="${n}"><div class="t4s_ratio t4s-carousel__nav-inner t4s-bg-11" style="${r};background: url(${a})"><img alt="${T4SThemeSP.escapeHtml(t.attr("alt"))}" loading="lazy" class="lazyloadt4s" data-src="${a}" data-widths="[80, 120, 160 ,180, 360, 540]" data-optimumx="1.8" data-sizes="auto" src="${a}"><span class="t4s-thumbnail__badge t4s-not-style t4s-op-0" aria-hidden="true">${l}</span></div></div>`}),e.$carouselNav.empty().append(v).addClass("is--nav-ready")),e.$el.find("[data-main-slide]:visible").length);e.$carouselNavLinks=e.$carouselNav.find(`.${c}:not(.is--media-hide):visible`),e.args.thumbVertical?e.$carouselNav.parents(".t4s-parent-nav").outerHeight()+20<e.$carouselNav.find(".t4s-col-item:not(.is--media-hide):visible").outerHeight()*t?e.$carouselNav.parents(".t4s-parent-nav").addClass("thumb-nav-active"):e.$carouselNav.parents(".t4s-parent-nav").removeClass("thumb-nav-active"):e.$carouselNav.parents(".t4s-parent-nav").outerWidth()+20<e.$carouselNav.find(".t4s-col-item:not(.is--media-hide):visible").outerWidth()*t?e.$carouselNav.parents(".t4s-parent-nav").addClass("thumb-nav-active"):e.$carouselNav.parents(".t4s-parent-nav").removeClass("thumb-nav-active"),e.$carouselNavLinks.eq(e.$carouselNavLinks.length-1).addClass("thumb-nav-visible-last")},thumbnailsGoto:function(t){var e,i;void 0!==this.$carouselNavLinks&&(e=(t=this.$carouselNavLinks.eq(t))[0],i=isBehaviorSmooth?0:350,this.$carouselNavScroller=w(`[data-thumb__scroller="${this.IdSlider}"]`),this.$carouselNav.find("."+a).removeClass(a),t.addClass(a),this.args.thumbVertical?(t=e.offsetTop,x?this.$carouselNavScroller[0].scrollTop=t-100:this.$carouselNavScroller.stop().animate({scrollTop:t-100},i)):(t=void 0!==e?e.offsetLeft:0,x?this.$carouselNavScroller[0].scrollLeft=t-100:this.$carouselNavScroller.stop().animate({scrollLeft:t-100},i)))},_switchMedia:function(t){this.$el.find(".flickityt4s-slider "+u.productMediaWrapper).eq(this.selectedIndex);t=this.$el.find(".flickityt4s-slider "+u.productMediaWrapper).eq(t);this.selectedIndex=this.flkty.selectedIndex,this.$groupBtn.removeAttr("hidden"),this.$mediaGroup.removeClass(e),this.flkty.options.draggable=!0,w(u.productMediaPlay).each(function(){this.dispatchEvent(new CustomEvent("mediaHidden",{bubbles:!0,cancelable:!0})),this.removeAttribute(u.dataMediaPlay)}),t.is("[data-deferred-media]")||t.is('[data-media-type="360"]')?(this.flkty.options.draggable=!1,this.flkty.updateDraggable(),t.is('[data-media-type="model"]')?this.$mediaGroup.addClass(e):this.$groupBtn.attr("hidden",!0),t.attr(u.dataMediaPlay,""),t[0].dispatchEvent(new CustomEvent("mediaVisible",{bubbles:!0,cancelable:!0}))):this.flkty.updateDraggable()},_updateCarousel:function(t){var e=this;e.$carousel.on("update.flickityt4s",function(t){w(this).flickityt4s("deactivate").flickityt4s("activate"),e.$carouselNav&&e.thumbnailsMarkup("update")})},_updateBtnTab:function(t){var e=this;let i=w("#btn-tab-smart__prev--"+e.args.t4sidTab),n=w("#btn-tab-smart__next--"+e.args.t4sidTab);0!=n.length&&(e.$carousel.on("updateBtnTab.flickityt4s",function(t){i.off(f),n.off(f),e._customNavUI(i,n),e._customIcon(i,n)}),e.args.activeTab)&&e.$carousel.trigger("updateBtnTab.flickityt4s")},_customIcon:function(t,e){var i,n=this.args.customIcon;n&&(i=`<svg viewBox="${this.args.viewBox}" class="flickityt4s-button-icon t4s-cus-icon-slider is--cus-ic-${n}"><use href="#svg-slider-btn___prev-${n}"></svg><span class="t4s-flicky-btn-text">${T4Sstrings.btn_prev}</span>`,n=`<svg viewBox="${this.args.viewBox}" class="flickityt4s-button-icon t4s-cus-icon-slider is--cus-ic-${n}"><use href="#svg-slider-btn___next-${n}"></svg><span class="t4s-flicky-btn-text">${T4Sstrings.btn_next}</span>`,t.html(i),e.html(n))},_selectWithSelector:function(){var i=this;let n=w(`[data-carousel-id="${i.IdSlider}"]`);if(0!=n.length){n.on("click",function(){i.$carousel.flickityt4s("select",w(this).index())});let e=i.$carousel.find("[data-flickity-link]");i.$carousel.on(d,function(){n.filter("."+s).removeClass(s),n.eq(i.flkty.selectedIndex).addClass(s);var t=w(i.flkty.selectedElement).data("url");e[0]&&t&&e.attr("href",t)})}},_scrollbarCarousel:function(){var i=this;function t(){clearTimeout(i.recalculateScrollSizeTimeout),i.recalculateScrollSizeTimeout=setTimeout(function(){i.$scrollbar.css({"--width":i.flkty.size.width*i.flkty.size.width/i.flkty.slideableWidth+"px"}),i.flkty.size.width>=i.flkty.slideableWidth?i.$scrollbar.addClass("is--hidden"):i.$scrollbar.removeClass("is--hidden"),i.scrollInnerSize=i.$scrollbarDrag.width()/i.$scrollbar.width()},155)}i.$scrollbar=w(".t4s-carousel-scrollbar--"+i.IdSlider),i.$scrollbar[0]&&(i.$scrollbarDrag=i.$scrollbar.find(".t4s-carousel-scrollbar__drag"),i.scrollInnerSize=0,i.recalculateScrollSizeTimeout,t(),T.on("resize.scrollbar"+i.IdSlider,T4SThemeSP.debounce(400,function(){t()})),i.$carousel.on("scroll.flickityt4s",function(t,e){i.$scrollbar.css({"--left":e*(1-i.scrollInnerSize)*100+"%"})}))},_scrollbarDraggableCarousel:function(){}}),i}(),window.liquidWindowWidth=function(){return window.innerWidth},T4SThemeSP.initCarousel=function(){var t=w(".flickityt4s:not(.flickityt4s-later):not(.flickityt4s-enabled)");0!=t.length&&t.each(function(){this.flickityt4s=new T4SThemeSP.Carousel(this)})},T4SThemeSP.initWhenVisible=function(i){var t=i.threshold||0;new IntersectionObserver((t,e)=>{t.forEach(t=>{t.isIntersecting&&"function"==typeof i.callback&&(i.callback(),e.unobserve(t.target))})},{rootMargin:"0px 0px "+t+"px 0px"}).observe(i.element)},T4SThemeSP.Isotopet4s={initEach:function(){var t=w(".isotopet4s:not(.isotopet4s-later):not(.isotopet4s-enabled)");0!=t.length&&(t.each(function(){u(w(this))}),h())},init:u,filter:h},T4SThemeSP.T4SWrappTable=function(){w("table:not('.t4s-table-res')").wrap("<div class='t4s-table-res-df'></div>")},T4SThemeSP.AccordionMobileInt=function(){var t,e=w(".t4s-accordion-mb-true");0!=e.length&&(t=e.data("t4s-w-toggle")||1024,w(window).width()<=t?e.removeClass("t4s-type-tabs").addClass("t4s-type-accordion"):e.removeClass("t4s-type-accordion").addClass("t4s-type-tabs"))},T4SThemeSP.CartAttrHidden=function(){var t,e=w("[data-cart-attr-rm]"),i=0;0==e.length||T4Sconfigs.CartAttrHidden||(t=setInterval(function(){e.val(""),15==i&&clearInterval(t),i++},500),r.on("click",'button[type=submit][name="checkout"]',function(t){e.val("")}))},T4SThemeSP.announcement=function(){var t=".t4s-announcement-bar__close",e=w(".t4s-announcement-bar"),i=e.attr("data-ver"),n=T4Sconfigs.theme,o="t4s_announcement_"+n+"_"+i,s=parseInt(e.attr("data-date"));0!=e.length&&"closed"!=CookiesT4.get(o)&&("closed"==CookiesT4.get(n="t4s_announcement_"+n+"_"+("1_nt"==i?"2_nt":"1_nt"))&&CookiesT4.remove(n),w(t).on("click",function(t){e.css("min-height","auto").attr("aria-hidden",!0).slideUp(),w("html").css("--announcement-height",""),IsDesignMode||CookiesT4.set(o,"closed",{expires:s,path:"/"})}))},T4SThemeSP.Marquee3k=(n="marqueet4s-enabled",o="t4s-marquee--animation",s="t4s-marquee--duplicate",d.prototype=Object.assign({},d.prototype,{resizeHandler:function(){if(this.width!=window.innerWidth){this.width=window.innerWidth,this.marquee3kItem.classList.remove(o);var t=this.el.querySelectorAll("."+s);t.length&&t.forEach(t=>t.remove());for(var e=!1,i=(i=window.innerWidth/this.marquee3kItem.offsetWidth)==1/0?5:i,n=0;n<i;n++)(e=this.marquee3kItem.cloneNode(!0)).setAttribute("aria-hidden",!0),e.classList.add(s),e.classList.add(o),this.el.append(e);this.marquee3kItem.classList.add(o)}}}),d),T4SThemeSP.initMarquee3k=function(){var t=w(".t4s-marquee:not(.marqueet4s-enabled)");0!=t.length&&t.each(function(){this.marquee3k=new T4SThemeSP.Marquee3k(this)})},T4SThemeSP.initVarHeight=function(){var t="[data-get-height]:not(.var-css-enabled)",e=w(t);function i(){e.each(function(){w(this).closest(".t4s-section").css("--var-t4s-height",w(this).height()+"px")})}0!=e.length&&(i(),T.on("resize.varHeight",T4SThemeSP.debounce(550,function(){e=w(t),i()})))};var f,p,g,m,y,v,b,x,S,_,k;window.location.search.indexOf("_posted=true")<0||(a=localStorage.getItem("t4s-recentform")||"xyz",w(` form:not(${window.location.hash}):not(#${a})`).each(function(){w(this).find("[data-t4s-response-form]").hide()})),window.location.href.indexOf(T4Sconfigs.preViewBar)<0||w("html").addClass("is--hidden-previewbar");function I(t,e,i=!1){var n;p||(t&&t.preventDefault(),t=e.attr(y),t=JSON.parse(t||n||"{}"),(n=i?e:w(t.id)).trigger("opendDrawer"),n.attr("aria-hidden","false"),S.addClass(f.overlayVisible),p=!0,r.addClass(_+b),x.css({"margin-right":T4SThemeSP.scrollbarWidth}),T4SThemeSP.Helpers.disableBodyScroll(!0,k))}function P(t,e){p&&(t&&t.preventDefault(),t=void 0!==e?w(e.attr("data-drawer-target")):void 0,(e=1<(e=w(".t4s-drawer[aria-hidden=false]")).length?t:e).attr("aria-hidden","true"),w(".t4s-drawer[aria-hidden=false]").length<=1)&&(S.removeClass(f.overlayVisible),p=!1,e.one("transitionend",function(){r.removeClass(_+b),x.css({"margin-right":""}),T4SThemeSP.Helpers.disableBodyScroll(!1,k)}),S.off(g),r.off(g),r.off(m))}function D(){S.on(g,function(t){P(t)}),r.on(g,"[data-drawer-close]",function(t){P(t,w(this))}),r.on("keyup.drawer",function(t){27===t.keyCode&&(P(t),window.closeCustomKey)&&window.closeCustomKey()})}T4SThemeSP.Helpers={disableBodyScroll:function(){function i(t){!1!==a&&t.target.closest(r)||t.preventDefault()}function n(t){1===t.targetTouches.length&&(s=t.targetTouches[0].clientY)}function o(t){var e;1===t.targetTouches.length&&(e=t.targetTouches[0].clientY-s,0===a.scrollTop&&0<e&&t.preventDefault(),a.scrollHeight-a.scrollTop<=a.clientHeight)&&e<0&&t.preventDefault()}var s,r=!1,a=!1;return Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.closest=function(t){var e=this;if(!document.documentElement.contains(el))return null;do{if(e.matches(t))return e}while(null!==(e=e.parentElement));return el}),function(t,e){void 0!==e&&(r=e,a=document.querySelector(e)),!0===t?(!1!==a&&(a.addEventListener("touchstart",n,!1),a.addEventListener("touchmove",o,!1)),document.body.addEventListener("touchmove",i,!1)):(!1!==a&&(a.removeEventListener("touchstart",n,!1),a.removeEventListener("touchmove",o,!1)),document.body.removeEventListener("touchmove",i,!1))}},debounce:function(n,o,s){var r;return function(){var t=this,e=arguments,i=s&&!r;clearTimeout(r),r=setTimeout(function(){r=null,s||n.apply(t,e)},o),i&&n.apply(t,e)}},getScript:function(s,r){return new Promise(function(i,n){var o=document.createElement("script"),t=r||document.getElementsByTagName("script")[0];function e(t,e){!e&&o.readyState&&!/loaded|complete/.test(o.readyState)||(o.onload=null,o.onreadystatechange=null,o=void 0,(e?n:i)())}o.async=!0,o.defer=!0,o.onload=e,o.onreadystatechange=e,o.src=s,t.parentNode.insertBefore(o,t)})},loadScript:function(t,e){null!=t&&w.ajax({url:t,dataType:"script",success:e,async:!0})},prepareTransition:function(e){e.addEventListener("transitionend",function(t){t.currentTarget.classList.remove("is-transitioning")},{once:!0});var i=0;["transition-duration","-moz-transition-duration","-webkit-transition-duration","-o-transition-duration"].forEach(function(t){t=getComputedStyle(e)[t];t&&(t.replace(/\D/g,""),i=i||parseFloat(t))}),0!==i&&(e.classList.add("is-transitioning"),e.offsetWidth)},cookiesEnabled:function(){var t=navigator.cookieEnabled;return t||(document.cookie="testcookie",t=-1!==document.cookie.indexOf("testcookie")),t},promiseStylesheet:function(t){var n=t,t=n.match(/[\w-]+\.(css)/g)[0];return void 0===T4SThemeSP.stylesheetPromise[t]&&(T4SThemeSP.stylesheetPromise[t]=new Promise(function(t){var e=document.getElementsByTagName("head")[0],i=document.createElement("link");i.rel="stylesheet",i.type="text/css",i.href=n,i.media="all",e.appendChild(i),i.loaded&&t(),i.addEventListener("load",function(){setTimeout(t,100)})})),T4SThemeSP.stylesheetPromise[t]}},T4SThemeSP.stylesheetPromise={},T4SThemeSP.getScrollbar=function(){var e=w("html");function t(){var t=window.innerWidth-document.body.clientWidth;0<=t&&e.css({"--scroll-w":t+"px"}),T4SThemeSP.scrollbarWidth=t}t(),window.addEventListener("resize",function(i){let n=null,o;var t=(...t)=>{var e;o=t,null===n&&(n=requestAnimationFrame((e=this,()=>{n=null,i.apply(e,o)})))};return t.cancel=()=>{cancelAnimationFrame(n),n=null},t}(t))},T4SThemeSP.Drawer=(p=!(f={overlayVisible:"is--visible"}),g="click.drawer",m="keyup.drawer",y="data-drawer-options",v="data-drawer-delay",b=" is--opend-drawer",x=w("html"),S=w(".t4s-close-overlay"),_="t4s-lock-scroll",k="[data-t4s-scroll-me]",{init:function(){t.on(g,"["+y+"]:not(["+v+"])",function(t){I(t,w(this)),D()});{let t=w("[data-sidebar-trigger]"),e=w(".t4s-btn-sidebar");0!=t.length&&0!=e.length&&t.on(g,function(t){t.preventDefault(),e.trigger(g)})}},opend:function(t){I(null,t,!(p=!1)),D()},close:P,remove:function(t){w("["+y+'*="#'+t+'"]').removeAttr(v)}})}(jQuery_T4NT),jQuery_T4NT(document).ready(function(t){T4SThemeSP.fullHeightFirtSe(),T4SThemeSP.initVarHeight(),T4SThemeSP.initMarquee3k(),T4SThemeSP.initCarousel(),T4SThemeSP.Isotopet4s.initEach(),T4SThemeSP.announcement(),T4SThemeSP.T4SWrappTable(),T4SThemeSP.CartAttrHidden(),T4SThemeSP.Drawer.init(),T4SThemeSP.getScrollbar(),"fetch"in window&&"assign"in Object&&"ResizeObserver"in window&&"IntersectionObserver"in window&&"DateTimeFormat"in Intl?$script(T4Sconfigs.script2,function(){IsDesignMode&&$script(T4Sconfigs.script9),$script(T4Sconfigs.script10)}):$script(T4Sconfigs.script1,function(){$script(T4Sconfigs.script2,function(){IsDesignMode&&$script(T4Sconfigs.script9),$script(T4Sconfigs.script10)})}),0<t("[data-t4s-type]").length&&$script(T4Sconfigs.script12d)}),jQuery_T4NT(window).on("resize",function(){T4SThemeSP.AccordionMobileInt()}),"ontouchend"in document){let t=0;const _da=()=>{1<++t&&document.removeEventListener("touchend",_da)};document.addEventListener("touchend",_da)}