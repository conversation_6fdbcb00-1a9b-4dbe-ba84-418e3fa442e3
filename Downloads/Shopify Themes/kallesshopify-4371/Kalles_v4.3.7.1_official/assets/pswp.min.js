!function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipe=t()}(this,function(){"use strict";return function(e,t,n,o){var i={features:null,bind:function(e,t,n,o){var i=(o?"remove":"add")+"EventListener";t=t.split(" ");for(var a=0;a<t.length;a++)t[a]&&e[i](t[a],n,!1)},isArray:function(e){return e instanceof Array},createEl:function(e,t){var n=document.createElement(t||"div");return e&&(n.className=e),n},getScrollY:function(){var e=window.pageYOffset;return void 0!==e?e:document.documentElement.scrollTop},unbind:function(e,t,n){i.bind(e,t,n,!0)},removeClass:function(e,t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(n," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")},addClass:function(e,t){i.hasClass(e,t)||(e.className+=(e.className?" ":"")+t)},hasClass:function(e,t){return e.className&&new RegExp("(^|\\s)"+t+"(\\s|$)").test(e.className)},getChildByClass:function(e,t){for(var n=e.firstChild;n;){if(i.hasClass(n,t))return n;n=n.nextSibling }},arraySearch:function(e,t,n){for(var o=e.length;o--;)if(e[o][n]===t)return o;return-1},extend:function(e,t,n){for(var o in t)if(t.hasOwnProperty(o)){if(n&&e.hasOwnProperty(o))continue;e[o]=t[o] }},easing:{sine:{out:function(e){return Math.sin(e*(Math.PI/2))},inOut:function(e){return-(Math.cos(Math.PI*e)-1)/2 }},cubic:{out:function(e){return--e*e*e+1 }}},detectFeatures:function(){if(i.features)return i.features;var e=i.createEl().style,t="",n={};if(n.oldIE=document.all&&!document.addEventListener,n.touch="ontouchstart"in window,window.requestAnimationFrame&&(n.raf=window.requestAnimationFrame,n.caf=window.cancelAnimationFrame),n.pointerEvent=!!window.PointerEvent||navigator.msPointerEnabled,!n.pointerEvent){var o=navigator.userAgent;if(/iP(hone|od)/.test(navigator.platform)){var a=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);a&&a.length>0&&(a=parseInt(a[1],10))>=1&&a<8&&(n.isOldIOSPhone=!0)}var r=o.match(/Android\s([0-9\.]*)/),l=r?r[1]:0;(l=parseFloat(l))>=1&&(l<4.4&&(n.isOldAndroid=!0),n.androidVersion=l),n.isMobileOpera=/opera mini|opera mobi/i.test(o)}for(var s,u,c=["transform","perspective","animationName"],d=["","webkit","Moz","ms","O"],p=0;p<4;p++){t=d[p];for(var m=0;m<3;m++)s=c[m],u=t+(t?s.charAt(0).toUpperCase()+s.slice(1):s),!n[s]&&u in e&&(n[s]=u);t&&!n.raf&&(t=t.toLowerCase(),n.raf=window[t+"RequestAnimationFrame"],n.raf&&(n.caf=window[t+"CancelAnimationFrame"]||window[t+"CancelRequestAnimationFrame"]))}if(!n.raf){var f=0;n.raf=function(e){var t=(new Date).getTime(),n=Math.max(0,16-(t-f)),o=window.setTimeout(function(){e(t+n)},n);return f=t+n,o},n.caf=function(e){clearTimeout(e) }}return n.svg=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,i.features=n,n }};i.detectFeatures(),i.features.oldIE&&(i.bind=function(e,t,n,o){t=t.split(" ");for(var i,a=(o?"detach":"attach")+"Event",r=function(){n.handleEvent.call(n)},l=0;l<t.length;l++)if(i=t[l])if("object"==typeof n&&n.handleEvent){if(o){if(!n["oldIE"+i])return!1}else n["oldIE"+i]=r;e[a]("on"+i,n["oldIE"+i])}else e[a]("on"+i,n)});var a=this,r={allowPanToNext:!0,spacing:.12,bgOpacity:1,mouseUsed:!1,loop:!0,pinchToClose:!0,closeOnScroll:!0,closeOnVerticalDrag:!0,verticalDragRange:.75,hideAnimationDuration:333,showAnimationDuration:333,showHideOpacity:!1,focus:!0,escKey:!0,arrowKeys:!0,mainScrollEndFriction:.35,panEndFriction:.35,isClickableElement:function(e){return"A"===e.tagName},getDoubleTapZoom:function(e,t){return e?1:t.initialZoomLevel<.7?1:1.33},maxSpreadZoom:1.33,modal:!0,scaleMode:"fit"};i.extend(r,o);var l,s,u,c,d,p,m,f,h,v,g,y,w,x,b,C,T,I,E,_,S,D,k,M,F,O,A,R,L,P,Z,z,N,U,K,B,H,W,G,Y,q,V,X,j,$,Q,J,ee,te,ne,oe,ie,ae,re,le,se,ue={x:0,y:0},ce={x:0,y:0},de={x:0,y:0},pe={},me=0,fe={},he={x:0,y:0},ve=0,ge=!0,ye=[],we={},xe=!1,be=function(e,t){i.extend(a,t.publicMethods),ye.push(e)},Ce=function(e){var t=Kt();return e>t-1?e-t:e<0?t+e:e},Te={},Ie=function(e,t){return Te[e]||(Te[e]=[]),Te[e].push(t)},Ee=function(e){var t=Te[e];if(t){var n=Array.prototype.slice.call(arguments);n.shift();for(var o=0;o<t.length;o++)t[o].apply(a,n) }},_e=function(){return(new Date).getTime()},Se=function(e){re=e,a.bg.style.opacity=e*r.bgOpacity},De=function(e,t,n,o,i){(!xe||i&&i!==a.currItem)&&(o/=i?i.fitRatio:a.currItem.fitRatio),e[D]=y+t+"px, "+n+"px"+w+" scale("+o+")"},ke=function(e){te&&(e&&(v>a.currItem.fitRatio?xe||(jt(a.currItem,!1,!0),xe=!0):xe&&(jt(a.currItem),xe=!1)),De(te,de.x,de.y,v))},Me=function(e){e.container&&De(e.container.style,e.initialPosition.x,e.initialPosition.y,e.initialZoomLevel,e)},Fe=function(e,t){t[D]=y+e+"px, 0px"+w},Oe=function(e,t){if(!r.loop&&t){var n=c+(he.x*me-e)/he.x,o=Math.round(e-ct.x);(n<0&&o>0||n>=Kt()-1&&o<0)&&(e=ct.x+o*r.mainScrollEndFriction)}ct.x=e,Fe(e,d)},Ae=function(e,t){var n=dt[e]-fe[e];return ce[e]+ue[e]+n-n*(t/g)},Re=function(e,t){e.x=t.x,e.y=t.y,t.id&&(e.id=t.id)},Le=function(e){e.x=Math.round(e.x),e.y=Math.round(e.y)},Pe=null,Ze=function(){Pe&&(i.unbind(document,"mousemove",Ze),i.addClass(e,"pswp--has_mouse"),r.mouseUsed=!0,Ee("mouseUsed")),Pe=setTimeout(function(){Pe=null},100)},ze=function(e,t){var n=Yt(a.currItem,pe,e);return t&&(ee=n),n},Ne=function(e){return e||(e=a.currItem),e.initialZoomLevel},Ue=function(e){return e||(e=a.currItem),e.w>0?r.maxSpreadZoom:1},Ke=function(e,t,n,o){return o===a.currItem.initialZoomLevel?(n[e]=a.currItem.initialPosition[e],!0):(n[e]=Ae(e,o),n[e]>t.min[e]?(n[e]=t.min[e],!0):n[e]<t.max[e]&&(n[e]=t.max[e],!0))},Be=function(e){var t="";r.escKey&&27===e.keyCode?t="close":r.arrowKeys&&(37===e.keyCode?t="prev":39===e.keyCode&&(t="next")),t&&(e.ctrlKey||e.altKey||e.shiftKey||e.metaKey||(e.preventDefault?e.preventDefault():e.returnValue=!1,a[t]()))},He=function(e){e&&(V||q||ne||H)&&(e.preventDefault(),e.stopPropagation())},We=function(){a.setScrollOffset(0,i.getScrollY())},Ge={},Ye=0,qe=function(e){Ge[e]&&(Ge[e].raf&&O(Ge[e].raf),Ye--,delete Ge[e])},Ve=function(e){Ge[e]&&qe(e),Ge[e]||(Ye++,Ge[e]={})},Xe=function(){for(var e in Ge)Ge.hasOwnProperty(e)&&qe(e)},je=function(e,t,n,o,i,a,r){var l,s=_e();Ve(e);var u=function(){if(Ge[e]){if((l=_e()-s)>=o)return qe(e),a(n),void(r&&r());a((n-t)*i(l/o)+t),Ge[e].raf=F(u) }};u()},$e={shout:Ee,listen:Ie,viewportSize:pe,options:r,isMainScrollAnimating:function(){return ne},getZoomLevel:function(){return v},getCurrentIndex:function(){return c},isDragging:function(){return G},isZooming:function(){return Q},setScrollOffset:function(e,t){fe.x=e,P=fe.y=t,Ee("updateScrollOffset",fe)},applyZoomPan:function(e,t,n,o){de.x=t,de.y=n,v=e,ke(o)},init:function(){if(!l&&!s){var n;a.framework=i,a.template=e,a.bg=i.getChildByClass(e,"pswp__bg"),A=e.className,l=!0,Z=i.detectFeatures(),F=Z.raf,O=Z.caf,D=Z.transform,L=Z.oldIE,a.scrollWrap=i.getChildByClass(e,"pswp__scroll-wrap"),a.container=i.getChildByClass(a.scrollWrap,"pswp__container"),d=a.container.style,a.itemHolders=C=[{el:a.container.children[0],wrap:0,index:-1},{el:a.container.children[1],wrap:0,index:-1},{el:a.container.children[2],wrap:0,index:-1}],C[0].el.style.display=C[2].el.style.display="none",function(){if(D){var t=Z.perspective&&!M;return y="translate"+(t?"3d(":"("),void(w=Z.perspective?", 0px)":")")}D="left",i.addClass(e,"pswp--ie"),Fe=function(e,t){t.left=e+"px"},Me=function(e){var t=e.fitRatio>1?1:e.fitRatio,n=e.container.style,o=t*e.w,i=t*e.h;n.width=o+"px",n.height=i+"px",n.left=e.initialPosition.x+"px",n.top=e.initialPosition.y+"px"},ke=function(){if(te){var e=te,t=a.currItem,n=t.fitRatio>1?1:t.fitRatio,o=n*t.w,i=n*t.h;e.width=o+"px",e.height=i+"px",e.left=de.x+"px",e.top=de.y+"px" }}}(),h={resize:a.updateSize,orientationchange:function(){clearTimeout(z),z=setTimeout(function(){pe.x!==a.scrollWrap.clientWidth&&a.updateSize()},500)},scroll:We,keydown:Be,click:He};var o=Z.isOldIOSPhone||Z.isOldAndroid||Z.isMobileOpera;for(Z.animationName&&Z.transform&&!o||(r.showAnimationDuration=r.hideAnimationDuration=0),n=0;n<ye.length;n++)a["init"+ye[n]]();t&&(a.ui=new t(a,i)).init(),Ee("firstUpdate"),c=c||r.index||0,(isNaN(c)||c<0||c>=Kt())&&(c=0),a.currItem=Ut(c),(Z.isOldIOSPhone||Z.isOldAndroid)&&(ge=!1),e.setAttribute("aria-hidden","false"),r.modal&&(ge?e.style.position="fixed":(e.style.position="absolute",e.style.top=i.getScrollY()+"px")),void 0===P&&(Ee("initialLayout"),P=R=i.getScrollY());var u="pswp--open ";for(r.mainClass&&(u+=r.mainClass+" "),r.showHideOpacity&&(u+="pswp--animate_opacity "),u+=M?"pswp--touch":"pswp--notouch",u+=Z.animationName?" pswp--css_animation":"",u+=Z.svg?" pswp--svg":"",i.addClass(e,u),a.updateSize(),p=-1,ve=null,n=0;n<3;n++)Fe((n+p)*he.x,C[n].el.style);L||i.bind(a.scrollWrap,f,a),Ie("initialZoomInEnd",function(){a.setContent(C[0],c-1),a.setContent(C[2],c+1),C[0].el.style.display=C[2].el.style.display="block",r.focus&&e.focus(),i.bind(document,"keydown",a),Z.transform&&i.bind(a.scrollWrap,"click",a),r.mouseUsed||i.bind(document,"mousemove",Ze),i.bind(window,"resize scroll orientationchange",a),Ee("bindEvents")}),a.setContent(C[1],c),a.updateCurrItem(),Ee("afterInit"),ge||(x=setInterval(function(){Ye||G||Q||v!==a.currItem.initialZoomLevel||a.updateSize()},1e3)),i.addClass(e,"pswp--visible") }},close:function(){l&&(l=!1,s=!0,Ee("close"),i.unbind(window,"resize scroll orientationchange",a),i.unbind(window,"scroll",h.scroll),i.unbind(document,"keydown",a),i.unbind(document,"mousemove",Ze),Z.transform&&i.unbind(a.scrollWrap,"click",a),G&&i.unbind(window,m,a),clearTimeout(z),Ee("unbindEvents"),Bt(a.currItem,null,!0,a.destroy))},destroy:function(){Ee("destroy"),Pt&&clearTimeout(Pt),e.setAttribute("aria-hidden","true"),e.className=A,x&&clearInterval(x),i.unbind(a.scrollWrap,f,a),i.unbind(window,"scroll",a),ft(),Xe(),Te=null},panTo:function(e,t,n){n||(e>ee.min.x?e=ee.min.x:e<ee.max.x&&(e=ee.max.x),t>ee.min.y?t=ee.min.y:t<ee.max.y&&(t=ee.max.y)),de.x=e,de.y=t,ke()},handleEvent:function(e){e=e||window.event,h[e.type]&&h[e.type](e)},goTo:function(e){var t=(e=Ce(e))-c;ve=t,c=e,a.currItem=Ut(c),me-=t,Oe(he.x*me),Xe(),ne=!1,a.updateCurrItem()},next:function(){a.goTo(c+1)},prev:function(){a.goTo(c-1)},updateCurrZoomItem:function(e){if(e&&Ee("beforeChange",0),C[1].el.children.length){var t=C[1].el.children[0];te=i.hasClass(t,"pswp__zoom-wrap")?t.style:null}else te=null;ee=a.currItem.bounds,g=v=a.currItem.initialZoomLevel,de.x=ee.center.x,de.y=ee.center.y,e&&Ee("afterChange")},invalidateCurrItems:function(){b=!0;for(var e=0;e<3;e++)C[e].item&&(C[e].item.needsUpdate=!0)},updateCurrItem:function(e){if(0!==ve){var t,n=Math.abs(ve);if(!(e&&n<2)){a.currItem=Ut(c),xe=!1,Ee("beforeChange",ve),n>=3&&(p+=ve+(ve>0?-3:3),n=3);for(var o=0;o<n;o++)ve>0?(t=C.shift(),C[2]=t,Fe((++p+2)*he.x,t.el.style),a.setContent(t,c-n+o+1+1)):(t=C.pop(),C.unshift(t),Fe(--p*he.x,t.el.style),a.setContent(t,c+n-o-1-1));if(te&&1===Math.abs(ve)){var i=Ut(T);i.initialZoomLevel!==v&&(Yt(i,pe),jt(i),Me(i))}ve=0,a.updateCurrZoomItem(),T=c,Ee("afterChange") }}},updateSize:function(t){if(!ge&&r.modal){var n=i.getScrollY();if(P!==n&&(e.style.top=n+"px",P=n),!t&&we.x===window.innerWidth&&we.y===window.innerHeight)return;we.x=window.innerWidth,we.y=window.innerHeight,e.style.height=we.y+"px"}if(pe.x=a.scrollWrap.clientWidth,pe.y=a.scrollWrap.clientHeight,We(),he.x=pe.x+Math.round(pe.x*r.spacing),he.y=pe.y,Oe(he.x*me),Ee("beforeResize"),void 0!==p){for(var o,l,s,u=0;u<3;u++)o=C[u],Fe((u+p)*he.x,o.el.style),s=c+u-1,r.loop&&Kt()>2&&(s=Ce(s)),(l=Ut(s))&&(b||l.needsUpdate||!l.bounds)?(a.cleanSlide(l),a.setContent(o,s),1===u&&(a.currItem=l,a.updateCurrZoomItem(!0)),l.needsUpdate=!1):-1===o.index&&s>=0&&a.setContent(o,s),l&&l.container&&(Yt(l,pe),jt(l),Me(l));b=!1}g=v=a.currItem.initialZoomLevel,(ee=a.currItem.bounds)&&(de.x=ee.center.x,de.y=ee.center.y,ke(!0)),Ee("resize")},zoomTo:function(e,t,n,o,a){t&&(g=v,dt.x=Math.abs(t.x)-de.x,dt.y=Math.abs(t.y)-de.y,Re(ce,de));var r=ze(e,!1),l={};Ke("x",r,l,e),Ke("y",r,l,e);var s=v,u=de.x,c=de.y;Le(l);var d=function(t){1===t?(v=e,de.x=l.x,de.y=l.y):(v=(e-s)*t+s,de.x=(l.x-u)*t+u,de.y=(l.y-c)*t+c),a&&a(t),ke(1===t)};n?je("customZoomTo",0,1,n,o||i.easing.sine.inOut,d):d(1) }},Qe={},Je={},et={},tt={},nt={},ot=[],it={},at=[],rt={},lt=0,st={x:0,y:0},ut=0,ct={x:0,y:0},dt={x:0,y:0},pt={x:0,y:0},mt=function(e,t){return rt.x=Math.abs(e.x-t.x),rt.y=Math.abs(e.y-t.y),Math.sqrt(rt.x*rt.x+rt.y*rt.y)},ft=function(){X&&(O(X),X=null)},ht=function(){G&&(X=F(ht),kt())},vt=function(e,t){return!(!e||e===document)&&!(e.getAttribute("class")&&e.getAttribute("class").indexOf("pswp__scroll-wrap")>-1)&&(t(e)?e:vt(e.parentNode,t))},gt={},yt=function(e,t){return gt.prevent=!vt(e.target,r.isClickableElement),Ee("preventDragEvent",e,t,gt),gt.prevent},wt=function(e,t){return t.x=e.pageX,t.y=e.pageY,t.id=e.identifier,t},xt=function(e,t,n){n.x=.5*(e.x+t.x),n.y=.5*(e.y+t.y)},bt=function(){var e=de.y-a.currItem.initialPosition.y;return 1-Math.abs(e/(pe.y/2))},Ct={},Tt={},It=[],Et=function(e){for(;It.length>0;)It.pop();return k?(se=0,ot.forEach(function(e){0===se?It[0]=e:1===se&&(It[1]=e),se++})):e.type.indexOf("touch")>-1?e.touches&&e.touches.length>0&&(It[0]=wt(e.touches[0],Ct),e.touches.length>1&&(It[1]=wt(e.touches[1],Tt))):(Ct.x=e.pageX,Ct.y=e.pageY,Ct.id="",It[0]=Ct),It},_t=function(e,t){var n,o,i,l,s=de[e]+t[e],u=t[e]>0,c=ct.x+t.x,d=ct.x-it.x;return n=s>ee.min[e]||s<ee.max[e]?r.panEndFriction:1,s=de[e]+t[e]*n,!r.allowPanToNext&&v!==a.currItem.initialZoomLevel||(te?"h"!==oe||"x"!==e||q||(u?(s>ee.min[e]&&(n=r.panEndFriction,ee.min[e],o=ee.min[e]-ce[e]),(o<=0||d<0)&&Kt()>1?(l=c,d<0&&c>it.x&&(l=it.x)):ee.min.x!==ee.max.x&&(i=s)):(s<ee.max[e]&&(n=r.panEndFriction,ee.max[e],o=ce[e]-ee.max[e]),(o<=0||d>0)&&Kt()>1?(l=c,d>0&&c<it.x&&(l=it.x)):ee.min.x!==ee.max.x&&(i=s))):l=c,"x"!==e)?void(ne||j||v>a.currItem.fitRatio&&(de[e]+=t[e]*n)):(void 0!==l&&(Oe(l,!0),j=l!==it.x),ee.min.x!==ee.max.x&&(void 0!==i?de.x=i:j||(de.x+=t.x*n)),void 0!==l)},St=function(e){if(!("mousedown"===e.type&&e.button>0)){if(Nt)return void e.preventDefault();if(!W||"mousedown"!==e.type){if(yt(e,!0)&&e.preventDefault(),Ee("pointerDown"),k){var t=i.arraySearch(ot,e.pointerId,"id");t<0&&(t=ot.length),ot[t]={x:e.pageX,y:e.pageY,id:e.pointerId }}var n=Et(e),o=n.length;$=null,Xe(),G&&1!==o||(G=ie=!0,i.bind(window,m,a),B=le=ae=H=j=V=Y=q=!1,oe=null,Ee("firstTouchStart",n),Re(ce,de),ue.x=ue.y=0,Re(tt,n[0]),Re(nt,tt),it.x=he.x*me,at=[{x:tt.x,y:tt.y}],U=N=_e(),ze(v,!0),ft(),ht()),!Q&&o>1&&!ne&&!j&&(g=v,q=!1,Q=Y=!0,ue.y=ue.x=0,Re(ce,de),Re(Qe,n[0]),Re(Je,n[1]),xt(Qe,Je,pt),dt.x=Math.abs(pt.x)-de.x,dt.y=Math.abs(pt.y)-de.y,J=mt(Qe,Je)) }}},Dt=function(e){if(e.preventDefault(),k){var t=i.arraySearch(ot,e.pointerId,"id");if(t>-1){var n=ot[t];n.x=e.pageX,n.y=e.pageY }}if(G){var o=Et(e);if(oe||V||Q)$=o;else if(ct.x!==he.x*me)oe="h";else{var a=Math.abs(o[0].x-tt.x)-Math.abs(o[0].y-tt.y);Math.abs(a)>=10&&(oe=a>0?"h":"v",$=o) }}},kt=function(){if($){var e=$.length;if(0!==e)if(Re(Qe,$[0]),et.x=Qe.x-tt.x,et.y=Qe.y-tt.y,Q&&e>1){if(tt.x=Qe.x,tt.y=Qe.y,!et.x&&!et.y&&function(e,t){return e.x===t.x&&e.y===t.y}($[1],Je))return;Re(Je,$[1]),q||(q=!0,Ee("zoomGestureStarted"));var t=mt(Qe,Je),n=Rt(t);n>a.currItem.initialZoomLevel+a.currItem.initialZoomLevel/15&&(le=!0);var o=1,i=Ne(),l=Ue();if(n<i)if(r.pinchToClose&&!le&&g<=a.currItem.initialZoomLevel){var s=1-(i-n)/(i/1.2);Se(s),Ee("onPinchClose",s),ae=!0}else(o=(i-n)/i)>1&&(o=1),n=i-o*(i/3);else n>l&&((o=(n-l)/(6*i))>1&&(o=1),n=l+o*i);o<0&&(o=0),xt(Qe,Je,st),ue.x+=st.x-pt.x,ue.y+=st.y-pt.y,Re(pt,st),de.x=Ae("x",n),de.y=Ae("y",n),B=n>v,v=n,ke()}else{if(!oe)return;if(ie&&(ie=!1,Math.abs(et.x)>=10&&(et.x-=$[0].x-nt.x),Math.abs(et.y)>=10&&(et.y-=$[0].y-nt.y)),tt.x=Qe.x,tt.y=Qe.y,0===et.x&&0===et.y)return;if("v"===oe&&r.closeOnVerticalDrag&&"fit"===r.scaleMode&&v===a.currItem.initialZoomLevel){ue.y+=et.y,de.y+=et.y;var u=bt();return H=!0,Ee("onVerticalDrag",u),Se(u),void ke()}(function(e,t,n){if(e-U>50){var o=at.length>2?at.shift():{};o.x=t,o.y=n,at.push(o),U=e }})(_e(),Qe.x,Qe.y),V=!0,ee=a.currItem.bounds,_t("x",et)||(_t("y",et),Le(de),ke()) }}},Mt=function(e){if(Z.isOldAndroid){if(W&&"mouseup"===e.type)return;e.type.indexOf("touch")>-1&&(clearTimeout(W),W=setTimeout(function(){W=0},600))}var t;if(Ee("pointerUp"),yt(e,!1)&&e.preventDefault(),k){var n=i.arraySearch(ot,e.pointerId,"id");n>-1&&(t=ot.splice(n,1)[0],navigator.msPointerEnabled?(t.type={4:"mouse",2:"touch",3:"pen"}[e.pointerType],t.type||(t.type=e.pointerType||"mouse")):t.type=e.pointerType||"mouse")}var o,l=Et(e),s=l.length;if("mouseup"===e.type&&(s=0),2===s)return $=null,!0;1===s&&Re(nt,l[0]),0!==s||oe||ne||(t||("mouseup"===e.type?t={x:e.pageX,y:e.pageY,type:"mouse"}:e.changedTouches&&e.changedTouches[0]&&(t={x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY,type:"touch"})),Ee("touchRelease",e,t));var u=-1;if(0===s&&(G=!1,i.unbind(window,m,a),ft(),Q?u=0:-1!==ut&&(u=_e()-ut)),ut=1===s?_e():-1,o=-1!==u&&u<150?"zoom":"swipe",Q&&s<2&&(Q=!1,1===s&&(o="zoomPointerUp"),Ee("zoomGestureEnded")),$=null,V||q||ne||H)if(Xe(),K||(K=Ft()),K.calculateSwipeSpeed("x"),H)if(bt()<r.verticalDragRange)a.close();else{var c=de.y,d=re;je("verticalDrag",0,1,300,i.easing.cubic.out,function(e){de.y=(a.currItem.initialPosition.y-c)*e+c,Se((1-d)*e+d),ke()}),Ee("onVerticalDrag",1)}else{if((j||ne)&&0===s){if(At(o,K))return;o="zoomPointerUp"}if(!ne)return"swipe"!==o?void Lt():void(!j&&v>a.currItem.fitRatio&&Ot(K)) }},Ft=function(){var e,t,n={lastFlickOffset:{},lastFlickDist:{},lastFlickSpeed:{},slowDownRatio:{},slowDownRatioReverse:{},speedDecelerationRatio:{},speedDecelerationRatioAbs:{},distanceOffset:{},backAnimDestination:{},backAnimStarted:{},calculateSwipeSpeed:function(o){at.length>1?(e=_e()-U+50,t=at[at.length-2][o]):(e=_e()-N,t=nt[o]),n.lastFlickOffset[o]=tt[o]-t,n.lastFlickDist[o]=Math.abs(n.lastFlickOffset[o]),n.lastFlickDist[o]>20?n.lastFlickSpeed[o]=n.lastFlickOffset[o]/e:n.lastFlickSpeed[o]=0,Math.abs(n.lastFlickSpeed[o])<.1&&(n.lastFlickSpeed[o]=0),n.slowDownRatio[o]=.95,n.slowDownRatioReverse[o]=1-n.slowDownRatio[o],n.speedDecelerationRatio[o]=1},calculateOverBoundsAnimOffset:function(e,t){n.backAnimStarted[e]||(de[e]>ee.min[e]?n.backAnimDestination[e]=ee.min[e]:de[e]<ee.max[e]&&(n.backAnimDestination[e]=ee.max[e]),void 0!==n.backAnimDestination[e]&&(n.slowDownRatio[e]=.7,n.slowDownRatioReverse[e]=1-n.slowDownRatio[e],n.speedDecelerationRatioAbs[e]<.05&&(n.lastFlickSpeed[e]=0,n.backAnimStarted[e]=!0,je("bounceZoomPan"+e,de[e],n.backAnimDestination[e],t||300,i.easing.sine.out,function(t){de[e]=t,ke()}))))},calculateAnimOffset:function(e){n.backAnimStarted[e]||(n.speedDecelerationRatio[e]=n.speedDecelerationRatio[e]*(n.slowDownRatio[e]+n.slowDownRatioReverse[e]-n.slowDownRatioReverse[e]*n.timeDiff/10),n.speedDecelerationRatioAbs[e]=Math.abs(n.lastFlickSpeed[e]*n.speedDecelerationRatio[e]),n.distanceOffset[e]=n.lastFlickSpeed[e]*n.speedDecelerationRatio[e]*n.timeDiff,de[e]+=n.distanceOffset[e])},panAnimLoop:function(){if(Ge.zoomPan&&(Ge.zoomPan.raf=F(n.panAnimLoop),n.now=_e(),n.timeDiff=n.now-n.lastNow,n.lastNow=n.now,n.calculateAnimOffset("x"),n.calculateAnimOffset("y"),ke(),n.calculateOverBoundsAnimOffset("x"),n.calculateOverBoundsAnimOffset("y"),n.speedDecelerationRatioAbs.x<.05&&n.speedDecelerationRatioAbs.y<.05))return de.x=Math.round(de.x),de.y=Math.round(de.y),ke(),void qe("zoomPan") }};return n},Ot=function(e){return e.calculateSwipeSpeed("y"),ee=a.currItem.bounds,e.backAnimDestination={},e.backAnimStarted={},Math.abs(e.lastFlickSpeed.x)<=.05&&Math.abs(e.lastFlickSpeed.y)<=.05?(e.speedDecelerationRatioAbs.x=e.speedDecelerationRatioAbs.y=0,e.calculateOverBoundsAnimOffset("x"),e.calculateOverBoundsAnimOffset("y"),!0):(Ve("zoomPan"),e.lastNow=_e(),void e.panAnimLoop())},At=function(e,t){var n,o,l;if(ne||(lt=c),"swipe"===e){var s=tt.x-nt.x,u=t.lastFlickDist.x<10;s>30&&(u||t.lastFlickOffset.x>20)?o=-1:s<-30&&(u||t.lastFlickOffset.x<-20)&&(o=1)}o&&((c+=o)<0?(c=r.loop?Kt()-1:0,l=!0):c>=Kt()&&(c=r.loop?0:Kt()-1,l=!0),l&&!r.loop||(ve+=o,me-=o,n=!0));var d,p=he.x*me,m=Math.abs(p-ct.x);return n||p>ct.x==t.lastFlickSpeed.x>0?(d=Math.abs(t.lastFlickSpeed.x)>0?m/Math.abs(t.lastFlickSpeed.x):333,d=Math.min(d,400),d=Math.max(d,250)):d=333,lt===c&&(n=!1),ne=!0,Ee("mainScrollAnimStart"),je("mainScroll",ct.x,p,d,i.easing.cubic.out,Oe,function(){Xe(),ne=!1,lt=-1,(n||lt!==c)&&a.updateCurrItem(),Ee("mainScrollAnimComplete")}),n&&a.updateCurrItem(!0),n},Rt=function(e){return 1/J*e*g},Lt=function(){var e=v,t=Ne(),n=Ue();v<t?e=t:v>n&&(e=n);var o,r=re;return ae&&!B&&!le&&v<t?(a.close(),!0):(ae&&(o=function(e){Se((1-r)*e+r)}),a.zoomTo(e,0,200,i.easing.cubic.out,o),!0)};be("Gestures",{publicMethods:{initGestures:function(){var e=function(e,t,n,o,i){I=e+t,E=e+n,_=e+o,S=i?e+i:""};(k=Z.pointerEvent)&&Z.touch&&(Z.touch=!1),k?navigator.msPointerEnabled?e("MSPointer","Down","Move","Up","Cancel"):e("pointer","down","move","up","cancel"):Z.touch?(e("touch","start","move","end","cancel"),M=!0):e("mouse","down","move","up"),m=E+" "+_+" "+S,f=I,k&&!M&&(M=navigator.maxTouchPoints>1||navigator.msMaxTouchPoints>1),a.likelyTouchDevice=M,h[I]=St,h[E]=Dt,h[_]=Mt,S&&(h[S]=h[_]),Z.touch&&(f+=" mousedown",m+=" mousemove mouseup",h.mousedown=h[I],h.mousemove=h[E],h.mouseup=h[_]),M||(r.allowPanToNext=!1) }}});var Pt,Zt,zt,Nt,Ut,Kt,Bt=function(t,n,o,l){var s;Pt&&clearTimeout(Pt),Nt=!0,zt=!0,t.initialLayout?(s=t.initialLayout,t.initialLayout=null):s=r.getThumbBoundsFn&&r.getThumbBoundsFn(c);var d=o?r.hideAnimationDuration:r.showAnimationDuration,p=function(){qe("initialZoom"),o?(a.template.removeAttribute("style"),a.bg.removeAttribute("style")):(Se(1),n&&(n.style.display="block"),i.addClass(e,"pswp--animated-in"),Ee("initialZoom"+(o?"OutEnd":"InEnd"))),l&&l(),Nt=!1};if(!d||!s||void 0===s.x)return Ee("initialZoom"+(o?"Out":"In")),v=t.initialZoomLevel,Re(de,t.initialPosition),ke(),e.style.opacity=o?0:1,Se(1),void(d?setTimeout(function(){p()},d):p());!function(){var n=u,l=!a.currItem.src||a.currItem.loadError||r.showHideOpacity;t.miniImg&&(t.miniImg.style.webkitBackfaceVisibility="hidden"),o||(v=s.w/t.w,de.x=s.x,de.y=s.y-R,a[l?"template":"bg"].style.opacity=.001,ke()),Ve("initialZoom"),o&&!n&&i.removeClass(e,"pswp--animated-in"),l&&(o?i[(n?"remove":"add")+"Class"](e,"pswp--animate_opacity"):setTimeout(function(){i.addClass(e,"pswp--animate_opacity")},30)),Pt=setTimeout(function(){if(Ee("initialZoom"+(o?"Out":"In")),o){var a=s.w/t.w,r={x:de.x,y:de.y},u=v,c=re,m=function(t){1===t?(v=a,de.x=s.x,de.y=s.y-P):(v=(a-u)*t+u,de.x=(s.x-r.x)*t+r.x,de.y=(s.y-P-r.y)*t+r.y),ke(),l?e.style.opacity=1-t:Se(c-t*c)};n?je("initialZoom",0,1,d,i.easing.cubic.out,m,p):(m(1),Pt=setTimeout(p,d+20))}else v=t.initialZoomLevel,Re(de,t.initialPosition),ke(),Se(1),l?e.style.opacity=1:Se(1),Pt=setTimeout(p,d+20)},o?25:90)}()},Ht={},Wt=[],Gt={index:0,errorMsg:'<div class="pswp__error-msg"><a href="%url%" target="_blank">The image</a> could not be loaded.</div>',forceProgressiveLoading:!1,preload:[1,1],getNumItemsFn:function(){return Zt.length }},Yt=function(e,t,n){if(e.src&&!e.loadError){var o=!n;if(o&&(e.vGap||(e.vGap={top:0,bottom:0}),Ee("parseVerticalMargin",e)),Ht.x=t.x,Ht.y=t.y-e.vGap.top-e.vGap.bottom,o){var i=Ht.x/e.w,a=Ht.y/e.h;e.fitRatio=i<a?i:a;var l=r.scaleMode;"orig"===l?n=1:"fit"===l&&(n=e.fitRatio),n>1&&(n=1),e.initialZoomLevel=n,e.bounds||(e.bounds={center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0 }})}if(!n)return;return function(e,t,n){var o=e.bounds;o.center.x=Math.round((Ht.x-t)/2),o.center.y=Math.round((Ht.y-n)/2)+e.vGap.top,o.max.x=t>Ht.x?Math.round(Ht.x-t):o.center.x,o.max.y=n>Ht.y?Math.round(Ht.y-n)+e.vGap.top:o.center.y,o.min.x=t>Ht.x?0:o.center.x,o.min.y=n>Ht.y?e.vGap.top:o.center.y}(e,e.w*n,e.h*n),o&&n===e.initialZoomLevel&&(e.initialPosition=e.bounds.center),e.bounds}return e.w=e.h=0,e.initialZoomLevel=e.fitRatio=1,e.bounds={center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0 }},e.initialPosition=e.bounds.center,e.bounds},qt=function(e,t,n,o,i,r){t.loadError||o&&(t.imageAppended=!0,jt(t,o,t===a.currItem&&xe),n.appendChild(o),r&&setTimeout(function(){t&&t.loaded&&t.placeholder&&(t.placeholder.style.display="none",t.placeholder=null)},500))},Vt=function(e){e.loading=!0,e.loaded=!1;var t=e.img=i.createEl("pswp__img","img"),n=function(){e.loading=!1,e.loaded=!0,e.loadComplete?e.loadComplete(e):e.img=null,t.onload=t.onerror=null,t=null};return t.onload=n,t.onerror=function(){e.loadError=!0,n()},t.src=e.src,t},Xt=function(e,t){if(e.src&&e.loadError&&e.container)return t&&(e.container.innerHTML=""),e.container.innerHTML=r.errorMsg.replace("%url%",e.src),!0},jt=function(e,t,n){if(e.src){t||(t=e.container.lastChild);var o=n?e.w:Math.round(e.w*e.fitRatio),i=n?e.h:Math.round(e.h*e.fitRatio);e.placeholder&&!e.loaded&&(e.placeholder.style.width=o+"px",e.placeholder.style.height=i+"px"),t.style.width=o+"px",t.style.height=i+"px" }},$t=function(){if(Wt.length){for(var e,t=0;t<Wt.length;t++)(e=Wt[t]).holder.index===e.index&&qt(e.index,e.item,e.baseDiv,e.img,0,e.clearPlaceholder);Wt=[] }};be("Controller",{publicMethods:{lazyLoadItem:function(e){e=Ce(e);var t=Ut(e);t&&(!t.loaded&&!t.loading||b)&&(Ee("gettingData",e,t),t.src&&Vt(t))},initController:function(){i.extend(r,Gt,!0),a.items=Zt=n,Ut=a.getItemAt,Kt=r.getNumItemsFn,r.loop,Kt()<3&&(r.loop=!1),Ie("beforeChange",function(e){var t,n=r.preload,o=null===e||e>=0,i=Math.min(n[0],Kt()),l=Math.min(n[1],Kt());for(t=1;t<=(o?l:i);t++)a.lazyLoadItem(c+t);for(t=1;t<=(o?i:l);t++)a.lazyLoadItem(c-t)}),Ie("initialLayout",function(){a.currItem.initialLayout=r.getThumbBoundsFn&&r.getThumbBoundsFn(c)}),Ie("mainScrollAnimComplete",$t),Ie("initialZoomInEnd",$t),Ie("destroy",function(){for(var e,t=0;t<Zt.length;t++)(e=Zt[t]).container&&(e.container=null),e.placeholder&&(e.placeholder=null),e.img&&(e.img=null),e.preloader&&(e.preloader=null),e.loadError&&(e.loaded=e.loadError=!1);Wt=null})},getItemAt:function(e){return e>=0&&void 0!==Zt[e]&&Zt[e]},allowProgressiveImg:function(){return r.forceProgressiveLoading||!M||r.mouseUsed||screen.width>1200},setContent:function(e,t){r.loop&&(t=Ce(t));var n=a.getItemAt(e.index);n&&(n.container=null);var o,s=a.getItemAt(t);if(s){Ee("gettingData",t,s),e.index=t,e.item=s;var u=s.container=i.createEl("pswp__zoom-wrap");if(!s.src&&s.html&&(s.html.tagName?u.appendChild(s.html):u.innerHTML=s.html),Xt(s),Yt(s,pe),!s.src||s.loadError||s.loaded)s.src&&!s.loadError&&((o=i.createEl("pswp__img","img")).style.opacity=1,o.src=s.src,jt(s,o),qt(0,s,u,o));else{if(s.loadComplete=function(n){if(l){if(e&&e.index===t){if(Xt(n,!0))return n.loadComplete=n.img=null,Yt(n,pe),Me(n),void(e.index===c&&a.updateCurrZoomItem());n.imageAppended?!Nt&&n.placeholder&&(n.placeholder.style.display="none",n.placeholder=null):Z.transform&&(ne||Nt)?Wt.push({item:n,baseDiv:u,img:n.img,index:t,holder:e,clearPlaceholder:!0}):qt(0,n,u,n.img,0,!0)}n.loadComplete=null,n.img=null,Ee("imageLoadComplete",t,n) }},i.features.transform){var d="pswp__img pswp__img--placeholder";d+=s.msrc?"":" pswp__img--placeholder--blank";var p=i.createEl(d,s.msrc?"img":"");s.msrc&&(p.src=s.msrc),jt(s,p),u.appendChild(p),s.placeholder=p}s.loading||Vt(s),a.allowProgressiveImg()&&(!zt&&Z.transform?Wt.push({item:s,baseDiv:u,img:s.img,index:t,holder:e}):qt(0,s,u,s.img,0,!0))}zt||t!==c?Me(s):(te=u.style,Bt(s,o||s.img)),e.el.innerHTML="",e.el.appendChild(u)}else e.el.innerHTML=""},cleanSlide:function(e){e.img&&(e.img.onload=e.img.onerror=null),e.loaded=e.loading=e.img=e.imageAppended=!1 }}});var Qt,Jt,en={},tn=function(e,t,n){var o=document.createEvent("CustomEvent"),i={origEvent:e,target:e.target,releasePoint:t,pointerType:n||"touch"};o.initCustomEvent("pswpTap",!0,!0,i),e.target.dispatchEvent(o)};be("Tap",{publicMethods:{initTap:function(){Ie("firstTouchStart",a.onTapStart),Ie("touchRelease",a.onTapRelease),Ie("destroy",function(){en={},Qt=null})},onTapStart:function(e){e.length>1&&(clearTimeout(Qt),Qt=null)},onTapRelease:function(e,t){if(t&&!V&&!Y&&!Ye){var n=t;if(Qt&&(clearTimeout(Qt),Qt=null,function(e,t){return Math.abs(e.x-t.x)<25&&Math.abs(e.y-t.y)<25}(n,en)))return void Ee("doubleTap",n);if("mouse"===t.type)return void tn(e,t,"mouse");if("BUTTON"===e.target.tagName.toUpperCase()||i.hasClass(e.target,"pswp__single-tap"))return void tn(e,t);Re(en,n),Qt=setTimeout(function(){tn(e,t),Qt=null},300) }} }}),be("DesktopZoom",{publicMethods:{initDesktopZoom:function(){L||(M?Ie("mouseUsed",function(){a.setupDesktopZoom()}):a.setupDesktopZoom(!0))},setupDesktopZoom:function(t){Jt={};var n="wheel mousewheel DOMMouseScroll";Ie("bindEvents",function(){i.bind(e,n,a.handleMouseWheel)}),Ie("unbindEvents",function(){Jt&&i.unbind(e,n,a.handleMouseWheel)}),a.mouseZoomedIn=!1;var o,r=function(){a.mouseZoomedIn&&(i.removeClass(e,"pswp--zoomed-in"),a.mouseZoomedIn=!1),v<1?i.addClass(e,"pswp--zoom-allowed"):i.removeClass(e,"pswp--zoom-allowed"),l()},l=function(){o&&(i.removeClass(e,"pswp--dragging"),o=!1)};Ie("resize",r),Ie("afterChange",r),Ie("pointerDown",function(){a.mouseZoomedIn&&(o=!0,i.addClass(e,"pswp--dragging"))}),Ie("pointerUp",l),t||r()},handleMouseWheel:function(e){if(v<=a.currItem.fitRatio)return r.modal&&(!r.closeOnScroll||Ye||G?e.preventDefault():D&&Math.abs(e.deltaY)>2&&(u=!0,a.close())),!0;if(e.stopPropagation(),Jt.x=0,"deltaX"in e)1===e.deltaMode?(Jt.x=18*e.deltaX,Jt.y=18*e.deltaY):(Jt.x=e.deltaX,Jt.y=e.deltaY);else if("wheelDelta"in e)e.wheelDeltaX&&(Jt.x=-.16*e.wheelDeltaX),e.wheelDeltaY?Jt.y=-.16*e.wheelDeltaY:Jt.y=-.16*e.wheelDelta;else{if(!("detail"in e))return;Jt.y=e.detail}ze(v,!0);var t=de.x-Jt.x,n=de.y-Jt.y;(r.modal||t<=ee.min.x&&t>=ee.max.x&&n<=ee.min.y&&n>=ee.max.y)&&e.preventDefault(),a.panTo(t,n)},toggleDesktopZoom:function(t){t=t||{x:pe.x/2+fe.x,y:pe.y/2+fe.y};var n=r.getDoubleTapZoom(!0,a.currItem),o=v===n;a.mouseZoomedIn=!o,a.zoomTo(o?a.currItem.initialZoomLevel:n,t,333),i[(o?"remove":"add")+"Class"](e,"pswp--zoomed-in") }}});var nn,on,an,rn,ln,sn,un,cn,dn,pn,mn,fn,hn={history:!0,galleryUID:1},vn=function(){return mn.hash.substring(1)},gn=function(){nn&&clearTimeout(nn),an&&clearTimeout(an)},yn=function(){var e=vn(),t={};if(e.length<5)return t;var n,o=e.split("&");for(n=0;n<o.length;n++)if(o[n]){var i=o[n].split("=");i.length<2||(t[i[0]]=i[1])}if(r.galleryPIDs){var a=t.pid;for(t.pid=0,n=0;n<Zt.length;n++)if(Zt[n].pid===a){t.pid=n;break }}else t.pid=parseInt(t.pid,10)-1;return t.pid<0&&(t.pid=0),t},wn=function(){if(an&&clearTimeout(an),Ye||G)an=setTimeout(wn,500);else{rn?clearTimeout(on):rn=!0;var e=c+1,t=Ut(c);t.hasOwnProperty("pid")&&(e=t.pid);var n=un+"&gid="+r.galleryUID+"&pid="+e;cn||-1===mn.hash.indexOf(n)&&(pn=!0);var o=mn.href.split("#")[0]+"#"+n;fn?"#"+n!==window.location.hash&&history[cn?"replaceState":"pushState"]("",document.title,o):cn?mn.replace(o):mn.hash=n,cn=!0,on=setTimeout(function(){rn=!1},60) }};be("History",{publicMethods:{initHistory:function(){if(i.extend(r,hn,!0),r.history){mn=window.location,pn=!1,dn=!1,cn=!1,un=vn(),fn="pushState"in history,un.indexOf("gid=")>-1&&(un=(un=un.split("&gid=")[0]).split("?gid=")[0]),Ie("afterChange",a.updateURL),Ie("unbindEvents",function(){i.unbind(window,"hashchange",a.onHashChange)});var e=function(){sn=!0,dn||(pn?history.back():un?mn.hash=un:fn?history.pushState("",document.title,mn.pathname+mn.search):mn.hash=""),gn()};Ie("unbindEvents",function(){u&&e()}),Ie("destroy",function(){sn||e()}),Ie("firstUpdate",function(){c=yn().pid});var t=un.indexOf("pid=");t>-1&&"&"===(un=un.substring(0,t)).slice(-1)&&(un=un.slice(0,-1)),setTimeout(function(){l&&i.bind(window,"hashchange",a.onHashChange)},40) }},onHashChange:function(){return vn()===un?(dn=!0,void a.close()):void(rn||(ln=!0,a.goTo(yn().pid),ln=!1))},updateURL:function(){gn(),ln||(cn?nn=setTimeout(wn,800):wn()) }}}),i.extend(a,$e) }}),function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipeUI_Default=t()}(this,function(){"use strict";return function(e,t){var n,o,i,a,r,l,s,u,c,d,p,m,f,h,v,g,y,w,x=this,b=!1,C=!0,T=!0,I={barsSize:{top:44,bottom:"auto"},closeElClasses:["item","caption","zoom-wrap","ui","top-bar"],timeToIdle:4e3,timeToIdleOutside:1e3,loadingIndicatorDelay:1e3,addCaptionHTMLFn:function(e,t){return e.title?(t.children[0].innerHTML=e.title,!0):(t.children[0].innerHTML="",!1)},closeEl:!0,captionEl:!0,fullscreenEl:!0,zoomEl:!0,shareEl:!0,counterEl:!0,arrowEl:!0,preloaderEl:!0,tapToClose:!1,tapToToggleControls:!0,clickToCloseNonZoomable:!0,shareButtons:[{id:"facebook",label:"Share on Facebook",url:"https://www.facebook.com/sharer/sharer.php?u={{ url }}"},{id:"twitter",label:"Tweet",url:"https://twitter.com/intent/tweet?text={{ text }}&url={{ url }}"},{id:"pinterest",label:"Pin it",url:"http://www.pinterest.com/pin/create/button/?url={{ url }}&media={{ image_url }}&description={{ text }}"},{id:"download",label:"Download image",url:"{{ raw_image_url }}",download:!0}],getImageURLForShare:function(){return e.currItem.src||""},getPageURLForShare:function(){return window.location.href},getTextForShare:function(){return e.currItem.title||""},indexIndicatorSep:" / ",fitControlsWidth:1200},E=function(e){if(g)return!0;e=e||window.event,v.timeToIdle&&v.mouseUsed&&!c&&L();for(var n,o,i=(e.target||e.srcElement).getAttribute("class")||"",a=0;a<N.length;a++)(n=N[a]).onTap&&i.indexOf("pswp__"+n.name)>-1&&(n.onTap(),o=!0);if(o){e.stopPropagation&&e.stopPropagation(),g=!0;var r=t.features.isOldAndroid?600:30;setTimeout(function(){g=!1},r) }},_=function(){return!e.likelyTouchDevice||v.mouseUsed||screen.width>v.fitControlsWidth},S=function(e,n,o){t[(o?"add":"remove")+"Class"](e,"pswp__"+n)},D=function(){var e=1===v.getNumItemsFn();e!==h&&(S(o,"ui--one-slide",e),h=e)},k=function(){S(s,"share-modal--hidden",T)},M=function(){return(T=!T)?(t.removeClass(s,"pswp__share-modal--fade-in"),setTimeout(function(){T&&k()},300)):(k(),setTimeout(function(){T||t.addClass(s,"pswp__share-modal--fade-in")},30)),T||O(),!1},F=function(t){var n=(t=t||window.event).target||t.srcElement;return e.shout("shareLinkClick",t,n),!(!n.href||!n.hasAttribute("download")&&(window.open(n.href,"pswp_share","scrollbars=yes,resizable=yes,toolbar=no,location=yes,width=550,height=420,top=100,left="+(window.screen?Math.round(screen.width/2-275):100)),T||M(),1))},O=function(){for(var e,t,n,o,i="",a=0;a<v.shareButtons.length;a++)e=v.shareButtons[a],t=v.getImageURLForShare(e),n=v.getPageURLForShare(e),o=v.getTextForShare(e),i+='<a href="'+e.url.replace("{{ url }}",encodeURIComponent(n)).replace("{{ image_url }}",encodeURIComponent(t)).replace("{{ raw_image_url }}",t).replace("{{ text }}",encodeURIComponent(o))+'" target="_blank" class="pswp__share--'+e.id+'"'+(e.download?"download":"")+">"+e.label+"</a>",v.parseShareButtonOut&&(i=v.parseShareButtonOut(e,i));s.children[0].innerHTML=i,s.children[0].onclick=F},A=function(e){for(var n=0;n<v.closeElClasses.length;n++)if(t.hasClass(e,"pswp__"+v.closeElClasses[n]))return!0},R=0,L=function(){clearTimeout(w),R=0,c&&x.setIdle(!1)},P=function(e){var t=(e=e||window.event).relatedTarget||e.toElement;t&&"HTML"!==t.nodeName||(clearTimeout(w),w=setTimeout(function(){x.setIdle(!0)},v.timeToIdleOutside))},Z=function(e){m!==e&&(S(p,"preloader--active",!e),m=e)},z=function(e){var n=e.vGap;if(_()){var r=v.barsSize;if(v.captionEl&&"auto"===r.bottom)if(a||((a=t.createEl("pswp__caption pswp__caption--fake")).appendChild(t.createEl("pswp__caption__center")),o.insertBefore(a,i),t.addClass(o,"pswp__ui--fit")),v.addCaptionHTMLFn(e,a,!0)){var l=a.clientHeight;n.bottom=parseInt(l,10)||44}else n.bottom=r.top;else n.bottom="auto"===r.bottom?0:r.bottom;n.top=r.top}else n.top=n.bottom=0},N=[{name:"caption",option:"captionEl",onInit:function(e){i=e }},{name:"share-modal",option:"shareEl",onInit:function(e){s=e},onTap:function(){M() }},{name:"button--share",option:"shareEl",onInit:function(e){l=e},onTap:function(){M() }},{name:"button--zoom",option:"zoomEl",onTap:e.toggleDesktopZoom},{name:"counter",option:"counterEl",onInit:function(e){r=e }},{name:"button--close",option:"closeEl",onTap:e.close},{name:"button--arrow--left",option:"arrowEl",onTap:e.prev},{name:"button--arrow--right",option:"arrowEl",onTap:e.next},{name:"button--fs",option:"fullscreenEl",onTap:function(){n.isFullscreen()?n.exit():n.enter() }},{name:"preloader",option:"preloaderEl",onInit:function(e){p=e }}];x.init=function(){t.extend(e.options,I,!0),v=e.options,o=t.getChildByClass(e.scrollWrap,"pswp__ui"),d=e.listen,function(){var e;d("onVerticalDrag",function(e){C&&e<.95?x.hideControls():!C&&e>=.95&&x.showControls()}),d("onPinchClose",function(t){C&&t<.9?(x.hideControls(),e=!0):e&&!C&&t>.9&&x.showControls()}),d("zoomGestureEnded",function(){(e=!1)&&!C&&x.showControls()})}(),d("beforeChange",x.update),d("doubleTap",function(t){var n=e.currItem.initialZoomLevel;e.getZoomLevel()!==n?e.zoomTo(n,t,333):e.zoomTo(v.getDoubleTapZoom(!1,e.currItem),t,333)}),d("preventDragEvent",function(e,t,n){var o=e.target||e.srcElement;o&&o.getAttribute("class")&&e.type.indexOf("mouse")>-1&&(o.getAttribute("class").indexOf("__caption")>0||/(SMALL|STRONG|EM)/i.test(o.tagName))&&(n.prevent=!1)}),d("bindEvents",function(){t.bind(o,"pswpTap click",E),t.bind(e.scrollWrap,"pswpTap",x.onGlobalTap),e.likelyTouchDevice||t.bind(e.scrollWrap,"mouseover",x.onMouseOver)}),d("unbindEvents",function(){T||M(),y&&clearInterval(y),t.unbind(document,"mouseout",P),t.unbind(document,"mousemove",L),t.unbind(o,"pswpTap click",E),t.unbind(e.scrollWrap,"pswpTap",x.onGlobalTap),t.unbind(e.scrollWrap,"mouseover",x.onMouseOver),n&&(t.unbind(document,n.eventK,x.updateFullscreen),n.isFullscreen()&&(v.hideAnimationDuration=0,n.exit()),n=null)}),d("destroy",function(){v.captionEl&&(a&&o.removeChild(a),t.removeClass(i,"pswp__caption--empty")),s&&(s.children[0].onclick=null),t.removeClass(o,"pswp__ui--over-close"),t.addClass(o,"pswp__ui--hidden"),x.setIdle(!1)}),v.showAnimationDuration||t.removeClass(o,"pswp__ui--hidden"),d("initialZoomIn",function(){v.showAnimationDuration&&t.removeClass(o,"pswp__ui--hidden")}),d("initialZoomOut",function(){t.addClass(o,"pswp__ui--hidden")}),d("parseVerticalMargin",z),function(){var e,n,i,a=function(o){if(o)for(var a=o.length,r=0;r<a;r++){e=o[r],n=e.className;for(var l=0;l<N.length;l++)i=N[l],n.indexOf("pswp__"+i.name)>-1&&(v[i.option]?(t.removeClass(e,"pswp__element--disabled"),i.onInit&&i.onInit(e)):t.addClass(e,"pswp__element--disabled")) }};a(o.children);var r=t.getChildByClass(o,"pswp__top-bar");r&&a(r.children)}(),v.shareEl&&l&&s&&(T=!0),D(),v.timeToIdle&&d("mouseUsed",function(){t.bind(document,"mousemove",L),t.bind(document,"mouseout",P),y=setInterval(function(){2==++R&&x.setIdle(!0)},v.timeToIdle/2)}),v.fullscreenEl&&!t.features.isOldAndroid&&(n||(n=x.getFullscreenAPI()),n?(t.bind(document,n.eventK,x.updateFullscreen),x.updateFullscreen(),t.addClass(e.template,"pswp--supports-fs")):t.removeClass(e.template,"pswp--supports-fs")),v.preloaderEl&&(Z(!0),d("beforeChange",function(){clearTimeout(f),f=setTimeout(function(){e.currItem&&e.currItem.loading?(!e.allowProgressiveImg()||e.currItem.img&&!e.currItem.img.naturalWidth)&&Z(!1):Z(!0)},v.loadingIndicatorDelay)}),d("imageLoadComplete",function(t,n){e.currItem===n&&Z(!0)}))},x.setIdle=function(e){c=e,S(o,"ui--idle",e)},x.update=function(){C&&e.currItem?(x.updateIndexIndicator(),v.captionEl&&(v.addCaptionHTMLFn(e.currItem,i),S(i,"caption--empty",!e.currItem.title)),b=!0):b=!1,T||M(),D()},x.updateFullscreen=function(o){o&&setTimeout(function(){e.setScrollOffset(0,t.getScrollY())},50),t[(n.isFullscreen()?"add":"remove")+"Class"](e.template,"pswp--fs")},x.updateIndexIndicator=function(){v.counterEl&&(r.innerHTML=e.getCurrentIndex()+1+v.indexIndicatorSep+v.getNumItemsFn())},x.onGlobalTap=function(n){var o=(n=n||window.event).target||n.srcElement;if(!g)if(n.detail&&"mouse"===n.detail.pointerType){if(A(o))return void e.close();t.hasClass(o,"pswp__img")&&(1===e.getZoomLevel()&&e.getZoomLevel()<=e.currItem.fitRatio?v.clickToCloseNonZoomable&&e.close():e.toggleDesktopZoom(n.detail.releasePoint))}else if(v.tapToToggleControls&&(C?x.hideControls():x.showControls()),v.tapToClose&&(t.hasClass(o,"pswp__img")||A(o)))return void e.close()},x.onMouseOver=function(e){var t=(e=e||window.event).target||e.srcElement;S(o,"ui--over-close",A(t))},x.hideControls=function(){t.addClass(o,"pswp__ui--hidden"),C=!1},x.showControls=function(){C=!0,b||x.update(),t.removeClass(o,"pswp__ui--hidden")},x.supportsFullscreen=function(){var e=document;return!!(e.exitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen||e.msExitFullscreen)},x.getFullscreenAPI=function(){var t,n=document.documentElement,o="fullscreenchange";return n.requestFullscreen?t={enterK:"requestFullscreen",exitK:"exitFullscreen",elementK:"fullscreenElement",eventK:o}:n.mozRequestFullScreen?t={enterK:"mozRequestFullScreen",exitK:"mozCancelFullScreen",elementK:"mozFullScreenElement",eventK:"moz"+o}:n.webkitRequestFullscreen?t={enterK:"webkitRequestFullscreen",exitK:"webkitExitFullscreen",elementK:"webkitFullscreenElement",eventK:"webkit"+o}:n.msRequestFullscreen&&(t={enterK:"msRequestFullscreen",exitK:"msExitFullscreen",elementK:"msFullscreenElement",eventK:"MSFullscreenChange"}),t&&(t.enter=function(){return u=v.closeOnScroll,v.closeOnScroll=!1,"webkitRequestFullscreen"!==this.enterK?e.template[this.enterK]():void e.template[this.enterK](Element.ALLOW_KEYBOARD_INPUT)},t.exit=function(){return v.closeOnScroll=u,document[this.exitK]()},t.isFullscreen=function(){return document[this.elementK]}),t }}}),function(e){"use strict";var t=window.T4Sstrings,n=e("body"),o=e("#photoswipe_template").html(),i="pswp__thumbnails",a="pswp_thumb_active";function r(r){var l=r.items,s=l.length,u=r.index,c=r.galleryItems,d={closeEl:!0,captionEl:!0,fullscreenEl:r.fullscreenEl||!0,zoomEl:!0,shareEl:r.shareEl||!0,counterEl:r.counterEl||!0,arrowEl:!0,preloaderEl:!0,history:!1,maxSpreadZoom:r.maxSpreadZoom||2,showHideOpacity:!0,bgOpacity:1,index:r.index,tapToToggleControls:!0,shareButtons:[{id:"facebook",label:t.pswp_facebook,url:"https://www.facebook.com/sharer/sharer.php?u={{ url }}"},{id:"twitter",label:t.pswp_twitter,url:"https://twitter.com/intent/tweet?text={{ text }}&url={{ url }}"},{id:"pinterest",label:t.pswp_pinterest,url:"https://www.pinterest.com/pin/create/button/?url={{ url }}&media={{ image_url }}&description={{ text }}"}],getThumbBoundsFn:function(e){var t=c.find(r.parents).eq(e);r.global&&(t=c.find("a[data-index="+e+"]").parents(r.parents));var n=window.pageYOffset||document.documentElement.scrollTop,o=t[0].getElementsByTagName("img")[0].getBoundingClientRect();return{x:o.left,y:o.top+n,w:o.width }}};n.find(".pswp__t4s").remove(),T4SThemeSP.$appendComponent.after(o),isThemeRTL&&(u=s-u-1,l=l.reverse());var p=document.querySelectorAll(".pswp__t4s")[0],m=e("."+i),f=new PhotoSwipe(p,PhotoSwipeUI_Default,l,d);if(f.init(),m.empty(),f.listen("close",function(){if(c.hasClass("flickityt4s-enabled")){if(isThemeRTL)var e=s-f.getCurrentIndex();else e=f.getCurrentIndex();c.flickityt4s("selectCell",e,!1,!0) }}),r.HasThumb){if(s<=1)return;for(u=0;u<s;u++)m.append('<div class="pswp_thumb_item" data-index="'+(u+1)+'"><img loading="lazy" src="'+l[u].src+'" alt="pswp-thumb-img"></div>');m.find('.pswp_thumb_item[data-index="'+(f.getCurrentIndex()+1)+'"]').addClass(a),f.listen("beforeChange",function(){var e=f.getCurrentIndex()+1,t=m.find('.pswp_thumb_item[data-index="'+e+'"]');t.siblings().removeClass(a),t.addClass(a)}),f.listen("afterChange",function(){var t,n,o,i,r;t=e("."+a)[0],o=(n=m)[0],i=t.getBoundingClientRect(),r=o.getBoundingClientRect(),i.left+i.width>r.width?n.animate({scrollLeft:t.offsetLeft+i.width-r.width+10},200):t.offsetLeft<o.scrollLeft&&n.animate({scrollLeft:t.offsetLeft-10},200)}),m.find(".pswp_thumb_item").on("click",function(){var t=e(this).data("index");f.goTo(t-1)}) }}function l(){e(".js-youtube").each(function(){this.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}',"*")}),e(".js-vimeo").each(function(){this.contentWindow.postMessage('{"method":"pause"}',"*")}),e("video:not(.t4s_bg_vid_html5)").each(function(){this.pause()}),e("product-model").each(function(){this.modelViewerUI&&this.modelViewerUI.pause()})}T4SThemeSP.GalleryPhotoSwipe=function(){var t=!1;e("[data-t4s-gallery].flickityt4s-enabled").on("dragEnd.flickityt4s",function(e,n){t=!0}),e(document).on("click","[data-t4s-gallery--open]",function(o){o.preventDefault();var i=e(this),a=i.parents("[data-t4s-gallery--item]"),l=i.closest("[data-t4s-gallery]"),s=l.find("[data-pswp-src]");if(0==s.length||t)t=!1;else{var u=l.is("[data-t4s-thumb-true]");r({index:a.index(),items:n(s),HasThumb:u,galleryItems:l,parents:"[data-t4s-gallery--item]",global:!1,maxSpreadZoom:parseFloat(l.attr("data-maxSpreadZoom")),fullscreenEl:l.attr("data-fullscreenEl"),shareEl:l.attr("data-shareEl"),counterEl:l.attr("data-counterEl")}) }});var n=function(t){var n=[];return t.each(function(){n.push({src:e(this).attr("data-pswp-src"),w:e(this).attr("data-pswp-w"),h:e(this).attr("data-pswp-h")})}),n }},T4SThemeSP.videoPoster=function(){e("[data-video-poster-btn]").on("click",function(o){o.preventDefault();var i=e(this).closest("[data-video-poster]"),a=i.find("video, iframe");l(),n(i,e(this)),setTimeout(function(){i.addClass(t),a.focus()},50)});var t="t4s-postervideo-playing";e("[data-video-poster-close]").on("click",function(n){n.preventDefault(),l();var o=e(this).closest("[data-video-poster]");o.removeAttr("loaded").removeClass(t),o.find("video, iframe").remove()});var n=function(e,t){if(!e.is("[loaded]")){var n=e.find("[data-video-insert]").length?e.find("[data-video-insert]"):e,o='<iframe src="src_t4s" class="class_t4s" title="" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>',i=JSON.parse(t.attr("data-options")||"{}"),a=i.type,r=i.vid,l=i.src,s=i.autoplay,u=i.loop,c="vimeo";a=="youtube"?o=o.replace("src_t4s","//www.youtube.com/embed/"+r+"?enablejsapi=1&showinfo=0&controls=1&modestbranding=1&autoplay="+ +s+"&rel=0"+(u?"&playlist="+r+"&loop=1":"")).replace("class_t4s","js-youtube"):a==c?o=o.replace("src_t4s","//player.vimeo.com/video/"+r+"?&portrait=0&byline=0&color="+i.accent_color+"&autoplay="+ +s+"&loop="+ +u).replace("class_t4s","js-vimeo"):(o='<video src="src_t4s" preload="auto" controls data-autoplay data-loop playsinline></video>'.replace("src_t4s",l),s&&(o=o.replace("data-autoplay","autoplay")),u&&(o=o.replace("data-loop","loop"))),n.append(o),e.attr("data-type-video-inline","").attr("loaded",!0) }} }}(jQuery_T4NT),jQuery_T4NT(document).ready(function(e){T4SThemeSP.GalleryPhotoSwipe(),T4SThemeSP.videoPoster()});