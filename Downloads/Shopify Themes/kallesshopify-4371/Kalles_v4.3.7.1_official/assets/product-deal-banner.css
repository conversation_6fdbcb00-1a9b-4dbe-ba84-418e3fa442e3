.t4s-banner-item1 .text1 {
	font-size: 14px;
	color: var(--text-cl1);
	margin-bottom: 10px;
	font-weight: 500;
	line-height: 14px;
	letter-spacing: 1px;
}
.t4s-banner-item1 .text2 {
	font-size: 35px;
	color: var(--text-cl2);
	margin-bottom: 10px;
	font-weight: 600;
	line-height: 35px;
}
.t4s-banner-item1 .text3 {
	font-size: 14px;
	color: var(--text-cl3);
	margin-bottom: 0;
	font-weight: 400;
	line-height: 20px;
}
.t4s-banner-item2 .text1 {
	font-size: 18px;
	color: var(--text-cl1);
	margin-bottom: 10px;
	font-weight: 500;
	line-height: 18px;
	letter-spacing: 1px;
}
.t4s-banner-item2 .text2 {
	font-size: 50px;
	color: var(--text-cl2);
	margin-bottom: 10px;
	font-weight: 600;
	line-height: 50px;
}
.t4s-product-outer,
.t4s-product-outer .t4s-product .t4s-product-wrapper {
	position: relative;
	overflow: hidden;
}
.t4s-product-outer .t4s-product .t4s-product-wrapper::before {
	content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
    transition: .5s;
    opacity: 0;
    visibility: hidden;
}
.t4s-product-outer .t4s-product:hover .t4s-product-wrapper::before {
	opacity: 1;
	visibility: visible;
}
.t4s-product-outer .t4s-product .t4s-product-info {
	position: absolute;
	z-index: 2;
	bottom: 0;
	padding: 10px 0;
	left: 0;
	right: 0;
	opacity: 0;
	visibility: hidden;
	transition: 0.5s ease 0s;
    background: linear-gradient(0deg,rgba(0,0,0,.6) 0,rgba(0,0,0,0) 100%);
}
.t4s-product-outer .t4s-product:hover .t4s-product-info {
	opacity: 1;
	visibility: visible;
}
.t4s-product-outer .t4s-countdown {
	opacity: 1;
	visibility: visible;
	position: absolute;
	z-index: 2;
	bottom: 0; 
	left: 0;
	right: 0;
	transition: 0.1s ease 0s;
}
.t4s-product-outer:hover .t4s-countdown {
	opacity: 0;
	visibility: hidden;
}
.t4s-product-outer .t4s-product .t4s-product-title a,
.t4s-product-outer .t4s-product .t4s-product-price {
	color: var(--t4s-light-color);
}
@media(min-width: 1025px) {
	.t4s-product-outer .t4s-countdown .time{padding: 20px 10px;}
	.t4s-product-outer .t4s-product .t4s-product-info{padding: 10px 15px;}
}
@media(max-width: 1024px) {
	.t4s-product-outer .t4s-product .t4s-product-inner:before,
	.t4s-product-outer .t4s-product .t4s-product-wrapper:before {
		display: none;
	}
	.t4s-product-outer .t4s-product .t4s-product-info {
		position: static;
		background: transparent;
		opacity: 1;
		visibility: visible;
	}
	.t4s-product-outer:hover .t4s-countdown,
	.t4s-product-outer .t4s-countdown {
		opacity: 1;
		visibility: visible;
		position: static;
	}
	.t4s-product-outer .t4s-product .t4s-product-title a,
	 .t4s-product-outer .t4s-product .t4s-product-price {
	 	color: var(--t4s-dark-color);
	 }
	 .t4s-product-deal-banner .t4s-product .t4s-product-countdown{
		font-size: 12px;padding:10px;
	}
}