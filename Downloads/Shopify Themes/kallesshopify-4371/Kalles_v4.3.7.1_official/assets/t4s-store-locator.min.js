!function(){const e=JSON.parse(document.querySelector("[data-store-locator-options]").getAttribute("data-store-locator-options"));if(mapboxgl.accessToken=atob(e.accessToken),document.addEventListener("shopify:section:load",e=>{if(![...e.target.classList].includes("t4s-main-store-locator"))return;const t=document.querySelector("[id^=t4s-store-locator-js]");if(t&&t){const e=document.createElement("script");e.src=t.src,t.parentNode.replaceChild(e,t) }}),"not4s"==mapboxgl.accessToken)return;const t=JSON.parse(document.getElementById(`stores-json${e.sid}`).textContent),o=new mapboxgl.Map({container:`map${e.sid}`,style:{streets:"mapbox://styles/mapbox/streets-v12",outdoors:"mapbox://styles/mapbox/outdoors-v12",light:"mapbox://styles/mapbox/light-v11",dark:"mapbox://styles/mapbox/dark-v11",satellite_streets:"mapbox://styles/mapbox/satellite-streets-v12"}[e.style],center:t.features[0].geometry.coordinates,zoom:e.zoom,scrollZoom:e.scrollZoom,attributionControl:!1,pitch:e.pitch,bearing:e.bearing});if(o.addControl(new mapboxgl.NavigationControl),e.enableSearchBox){const e=new MapboxGeocoder({accessToken:mapboxgl.accessToken,mapboxgl:mapboxgl,marker:!0,render:function(e){var t=e.place_name.split(",");return'<div class="mapboxgl-ctrl-geocoder--suggestion"><div class="mapboxgl-ctrl-geocoder--suggestion-title needsclick">'+t[0]+'</div><div class="mapboxgl-ctrl-geocoder--suggestion-address needsclick">'+t.splice(1,t.length).join(",")+"</div></div>" }});o.addControl(e,"top-left")}t.features.forEach((e,t)=>{e.properties.id=t}),o.on("idle",()=>{o.setFog({}),o.resize()}),o.on("load",()=>{o.addSource("places",{type:"geojson",data:t}),function(t){const o=document.getElementById(`listings${e.sid}`);o.innerHTML="";for(const e of t.features){const s=o.appendChild(document.createElement("div")),{name:r,content:c}=e.properties;s.id=`t4s-listing-${e.properties.id}`,s.className="t4s-store-locator__item";const i=s.appendChild(document.createElement("a"));i.href="#",i.className="t4s-store-locator__title needsclick",i.id=`t4s-link-${e.properties.id}`,i.innerHTML=`${r}`;const l=s.appendChild(document.createElement("div"));l.className="t4s-rte",l.innerHTML=c,s.addEventListener("click",function(e){e.preventDefault();for(const e of t.features)this.id===`t4s-listing-${e.properties.id}`&&(n(e),a(e));const s=o.getElementsByClassName("is--active");s[0]&&s[0].classList.remove("is--active"),this.classList.add("is--active")}) }}(t),function(){for(const e of t.features){const t=document.createElement("div");t.id=`t4s-marker-${e.properties.id}`,t.className="t4s-store-locator__marker",s||(t.innerHTML='<svg class="t4s-icon t4s-icon--store-locator" aria-hidden="true" focusable="false" role="presentation"><use href="#icon--store-locator-marker"></use></svg>'),new mapboxgl.Marker(t,{offset:[0,-23]}).setLngLat(e.geometry.coordinates).addTo(o),t.addEventListener("click",t=>{n(e),a(e);const o=document.getElementsByClassName("is--active");t.stopPropagation(),o[0]&&o[0].classList.remove("is--active");const s=document.getElementById(`t4s-listing-${e.properties.id}`);s.classList.add("is--active")}) }}()});const s=e.isImgMarker;function n(e){o.flyTo({center:e.geometry.coordinates,zoom:15})}function a(e){const t=document.getElementsByClassName("mapboxgl-popup");t[0]&&t[0].remove();const{name:s,content:n}=e.properties,a=new mapboxgl.Popup({closeOnClick:!1}).setLngLat(e.geometry.coordinates).setHTML(`<h3>${s}</h3>${n}`).addTo(o);a._container.querySelector(".mapboxgl-popup-content p").classList.add("t4s-rte"),window.width<768&&setTimeout(()=>{a._container.scrollIntoView({block:"end",behavior:"smooth"})},100) }}();