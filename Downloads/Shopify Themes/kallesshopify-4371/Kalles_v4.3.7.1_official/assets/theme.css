/*
Theme Name: <PERSON>lles 
Author: The4Studio
Author URI: https://the4.co/
Version: 1.0
/* -------------------------------------------
[Table of contents]
1. SWATCH COLORS CSS
2. TOOLTIPS
3. ANIMATE CSS
4. MAGNIFIC POPUP CSS
5. T4s Modal CSS
6. PHOTOSWIPE
7. SCROLLBAR CSS
8. LOADING CSS
9. PREVENT SCROLLING CSS
10. COLORS CSS
11. SWATCH LIMIT
12. CLOSE OVERLAY
12A. LOCK SCROLL
12B. HEADER
15. DRIFT ZOOM
16. MEGA MENU
17. CAROUSEL NAV
18. SHOPIFY MODEL
19. NOTICES
20. OTHER CSS
21. PRODUCT ITEM HOVER

------------------------------------------- */

/* ==========================================================================
   1. SWATCH COLORS CSS
   ========================================================================== */
.t4sfrm-remove-vsoldout .t4s-soldout,
.t4sp-remove-unavail-true .t4s-unavailable {
    display: none !important;
}
.is-t4sOptioNname .t4s-soldout {
    border: dashed 0.5px rgb(119 119 119 / 20%);
}
.is-t4sOptioNname .t4s-soldout>span {
    text-decoration: line-through;
    opacity: .5;
}
.t4s-style-color .t4s-swatch.is-t4scolor .t4s-swatch-item {
    position: relative;
    overflow: hidden;
}
.t4s-style-color .is-t4scolor .t4s-soldout {
    border: dashed 0.5px rgb(119 119 119 / 20%);
}
.t4s-style-color .is-t4scolor .t4s-soldout:before, .t4s-style-color .is-t4scolor .t4s-soldout:after {
    content: " ";
    position: absolute;
    left: auto;
    right: auto;
    top: 50%;
    width: 100%;
    height: .5px;
    display: inline-block;
    background-color: #222;
     z-index: 2;
    transform: rotate(45deg);
    transition: background-color .2s ease-in-out,transform .2s ease-in-out,width .2s ease-in-out; 
}
.t4s-style-color .is-t4scolor .t4s-soldout:after {
    transform: rotate(-45deg);
}
/* Swatch end */

/* ==========================================================================
   2. TOOLTIPS
   ========================================================================== */
.t4s-tooltip {
  position: absolute;
  /* z-index: 1070; */
  z-index: 2022;
  opacity: 0;
  visibility: hidden;
  will-change: transform, opacity, visibility;    
}
.t4s-modal-opened .t4s-tooltip {
  z-index: 99999;
}
.t4s-tooltip.is--show {
  visibility: visible;
  opacity: 1;
  animation: t4s-ani-fadeIn .4s ease;
}
.t4s-tooltip .t4s-tt-arrow {
  position: absolute;
  background-color: var(--t4s-tooltip-background);
  width: 8px;
  height: 8px;
  transform: rotate(45deg);
}

.t4s-tooltip:not(.t4scss-tooltip) {
    pointer-events: none
}

.t4s-tooltip-inner {
    font-weight: 400;
    font-size: 12px;
    line-height: 12px;
    padding: 8px 10px;
    max-width: 250px;
    background-color: var(--t4s-tooltip-background);
    color: var(--t4s-tooltip-color);
    word-break: break-word;
    border-radius: 0
}

/* .t4s-tooltip.bs-tooltip-top {
    margin-top: -3px;
    padding: 5px 0
} */

/* .t4s-tooltip.bs-tooltip-top .t4s-tt-arrow {
    bottom: 0;
    left: 50%;
    border-width: 5px 5px 0;
    border-top-color: rgba(0,0,0,.9)
} */

/* .t4s-tooltip.bs-tooltip-bottom {
    margin-bottom: -3px;
    padding: 5px 0
} */

/* .t4s-tooltip.bs-tooltip-bottom .t4s-tt-arrow {
    top: 0;
    left: 50%;
    border-width: 0 5px 5px;
    border-bottom-color: rgba(0,0,0,.9)
} */

/* .t4s-tooltip.bs-tooltip-left {
    margin-left: -3px
} */

/* .t4s-tooltip.bs-tooltip-left .t4s-tt-arrow {
    top: 50%;
    right: 0;
    border-width: 5px 0 5px 5px;
    border-left-color: rgba(0,0,0,.9)
} */

/* .t4s-tooltip.bs-tooltip-right {
    margin-left: 3px
} */

/* .t4s-tooltip.bs-tooltip-right .t4s-tt-arrow {
    top: 50%;
    left: 0;
    border-width: 5px 5px 5px 0;
    border-right-color: rgba(0,0,0,.9)
} */
/* Tooltip end */

/* ==========================================================================
   3. ANIMATE CSS
   ========================================================================== */
    @keyframes t4s-ani-rotate {
        100% {
            transform: rotate(360deg)
        }
    }

    @keyframes t4s-ani-fadeIn {
        from {
            opacity: 0
        }

        to {
            opacity: 1
        }
    }

    @keyframes t4s-ani-fadeOut {
        from {
            opacity: 1
        }

        to {
            opacity: 0
        }
    }

    @keyframes t4s-ani-fadeInBottomShort {
        from {
            transform: translate3d(0, 20px, 0)
        }

        to {
            transform: none
        }
    }

    @keyframes t4s-ani-ShakePrev {
        25% {
            transform: translate3d(0px, 0, 0)
        }

        50% {
            transform: translate3d(3px, 0, 0)
        }

        75% {
            transform: translate3d(-3px, 0, 0)
        }
    }

    @keyframes t4s-ani-ShakeNext {
        25% {
            transform: translate3d(0px, 0, 0)
        }

        50% {
            transform: translate3d(-3px, 0, 0)
        }

        75% {
            transform: translate3d(3px, 0, 0)
        }
    }

    @keyframes t4s-ani-FadeInLoader {
        from {
            opacity: 0
        }

        to {
            opacity: .8
        }
    }

    @keyframes t4s-ani-SlideFromTop {
        from {
            transform: translateY(-150px)
        }

        to {
            transform: none
        }
    }

    @keyframes t4s-ani-SlideFromBottom {
        from {
            transform: translateY(150px)
        }

        to {
            transform: none
        }
    }

    @keyframes t4s-ani-SlideFromLeft {
        from {
            transform: translateX(-150px)
        }

        to {
            transform: none
        }
    }

    @keyframes t4s-ani-SlideFromRight {
        from {
            transform: translateX(150px)
        }

        to {
            transform: none
        }
    }

    @keyframes t4s-ani-RightFlipY {
        from {
            transform: translateX(100px) rotate3d(0, 1, 0, 90deg) scale(0.6)
        }

        to {
            transform: perspective(1000px) translateY(0px) rotate3d(0, 1, 0, 0deg) scale(1)
        }
    }

    @keyframes t4s-ani-LeftFlipY {
        from {
            transform: translateX(-100px) rotate3d(0, 1, 0, -90deg) scale(0.6)
        }

        to {
            transform: perspective(1000px) translateY(0px) rotate3d(0, 1, 0, 0deg) scale(1)
        }
    }

    @keyframes t4s-ani-TopFlipX {
        from {
            transform: translateY(-100px) rotate3d(1, 0, 0, 90deg) scale(0.6)
        }

        to {
            transform: perspective(1000px) translateY(0) rotate3d(1, 0, 0, 0deg) scale(1)
        }
    }

    @keyframes t4s-ani-BottomFlipX {
        from {
            transform: translateY(100px) rotate3d(1, 0, 0, 90deg) scale(0.6)
        }

        to {
            transform: perspective(1000px) translateY(0) rotate3d(1, 0, 0, 0deg) scale(1)
        }
    }

    @keyframes t4s-ani-ZoomIn {
        from {
            transform: scale(0.7)
        }

        to {
            transform: none
        }
    }

    @keyframes t4s-ani-RotateZ {
        from {
            transform: matrix3d(0.70592, 0.02465, 0.37557, -0.00062, -0.06052, 0.79532, 0.06156, -0.0001, -0.46435, -0.10342, 0.87958, -0.00146, -21.42566, 4.13698, 4.81749, 0.99197085)
        }

        to {
            transform: none
        }
    }
   @-webkit-keyframes t4s-icon-pop {
     0% {
       -webkit-transform: scale(1);
               transform: scale(1);
     }
     40% {
       top: -3px; 
       -webkit-transform: scale(1.15) rotate(-5deg); 
               transform: scale(1.15) rotate(-5deg);
     }
     100% {
       top: 0; 
       -webkit-transform: scale(1) rotate(0); 
               transform: scale(1) rotate(0);
     }
   }

   @keyframes t4s-icon-pop {
     0% {
       -webkit-transform: scale(1);
               transform: scale(1);
     }
     40% {
       top: -3px; 
       -webkit-transform: scale(1.15) rotate(-5deg); 
               transform: scale(1.15) rotate(-5deg);
     }
     100% {
       top: 0; 
       -webkit-transform: scale(1) rotate(0); 
               transform: scale(1) rotate(0);
     }
   }
   .t4s-site-nav__icons.is--hover2 .t4s-site-nav__icon svg.t4s-icon {
      transition: 0.35s cubic-bezier(0.25, 0.1, 0.25, 1);
   }
   .t4s-site-nav__icons.is--hover2 .t4s-site-nav__icon:hover svg.t4s-icon {
      transform: scale(1.15);
   }
   .t4s-site-nav__icons.is--hover3 .t4s-site-nav__icon:hover svg.t4s-icon {
/*        -webkit-animation: t4s-icon-pop 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
       animation: t4s-icon-pop 0.6s cubic-bezier(0.25, 0.1, 0.25, 1); */
       -webkit-animation: t4s-icon-pop 0.6s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
       animation: t4s-icon-pop 0.6s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
       -webkit-animation-fill-mode: both;
       animation-fill-mode: both;
   }

/* ==========================================================================
   4. MAGNIFIC POPUP CSS
   ========================================================================== */
    .mfp-bg,.t4sp-theme .mfp-bg {
        position: fixed;
        z-index: 1102 !important;
        overflow: hidden;
        background-color: rgba(0,0,0,0.7)
    }

    .global-color-scheme-light .mfp-bg {
        background-color: rgba(0,0,0,0.8)
    }

    .mfp-wrap,.t4sp-theme .mfp-wrap {
        position: fixed;
        z-index: 1103 !important;
        outline: none !important;
        -webkit-backface-visibility: hidden
    }

    .mfp-container {
        position: absolute;
        padding: 0 8px;
        text-align: center;
        overflow: auto;
    }

    .mfp-container:before {
        content: '';
        display: inline-block;
        height: 100%;
        vertical-align: middle
    }

    .mfp-content {
        position: relative;
        z-index: 1105;
        display: inline-block;
        margin: 0 auto;
        vertical-align: middle;
        text-align: left
    }

    .mfp-inline-holder .mfp-content {
        width: 100%;
        cursor: auto
    }

    .mfp-close,.mfp-arrow,.mfp-preloader,.mfp-counter {
        user-select: none
    }

    .mfp-preloader {
        position: absolute;
        top: 50%;
        right: 8px;
        left: 8px;
        z-index: 1104;
        margin-top: -.8em;
        width: auto;
        color: #CCC;
        text-align: center
    }

    .mfp-preloader a {
        color: #CCC
    }

    .mfp-preloader a:hover {
        color: #FFF
    }

    .mfp-s-ready .mfp-preloader,.mfp-s-error .mfp-content {
        display: none
    }

    .mfp-iframe-holder,.mfp-video-holder {
        padding-top: 40px;
        padding-bottom: 40px
    }

    .mfp-iframe-holder .mfp-content,.mfp-video-holder .mfp-content {
        max-width: 900px;
        width: 100%;
        line-height: 0
    }

    .mfp-iframe-scaler,.mfp-video-scaler {
        overflow: hidden;
        padding-top: 56.25%;
        width: 100%;
        height: 0
    }

    .mfp-iframe-scaler iframe,.mfp-video-scaler video {
        position: absolute;
        display: block;
        background: #000
    }

    .t4s-mfp-popup {
        margin: 25px auto;
        padding: 30px;
        width: 100%;
        background-color: var(--t4s-light-color)
    }

    [class*="color-scheme-light"] .t4s-mfp-popup {
        background-color: #101010
    }

    .mfp-move-horizontal .mfp-with-anim {
        opacity: 0;
        transition: transform .3s ease, opacity .3s ease;
        transform: translateX(-100px)
    }

    .mfp-move-horizontal .mfp-with-anim .summary-inner {
        opacity: 0;
        transition: transform .3s ease, opacity .3s ease;
        transform: translateY(30px)
    }

    .mfp-move-horizontal.mfp-bg {
        opacity: 0;
        transition: opacity .25s ease, visibility 0s ease .25s
    }

    .mfp-move-horizontal.mfp-ready .mfp-with-anim,.mfp-move-horizontal.mfp-ready .mfp-with-anim .summary-inner {
        opacity: 1;
        transform: none
    }

    .mfp-move-horizontal.mfp-ready.mfp-bg {
        opacity: 1;
        transition: opacity .25s ease, visibility 0s ease
    }

    .mfp-move-horizontal.mfp-removing .mfp-with-anim {
        opacity: 0;
        transform: translateX(100px)
    }

    .mfp-move-horizontal.mfp-removing.mfp-bg {
        opacity: 0
    }

    .mfp-move-horizontal.mfp-removing .mfp-close {
        display: none
    }

    .mfp-bg,.mfp-wrap,.mfp-container,.mfp-iframe-scaler iframe,.mfp-video-scaler video {
        top: 0;
        left: 0;
        width: 100%;
        height: 100%
    }

    button.mfp-close {
        position: fixed;
        top: 0;
        right: 0;
        bottom: auto;
        left: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1104;
        padding: 0;
        width: 50px;
        height: 50px;
        background: none;
        color: #FFF;
        text-align: center;
        font-weight: 400;
        font-size: 0;
        opacity: 1;
        cursor: pointer;
        transition: all .25s ease;
        touch-action: manipulation;
        opacity: 0;
        transition: all .25s ease, opacity 0s ease 0s
    }
    button.mfp-close .t4smfp-icon-close,.t4s-iconsvg-close {
        width: 16px;
        height: 16px;
        stroke-width: 1.5px;
        display: inline-block;
        pointer-events: none;
    }
    button.mfp-close:before,button.mfp-close:after {
        display: none !important;
    }

    button.mfp-close:hover {
        background-color: rgba(0,0,0,0.3);
        box-shadow: none;
        color: #FFF;
        opacity: 1
    }

    .mfp-ready button.mfp-close {
        opacity: 1;
        transition: all .25s ease, opacity .3s ease .35s
    }
    .t4s-loading-spin {
        position: absolute;
        top: 50%;
        margin-top: -9px;
        display: inline-block;
        width: 18px;
        height: 18px;
        border: 1px solid rgba(255,255,255,0.3);
        border-left-color: #fff;
        border-radius: 50%;
        vertical-align: middle;
        animation: t4s-ani-rotate 450ms infinite linear;
    }
    .t4s-spin-centered {
        left: 50%;
        margin-left: -9px;
    }
    .t4s-spin-medium {
        border-width: 2px;
        width: 35px;
        height: 35px;
        margin-top: -17.5px;
    }
    .t4s-spin-centered.t4s-spin-medium {
        margin-left: -17.5px;
    }
    .mfp-ajax-holder .mfp-content,.mfp-inline-holder .mfp-content {
       width: 100%;
       cursor: auto
    }
    .mfp-ajax-cur {
      cursor: progress
    }
    .t4s-modal-opened.t4sp-theme .mfp-bg {
     z-index: 99992!important;
    }
    .t4s-modal-opened.t4sp-theme .mfp-wrap,
    .t4s-modal-opened.t4sp-theme .pswp__t4s {
     z-index: 99993!important;
    }
   .t4s-mfp-btn-close-inline { position: relative }
   .t4s-mfp-btn-close-inline button.mfp-close {
      position: absolute;
      width: auto;
      top: -15px;
      right: -15px;
      padding: 15px;
      height: auto;
      background-color: #000;
   }
   .t4s-mfp-btn-close-inline button.mfp-close:hover { opacity: .8; }

/* ==========================================================================
   5. T4s Modal CSS
   ========================================================================== */
    .t4s-modal {
        display: none;
        bottom: 0;
        left: 0;
        opacity: 1;
        overflow: hidden;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 9999;
        color: var(--text-color);
        align-items: center;
        justify-content: center;
    }
    .t4s-modal--is-active {
        display: flex!important;
        overflow: hidden;
    }
    .t4s-modal-opened,
    html.js.is-opening-mfp,
    html.js.t4s-modal-opened{
      overflow: hidden !important;
    }
    .t4s-modal-opened .t4s-modal:before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #e0e0e0;
        animation: overlay-on .3s forwards;
        cursor: pointer;
    }
    .t4s-modal__inner {
      background-color: #fff;
      width:100%;
      max-width: 950px;
      margin: 15px;
      box-shadow: 0 12px 25px rgb(0 0 0 / 15%);
    }
    .t4s-modal__content {
        position: relative;
        max-width: 100%; 
        max-height: 80vh;
        overflow: auto;
       -webkit-overflow-scrolling: touch;
    }
    .t4s-modal-opened .t4s-modal .t4s-modal__inner {
      animation:t4s-modal-opening 0.3s forwards;
    }

    .t4s-modal-opened .t4s-modal:before{
      content:"";
      position:fixed;
      top:0;
      left:0;
      width:100%;
      height:100%;
      background-color:#e0e0e0;
      opacity: .6 !important;
      animation: none !important;
      /* animation:t4s-overlay-on 0.3s forwards; */
      cursor:pointer;
    }

    .t4s-modal-closing .t4s-modal .t4s-modal__inner {
      animation:t4s-modal-closing 0.15s forwards;
    }

    .t4s-modal-closing .t4s-modal:before {
      content:"";
      position:fixed;
      top:0;
      left:0;
      width:100%;
      height:100%;
      background-color:#e0e0e0;
      animation:t4s-overlay-off 0.15s forwards;
    }

    .t4s-modal--is-closing {
      display:flex !important;
      overflow:hidden;
    }
    .t4s-modal-close {
        border: 0;
        padding: 6px;
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(25%,-25%);
        z-index: 2;
        transition: transform .15s ease-out;
        width: 40px;
        height: 40px;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .t4s-modal-close .t4s-modal-icon-close {
        width: 16px;
        height: 16px;
        stroke-width: 1.5px;
        display: inline-block;
        pointer-events: none;
    }
    .rtl_true .t4s-modal-close {
       left: 0;
       right: auto;
       transform: translate(-25%,-25%);
    }
    @keyframes t4s-modal-opening {
      0%{
        opacity:0;
        transform:scale(0.97);
      }
      to{
        opacity:1;
        transform:scale(1);
      }
    }

    @keyframes t4s-modal-closing{
      0%{
        opacity:1;
        transform:scale(1);
      }
      to{
        opacity:0;
        transform:scale(0.97);
      }
    }
    @keyframes t4s-overlay-on{
      0%{ opacity:0; }
      to{ opacity:0.6; }
    }

    @keyframes t4s-overlay-off{
      0%{ opacity:0.6; }
      to{ opacity:0; }
    }
    @media (min-width: 769px) {
        .t4s-modal__content {
            max-height: 90vh;    
        }
    }
    html.js {
        overflow: visible !important;
    }
    @media (min-width: 768px) {
      html.is-opening-mfp {
          overflow: visible !important;
      }
      html.is-opening-mfp body {
          overflow: hidden !important;
      }
      html.is-opening-mfp .mfp-wrap {
          overflow: hidden auto !important;
      }
    }

/* ==========================================================================
   6. PHOTOSWIPE
   ========================================================================== */
    /*! PhotoSwipe main CSS by Dmitry Semenov | photoswipe.com | MIT license */
    /*
        Styles for basic PhotoSwipe functionality (sliding area, open/close transitions)
    */
    /* pswp = photoswipe */
    .pswp {
      display: none;
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      overflow: hidden;
      -ms-touch-action: none;
      touch-action: none;
      z-index: 1500;
      -webkit-text-size-adjust: 100%;
      /* create separate layer, to avoid paint on window.onscroll in webkit/blink */
      -webkit-backface-visibility: hidden;
      outline: none; }
      .pswp * {
        -webkit-box-sizing: border-box;
                box-sizing: border-box; }
      .pswp img {
        max-width: none; }

    /* style is added when JS option showHideOpacity is set to true */
    .pswp--animate_opacity {
      /* 0.001, because opacity:0 doesn't trigger Paint action, which causes lag at start of transition */
      opacity: 0.001;
      will-change: opacity;
      /* for open/close transition */
      -webkit-transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
              transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1); }

    .pswp--open {
      display: block; }

    .pswp--zoom-allowed .pswp__img {
      /* autoprefixer: off */
      cursor: -webkit-zoom-in;
      cursor: -moz-zoom-in;
      cursor: zoom-in; }

    .pswp--zoomed-in .pswp__img {
      /* autoprefixer: off */
      cursor: -webkit-grab;
      cursor: -moz-grab;
      cursor: grab; }

    .pswp--dragging .pswp__img {
      /* autoprefixer: off */
      cursor: -webkit-grabbing;
      cursor: -moz-grabbing;
      cursor: grabbing; }

    /*
        Background is added as a separate element.
        As animating opacity is much faster than animating rgba() background-color.
    */
    .pswp__bg {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: #000;
      opacity: 0;
      -webkit-transform: translateZ(0);
              transform: translateZ(0);
      -webkit-backface-visibility: hidden;
      will-change: opacity; }

    .pswp__scroll-wrap {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: hidden; }

    .pswp__container,
    .pswp__zoom-wrap {
      -ms-touch-action: none;
      touch-action: none;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0; }

    /* Prevent selection and tap highlights */
    .pswp__container,
    .pswp__img {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none; }

    .pswp__zoom-wrap {
      position: absolute;
      width: 100%;
      -webkit-transform-origin: left top;
      -ms-transform-origin: left top;
      transform-origin: left top;
      /* for open/close transition */
      -webkit-transition: -webkit-transform 333ms cubic-bezier(0.4, 0, 0.22, 1);
              transition: transform 333ms cubic-bezier(0.4, 0, 0.22, 1); }

    .pswp__bg {
      will-change: opacity;
      /* for open/close transition */
      -webkit-transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
              transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1); }

    .pswp--animated-in .pswp__bg,
    .pswp--animated-in .pswp__zoom-wrap {
      -webkit-transition: none;
      transition: none; }

    .pswp__container,
    .pswp__zoom-wrap {
      -webkit-backface-visibility: hidden; }

    .pswp__item {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      overflow: hidden; }

    .pswp__img {
      position: absolute;
      width: auto;
      height: auto;
      top: 0;
      left: 0; }

    /*
        stretched thumbnail or div placeholder element (see below)
        style is added to avoid flickering in webkit/blink when layers overlap
    */
    .pswp__img--placeholder {
      -webkit-backface-visibility: hidden; }

    /*
        div element that matches size of large image
        large image loads on top of it
    */
    .pswp__img--placeholder--blank {
      background: #222; }

    .pswp--ie .pswp__img {
      width: 100% !important;
      height: auto !important;
      left: 0;
      top: 0; }

    /*
        Error message appears when image is not loaded
        (JS option errorMsg controls markup)
    */
    .pswp__error-msg {
      position: absolute;
      left: 0;
      top: 50%;
      width: 100%;
      text-align: center;
      font-size: 14px;
      line-height: 16px;
      margin-top: -8px;
      color: #CCC; }

    .pswp__error-msg a {
      color: #CCC;
      text-decoration: underline; }

    /*! PhotoSwipe Default UI CSS by Dmitry Semenov | photoswipe.com | MIT license */
    /*

        Contents:

        1. Buttons
        2. Share modal and links
        3. Index indicator ("1 of X" counter)
        4. Caption
        5. Loading indicator
        6. Additional styles (root element, top bar, idle state, hidden state, etc.)

    */
    /*
        
        1. Buttons

     */
    /* <button> css reset */
    .pswp__button {
      width: 44px;
      height: 44px;
      position: relative;
      background: none;
      cursor: pointer;
      overflow: visible;
      -webkit-appearance: none;
      display: block;
      border: 0 !important;
      padding: 0 !important;
      margin: 0 !important;
      float: right;
      opacity: 0.75;
      -webkit-transition: opacity 0.2s;
              transition: opacity 0.2s;
      -webkit-box-shadow: none;
              box-shadow: none;
          background-color: transparent !important;;
     }
      .pswp__button:focus, .pswp__button:hover {
        opacity: 1; }
      .pswp__button:active {
        outline: none;
        opacity: 0.9; }
      .pswp__button::-moz-focus-inner {
        padding: 0;
        border: 0; }

    /* pswp__ui--over-close class it added when mouse is over element that should close gallery */
    .pswp__ui--over-close .pswp__button--close {
      opacity: 1; }

    .pswp__button,
    .pswp__button--arrow--left:before,
    .pswp__button--arrow--right:before {
      background: url(default-skin.png?v=1904) 0 0 no-repeat;
      background-size: 264px 88px;
      width: 44px;
      height: 44px; }

    @media (-webkit-min-device-pixel-ratio: 1.1), (-webkit-min-device-pixel-ratio: 1.09375), (min-resolution: 105dpi), (min-resolution: 1.1dppx) {
      /* Serve SVG sprite if browser supports SVG and resolution is more than 105dpi */
      .pswp--svg .pswp__button,
      .pswp--svg .pswp__button--arrow--left:before,
      .pswp--svg .pswp__button--arrow--right:before {
        background-image: url(default-skin.svg?v=1904); }
      .pswp--svg .pswp__button--arrow--left,
      .pswp--svg .pswp__button--arrow--right {
        background: none; } }

    .pswp__button--close {
      background-position: 0 -44px; }

    .pswp__button--share {
      background-position: -44px -44px; }

    .pswp__button--fs {
      display: none; }

    .pswp--supports-fs .pswp__button--fs {
      display: block; }

    .pswp--fs .pswp__button--fs {
      background-position: -44px 0; }

    .pswp__button--zoom {
      display: none;
      background-position: -88px 0; }

    .pswp--zoom-allowed .pswp__button--zoom {
      display: block; }

    .pswp--zoomed-in .pswp__button--zoom {
      background-position: -132px 0; }

    /* no arrows on touch screens */
    .pswp--touch .pswp__button--arrow--left,
    .pswp--touch .pswp__button--arrow--right {
      visibility: hidden; }

    /*
        Arrow buttons hit area
        (icon is added to :before pseudo-element)
    */
    .pswp__button--arrow--left,
    .pswp__button--arrow--right {
      background: none;
      top: 50%;
      margin-top: -50px;
      width: 70px;
      height: 100px;
      position: absolute; }

    .pswp__button--arrow--left {
      left: 0; }

    .pswp__button--arrow--right {
      right: 0; }

    .pswp__button--arrow--left:before,
    .pswp__button--arrow--right:before {
      content: '';
      top: 35px;
      background-color: rgba(0, 0, 0, 0.3);
      height: 30px;
      width: 32px;
      position: absolute; }

    .pswp__button--arrow--left:before {
      left: 6px;
      background-position: -138px -44px; }

    .pswp__button--arrow--right:before {
      right: 6px;
      background-position: -94px -44px; }

    /*

        2. Share modal/popup and links

     */
    .pswp__counter,
    .pswp__share-modal {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
          user-select: none; }

    .pswp__share-modal {
      display: block;
      background: rgba(0, 0, 0, 0.5);
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      padding: 10px;
      position: absolute;
      z-index: 1600;
      opacity: 0;
      -webkit-transition: opacity 0.25s ease-out;
              transition: opacity 0.25s ease-out;
      -webkit-backface-visibility: hidden;
      will-change: opacity; }

    .pswp__share-modal--hidden {
      display: none; }

    .pswp__share-tooltip {
      z-index: 1620;
      position: absolute;
      background: #FFF;
      top: 56px;
      border-radius: 2px;
      display: block;
      width: auto;
      right: 44px;
      -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25);
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25);
      -webkit-transform: translateY(6px);
          -ms-transform: translateY(6px);
              transform: translateY(6px);
      -webkit-transition: -webkit-transform 0.25s;
              transition: transform 0.25s;
      -webkit-backface-visibility: hidden;
      will-change: transform; }
      .pswp__share-tooltip a {
        display: block;
        padding: 8px 12px;
        color: #000;
        text-decoration: none;
        font-size: 14px;
        line-height: 18px; }
        .pswp__share-tooltip a:hover {
          text-decoration: none;
          color: #000; }
        .pswp__share-tooltip a:first-child {
          /* round corners on the first/last list item */
          border-radius: 2px 2px 0 0; }
        .pswp__share-tooltip a:last-child {
          border-radius: 0 0 2px 2px; }

    .pswp__share-modal--fade-in {
      opacity: 1; }
      .pswp__share-modal--fade-in .pswp__share-tooltip {
        -webkit-transform: translateY(0);
            -ms-transform: translateY(0);
                transform: translateY(0); }

    /* increase size of share links on touch devices */
    .pswp--touch .pswp__share-tooltip a {
      padding: 16px 12px; }

    a.pswp__share--facebook:before {
      content: '';
      display: block;
      width: 0;
      height: 0;
      position: absolute;
      top: -12px;
      right: 15px;
      border: 6px solid transparent;
      border-bottom-color: #FFF;
      -webkit-pointer-events: none;
      -moz-pointer-events: none;
      pointer-events: none; }

    a.pswp__share--facebook:hover {
      background: #3E5C9A;
      color: #FFF; }
      a.pswp__share--facebook:hover:before {
        border-bottom-color: #3E5C9A; }

    a.pswp__share--twitter:hover {
      background: #55ACEE;
      color: #FFF; }

    a.pswp__share--pinterest:hover {
      background: #CCC;
      color: #CE272D; }

    a.pswp__share--download:hover {
      background: #DDD; }

    /*

        3. Index indicator ("1 of X" counter)

     */
    .pswp__counter {
      position: absolute;
      left: 0;
      top: 0;
      height: 44px;
      font-size: 13px;
      line-height: 44px;
      color: #FFF;
      opacity: 0.75;
      padding: 0 10px; }

    /*
        
        4. Caption

     */
    .pswp__caption {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      min-height: 44px; }
      .pswp__caption small {
        font-size: 11px;
        color: #BBB; }

    .pswp__caption__center {
      text-align: center;
      max-width: 420px;
      margin: 0 auto;
      font-size: 13px;
      padding: 10px;
      line-height: 20px;
      color: #CCC; }

    .pswp__caption--empty {
      display: none; }

    /* Fake caption element, used to calculate height of next/prev image */
    .pswp__caption--fake {
      visibility: hidden; }

    /*

        5. Loading indicator (preloader)

        You can play with it here - http://codepen.io/dimsemenov/pen/yyBWoR

     */
    .pswp__preloader {
      width: 44px;
      height: 44px;
      position: absolute;
      top: 0;
      left: 50%;
      margin-left: -22px;
      opacity: 0;
      -webkit-transition: opacity 0.25s ease-out;
              transition: opacity 0.25s ease-out;
      will-change: opacity;
      direction: ltr; }

    .pswp__preloader__icn {
      width: 20px;
      height: 20px;
      margin: 12px; }

    .pswp__preloader--active {
      opacity: 1; }
      .pswp__preloader--active .pswp__preloader__icn {
        /* We use .gif in browsers that don't support CSS animation */
        background: url(preloader.gif?v=1904) 0 0 no-repeat; }

    .pswp--css_animation .pswp__preloader--active {
      opacity: 1; }
      .pswp--css_animation .pswp__preloader--active .pswp__preloader__icn {
        -webkit-animation: clockwise 500ms linear infinite;
                animation: clockwise 500ms linear infinite; }
      .pswp--css_animation .pswp__preloader--active .pswp__preloader__donut {
        -webkit-animation: donut-rotate 1000ms cubic-bezier(0.4, 0, 0.22, 1) infinite;
                animation: donut-rotate 1000ms cubic-bezier(0.4, 0, 0.22, 1) infinite; }

    .pswp--css_animation .pswp__preloader__icn {
      background: none;
      opacity: 0.75;
      width: 14px;
      height: 14px;
      position: absolute;
      left: 15px;
      top: 15px;
      margin: 0; }

    .pswp--css_animation .pswp__preloader__cut {
      /* 
                The idea of animating inner circle is based on Polymer ("material") loading indicator 
                 by Keanu Lee https://blog.keanulee.com/2014/10/20/the-tale-of-three-spinners.html
            */
      position: relative;
      width: 7px;
      height: 14px;
      overflow: hidden; }

    .pswp--css_animation .pswp__preloader__donut {
      -webkit-box-sizing: border-box;
              box-sizing: border-box;
      width: 14px;
      height: 14px;
      border: 2px solid #FFF;
      border-radius: 50%;
      border-left-color: transparent;
      border-bottom-color: transparent;
      position: absolute;
      top: 0;
      left: 0;
      background: none;
      margin: 0; }

    @media screen and (max-width: 1024px) {
      .pswp__preloader {
        position: relative;
        left: auto;
        top: auto;
        margin: 0;
        float: right; } }

    @-webkit-keyframes clockwise {
      0% {
        -webkit-transform: rotate(0deg);
                transform: rotate(0deg); }
      100% {
        -webkit-transform: rotate(360deg);
                transform: rotate(360deg); } }

    @keyframes clockwise {
      0% {
        -webkit-transform: rotate(0deg);
                transform: rotate(0deg); }
      100% {
        -webkit-transform: rotate(360deg);
                transform: rotate(360deg); } }

    @-webkit-keyframes donut-rotate {
      0% {
        -webkit-transform: rotate(0);
                transform: rotate(0); }
      50% {
        -webkit-transform: rotate(-140deg);
                transform: rotate(-140deg); }
      100% {
        -webkit-transform: rotate(0);
                transform: rotate(0); } }

    @keyframes donut-rotate {
      0% {
        -webkit-transform: rotate(0);
                transform: rotate(0); }
      50% {
        -webkit-transform: rotate(-140deg);
                transform: rotate(-140deg); }
      100% {
        -webkit-transform: rotate(0);
                transform: rotate(0); } }

    /*
        
        6. Additional styles

     */
    /* root element of UI */
    .pswp__ui {
      -webkit-font-smoothing: auto;
      visibility: visible;
      opacity: 1;
      z-index: 1550; }

    /* top black bar with buttons and "1 of X" indicator */
    .pswp__top-bar {
      position: absolute;
      left: 0;
      top: 0;
      height: 44px;
      width: 100%; }

    .pswp__caption,
    .pswp__top-bar,
    .pswp--has_mouse .pswp__button--arrow--left,
    .pswp--has_mouse .pswp__button--arrow--right {
      -webkit-backface-visibility: hidden;
      will-change: opacity;
      -webkit-transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
              transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1); }

    /* pswp--has_mouse class is added only when two subsequent mousemove events occur */
    .pswp--has_mouse .pswp__button--arrow--left,
    .pswp--has_mouse .pswp__button--arrow--right {
      visibility: visible; }

    .pswp__top-bar,
    .pswp__caption {
      background-color: rgba(0, 0, 0, 0.5); }

    /* pswp__ui--fit class is added when main image "fits" between top bar and bottom bar (caption) */
    .pswp__ui--fit .pswp__top-bar,
    .pswp__ui--fit .pswp__caption {
      background-color: rgba(0, 0, 0, 0.3); }

    /* pswp__ui--idle class is added when mouse isn't moving for several seconds (JS option timeToIdle) */
    .pswp__ui--idle .pswp__top-bar {
      opacity: 0; }

    .pswp__ui--idle .pswp__button--arrow--left,
    .pswp__ui--idle .pswp__button--arrow--right {
      opacity: 0; }

    /*
        pswp__ui--hidden class is added when controls are hidden
        e.g. when user taps to toggle visibility of controls
    */
    .pswp__ui--hidden .pswp__top-bar,
    .pswp__ui--hidden .pswp__caption,
    .pswp__ui--hidden .pswp__button--arrow--left,
    .pswp__ui--hidden .pswp__button--arrow--right {
      /* Force paint & create composition layer for controls. */
      opacity: 0.001; }

    /* pswp__ui--one-slide class is added when there is just one item in gallery */
    .pswp__ui--one-slide .pswp__button--arrow--left,
    .pswp__ui--one-slide .pswp__button--arrow--right,
    .pswp__ui--one-slide .pswp__counter {
      display: none; }

    .pswp__element--disabled {
      display: none !important; }

    .pswp--minimal--dark .pswp__top-bar {
      background: none; }

    /* nathan custom thumbnails */
    .pswp__thumbnails {
        padding: 5px;
        position: absolute;
        bottom: 0;
        width: 100%;
        margin: 0;
        white-space: nowrap;
        overflow: auto;
        opacity: 0;
        background: 0 0;
        transition: opacity .2s ease-out;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
        text-align: center;
        transform: translate3d(0,100%,0);
        background-color: rgba(0,0,0,.8);
    }
    .pswp_thumb_item {
        min-width: 50px;
        width: 50px;
        margin: 5px;
        border-radius: 2px;
        overflow: hidden;
        position: relative;
        cursor: pointer;
        display: inline-block;
        background-color: rgba(0,0,0,1);
    }
    .pswp_thumb_item img {
        width: 100%;
    }
    .pswp--animated-in .pswp__thumbnails {
        opacity: 1;
        transition: transform .6s cubic-bezier(.19,1,.22,1) 0s;
        transform: translate3d(0,0,0);
    }
/*     .pswp_thumb_item:not(.pswp_thumb_active) {
       opacity: .6;
       box-shadow: 0 0 0 2px var(--accent-color);
    } */
    .pswp_thumb_item img {
        margin-bottom: -5px;
        opacity: .6;
        transition: transform .6s,opacity .6s;
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
    .pswp_thumb_item img:hover,  .pswp_thumb_item.pswp_thumb_active img {
        -ms-transform: translateY(-5px);
        transform: translateY(-5px);
        opacity: 1;
    }
    @media (min-width: 768px) {
        .pswp_thumb_item {
           min-width: 70px;
           width: 70px;
        }
    }

/* ==========================================================================
   7. SCROLLBAR CSS
   ========================================================================== */
   @media (min-width: 1025px) {

        .t4s-scrollbar ::-webkit-scrollbar,.t4s-current-scrollbar::-webkit-scrollbar {
            height: 5px;              /* height of horizontal scrollbar ← You're missing this */
            width: 5px;               /* width of vertical scrollbar */
        }

        .t4s-scrollbar ::-webkit-scrollbar-track,.t4s-current-scrollbar::-webkit-scrollbar-track {
            background-color: rgba(0,0,0,.05)
        }

        .t4s-scrollbar ::-webkit-scrollbar-thumb,.t4s-current-scrollbar::-webkit-scrollbar-thumb {
            background-color: rgba(0,0,0,.12)
        }

        .t4s-scheme-light .t4s-scrollbar ::-webkit-scrollbar-track,.t4s-scrollbar.t4s-scheme-light ::-webkit-scrollbar-track,
        .t4s-scheme-light .t4s-current-scrollbar::-webkit-scrollbar-track,.t4s-current-scrollbar.t4s-scheme-light::-webkit-scrollbar-track {
            background-color: rgba(255,255,255,.05)
        }

        .t4s-scheme-light .t4s-scrollbar ::-webkit-scrollbar-thumb,.t4s-scrollbar.t4s-scheme-light ::-webkit-scrollbar-thumb,
        .t4s-scheme-light .t4s-current-scrollbar::-webkit-scrollbar-thumb,.t4s-current-scrollbar.t4s-scheme-light::-webkit-scrollbar-thumb {
            background-color: rgba(255,255,255,.12)
        }

   }

/* ==========================================================================
   8. LOADING CSS

   <div class="loading-icon"><div class="loading-icon__animation"></div></div>
   ========================================================================== */
    
    .loading-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      display: block;
      transform: translate(-50%, -50%);
    }

    .loading-icon__animation {
      position: relative;
      left: -32px;
      width: 10px;
      height: 10px;
      color: var(--loading-icon-color);
      border-radius: 5px;
      box-shadow: 16px 0 0 0 var(--loading-icon-color), 32px 0 0 0 var(--loading-icon-color), 48px 0 0 0 var(--loading-icon-color);
      animation: dotTyping 1.5s infinite linear;
    }

   @keyframes dotTyping {
      0% {
        box-shadow: 16px 0 0 0 var(--loading-icon-color), 32px 0 0 0 var(--loading-icon-color), 48px 0 0 0 var(--loading-icon-color);
      }
      16.667% {
        box-shadow: 16px -10px 0 0 var(--loading-icon-color), 32px 0 0 0 var(--loading-icon-color), 48px 0 0 0 var(--loading-icon-color);
      }
      33.333% {
        box-shadow: 16px 0 0 0 var(--loading-icon-color), 32px 0 0 0 var(--loading-icon-color), 48px 0 0 0 var(--loading-icon-color);
      }
      50% {
        box-shadow: 16px 0 0 0 var(--loading-icon-color), 32px -10px 0 0 var(--loading-icon-color), 48px 0 0 0 var(--loading-icon-color);
      }
      66.667% {
        box-shadow: 16px 0 0 0 var(--loading-icon-color), 32px 0 0 0 var(--loading-icon-color), 48px 0 0 0 var(--loading-icon-color);
      }
      83.333% {
        box-shadow: 16px 0 0 0 var(--loading-icon-color), 32px 0 0 0 var(--loading-icon-color), 48px -10px 0 0 var(--loading-icon-color);
      }
      100% {
        box-shadow: 16px 0 0 0 var(--loading-icon-color), 32px 0 0 0 var(--loading-icon-color), 48px 0 0 0 var(--loading-icon-color);
      }
    }
    .product__add-to-cart-button.cart-drawer--loading {
        --loading-icon-color: #ffffff;
        color: transparent;
    }
/*     https://www.figma.com/proto/sRLXS1tPIU323eF8MaswTK/01-Cobis?node-id=4961%3A35361&scaling=min-zoom&page-id=2680%3A5534 */
    .lazyloadt4s-loaderis-load-css:before,
    .lazyloadt4s-loader.is-load-css:after {
        position: absolute;
        content: "";
        bottom: 0;
        left: 0;
        top: 0;
        right: 0;
        height: 100%;
        width: 100%;
        overflow: hidden;
        background-color: #fff;
        border-radius: 5px;
        width: 50px;
        height: 5px;
        border-radius: 2.5px;
        margin: auto;
        z-index: 1;
        box-shadow: 0 0 1px #0006;
    }
    .lazyloadt4s-loader.is-load-css:after {
        transform: scaleX(0);
        animation: progress_bar--progressBarFillScale 20s cubic-bezier(.01,1.06,.71,1) forwards;
        transform-origin: 0 0;
        background-color: #222;
        z-index: 2;
    }
    @keyframes progress_bar--progressBarFillScale {
        0% {
            transform: scaleX(.001)
        }

        1% {
            transform: scaleX(.001)
        }

        to {
            transform: scaleX(.97)
        }
    }

/*     <div class="loading__spinner" hidden><svg aria-hidden="true" focusable="false" role="presentation" class="t4s-svg-spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
                          <circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                        </svg></div> */
/*     .t4s-predictive-search .t4s-svg-spinner {
        width: 1.5rem;
        height: 1.5rem;
        line-height: 0;
        --color-foreground: 18, 18, 18;
    } */
    .t4s-cart-spinner:not([hidden]),
    .t4s-svg-spinner:not([hidden]),
    .t4s-btn-loading__svg.is--loading .t4s-svg__spinner {
      animation: t4s_rotator 1.4s linear infinite;
    }

    @keyframes t4s_rotator {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(270deg);
      }
    }
    .t4s-cart-spinner:not([hidden]) .t4s-path,
    .t4s-svg-spinner:not([hidden]) .t4s-path,
    .t4s-btn-loading__svg.is--loading .t4s-path {
      stroke-dasharray: 280;
      stroke-dashoffset: 0;
      transform-origin: center;
      stroke: var(--secondary-color);
      animation: t4s_dash 1.4s ease-in-out infinite;
    }

    @media screen and (forced-colors: active) {
      .t4s-cart-spinner:not([hidden]) .t4s-path,
      .t4s-svg-spinner:not([hidden]) .t4s-path {
        stroke: CanvasText;
      }
    }

    @keyframes t4s_dash {
      0% {
        stroke-dashoffset: 280;
      }
      50% {
        stroke-dashoffset: 75;
        transform: rotate(135deg);
      }
      100% {
        stroke-dashoffset: 280;
        transform: rotate(450deg);
      }
    }
   .t4s-btn-loading__svg.is--loading {
       position: relative;
       pointer-events: none;
   }
   .t4s-btn-loading__svg.is--loading .t4s-btn-atc_text,
   .t4s-btn-loading__svg.is--loading .t4s-btn-icon,
   .t4s-btn-loading__svg.is--loading .t4s-btn-op0 {
       opacity: 0;
   }
   .t4s-btn-loading__svg.is--loading >.t4s-loading__spinner {
       top: 50%;
       left: 50%;
       width: 1.8rem;
       transform: translate(-50%,-50%);
       position: absolute;
       display: flex;
   }
   .t4s-btn-loading__svg.is--loading .t4s-path,
   .t4s-svg-spinner:not([hidden]) .t4s-path {
       stroke: currentColor;
   }
   .is--pe-none { pointer-events: none !important; }

   /* Effect loading when ajax filter, panigation on main section */
   .t4s-section-main .is--enabled.is--loading {
    opacity: .5;
    pointer-events: none;
   }

/* ==========================================================================
   9. PREVENT SCROLLING CSS
   ========================================================================== */
    
  body.t4s-prevent-scrolling {
      overflow: hidden;
      position: fixed;
      width: 100%; 
  }

/* ==========================================================================
   10. COLORS CSS
   ========================================================================== */
    /*150 colors css*/
    [class*=' bg_color_'] {
        font-size: 0;
        --t4s-bg-color: #e5e5e5;
        background-color: var(--t4s-bg-color);
        background-image: var(--swatch--background);
    }

    .bg_color_cyan {
        --t4s-bg-color: #25bdb0
    }

    .bg_color_pink {
        --t4s-bg-color: #fcc6de
    }

    .bg_color_black {
        --t4s-bg-color: #000
    }

    .bg_color_grey {
        --t4s-bg-color: #ccc
    }

    .bg_color_green {
        --t4s-bg-color: #e1eb78
    }

    .bg_color_sliver {
        --t4s-bg-color: #f2f2f2
    }

    .bg_color_blue {
        --t4s-bg-color: #a8bcd4
    }

    .bg_color_purple {
        --t4s-bg-color: #7e0b80
    }

    .bg_color_white {
        --t4s-bg-color: #fff;
        border: 1px solid #ddd
    }

    .bg_color_brown {
        --t4s-bg-color: #977945
    }

    .bg_color_red {
        --t4s-bg-color: #f63400
    }

    .bg_color_brown {
        --t4s-bg-color: #ad8111
    }

    .bg_color_gold {
        --t4s-bg-color: #cea525
    }

    .bg_color_beige {
        --t4s-bg-color: #f0deba
    }

    .bg_color_gray {
        --t4s-bg-color: #bfbfbf
    }

    .bg_color_orange {
        --t4s-bg-color: #ff7900
    }

    .bg_color_yellow {
        --t4s-bg-color: #dcdb79
    }

    .bg_color_navy {
        --t4s-bg-color: #001f3f
    }

    .bg_color_aqua {
        --t4s-bg-color: #7fdbff
    }

    .bg_color_teal {
        --t4s-bg-color: #39cccc
    }

    .bg_color_gun-smoke {
        --t4s-bg-color: #12221f
    }

    .bg_color_olive {
        --t4s-bg-color: olive
    }

    .bg_color_lime {
        --t4s-bg-color: #0f0
    }

    .bg_color_rose-gold {
        --t4s-bg-color: #fcdbd6
    }

    .bg_color_clear {
        --t4s-bg-color: #caf6ea
    }

    .bg_color_maroon {
        --t4s-bg-color: maroon
    }

    .bg_color_ivory {
        --t4s-bg-color: ivory
    }

    .bg_color_lavenderblush {
        --t4s-bg-color: #b0e0e6
    }

    .bg_color_powderblue {
        --t4s-bg-color: #f012be
    }

    .bg_color_darkgreen {
        --t4s-bg-color: #006400
    }

    .bg_color_saddlebrown {
        --t4s-bg-color: #8b4513
    }

    .bg_color_lightcoral {
        --t4s-bg-color: #f08080
    }

    .bg_color_palevioletred {
        --t4s-bg-color: #db7093
    }

    .bg_color_chocolate {
        --t4s-bg-color: #d2691e
    }

    .bg_color_darkolivegreen {
        --t4s-bg-color: #556b2f
    }

    .bg_color_darkred {
        --t4s-bg-color: #8b0000
    }

    .bg_color_mediumaquamarine {
        --t4s-bg-color: #66cdaa
    }

    .bg_color_sandybrown {
        --t4s-bg-color: #f4a460
    }

    .bg_color_darkslategray {
        --t4s-bg-color: #2f4f4f
    }

    .bg_color_salmon {
        --t4s-bg-color: salmon
    }

    .bg_color_slateblue {
        --t4s-bg-color: #6a5acd
    }

    .bg_color_lightgray {
        --t4s-bg-color: #d3d3d3
    }

    .bg_color_lemonchiffon {
        --t4s-bg-color: #fffacd
    }

    .bg_color_thistle {
        --t4s-bg-color: #d8bfd8
    }

    .bg_color_wheat {
        --t4s-bg-color: wheat
    }

    .bg_color_lightsalmon {
        --t4s-bg-color: #ffa07a
    }

    .bg_color_hotpink {
        --t4s-bg-color: #ff69b4
    }

    .bg_color_lawngreen {
        --t4s-bg-color: #7cfc00
    }

    .bg_color_snow {
        --t4s-bg-color: snow
    }

    .bg_color_darkslateblue {
        --t4s-bg-color: #483d8b
    }

    .bg_color_coral {
        --t4s-bg-color: coral
    }

    .bg_color_limegreen {
        --t4s-bg-color: #32cd32
    }

    .bg_color_lightgrey {
        --t4s-bg-color: #d3d3d3
    }

    .bg_color_royalblue {
        --t4s-bg-color: #4169e1
    }

    .bg_color_dimgrey {
        --t4s-bg-color: #696969
    }

    .bg_color_lightseagreen {
        --t4s-bg-color: #20b2aa
    }

    .bg_color_skyblue {
        --t4s-bg-color: #87ceeb
    }

    .bg_color_orchid {
        --t4s-bg-color: orchid
    }

    .bg_color_darksalmon {
        --t4s-bg-color: #e9967a
    }

    .bg_color_darkorchid {
        --t4s-bg-color: #9932cc
    }

    .bg_color_cornflowerblue {
        --t4s-bg-color: #6495ed
    }

    .bg_color_cornsilk {
        --t4s-bg-color: #fff8dc
    }

    .bg_color_lightslategrey {
        --t4s-bg-color: #789
    }

    .bg_color_whitesmoke {
        --t4s-bg-color: #f5f5f5
    }

    .bg_color_seagreen {
        --t4s-bg-color: #2e8b57
    }

    .bg_color_crimson {
        --t4s-bg-color: #dc143c
    }

    .bg_color_azure {
        --t4s-bg-color: azure
    }

    .bg_color_paleturquoise {
        --t4s-bg-color: #afeeee
    }

    .bg_color_darkkhaki {
        --t4s-bg-color: #bdb76b
    }

    .bg_color_mediumvioletred {
        --t4s-bg-color: #c71585
    }

    .bg_color_lightgoldenrodyellow {
        --t4s-bg-color: #fafad2
    }

    .bg_color_violet {
        --t4s-bg-color: violet
    }

    .bg_color_peachpuff {
        --t4s-bg-color: #ffdab9
    }

    .bg_color_steelblue {
        --t4s-bg-color: #4682b4
    }

    .bg_color_blanchedalmond {
        --t4s-bg-color: #ffebcd
    }

    .bg_color_moccasin {
        --t4s-bg-color: #ffe4b5
    }

    .bg_color_antiquewhite {
        --t4s-bg-color: #faebd7
    }

    .bg_color_mediumturquoise {
        --t4s-bg-color: #48d1cc
    }

    .bg_color_lightskyblue {
        --t4s-bg-color: #87cefa
    }

    .bg_color_darkorange {
        --t4s-bg-color: #ff8c00
    }

    .bg_color_darkblue {
        --t4s-bg-color: #00008b
    }

    .bg_color_springgreen {
        --t4s-bg-color: #00ff7f
    }

    .bg_color_lavender {
        --t4s-bg-color: #e6e6fa
    }

    .bg_color_mediumpurple {
        --t4s-bg-color: #9370db
    }

    .bg_color_deepskyblue {
        --t4s-bg-color: #00bfff
    }

    .bg_color_mintcream {
        --t4s-bg-color: #f5fffa
    }

    .bg_color_deeppink {
        --t4s-bg-color: #ff1493
    }

    .bg_color_darkseagreen {
        --t4s-bg-color: #8fbc8f
    }

    .bg_color_papayawhip {
        --t4s-bg-color: #ffefd5
    }

    .bg_color_orangered {
        --t4s-bg-color: #ff4500
    }

    .bg_color_tomato {
        --t4s-bg-color: tomato
    }

    .bg_color_navajowhite {
        --t4s-bg-color: #ffdead
    }

    .bg_color_mediumorchid {
        --t4s-bg-color: #ba55d3
    }

    .bg_color_ghostwhite {
        --t4s-bg-color: #f8f8ff
    }

    .bg_color_slategrey {
        --t4s-bg-color: #708090
    }

    .bg_color_forestgreen {
        --t4s-bg-color: #228b22
    }

    .bg_color_dodgerblue {
        --t4s-bg-color: #1e90ff
    }

    .bg_color_linen {
        --t4s-bg-color: linen
    }

    .bg_color_blueviolet {
        --t4s-bg-color: #8a2be2
    }

    .bg_color_gainsboro {
        --t4s-bg-color: #dcdcdc
    }

    .bg_color_lightgreen {
        --t4s-bg-color: #90ee90
    }

    .bg_color_chartreuse {
        --t4s-bg-color: #7fff00
    }

    .bg_color_mistyrose {
        --t4s-bg-color: #ffe4e1
    }

    .bg_color_darkgray {
        --t4s-bg-color: #a9a9a9
    }

    .bg_color_peru {
        --t4s-bg-color: peru
    }

    .bg_color_tan {
        --t4s-bg-color: tan
    }

    .bg_color_palegreen {
        --t4s-bg-color: #98fb98
    }

    .bg_color_rebeccapurple {
        --t4s-bg-color: #639
    }

    .bg_color_darkcyan {
        --t4s-bg-color: #008b8b
    }

    .bg_color_oldlace {
        --t4s-bg-color: #fdf5e6
    }

    .bg_color_rosybrown {
        --t4s-bg-color: #bc8f8f
    }

    .bg_color_darkslategrey {
        --t4s-bg-color: #2f4f4f
    }

    .bg_color_lightslategray {
        --t4s-bg-color: #789
    }

    .bg_color_turquoise {
        --t4s-bg-color: #40e0d0
    }

    .bg_color_darkgoldenrod {
        --t4s-bg-color: #b8860b
    }

    .bg_color_honeydew {
        --t4s-bg-color: #f0fff0
    }

    .bg_color_mediumslateblue {
        --t4s-bg-color: #7b68ee
    }

    .bg_color_mediumspringgreen {
        --t4s-bg-color: #00fa9a
    }

    .bg_color_plum {
        --t4s-bg-color: plum
    }

    .bg_color_darkgrey {
        --t4s-bg-color: #a9a9a9
    }

    .bg_color_slategray {
        --t4s-bg-color: #708090
    }

    .bg_color_darkmagenta {
        --t4s-bg-color: #8b008b
    }

    .bg_color_lightblue {
        --t4s-bg-color: #add8e6
    }

    .bg_color_floralwhite {
        --t4s-bg-color: #fffaf0
    }

    .bg_color_dimgray {
        --t4s-bg-color: #696969
    }

    .bg_color_greenyellow {
        --t4s-bg-color: #adff2f
    }

    .bg_color_fuchsia {
        --t4s-bg-color: #f0f
    }

    .bg_color_cadetblue {
        --t4s-bg-color: #5f9ea0
    }

    .bg_color_yellowgreen {
        --t4s-bg-color: #9acd32
    }

    .bg_color_darkviolet {
        --t4s-bg-color: #9400d3
    }

    .bg_color_sienna {
        --t4s-bg-color: sienna
    }

    .bg_color_lightpink {
        --t4s-bg-color: #ffb6c1
    }

    .bg_color_mediumblue {
        --t4s-bg-color: #0000cd
    }

    .bg_color_bisque {
        --t4s-bg-color: bisque
    }

    .bg_color_aquamarine {
        --t4s-bg-color: #7fffd4
    }

    .bg_color_palegoldenrod {
        --t4s-bg-color: #eee8aa
    }

    .bg_color_aliceblue {
        --t4s-bg-color: #f0f8ff
    }

    .bg_color_olivedrab {
        --t4s-bg-color: #6b8e23
    }

    .bg_color_indigo {
        --t4s-bg-color: indigo
    }

    .bg_color_lightcyan {
        --t4s-bg-color: #e0ffff
    }

    .bg_color_midnightblue {
        --t4s-bg-color: #191970
    }

    .bg_color_khaki {
        --t4s-bg-color: khaki
    }

    .bg_color_burlywood {
        --t4s-bg-color: #deb887
    }

    .bg_color_lightyellow {
        --t4s-bg-color: #ffffe0
    }

    .bg_color_lightsteelblue {
        --t4s-bg-color: #b0c4de
    }

    .bg_color_goldenrod {
        --t4s-bg-color: #daa520
    }

    .bg_color_darkturquoise {
        --t4s-bg-color: #00ced1
    }

    .bg_color_magenta {
        --t4s-bg-color: #f0f
    }

    .bg_color_seashell {
        --t4s-bg-color: #fff5ee
    }

    .bg_color_indianred {
        --t4s-bg-color: #cd5c5c
    }

    .bg_color_mediumseagreen {
        --t4s-bg-color: #3cb371
    }

    .bg_color_firebrick {
        --t4s-bg-color: #b22222
    }
    .bg_color_ntloading {
        --t4s-bg-color: #f5f5f5;
    }

/* ==========================================================================
   11. SWATCH LIMIT
   ========================================================================== */
    .t4s-pr-color__item.is--colors-more a {
        position: relative;
        display: inline-flex;
        justify-content: center;
        align-items: center;
    }
    .t4s-pr-color__item.is--colors-more a:before {
        content: '...';
        content: var(--text);
        z-index: 10;
        color: #222;
        font-size: 9px;
        overflow: hidden;
/*      
        position: absolute;
        top: 0;
        left: 2px;
        right: 2px;
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center */
    }

    .t4s-product-colors.is-t4s--opended .t4s-pr-color__item.is--colors-more a:before {
        content: "-";
        content: var(--text2)
    }
    .t4s-product-colors.is-t4s--opended .t4s-pr-color__item:not(.is--colors-more) {
        display: inline-block!important;
    }
/*     .t4s-pr-color__value.lazyloadt4sed {
        animation: 1.25s cubic-bezier(.26,.54,.32,1) forwards t4s-ani-fadeIn;
    } */

/* ==========================================================================
   12. CLOSE OVERLAY
   ========================================================================== */

.t4s-close-overlay {
   position: fixed;
   top: 0;
   left: 0;
   height: 100vh;
   width: 100vw;
   z-index: 468;
   visibility: hidden;
   pointer-events: none;
   opacity: 0;
   background: rgba(0,0,0,.7);
   transition: opacity .3s ease-in-out,visibility .3s ease-in-out;
}
.t4s-close-overlay.is--visible {
    pointer-events: auto;
    opacity: 1;
    visibility: visible;
    transition: opacity .25s,visibility;
}


/* ==========================================================================
   12A. LOCK SCROLL
   ========================================================================== */

.t4s-lock-scroll {
    overflow: hidden;
}
[data-t4s-scroll-me] {
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}


/* ==========================================================================
   12B. HEADER
   ========================================================================== */
header {
    position: relative;
}
.t4s-count-box,
.t4s-site-nav__icon .ssw-counter-fave-menu {
    width: 15px;
    height: 15px;
    letter-spacing: 0;
    font-weight: 400;
    font-size: 10px;
    line-height: 15px;
    box-shadow: 1px 1px 3px 0 rgb(0 0 0 / 30%);
    opacity: 1;
    transition: .3s ease-in-out;
    background-color: var(--h-count-bgcolor);
    color: var(--h-count-color);
    text-align: center;
    border-radius: 50%;
    top: -5px;
    right: -9px;
}
.t4s-use__kalles .t4s-count-box { right: -6px; }
.t4s-use__line .t4s-count-box { top: -3px;right: -7px; }
.t4s-section-header.animate {
    transition: transform .15s ease-out;
}
.shopify-section-header-hidden {
    transform: translateY(-100%);
}
.t4s-icon-select-arrow {
    opacity: 1;
    width: 10px;
    height: 10px;
    position: absolute;
    top: calc(50% - 5px);
    right: 12px;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
}
.t4s_lb_nav {
    position: absolute;
    color: #fff;
    font-size: 9px;
    padding: 0 8px;
    border-radius: 50px;
    top: 50%;
    line-height: 18px;
    background: #01bad4;
    transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    right: -5px;
    margin-top: -14px;
    -webkit-transition: opacity .3s ease-in-out;
    -moz-transition: opacity .3s ease-in-out;
    -o-transition: opacity .3s ease-in-out;
    transition: opacity .3s ease-in-out;
    opacity: 1;
}
.shopify-section-header-sticky,
.t4sp-sticky .t4s-section-header.shopify-section-header-sticky {
    position: sticky;
    top: 0;
    z-index: 460;
    -webkit-box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
}
.no-transiton-nav-a .t4s-section-header .t4s-push-menu-btn,
.no-transiton-nav-a .t4s-section-header .t4s-nav__ul>li> a,
.no-transiton-nav-a .t4s-section-header .t4s-nav__ul>li> a *,
.no-transiton-nav-a .t4s-section-header .t4s-site-nav__icon a,
.no-transiton-nav-a .t4s-section-header .t4s-site-nav__icon a *,
.no-transiton-nav-a .t4s-section-header .t4s-site-nav__icon svg {
    transition: none !important;
}

/* ==========================================================================
   15. DRIFT ZOOM
   ========================================================================== */
@keyframes drift-fadeZoomIn {
  0% {
    transform: scale(1.5);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes drift-fadeZoomOut {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  15% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(0.5);
    opacity: 0;
  }
}

@keyframes drift-loader-rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  50% {
    transform: translate(-50%, -50%) rotate(-180deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(-360deg);
  }
}

@keyframes drift-loader-before {
  0% {
    transform: scale(1);
  }
  10% {
    transform: scale(1.2) translateX(6px);
  }
  25% {
    transform: scale(1.3) translateX(8px);
  }
  40% {
    transform: scale(1.2) translateX(6px);
  }
  50% {
    transform: scale(1);
  }
  60% {
    transform: scale(0.8) translateX(6px);
  }
  75% {
    transform: scale(0.7) translateX(8px);
  }
  90% {
    transform: scale(0.8) translateX(6px);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes drift-loader-after {
  0% {
    transform: scale(1);
  }
  10% {
    transform: scale(1.2) translateX(-6px);
  }
  25% {
    transform: scale(1.3) translateX(-8px);
  }
  40% {
    transform: scale(1.2) translateX(-6px);
  }
  50% {
    transform: scale(1);
  }
  60% {
    transform: scale(0.8) translateX(-6px);
  }
  75% {
    transform: scale(0.7) translateX(-8px);
  }
  90% {
    transform: scale(0.8) translateX(-6px);
  }
  100% {
    transform: scale(1);
  }
}

@-webkit-keyframes drift-fadeZoomIn {
  0% {
    -webkit-transform: scale(1.5);
    opacity: 0;
  }
  100% {
    -webkit-transform: scale(1);
    opacity: 1;
  }
}

@-webkit-keyframes drift-fadeZoomOut {
  0% {
    -webkit-transform: scale(1);
    opacity: 1;
  }
  15% {
    -webkit-transform: scale(1.1);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(0.5);
    opacity: 0;
  }
}

@-webkit-keyframes drift-loader-rotate {
  0% {
    -webkit-transform: translate(-50%, -50%) rotate(0);
  }
  50% {
    -webkit-transform: translate(-50%, -50%) rotate(-180deg);
  }
  100% {
    -webkit-transform: translate(-50%, -50%) rotate(-360deg);
  }
}

@-webkit-keyframes drift-loader-before {
  0% {
    -webkit-transform: scale(1);
  }
  10% {
    -webkit-transform: scale(1.2) translateX(6px);
  }
  25% {
    -webkit-transform: scale(1.3) translateX(8px);
  }
  40% {
    -webkit-transform: scale(1.2) translateX(6px);
  }
  50% {
    -webkit-transform: scale(1);
  }
  60% {
    -webkit-transform: scale(0.8) translateX(6px);
  }
  75% {
    -webkit-transform: scale(0.7) translateX(8px);
  }
  90% {
    -webkit-transform: scale(0.8) translateX(6px);
  }
  100% {
    -webkit-transform: scale(1);
  }
}

@-webkit-keyframes drift-loader-after {
  0% {
    -webkit-transform: scale(1);
  }
  10% {
    -webkit-transform: scale(1.2) translateX(-6px);
  }
  25% {
    -webkit-transform: scale(1.3) translateX(-8px);
  }
  40% {
    -webkit-transform: scale(1.2) translateX(-6px);
  }
  50% {
    -webkit-transform: scale(1);
  }
  60% {
    -webkit-transform: scale(0.8) translateX(-6px);
  }
  75% {
    -webkit-transform: scale(0.7) translateX(-8px);
  }
  90% {
    -webkit-transform: scale(0.8) translateX(-6px);
  }
  100% {
    -webkit-transform: scale(1);
  }
}

.drift-zoom-pane {
  /* background: rgba(0, 0, 0, 0.5); */
  /* This is required because of a bug that causes border-radius to not
  work with child elements in certain cases. */
  background: #fff;
  transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  box-shadow: 0 1px 5px rgb(127 127 127 / 2%), 0 5px 18px rgb(127 127 127 / 20%);
  z-index: 3;
}

.drift-zoom-pane.drift-opening {
  animation: drift-fadeZoomIn 180ms ease-out;
  -webkit-animation: drift-fadeZoomIn 180ms ease-out;
}

.drift-zoom-pane.drift-closing {
  animation: drift-fadeZoomOut 210ms ease-in;
  -webkit-animation: drift-fadeZoomOut 210ms ease-in;
}

.drift-zoom-pane.drift-inline {
  position: absolute;
  width: 150px;
  height: 150px;
  border-radius: 75px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.3);
}

.drift-loading .drift-zoom-pane-loader {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  width: 66px;
  height: 20px;
  animation: drift-loader-rotate 1800ms infinite linear;
  -webkit-animation: drift-loader-rotate 1800ms infinite linear;
}

.drift-zoom-pane-loader:before,
.drift-zoom-pane-loader:after {
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  position: absolute;
  top: 50%;
  margin-top: -10px;
  border-radius: 20px;
  background-color: rgba(var(--accent-color-rgb), 0.9);
}

.drift-zoom-pane-loader:before {
  left: 0;
  animation: drift-loader-before 1800ms infinite linear;
  -webkit-animation: drift-loader-before 1800ms infinite linear;
}

.drift-zoom-pane-loader:after {
  right: 0;
  animation: drift-loader-after 1800ms infinite linear;
  -webkit-animation: drift-loader-after 1800ms infinite linear;
  animation-delay: -900ms;
  -webkit-animation-delay: -900ms;
}

.drift-bounding-box {
    background: rgba(255,255,255,.25);
    box-shadow: 0 0 0 1px rgb(0 0 0 / 40%) inset;
    z-index: 500;
}
.drift-zoom-pane img {
    position: absolute;
    display: block;
    max-width: none !important;
    max-height: none !important;
    object-fit: contain;
    top: 0;
    left: 0;
}
.t4s-product__zoom-wrapper .drift-zoom-pane {
    top: 0;
    left: 0;
    height: 520px;
    width: 100%;
    max-width: 520px;
}
.is-zoom-type__external .t4s-product__info-container {
    transition: opacity .2s;
}
.is-zoom-type__external .zoom_fade_info .t4s-product__info-container {
    opacity: .3;
}

/* ==========================================================================
   16. MEGA MENU
   ========================================================================== */
.t4s-type__mega> .t4s-sub-menu,
.t4s-type__drop> .t4s-sub-menu,
.t4s-type__lv0_mega> .t4s-sub-menu,
.t4s-type__lv0_drop> .t4s-sub-menu {
     position: absolute;
     min-width: 250px;
     background-color: var(--t4s-body-background);
     top: 100%;
     z-index: 101;
     padding: 0;
     -webkit-box-shadow: inset 0 0 0 1px #eaeaea, 0 5px 20px rgb(0 0 0 / 15%);
     box-shadow: inset 0 0 0 1px #eaeaea, 0 5px 20px rgb(0 0 0 / 15%);
     box-shadow: 0 0 3px rgb(0 0 0 / 15%);
     opacity: 0;
     visibility: hidden;
    transition: .2s ease-in-out;
    pointer-events: none !important;
}
.t4s-type__drop> .t4s-sub-menu .t4s_lb_nav{
  position: relative;
  margin-inline-start: 5px;
  display: inline-block;
}
.t4s-type__drop> .t4s-sub-menu{padding:10px 0}
.t4s-type__mega> .t4s-sub-menu *,
.t4s-type__drop> .t4s-sub-menu *,
.t4s-type__lv0_mega> .t4s-sub-menu *,
.t4s-type__lv0_drop> .t4s-sub-menu  * {
    pointer-events: none !important;
}
.t4s-type__mega> .t4s-sub-menu,
.t4s-type__lv0_mega> .t4s-sub-menu {
   padding: 20px 15px;
   max-width: 100vw;
   max-height: 75vh;
   overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}
.t4s-type__mega> .t4s-sub-menu,
.t4s-type__lv0_mega> .t4s-sub-menu {
   max-width: var(--t4s-max-width,100vw);
   max-height: var(--t4s-max-height,75vh);
}
#shopify-section-header-vertical .t4s-type__mega > .t4s-sub-menu {
   max-width: calc(100vw - 280px);
   max-width: -webkit-calc(100vw - 280px);
}
.t4s-sub-menu .t4s-container {
    max-width: 100%;
}
.t4s-menu-item.has--children.is-action__hover .t4s-sub-menu {
   pointer-events: auto !important;
   opacity: 1;
   visibility: visible;
}
.t4s-menu-item.has--children.is-action__hover .t4s-sub-menu * {
   pointer-events: auto !important;
}
.t4s-nav__hover_sideup .t4s-nav__ul>li>.t4s-sub-menu,
.t4s-nav__hover_sideup #t4s-nav-categories>li>.t4s-sub-menu {
    -webkit-transform: translateY(15px) translateZ(0);
    transform: translateY(15px) translateZ(0);
    transition: opacity .2s,visibility .2s,transform .2s,-webkit-transform .2s
}
.t4s-nav__hover_sideup .t4s-nav__ul>li.t4s-menu-item.is-action__hover>.t4s-sub-menu,
.t4s-nav__hover_sideup #t4s-nav-categories>li.t4s-menu-item.is-action__hover>.t4s-sub-menu {
    -webkit-transform: none;
    transform: none
}
/* .t4s-nav__ul>li.t4s-menu-item:after {
    content: '';
    position: absolute;
    right: 0;
    bottom: 11px;
    left: 0;
    height: 20px;
    pointer-events: none;
    opacity: 0;
}
 */
.t4s-nav__ul>li.t4s-menu-item.is-action__hover:after {
    pointer-events: auto;
    opacity: 1;
}
/* .t4s-section-header:not(.calc-pos-submenu) {
    overflow-x: hidden;
} */
.menu-width__full .t4s-sub-menu {
   width: 100vw;
   left: 0;
}
.t4s-header__wrapper:not(.t4s-layout_vertical) .menu-width__full .t4s-sub-menu {
   left: 0 !important;
   max-width: 100vw !important;
}
.menu-width__full .t4s-sub-menu .t4s-container {
    max-width: 100%;
}
.t4s-sub-menu.is--center-screen {
    left: 50%;
    transform: translateX(-50%);
}
.t4s-nav__hover_sideup .t4s-nav__ul>li>.t4s-sub-menu.is--center-screen,
.t4s-nav__hover_sideup #t4s-nav-categories>li>.t4s-sub-menu.is--center-screen {
    -webkit-transform: translateX(-50%) translateY(15px) translateZ(0);
    transform: translateX(-50%) translateY(15px) translateZ(0);
}
.t4s-nav__hover_sideup .t4s-nav__ul>li.t4s-menu-item.is-action__hover>.t4s-sub-menu.is--center-screen,
.t4s-nav__hover_sideup #t4s-nav-categories>li.t4s-menu-item.is-action__hover>.t4s-sub-menu.is--center-screen {
    -webkit-transform: translateX(-50%) translateY(0) translateZ(0);
    transform: translateX(-50%) translateY(0) translateZ(0);
}

/* ==========================================================================
   17. CAROUSEL NAV
   ========================================================================== */
/* Hover thumb 1 */
.carousel__nav-hover1 .t4s-carousel__nav-item:not(.is-nav-selected) {
  opacity: .5;
  transition: transform .6s,opacity .6s;
}
.carousel__nav-hover1 .t4s-carousel__nav-item.is-nav-selected, 
.carousel__nav-hover1 .t4s-carousel__nav-item:hover {
  opacity: 1;
}

/* End hover thumb 1 */
/* Hover thumb 2 */
.carousel__nav-hover2 .t4s-carousel__nav-item {
  overflow: hidden
}
.carousel__nav-hover2 .t4s-carousel__nav-item .t4s-carousel__nav-inner {
  margin-bottom: -5px;
  opacity: .5;
  transition: transform .6s,opacity .6s;
  transform: translateY(0);
  overflow: hidden
}
.carousel__nav-hover2 .t4s-carousel__nav-item.is-nav-selected .t4s-carousel__nav-inner, 
.carousel__nav-hover2 .t4s-carousel__nav-item:hover .t4s-carousel__nav-inner {
  transform: translateY(-5px);
  opacity: 1;
}
.t4s-thumbnail__badge:empty {
    display: none !important;
}
.t4s-thumbnail__badge {
   --thumb-badge-size:  20px;
   --thumb-badge-icon:  10px;
   --thumb-video-icon:  8px;
    position: absolute;
    width: var(--thumb-badge-size);
    height: var(--thumb-badge-size);
    left: auto;
    top: 5px;
    background-color: var(--t4s-light-color);
    box-shadow: 0 0 5px rgb(0 0 0 / 9%);
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 40px;
    right: 5px;
    opacity: 1;
   transition: transform .6s,opacity .6s;
}
.t4s-thumbnail__badge svg {
    width: var(--thumb-badge-icon);
    height: auto;
}
[data-mdtype="video"] .t4s-thumbnail__badge svg {
    width: var(--thumb-video-icon);
}
@media screen and (min-width: 768px) {
   
   .t4s-thumbnail__badge {
      --thumb-badge-size:  30px;
      --thumb-badge-icon:  12px;
      --thumb-video-icon:  10px;
   }
}
/* End hover thumb 2 */

/* ==========================================================================
   18. SHOPIFY MODEL
   ========================================================================== */
/* .shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster,
button.plyr__control.plyr__control--overlaid {
    background-color: #5b5b5b;
    color: #f3f3f3 !important;
    border: 1px solid rgba(243,243,243,0.05)
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:hover,.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:focus {
    color: #f3f3f3
}
.plyr__control--overlaid, .btnt4_style_2 .shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster { 
border-radius: 50% !important;
}
.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-area {
    background: #f3f3f3;
    border-color: rgba(91,91,91,0.05);
} */
.t4s-deferred-media video,
.t4s-deferred-media iframe,
.t4s-deferred-media model-viewer,
.t4s-deferred-media .shopify-model-viewer-ui, 
.t4s-deferred-media .plyr,
.t4s-deferred-media .media-item {
    display: block;
    max-width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
}
.t4s-deferred-media.is-media__initialized .t4s-product__media {
    opacity: 0;
}
.t4s-pr-group-btns>button {

}

.t4s-pr-group-btns[hidden] {
    opacity: 0;
    pointer-events: none;
}
.t4s-pr-group-btns[hidden] button {
    pointer-events: none;
}
.is-shopify-xr__showing .t4s-pr-group-btns[hidden] {
    opacity: 1;
    pointer-events: auto;
}
.is-shopify-xr__showing .t4s-pr-group-btns button:not(.t4s-pr__view-in-space) {
   display: none !important;
}
.t4s-deferred-media model-viewer, 
.t4s-deferred-media .shopify-model-viewer-ui, 
.t4s-deferred-media .plyr, 
.t4s-deferred-media .plyr__video-wrapper,
.t4s-deferred-media .media-video {
    height: 100%;
}
.plyr.plyr--full-ui.plyr--video .plyr__video-wrapper {
    background-color: transparent;
    height: 100%;
}
.plyr.plyr--full-ui.plyr--video .plyr__poster {
    --color-video-bg: #f2f2f2;
    background-color: var(--color-video-bg);
    pointer-events: none;
}
.t4s-deferred-media.is--adding {
    opacity: 0;
    visibility: hidden;
    transition: visibility .35s ease, opacity .35s ease;
}
.t4s-deferred-media.is-media__initialized {
    opacity: 1;
    visibility: visible;
}

/* ==========================================================================
   19. NOTICES
   ========================================================================== */
.is--error-checkbox input[type="checkbox"][data-agreemail-checkbox]:not(:checked), 
.is--error-checkbox input[type="checkbox"][data-agreemail-checkbox]:not(:checked)+label:before,
.is--error-email input[type="email"],
.is--error-email .t4s-newsletter__inner {
    border-color: var(--t4s-error-color) !important;
}
#t4s-notices__wrapper {
    position: fixed;
    bottom: 0px;
    left: 0px;
    right: 0px;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    align-items: center;
    z-index: 69900;
    background-color: var(--t4s-warning-color);
    color: var(--t4s-light-color);
    cursor: pointer;
    transform: translate3d(0px, 100%, 0px);
    transition: all 0.35s ease 0s;
    min-height: 60px;
    padding: 10px 80px 10px 65px;
    border: 0px;
}
#t4s-notices__wrapper[data-notices-status="success"] {
    background-color: var(--t4s-success-color);
}
#t4s-notices__wrapper[data-notices-status="error"] {
    background-color: var(--t4s-error-color);
}
#t4s-notices__wrapper.is--show {
    transform: none;
}
#t4s-notices__wrapper .t4s-svg {
    fill: currentcolor;
    width: 18px;
    color: var(--t4s-light-color);
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 24px;
    opacity: 0;
    display: none;
}
#t4s-notices__wrapper[data-notices-status="warning"] .t4s-svg-triangle-exclamation,
#t4s-notices__wrapper[data-notices-status="error"] .t4s-svg-triangle-exclamation,
#t4s-notices__wrapper[data-notices-status="success"] .t4s-svg-circle-check {
    display: block;
}
#t4s-notices__wrapper button.t4s-notices__close {
    position: absolute;
    inset: 0px 0px 0px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    background-color: rgba(0, 0, 0, 0.1);
    font-weight: 500;
    font-size: 45px;
    cursor: pointer;
    transition: background-color 0.25s ease 0s;
}
#t4s-notices__wrapper .t4s-svg-close {
    stroke-width: 1.5px;
    opacity: 1;
    display: block;
}
.t4s-notices__progressbar {
    position: absolute;
    left: 0;
    top: 0;
    height: 4px;
    width: 100%;
    /* background-color: rgba(0, 0, 0, 0.1); */
    z-index: 1;
}
.t4s-notices__progressbar>span {
    display: block;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.25);
    border-radius: 0 0 3px 3px;
    animation-fill-mode: forwards;
    animation-timing-function: linear;
}
#t4s-notices__wrapper .t4s-notices__progressbar>span {
   animation-name: t4s-ani-w;
}

#t4s-notices__wrapper .t4s-notices__mess {
    opacity: 0;
}
#t4s-notices__wrapper.is--show .t4s-svg {
    animation: t4s-ani-revealIn 2s cubic-bezier(.25,1.6,.25,1) both;
}
#t4s-notices__wrapper.is--show .t4s-notices__mess {
    animation: t4s-ani-slideIn 1.35s cubic-bezier(.16,.81,.32,1) both;
}

@keyframes t4s-ani-w {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
@-webkit-keyframes t4s-ani-w {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

@keyframes t4s-ani-revealIn {
    0% {
        opacity: 0;
        transform: scale3d(.3,.3,1)
    }

    to {
        opacity: 1
    }
}
@-webkit-keyframes t4s-ani-revealIn {
    0% {
        opacity: 0;
        transform: scale3d(.3,.3,1)
    }

    to {
        opacity: 1
    }
}

@keyframes t4s-ani-slideIn {
    0% {
        opacity: 0;
        transform: translateX(50px)
    }

    to {
        opacity: 1;
        transform: translateX(0)
    }
}
@-webkit-keyframes t4s-ani-slideIn {
    0% {
        opacity: 0;
        transform: translateX(50px)
    }

    to {
        opacity: 1;
        transform: translateX(0)
    }
}

/* ==========================================================================
   20. OTHER CSS
   ========================================================================== */
.not--cookies .t4s-cookie-message {display: block}
.t4s-countdown-enabled {
    display: block;
}
.t4s-countdown-pr.expired_cdt4 { 
   display: block;
}
.t4s-drawer {
   position: fixed;
   top: 0;
   left: 0;
   visibility: hidden;
   width: calc(100vw - 65px);
   height: 100%;
   max-height: 100vh;
   max-height: none;
   z-index: 200;
   transition: transform 0.5s cubic-bezier(0.645, 0.045, 0.355, 1), visibility 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);
   background: var(--t4s-light-color);
   box-shadow: none;
   touch-action: manipulation;
   pointer-events: none;    
   display: flex;
   flex-direction: column;
   overflow: hidden;    
    -webkit-transform: translate3d(-104%,0,0);
    transform: translate3d(-104%,0,0);
}
/* .t4s-drawer__left {
   transform: translateX(calc(-100vw + 44px));
} */
.t4s-drawer__right {
   right: 0;
   left: auto;
    -webkit-transform: translate3d(104%,0,0);
    transform: translate3d(104%,0,0);
}
.rtl_true .t4s-drawer {
   right: 0;
   left: auto;
    -webkit-transform: translate3d(104%,0,0);
    transform: translate3d(104%,0,0);
}

.rtl_true .t4s-drawer__right {
   right: auto;
   left: 0;
    -webkit-transform: translate3d(-104%,0,0);
    transform: translate3d(-104%,0,0);
}

/* ==========================================================================
   21. PRODUCT ITEM HOVER
   ========================================================================== */
.t4s-product-img img {
    -webkit-transition: opacity .5s,-webkit-transform 2s cubic-bezier(0,0,.44,1.18);
    transition: opacity .5s,transform 2s cubic-bezier(0,0,.44,1.18),-webkit-transform 2s cubic-bezier(0,0,.44,1.18);
}
.t4s-product-hover-img {
    opacity: 0; 
    background-color: var(--t4s-light-color);
}
/* 
0. No animation
1. Opacity 
*/
.t4s-product-inner:hover .t4s-product-hover-img {
    opacity: 1;
}
.pr_img_effect_0 .t4s-product-img img {
    transition: none;
}

/* 2. Zoom */
.pr_img_effect_2 .t4s-product-inner:hover .t4s-product-hover-img {
    opacity: 1;
}
.pr_img_effect_2 .t4s-product-inner:hover .t4s-product-hover-img,
.pr_img_effect_2.enable_eff_img1_true .t4s-product-inner:hover .t4s-product-main-img {
    transform: scale(1.09);
    -webkit-transform: scale(1.09);
    opacity: 1;
}

/* 3. Move Bottom */
.pr_img_effect_3 .t4s-product-main-img {
    opacity: 1;
    -webkit-transition: -webkit-transform .4s ease;
    transition: transform .4s ease;
}
.pr_img_effect_3 .t4s-product-hover-img {
    -webkit-transform: translate(0,-100%);
    transform: translate(-0,-100%);
    -webkit-transition: -webkit-transform .4s ease;
    transition: transform .4s ease;
    opacity: 1;
}
.pr_img_effect_3 .t4s-product-inner:hover .is-show-img2 .t4s-product-main-img {
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
}
.pr_img_effect_3 .t4s-product-inner:hover .t4s-product-hover-img {
    -webkit-transform: translate(0,0);
    transform: translate(0,0);
}

/* 4. Flip Card */
.pr_img_effect_4 .t4s-product .t4s-product-inner {
    perspective: 1000px;
}
.pr_img_effect_4 .t4s-product-img {
  transition: transform 0.6s;
  transform-style: preserve-3d;
}
.pr_img_effect_4 .t4s-product-img img {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  opacity: 1;
}
.pr_img_effect_4 .t4s-product-inner:hover .t4s-product-img,
.pr_img_effect_4 .t4s-product-hover-img  {
  -webkit-transform: rotateY(180deg);
  transform: rotateY(180deg);
}
.pr_img_effect_4 .t4s-product-inner:hover .t4s-product-img:not(.is-show-img2) {
  -webkit-transform: rotateY(360deg);
  transform: rotateY(360deg);
}

/* 5. Rotate Zoom */
.pr_img_effect_5 .t4s-product-hover-img {
    transition:  opacity .5s, transform 1.4s cubic-bezier(0,0,.44,1.18),-webkit-transform 1.4s cubic-bezier(0,0,.44,1.18);
}
.pr_img_effect_5.enable_eff_img1_true .t4s-product-inner:hover .t4s-product-main-img,
.pr_img_effect_5 .t4s-product-inner:hover .t4s-product-hover-img {
    -webkit-transform: scale(1.25) rotate(10deg);
    transform: scale(1.25) rotate(10deg);
}

/* ==========================================================================
   1X. ECLIPSE TITLE
   ========================================================================== */
/* .t4s-product-title {
   overflow: hidden;
}
.t4s-product-title > a {
    white-space: nowrap;
    display: block;
}
.t4s-product-title > a:hover {
    transform: translateX(0);
    animation: 6s linear 2s 1 show_msg_first,12s linear 8s infinite show_msg;
    animation: 6s linear 2s 1 show_msg_first,12s linear 8s infinite show_msg;
}
@keyframes show_msg_first {
    from {
        transform: translateX(0)
    }

    to {
        transform: translateX(-100%)
    }
}

@keyframes show_msg {
    from {
        transform: translateX(100%)
    }

    to {
        transform: translateX(-100%)
    }
} */