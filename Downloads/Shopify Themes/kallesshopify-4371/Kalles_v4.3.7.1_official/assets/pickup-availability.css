.t4s-pickup-availability-popup {
    max-width: 570px;
    margin: 25px auto;
    width: 100%;
    background-color: var(--t4s-light-color);
}
.t4s-pickup-availability-information-container {
    margin-inline-start: 5px;
}
.t4s-pickup-availability-information__title strong {
    color: var(--secondary-color);
}

.t4s-pickup-availability-information__title, 
.t4s-pickup-availability-information__stock,
.t4s-product-pickup__variant,
.t4s-pickup-availability-list__address p:last-child,
.t4s-pickup-availability-list__phone {
    margin-bottom: 0;
}
.t4s-pickup-availability-small-text {
    font-size: 13px;
}

button.t4s-pickup-availability-information__button,
.t4s-pickup-availability-list__btn {
    text-decoration: underline;
    cursor: pointer;
    border: none;
    padding: 0;
    background: transparent;
    margin-top: 8px;
    color: var(--secondary-color);
}
.t4s-pickup-availability-list__btn:hover {
    text-decoration: underline;
}
.t4s-pickup-availability-container .t4s-icon-svg,
.t4s-pickup-availability-list .t4s-icon-svg {
    min-width: 12px;
    min-height: 12px;
    width: 12px;
    height: 12px;
    vertical-align: middle;
    display: inline-block;
    margin-top: 5px;
    margin-inline-end: 4px;
}
.t4s-pickup-availability-container .t4s-icon-svg path,
.t4s-pickup-availability-list .t4s-icon-svg path {
    fill: inherit;
    stroke: inherit;
}

.t4s-pickup-availability-container .t4s-icon-in-stock,
.t4s-pickup-availability-list .t4s-icon-in-stock {
    fill: var(--t4s-success-color);
}
.t4s-pickup-availability-container .t4s-icon-out-of-stock,
.t4s-pickup-availability-list .t4s-icon-out-of-stock {
    fill: var(--t4s-error-color);
}
.t4s-pickup-availability-list__stock .t4s-icon-svg {
    margin-top: 0;
    margin-inline-end: 2px;
    margin-bottom: 3px;
    margin-inline-start: 0;
}
.t4s-pickup-availability-list .t4s-icon-map {
    margin: 0;
    fill: currentcolor;
}
.t4s-pickup-availability__product-information,
.t4s-product-pickup {
    padding: 20px;
    background-color: rgba(var(--border-color-rgb),.3);
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
.t4s-product-pickup__img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    display: block;
    position: relative;
    margin-inline-end: 20px;
}
.t4s-product-pickup__img img {
    position: absolute;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}
.t4s-product-pickup__title {
    color: var(--secondary-color);
    font-weight: 600;
    font-size: 15px;
}
.t4s-product-pickup__price {
    color: var(--secondary-price-color);
}
.t4s-product-pickup__price ins{
    color: var(--primary-price-color);
}
.t4s-pickup-availability-list {
    padding: 20px;
    margin: 0;
    text-align: start;
}
.t4s-pickup-availability-list__location {
    font-size: 14px;
    margin-bottom: 8px;
}
.t4s-pickup-availability-list__stock {
    margin-top: 8px;
    margin-bottom: 16px;
}
.t4s-pickup-availability-list__address {
    font-size: 13px;
    font-style: normal;
    margin-bottom: 0;
}
.t4s-pickup-availability-list__item + .t4s-pickup-availability-list__item {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}