#t4s-login-sidebar .t4s-drawer__header > span,
.t4s-content-login-sidebar {
	display: none;
	font-weight: 500;
	line-height: 1.428;
	font-size: 16px;
}
#t4s-login-sidebar .t4s-drawer__header > span[aria-hidden=false],
.t4s-content-login-sidebar[aria-hidden=false] {
	display: block;
    -webkit-animation: 1.25s t4s-ani-fadeIn;
    animation: 1.25s t4s-ani-fadeIn;
}
#t4s-login-sidebar .t4s-drawer__header {
	padding: 5px 0 5px 20px;
	border-bottom: solid 1px var(--border-color);
}
#t4s-login-sidebar .t4s-drawer__close:hover {
	background-color: transparent;
}
#t4s-login-sidebar .t4s-drawer__close svg {
	transition: 0.5s ease 0s;
}
#t4s-login-sidebar .t4s-drawer__close:not(:hover) > svg {
    transform: rotate(-180deg);
}
/* ------------------------Custom CSS----------------------------- */
#t4s-login-sidebar .t4s-content-login-sidebar {
	padding: 20px;
	overflow-x: hidden;
	overflow-y: auto;
	font-size: 14px;
	font-weight: 400;
	color: var(--text-color);
}
#t4s-login-sidebar a {
	color: var(--link-color);
	text-decoration: underline;
}
#t4s-login-sidebar a:hover {
	color: var(--link-color-hover);
}
.t4s-btn-full-width {
	width: 100%;
}
#t4s-login-sidebar .t4s_field  {
	text-align: inherit;
}
#t4s-login-sidebar .t4s_field label {
    position: absolute;
    top: 0;
    left: 14px;
    transform: scale(1);
    transform-origin: left top;
    transition: transform .2s ease-in-out;
    pointer-events: none;
    display: flex;
    align-items: center;
    height: 100%
}

#t4s-login-sidebar .t4s_field input:-webkit-autofill,
#t4s-login-sidebar .t4s_field input:focus,
#t4s-login-sidebar .t4s_field input:not(:placeholder-shown) {
    padding: 18px 14px 6px
}

#t4s-login-sidebar .t4s_field input:-webkit-autofill~label,
#t4s-login-sidebar .t4s_field input:focus~label,
#t4s-login-sidebar .t4s_field input:not(:placeholder-shown)~label {
    transform: translateY(-6px) scale(.8)
}

#t4s-login-sidebar .t4s_fieldinput::-webkit-search-cancel-button,
.t4s_field__input::-webkit-search-cancel-button {
    display: none
}

#t4s-login-sidebar .t4s_field input::-webkit-autofill,
#t4s-login-sidebar .t4s_field input:::autofill {
    color: inherit
}

#t4s-login-sidebar .t4s_field input:-webkit-autofill {
    border-color: rgba(var(--text-color-rgb), .8);
    -webkit-box-shadow: 0 0 0 1000px #fff inset;
    -webkit-text-fill-color: inherit
}

#t4s-login-sidebar .t4s_field input::placeholder,
.t4s_field__input::placeholder {
    opacity: 0
}

