.t4s-checkbox-wrapper {
    width: 16px;
    height: 16px;
    min-width: 16px;
    border: 1px solid var(--border-color);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin-inline-end: 10px;
    border-radius: var(--t4s-other-radius);
}
.t4s-filter__values a {
    display: flex;
    transition: .3s;
}
.t4s-checkbox-wrapper svg {
    pointer-events: none;
    transform: scale(0);
    transition: .25s ease-in-out;
    width: 12px;
    height: 12px;
    color: var(--t4s-light-color);
}
.t4s-filter__values .is--selected>a>.t4s-checkbox-wrapper {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}
.t4s-filter__values .is--selected>a>.t4s-checkbox-wrapper svg {
    transform: scale(1);
}
.t4s-filter__values .is--disabled {
    opacity: .5;
    pointer-events: none;
}
/*! nouislider - 14.6.0 - 6/27/2020 */
.noUi-target,.noUi-target * {
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: none;
    -ms-touch-action: none;
    touch-action: none;
    -ms-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

.noUi-target {
    position: relative;
    background: #fafafa;
    border-radius: 4px;
    border: 1px solid #d3d3d3;
    box-shadow: inset 0 1px 1px #f0f0f0,0 3px 6px -5px #bbb
}

.noUi-base,.noUi-connects {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1
}

.noUi-connects {
    overflow: hidden;
    z-index: 0
}

.noUi-connect,.noUi-origin {
    will-change: transform;
    position: absolute;
    z-index: 1;
    top: 0;
    right: 0;
    transform-origin: 0 0;
    -webkit-transform-style: preserve-3d;
    transform-style: flat
}

.noUi-origin {
    height: 10%;
    width: 10%
}

/* .rtl_true .noUi-rtl.noUi-horizontal {
    direction: ltr
} */

.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin {
    left: 0;
    right: auto
}

.noUi-vertical .noUi-origin {
    width: 0
}

.noUi-horizontal .noUi-origin {
    height: 0
}

.noUi-touch-area {
    height: 100%;
    width: 100%
}

.noUi-state-tap .noUi-connect,.noUi-state-tap .noUi-origin {
    transition: transform .3s
}

.noUi-state-drag * {
    cursor: inherit!important
}

.noUi-horizontal {
    height: 18px
}

.noUi-vertical {
    width: 18px
}

.noUi-vertical .noUi-handle {
    width: 28px;
    height: 34px;
    right: -6px;
    top: -17px
}

.noUi-txt-dir-rtl.noUi-horizontal .noUi-handle {
    left: -17px;
    right: auto
}

.noUi-draggable {
    cursor: ew-resize
}

.noUi-vertical .noUi-draggable {
    cursor: ns-resize
}

.noUi-handle {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    position: absolute;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    background: #fff;
    cursor: default;
    box-shadow: inset 0 0 1px #fff,inset 0 1px 7px #ebebeb,0 3px 6px -3px #bbb
}

.noUi-active {
    box-shadow: inset 0 0 1px #fff,inset 0 1px 7px #ddd,0 3px 6px -3px #bbb
}

.noUi-handle:after,.noUi-handle:before {
    content: "";
    display: block;
    position: absolute;
    left: 14px
}

.noUi-vertical .noUi-handle:after,.noUi-vertical .noUi-handle:before {
    width: 14px;
    height: 1px;
    left: 6px;
    top: 14px
}

.noUi-vertical .noUi-handle:after {
    top: 17px
}

[disabled] .noUi-connect {
    background: #b8b8b8
}

[disabled] .noUi-handle,[disabled].noUi-handle,[disabled].noUi-target {
    cursor: not-allowed
}

.noUi-pips,.noUi-pips * {
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

.noUi-pips {
    position: absolute;
    color: #999
}

.noUi-value {
    position: absolute;
    white-space: nowrap;
    text-align: center
}

.noUi-value-sub {
    color: #ccc;
    font-size: 10px
}

.noUi-marker {
    position: absolute;
    background: #ccc
}

.noUi-marker-large,.noUi-marker-sub {
    background: #aaa
}

.noUi-pips-horizontal {
    padding: 10px 0;
    height: 80px;
    top: 100%;
    left: 0;
    width: 100%
}

.noUi-value-horizontal {
    transform: translate(-50%,50%)
}

.noUi-rtl .noUi-value-horizontal {
    transform: translate(50%,50%)
}

.noUi-marker-horizontal.noUi-marker {
    margin-left: -1px;
    width: 2px;
    height: 5px
}

.noUi-marker-horizontal.noUi-marker-sub {
    height: 10px
}

.noUi-marker-horizontal.noUi-marker-large {
    height: 15px
}

.noUi-pips-vertical {
    padding: 0 10px;
    height: 100%;
    top: 0;
    left: 100%
}

.noUi-value-vertical {
    transform: translate(0,-50%);
    padding-left: 25px
}

.noUi-rtl .noUi-value-vertical {
    transform: translate(0,50%)
}

.noUi-marker-vertical.noUi-marker {
    width: 5px;
    height: 2px;
    margin-top: -1px
}

.noUi-marker-vertical.noUi-marker-sub {
    width: 10px
}

.noUi-marker-vertical.noUi-marker-large {
    width: 15px
}

.noUi-tooltip {
    position: absolute;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    background: #fff;
    color: #000;
    padding: 5px;
    text-align: center;
    white-space: nowrap;
    display: none
}

.noUi-horizontal .noUi-tooltip {
    transform: translate(-50%,0);
    left: 50%;
    bottom: 120%
}

.noUi-vertical .noUi-tooltip {
    transform: translate(0,-50%);
    top: 50%;
    right: 120%
}

.noUi-horizontal .noUi-origin>.noUi-tooltip {
    transform: translate(50%,0);
    left: auto;
    bottom: 10px
}

.noUi-vertical .noUi-origin>.noUi-tooltip {
    transform: translate(0,-18px);
    top: auto;
    right: 28px
}

.price_steps_slider {
    width: 100%;
    margin-top: -1px;
    height: 2px;
    background-color: transparent;
    border: 0;
    box-shadow: none;
    margin-bottom: 20px
}

.noUi-connects {
    border-radius: 0;
    height: 2px;
    background-color: var(--border-color);
}

.noUi-connect {
    height: 2px;
    width: 100%;
    background: var(--accent-color);
}

.noUi-horizontal .noUi-handle {
    width: 34px;
    height: 28px;
    right: -17px;
    top: -11px;
    outline: 0;
    border: 0;
    box-shadow: none;
    border-radius: 0;
    background-color: transparent;
    cursor: ew-resize
}

.noUi-handle:after,.noUi-handle:before {
    background: var(--accent-color);
    top: 4px;
    width: 2px;
    height: 15px
}

.noUi-handle:after {
    left: 16px
}
.t4s-price_slider_amount .t4s-price_label {
    order: 1;
    margin-bottom: 10px;
    color: var(--text-color);
    font-size: 14px;
    display: block;
    width: 100%;
}
.t4s-price_slider_amount .t4s-price_label > span {
    color: var(--secondary-color);
    font-weight: 600;
}
.t4s-price_slider_amount .t4s-price_slider_btn {
    order: 2;
    margin-bottom: 10px;
    padding: 10px 14px;
    font-size: 12px;
    line-height: 16px;
    text-transform: uppercase;
    text-shadow: none;
    letter-spacing: .3px;
    font-weight: 600;
    min-width: 100px;
    border-radius: 5px;
    display: inline-block;
    vertical-align: top;
    color: var(--button-background);
    background-color: transparent;
    border: solid 2px var(--button-background); 
}
.t4s-price_slider_amount .t4s-price_slider_btn:hover {
    background-color: var(--button-background-hover);
    border-color: var(--button-background-hover);
    color: var(--button-color-hover);
}

.is--blockid_price .t4s-price_slider {
    margin-bottom: 20px;
}
.is--blockid_price .t4s-price_steps_slider {
    width: 100%;
    margin-top: -1px;
    height: 2px;
    background-color: transparent;
    border: 0;
    box-shadow: none;
    margin-bottom: 20px;
}
.is--blockid_price .t4s-price_slider_amount {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: -10px
}

.is--blockid_price .t4s-price_slider_amount .t4s-price_slider_btn {
    order: 2;
    margin-bottom: 10px;
    padding: 10px 14px;
    font-size: 12px;
    line-height: 16px;
    text-transform: uppercase;
    text-shadow: none;
    letter-spacing: .3px;
    font-weight: 600;
    min-width: 100px;
    border-radius: var(--btn-radius);
}
.t4s-price_label .t4s-from, .t4s-price_label .t4s-to {
    display: inline-block;
}

.t4s-filter-hidden.t4s-drawer .t4s-drawer__header span,
.t4s-filter-hidden.t4s-drawer .t4s-drawer__header button.t4s-drawer__close {
    font-size: 16px;
    font-weight: 500;
    background-color: transparent;
}
.t4s-filter-hidden.t4s-drawer .t4s-drawer__close svg {
    transition: 0.5s ease 0s;
}
.t4s-filter-hidden.t4s-drawer .t4s-drawer__close:not(:hover) > svg {
    transform: rotate(-180deg);
}
.t4s-filter-hidden.t4s-drawer .t4s-facets__form > div {
    padding: 20px;
    border-bottom: solid 1px var(--border-color);
}
.t4s-facets__form .t4s-facet .t4s-facet-title {
    font-size: 16px;
    margin-top: 0;
    margin-bottom: 0;
    font-weight: 500;
    position: relative;
    padding-bottom: 20px;
}
.t4s-facets__form .t4s-facet .t4s-facet-title::after {
    content: "";
    width: 60px;
    height: 2px;
    border-bottom: solid 2px;
    left: 0;
    bottom: 15px;
    position: absolute;
}
.t4s-facets__form .t4s-facet .t4s-facet-content .t4s-current-scrollbar {
    max-height: 250px;
    list-style: none;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
}
.t4s-facets__form .t4s-facet ul {
    padding: 0;
    margin: 0;
}
.t4s-facets__form .t4s-facet ul li {
    line-height: 25px;
    list-style: none;
    margin-bottom: 8px;
}
.t4s-facets__form .t4s-facet ul li a {
    text-transform: capitalize;
    display: flex;
    align-items: center;
    color: var(--secondary-color);
}
.t4s-facets__form .t4s-facet ul li a:hover,
.t4s-facets__form .t4s-facet ul.is--style-color li.is--selected a {
    color: var(--accent-color);
}
/* css style checkbox */
.is--style-checkbox {

}

/* css style tag */
.is--style-tag {

}
.t4s-facets__form .t4s-facet .is--style-tag li {
    display: inline-block;
    vertical-align: top;
}
.t4s-facets__form .t4s-facet .is--style-tag a {
    border: 1px solid var(--secondary-color);
    border-radius: var(--btn-radius);
    color: var(--secondary-color);
    display: inline-block;
    font-size: 13px;
    margin: 0 5px 0 0;
    padding: 2px 15px 1px;
}
.t4s-facets__form .t4s-facet .is--style-tag a:hover,
.t4s-facets__form .t4s-facet .is--style-tag .is--selected a {
    color: var(--t4s-light-color);
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}
/* css style color */
.is--style-color {

}
.t4s-facets__form .t4s-facet .is--style-color .t4s-filter_color {
    width: 24px;
    height: 24px;
    display: inline-block;
    border-radius: 50%;
    margin-right: 5px;
    text-align: center;
    line-height: 24px;
    border: 1px solid transparent;
    width: auto;
    height: auto;
    padding: 2px;
    position: relative;
}
.t4s-facets__form .t4s-facet .is--style-color .is--selected .t4s-filter_color,
.t4s-facets__form .t4s-facet .is--style-color a:hover .t4s-filter_color {
    border-color: var(--border-color);
}
.t4s-facets__form .t4s-facet .is--style-color .t4s-filter_color > span {
    width: 26px;
    height: 26px;
    display: block;
    border-radius: 50%;
    position: relative;
    background: var(--swatch--background, var(--t4s-bg-color));
    background-position: var(--swatch-focal-point, center);
    background-size: cover;
}
.t4s-facets__form .t4s-facet .is--style-color .t4s-filter_color > span::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    box-shadow: none;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50%;
    transition: .2s ease-in-out;
}
.t4s-facets__form .t4s-facet .is--style-color .t4s-filter_color > svg {
    pointer-events: none;
    transform: scale(0);
    transition: .25s ease-in-out;
    width: 12px;
    height: 12px;
    color: var(--t4s-light-color);
    position: absolute;
    margin: auto;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
.t4s-facets__form .t4s-facet .is--style-color .is--selected .t4s-filter_color > svg {
    transform: scale(1);
}

.t4s-facets__form .t4s-facet .is--style-default .is--selected a {
color: var(--accent-color);
}
.t4s-filter-hidden.t4s-drawer .t4s-drawer__bottom {
    padding: 15px 20px;
}
.t4s-toolbart-filter.t4s-toolbar-item{
    display: block;
}
.t4s-filter-hidden.t4s-drawer .t4s-value-count,
.t4s-sidebar .t4s-widget .t4s-value-count {
    margin: 0 5px;
}
.t4s-filter-area {
    display: none;
}
.t4s-filter-area {
    padding: 30px;
    margin: 30px 0 40px;
    border-radius: 5px;
    border: 1px solid var(--border-color);
    box-shadow: 0 0 10px rgb(0 0 0 / 15%);
}
.t4s-filter-area .t4s-facets__form {
     --ts-gutter-y: 20px;
     --ts-gutter-x: 15px;
}
@media (min-width: 768px) {
/*     .t4s-filter-area {
        padding: 30px;
        margin: 30px 0 40px;
        border-radius: 5px;
        border: 1px solid var(--border-color);
        box-shadow: 0 0 10px rgb(0 0 0 / 15%);
    } */
    .t4s-filter-area .t4s-facets__form {
         --ts-gutter-y: 30px;
         --ts-gutter-x: 30px;
    }
    .t4s-filter-area .t4s-facets__form>.t4s-col-item {
        width: 25%
    }
}