.t4s-col-inner {
  padding: var(--pd);
  background-color: var(--bg-cl);
}
.t4s-has-imgbg {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
.t4s-text-bl {
  font-size: var(--text-fs);
  font-weight: var(--text-fw);
  line-height: var(--text-lh);
  letter-spacing: var(--text-ls);
  font-style: var(--text-fonts);
  color: var(--text-cl);
  margin-bottom: var(--text-mgb);
}
.t4s-font-italic-true {
  font-style: italic !important;
}
.t4s-text-shadow-true {
  text-shadow: 0 0 4px rgba(0, 0, 0, 0.4);
}
.t4s-img-child::before,
.t4s-img-child::after {
  pointer-events: none;
}
.t4s-img-child,
.custom-menu,
.socials-block,
.t4s-newsletter-parent {
  margin-bottom: var(--mgb);
}
.t4s-img-child img {
  width: 100%;
  max-width: var(--width);
  display: inline-block;
  vertical-align: top;
}
.t4s-space-html {
  height: var(--height);
  margin-bottom: var(--mgb);
  vertical-align: top;
}
.t4s-space-html::before {
  content: "";
  display: inline-block;
  max-width: 100%;
  background-color: var(--color);
  width: var(--width);
  height: var(--height);
  vertical-align: top;
}
.t4s-custom-label {
  font-size: var(--text-fs);
  font-weight: var(--text-fw);
  line-height: var(--text-lh);
  letter-spacing: var(--text-ls);
  font-style: var(--text-fonts);
  color: var(--label-cl);
  background-color: var(--label-bg-cl);
  margin-bottom: var(--mgb);
  padding: 0 var(--pd-lr);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.t4s-custom-label[style*="--text-lh:0px;"] {
  line-height: 1;
}
@media (min-width: 768px) and (max-width: 1024px) {
  .t4s-space-html {
    height: var(--height-tl);
    margin-bottom: var(--mgb-tl);
  }
  .t4s-space-html::before {
    background-color: var(--color);
    width: var(--width-tl);
    height: var(--height-tl);
  }
  .t4s-hidden-tablet-true {
    display: none !important;
  }
  .t4s-col-inner {
    padding: var(--pd-tb);
  }
}
@media (max-width: 767px) {
  .t4s-col-inner {
    padding: var(--pd-mb);
  }
  .t4s-text-bl {
    font-size: var(--text-fs-mb);
    line-height: var(--text-lh-mb);
    letter-spacing: var(--text-ls-mb);
    margin-bottom: var(--text-mgb-mb);
  }
  .t4s-hidden-mobile-true {
    display: none !important;
  }
  .t4s-br-mb-true br {
    display: none !important;
  }

  .t4s-img-child,
  .custom-menu,
  .socials-block,
  .t4s-newsletter-parent,
  .t4s-custom-label {
    margin-bottom: var(--mgb-mb);
  }
  .t4s-space-html {
    height: var(--height-mb);
    margin-bottom: var(--mgb-mb);
  }
  .t4s-space-html::before {
    background-color: var(--color);
    width: var(--width-mb);
    height: var(--height-mb);
  }
  .t4s-img-child img {
    max-width: var(--width-mb);
  }
}
