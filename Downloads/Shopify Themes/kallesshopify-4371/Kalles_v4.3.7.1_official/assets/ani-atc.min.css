.is--animated,.is--infinite{--duration-time:1s}.is--animated{-webkit-animation-duration:var(--duration-time);-webkit-animation-fill-mode:both;animation-duration:var(--duration-time);animation-fill-mode:both}.is--infinite{animation-iteration-count:infinite;animation-duration:var(--duration-time)}.is--infinite:not(.t4s-ani-fadeIn){--duration-time:2s}@media print,(prefers-reduced-motion:reduce){.is--animated{-webkit-animation-duration:1ms!important;animation-duration:1ms!important;-webkit-transition-duration:1ms!important;transition-duration:1ms!important;-webkit-animation-iteration-count:1!important;animation-iteration-count:1!important }}.t4s-browser-Safari .is--animated.t4s-ani-swing{-webkit-transition-duration:1ms!important;transition-duration:1ms!important}@keyframes t4s-ani-bounce{20%,53%,80%,from,to{-webkit-animation-timing-function:cubic-bezier(0.215,0.61,0.355,1);animation-timing-function:cubic-bezier(0.215,0.61,0.355,1);-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}40%,43%{-webkit-animation-timing-function:cubic-bezier(0.755,0.05,0.855,0.06);animation-timing-function:cubic-bezier(0.755,0.05,0.855,0.06);-webkit-transform:translate3d(0,-30px,0);transform:translate3d(0,-30px,0)}70%{-webkit-animation-timing-function:cubic-bezier(0.755,0.05,0.855,0.06);animation-timing-function:cubic-bezier(0.755,0.05,0.855,0.06);-webkit-transform:translate3d(0,-15px,0);transform:translate3d(0,-15px,0)}90%{-webkit-transform:translate3d(0,-4px,0);transform:translate3d(0,-4px,0) }}.t4s-ani-bounce{-webkit-animation-name:t4s-ani-bounce;animation-name:t4s-ani-bounce;-webkit-transform-origin:center bottom;transform-origin:center bottom}@-webkit-keyframes t4s-ani-tada{from,to{-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}10%,20%{-webkit-transform:scale3d(.9,.9,.9) rotate3d(0,0,1,-3deg);transform:scale3d(.9,.9,.9) rotate3d(0,0,1,-3deg)}30%,50%,70%,90%{-webkit-transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg);transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg)}40%,60%,80%{-webkit-transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg);transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg) }}@keyframes t4s-ani-tada{from,to{-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1)}10%,20%{-webkit-transform:scale3d(.9,.9,.9) rotate3d(0,0,1,-3deg);transform:scale3d(.9,.9,.9) rotate3d(0,0,1,-3deg)}30%,50%,70%,90%{-webkit-transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg);transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg)}40%,60%,80%{-webkit-transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg);transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg) }}.t4s-ani-tada{-webkit-animation-name:t4s-ani-tada;animation-name:t4s-ani-tada}@-webkit-keyframes t4s-ani-swing{20%{-webkit-transform:rotate3d(0,0,1,15deg);transform:rotate3d(0,0,1,15deg)}40%{-webkit-transform:rotate3d(0,0,1,-10deg);transform:rotate3d(0,0,1,-10deg)}60%{-webkit-transform:rotate3d(0,0,1,5deg);transform:rotate3d(0,0,1,5deg)}80%{-webkit-transform:rotate3d(0,0,1,-5deg);transform:rotate3d(0,0,1,-5deg)}to{-webkit-transform:rotate3d(0,0,1,0deg);transform:rotate3d(0,0,1,0deg) }}@keyframes t4s-ani-swing{20%{-webkit-transform:rotate3d(0,0,1,15deg);transform:rotate3d(0,0,1,15deg)}40%{-webkit-transform:rotate3d(0,0,1,-10deg);transform:rotate3d(0,0,1,-10deg)}60%{-webkit-transform:rotate3d(0,0,1,5deg);transform:rotate3d(0,0,1,5deg)}80%{-webkit-transform:rotate3d(0,0,1,-5deg);transform:rotate3d(0,0,1,-5deg)}to{-webkit-transform:rotate3d(0,0,1,0deg);transform:rotate3d(0,0,1,0deg) }}.t4s-ani-swing{-webkit-transform-origin:top center;transform-origin:top center;-webkit-animation-name:t4s-ani-swing;animation-name:t4s-ani-swing}@-webkit-keyframes t4s-ani-flash{50%,from,to{opacity:1}25%,75%{opacity:0 }}@keyframes t4s-ani-flash{50%,from,to{opacity:1}25%,75%{opacity:0 }}.t4s-ani-flash{-webkit-animation-name:t4s-ani-flash;animation-name:t4s-ani-flash}@-webkit-keyframes t4s-ani-fadeIn{from{opacity:0}to{opacity:1 }}@keyframes t4s-ani-fadeIn{from{opacity:0}to{opacity:1 }}.t4s-ani-fadeIn{-webkit-animation-name:t4s-ani-fadeIn;animation-name:t4s-ani-fadeIn}@-webkit-keyframes t4s-ani-heartBeat{0%,28%,70%{-webkit-transform:scale(1);transform:scale(1)}14%,42%{-webkit-transform:scale(1.3);transform:scale(1.3) }}@keyframes t4s-ani-heartBeat{0%,28%,70%{-webkit-transform:scale(1);transform:scale(1)}14%,42%{-webkit-transform:scale(1.3);transform:scale(1.3) }}.t4s-ani-heartBeat{-webkit-animation-name:t4s-ani-heartBeat;animation-name:t4s-ani-heartBeat;-webkit-animation-duration:1.3s;animation-duration:1.3s;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}@-webkit-keyframes t4s-ani-shake{from,to{-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}10%,30%,50%,70%,90%{-webkit-transform:translate3d(-10px,0,0);transform:translate3d(-10px,0,0)}20%,40%,60%,80%{-webkit-transform:translate3d(10px,0,0);transform:translate3d(10px,0,0) }}@keyframes t4s-ani-shake{from,to{-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}10%,30%,50%,70%,90%{-webkit-transform:translate3d(-10px,0,0);transform:translate3d(-10px,0,0)}20%,40%,60%,80%{-webkit-transform:translate3d(10px,0,0);transform:translate3d(10px,0,0) }}.t4s-ani-shake{-webkit-animation-name:t4s-ani-shake;animation-name:t4s-ani-shake}