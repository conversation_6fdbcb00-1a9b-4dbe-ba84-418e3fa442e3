!function(t){"use strict";var e=t("body"),i=T4SThemeSP.cacheNameFirst,s=!!("ontouchstart"in window||window.DocumentTouch&&window.document instanceof DocumentTouch||window.navigator.maxTouchPoints||window.navigator.msMaxTouchPoints),n="has--search-terms",r="data-key",h=T4Sroutes.search_url;(async function(){const t=await fetch(window.Shopify.routes.root+"search/suggest?q=*&section_id=search-hidden&resources[type]=page");return!!t.ok||"417"!=t.status})().then((t=>{t&&(h=T4Sroutes.predictive_search_url)}));var o=function(){function i(e){this.container=e,this.$container=t(e),this.deferRequestBy=300,this.cachedResults={},this.$form=this.$container.find("[data-frm-search]"),this.form=this.$form[0],this.$input=this.$container.find("[data-input-search]"),this.input=this.$input[0],this.$results=this.$container.find("[data-results-search]"),this.results=this.$results[0],this.$skeleton=this.$container.find("[data-skeleton-search]"),this.$formListkey=this.$form.find("[data-listkey-search]"),this.$listSuggest=this.$form.find("[data-listsuggest-search]"),this.listSuggest=this.$listSuggest[0],this.isOpen=!1,this.sectionID=this.$container.attr("data-sid")||"predictive-search",this.$title=this.$container.find("[data-title-search]"),this.title=this.$title[0],this.$viewall=this.$container.find("[data-viewAll-search]"),this.viewall=this.$viewall[0],this.$select=this.$container.find("[data-cat-search]>select"),this.select=this.$select[0],this.currentVal=this.$select.val()||"*",this.hasClassTerms=!1,this.searchUrlForm=this.$form.serialize(),this.setupEventListeners(),this.onClickKey()}return i.prototype=Object.assign({},i.prototype,{setupEventListeners:function(){this.form.addEventListener("submit",this.onFormSubmit.bind(this));this.form.querySelector('[type="submit"]')||this.$form.append('<button type="submit" class="t4s-d-none"></button>'),this.input.addEventListener("input",this.debounce((t=>{this.onChange(t)}),this.deferRequestBy).bind(this)),this.input.addEventListener("focus",this.onFocus.bind(this)),this.select&&(this.select.addEventListener("change",this.onChangeSelectCat.bind(this)),this.$input.after('<input type="search" data-input-q name="q" value="" class="t4s-mini-search__input t4s-d-none">'),this.input.removeAttribute("name"),this.$input_q=this.$container.find("[data-input-q]"),this.input.addEventListener("input",(t=>{this.$input_q.val(this.getQuery())}))),this.container.addEventListener("focusout",this.onFocusOut.bind(this)),this.container.addEventListener("keyup",this.onKeyup.bind(this)),this.container.addEventListener("keydown",this.onKeydown.bind(this))},debounce:function(t,e){let i;return(...s)=>{clearTimeout(i),i=setTimeout((()=>t.apply(this,s)),e)}},getQuery:function(){var t=this.input.value.trim();if(this.select){var e=this.currentVal.trim();"*"!=e&&(t=""!==t?`product_type:${e} AND ${t}`:`product_type:${e}`)}return t},onChange:function(){const t=this.getQuery();if(!t.length)return this.close(!0),this.hasClassTerms&&(this.form.classList.remove(n),this.hasClassTerms=!1),this.$results.hide(),this.$viewall.hide(),void this.$skeletonElement.hide();this.getSearchResults(t),this.hasClassTerms||(this.form.classList.add(n),this.hasClassTerms=!0)},onFormSubmit:function(t){const e=this.getQuery().length;(!e||e&&this.container.querySelector('[aria-selected="true"] a'))&&t.preventDefault()},onFocus:function(){const t=this.getQuery();t.length&&("true"===this.container.getAttribute("results")?this.open():this.getSearchResults(t))},onChangeSelectCat:function(){this.currentVal=this.$select.val();const t=this.getQuery();this.getSearchResults(t)},onClickKey:function(){var e,i=this;i.$container.find("[data-listKey]").on("click",`[${r}]`,(function(n){n.preventDefault();var h=t(this);s?i.$input.val(h.attr(r)):(i.$input.val(h.attr(r)).focus(),i.$container.addClass("showing--results"),clearTimeout(e),e=setTimeout((function(){i.$container.removeClass("showing--results")}),1500)),i.input.dispatchEvent(new Event("input",{bubbles:!0}))})),i.$form.on("click","[data-clear-search]",(function(t){t.preventDefault(),i.$input.val(""),i.input.dispatchEvent(new Event("input",{bubbles:!0}))})),i.$container.on("opendDrawer",(function(e){s||t(this).one("transitionend webkitTransitionEnd oTransitionEnd",(function(){i.$input.focus()}))}))},onFocusOut:function(){setTimeout((()=>{this.container.contains(document.activeElement)||this.close()}))},onKeyup:function(t){switch(this.getQuery().length||this.close(!0),t.preventDefault(),t.code){case"ArrowUp":this.switchOption("up");break;case"ArrowDown":this.switchOption("down")}},onKeydown:function(t){"ArrowUp"!==t.code&&"ArrowDown"!==t.code||t.preventDefault()},switchOption:function(t){if(!this.container.getAttribute("open"))return;const e="up"===t,i=this.results.querySelector('[aria-selected="true"]'),s=this.results.querySelectorAll("li");let n=this.results.querySelector("li");e&&!i||(!e&&i?n=i.nextElementSibling||s[0]:e&&(n=i.previousElementSibling||s[s.length-1]),n!==i&&(n.setAttribute("aria-selected",!0),i&&i.setAttribute("aria-selected",!1),this.setLiveRegionText(n.textContent),this.input.setAttribute("aria-activedescendant",n.id)))},getSearchResults:function(t){if(!t.length)return this.$skeletonElement.hide(),this.$results.hide().html(""),this.$viewall.hide(),this.$formListkey.hide(),this.$title.hide(),void this.container.removeAttribute("loading");const e=t.replace(" ","-").toLowerCase();this.setLiveRegionLoadingState(),this.cachedResults[e]?this.renderSearchResults(this.cachedResults[e]):fetch(`${h}/?${this.searchUrlForm}${encodeURIComponent(t)}&section_id=${this.sectionID}`).then((t=>{if(!t.ok){var e=new Error(t.status);throw this.close(),e}return t.text()})).then((t=>{const i=(new DOMParser).parseFromString(t,"text/html").querySelector(`#shopify-section-${this.sectionID}`).innerHTML;this.cachedResults[e]=i,this.renderSearchResults(i)})).catch((t=>{throw this.close(),t}))},setLiveRegionLoadingState:function(){this.$skeletonElement=this.statusElement||this.$skeleton,this.$skeletonElement.show(),this.$results.hide(),this.$formListkey.hide(),this.$viewall.hide(),this.container.setAttribute("loading",!0)},renderSearchResults:function(t){var i=(new DOMParser).parseFromString(t,"text/html");this.title&&(this.title.innerHTML=i.querySelector("[data-title-search]").innerHTML),this.results&&(this.results.innerHTML=i.querySelector("[data-results-search]").innerHTML),this.listSuggest&&(this.listSuggest.innerHTML=i.querySelector("[data-listsuggest-search]")?.innerHTML);try{this.viewall.innerHTML=i.querySelector("[data-viewAll-search]").innerHTML}catch(t){}"function"==typeof T4SThemeSP.reinitProductGridItem&&T4SThemeSP.reinitProductGridItem(),e.trigger("currency:update"),this.container.setAttribute("results",!0),this.setLiveRegionResults(),this.open()},setLiveRegionResults:function(){this.$skeletonElement.hide(),this.$results.show(),this.$viewall.show(),this.$formListkey.show(),this.$title.show(),this.container.removeAttribute("loading")},getResultsMaxHeight:function(){return this.resultsMaxHeight=window.innerHeight-document.querySelector(".t4s-section-header").getBoundingClientRect().bottom,this.resultsMaxHeight},open:function(){this.results.style.maxHeight=this.resultsMaxHeight||`${this.getResultsMaxHeight()}px`,this.container.setAttribute("open",!0),this.input.setAttribute("aria-expanded",!0),this.isOpen=!0},close:function(t=!1){t&&(this.input.value="",this.container.removeAttribute("results"));const e=this.container.querySelector('[aria-selected="true"]');e&&e.setAttribute("aria-selected",!1),this.input.setAttribute("aria-activedescendant",""),this.container.removeAttribute("open"),this.input.setAttribute("aria-expanded",!1),this.resultsMaxHeight=!1,this.results.removeAttribute("style"),this.isOpen=!1}}),i}();T4SThemeSP.predictiveSearchInt=function(){var e,s,n,r;function h(){t("[data-predictive-search]:not(is--inted)").each((function(){t(this).addClass("is--inted"),this.predictiveSearch=new o(this)}))}e="timeSearchT4s"+i,s="dataSearchT4s"+i,n=isStorageSpSession&&sessionStorage.getItem(e)||0,n=parseInt(n),r=t("#t4s-search-hidden"),n>0&&n>=Date.now()?T4SThemeSP.Helpers.promiseStylesheet(T4Sconfigs.stylesheet4).then((function(){r.html(sessionStorage.getItem(s)),h(),"function"==typeof T4SThemeSP.reinitProductGridItem&&T4SThemeSP.reinitProductGridItem()})):T4SThemeSP.getToFetchSection("?section_id=search-hidden").then((i=>{"NVT_94"!=i&&T4SThemeSP.Helpers.promiseStylesheet(T4Sconfigs.stylesheet4).then((function(){r.html(t(i).html()),h(),"function"==typeof T4SThemeSP.reinitProductGridItem&&T4SThemeSP.reinitProductGridItem(),isStorageSpSession&&(n=Date.now()+24e6,sessionStorage.setItem(e,n),sessionStorage.setItem(s,t(i).html()))}))}))}}(jQuery_T4NT),jQuery_T4NT(document).ready((function(t){T4SThemeSP.predictiveSearchInt()}));