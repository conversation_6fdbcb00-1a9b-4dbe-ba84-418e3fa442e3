!1 in window&&function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.WHATWGFetch={})}(this,function(t){"use strict";var e="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof self&&self||void 0!==e&&e,n={searchParams:"URLSearchParams"in e,iterable:"Symbol"in e&&"iterator"in Symbol,blob:"FileReader"in e&&"Blob"in e&&function(){try{return new Blob,!0}catch(t){return!1 }}(),formData:"FormData"in e,arrayBuffer:"ArrayBuffer"in e};if(n.arrayBuffer)var r=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],o=ArrayBuffer.isView||function(t){return t&&r.indexOf(Object.prototype.toString.call(t))>-1};function i(t){if("string"!=typeof t&&(t=String(t)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(t)||""===t)throw new TypeError("Invalid character in header field name");return t.toLowerCase()}function s(t){return"string"!=typeof t&&(t=String(t)),t}function a(t){var e={next:function(){var e=t.shift();return{done:void 0===e,value:e }}};return n.iterable&&(e[Symbol.iterator]=function(){return e}),e}function c(t){this.map={},t instanceof c?t.forEach(function(t,e){this.append(e,t)},this):Array.isArray(t)?t.forEach(function(t){this.append(t[0],t[1])},this):t&&Object.getOwnPropertyNames(t).forEach(function(e){this.append(e,t[e])},this)}function u(t){if(t.bodyUsed)return Promise.reject(new TypeError("Already read"));t.bodyUsed=!0}function h(t){return new Promise(function(e,n){t.onload=function(){e(t.result)},t.onerror=function(){n(t.error) }})}function f(t){var e=new FileReader,n=h(e);return e.readAsArrayBuffer(t),n}function l(t){if(t.slice)return t.slice(0);var e=new Uint8Array(t.byteLength);return e.set(new Uint8Array(t)),e.buffer}function d(){return this.bodyUsed=!1,this._initBody=function(t){var e;this.bodyUsed=this.bodyUsed,this._bodyInit=t,t?"string"==typeof t?this._bodyText=t:n.blob&&Blob.prototype.isPrototypeOf(t)?this._bodyBlob=t:n.formData&&FormData.prototype.isPrototypeOf(t)?this._bodyFormData=t:n.searchParams&&URLSearchParams.prototype.isPrototypeOf(t)?this._bodyText=t.toString():n.arrayBuffer&&n.blob&&(e=t)&&DataView.prototype.isPrototypeOf(e)?(this._bodyArrayBuffer=l(t.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):n.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(t)||o(t))?this._bodyArrayBuffer=l(t):this._bodyText=t=Object.prototype.toString.call(t):this._bodyText="",this.headers.get("content-type")||("string"==typeof t?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):n.searchParams&&URLSearchParams.prototype.isPrototypeOf(t)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},n.blob&&(this.blob=function(){var t=u(this);if(t)return t;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this._bodyArrayBuffer?u(this)||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer)):this.blob().then(f)}),this.text=function(){var t,e,n,r=u(this);if(r)return r;if(this._bodyBlob)return t=this._bodyBlob,n=h(e=new FileReader),e.readAsText(t),n;if(this._bodyArrayBuffer)return Promise.resolve(function(t){for(var e=new Uint8Array(t),n=new Array(e.length),r=0;r<e.length;r++)n[r]=String.fromCharCode(e[r]);return n.join("")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},n.formData&&(this.formData=function(){return this.text().then(y)}),this.json=function(){return this.text().then(JSON.parse)},this}c.prototype.append=function(t,e){t=i(t),e=s(e);var n=this.map[t];this.map[t]=n?n+", "+e:e},c.prototype.delete=function(t){delete this.map[i(t)]},c.prototype.get=function(t){return t=i(t),this.has(t)?this.map[t]:null},c.prototype.has=function(t){return this.map.hasOwnProperty(i(t))},c.prototype.set=function(t,e){this.map[i(t)]=s(e)},c.prototype.forEach=function(t,e){for(var n in this.map)this.map.hasOwnProperty(n)&&t.call(e,this.map[n],n,this)},c.prototype.keys=function(){var t=[];return this.forEach(function(e,n){t.push(n)}),a(t)},c.prototype.values=function(){var t=[];return this.forEach(function(e){t.push(e)}),a(t)},c.prototype.entries=function(){var t=[];return this.forEach(function(e,n){t.push([n,e])}),a(t)},n.iterable&&(c.prototype[Symbol.iterator]=c.prototype.entries);var p=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function m(t,e){if(!(this instanceof m))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');var n,r,o=(e=e||{}).body;if(t instanceof m){if(t.bodyUsed)throw new TypeError("Already read");this.url=t.url,this.credentials=t.credentials,e.headers||(this.headers=new c(t.headers)),this.method=t.method,this.mode=t.mode,this.signal=t.signal,o||null==t._bodyInit||(o=t._bodyInit,t.bodyUsed=!0)}else this.url=String(t);if(this.credentials=e.credentials||this.credentials||"same-origin",!e.headers&&this.headers||(this.headers=new c(e.headers)),this.method=(r=(n=e.method||this.method||"GET").toUpperCase(),p.indexOf(r)>-1?r:n),this.mode=e.mode||this.mode||null,this.signal=e.signal||this.signal,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&o)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(o),!("GET"!==this.method&&"HEAD"!==this.method||"no-store"!==e.cache&&"no-cache"!==e.cache)){var i=/([?&])_=[^&]*/;i.test(this.url)?this.url=this.url.replace(i,"$1_="+(new Date).getTime()):this.url+=(/\?/.test(this.url)?"&":"?")+"_="+(new Date).getTime() }}function y(t){var e=new FormData;return t.trim().split("&").forEach(function(t){if(t){var n=t.split("="),r=n.shift().replace(/\+/g," "),o=n.join("=").replace(/\+/g," ");e.append(decodeURIComponent(r),decodeURIComponent(o)) }}),e}function v(t,e){if(!(this instanceof v))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');e||(e={}),this.type="default",this.status=void 0===e.status?200:e.status,this.ok=this.status>=200&&this.status<300,this.statusText="statusText"in e?e.statusText:"",this.headers=new c(e.headers),this.url=e.url||"",this._initBody(t)}m.prototype.clone=function(){return new m(this,{body:this._bodyInit})},d.call(m.prototype),d.call(v.prototype),v.prototype.clone=function(){return new v(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new c(this.headers),url:this.url})},v.error=function(){var t=new v(null,{status:0,statusText:""});return t.type="error",t};var b=[301,302,303,307,308];v.redirect=function(t,e){if(-1===b.indexOf(e))throw new RangeError("Invalid status code");return new v(null,{status:e,headers:{location:t }})},t.DOMException=e.DOMException;try{new t.DOMException}catch(e){t.DOMException=function(t,e){this.message=t,this.name=e;var n=Error(t);this.stack=n.stack},t.DOMException.prototype=Object.create(Error.prototype),t.DOMException.prototype.constructor=t.DOMException}function g(r,o){return new Promise(function(i,a){var u=new m(r,o);if(u.signal&&u.signal.aborted)return a(new t.DOMException("Aborted","AbortError"));var h=new XMLHttpRequest;function f(){h.abort()}h.onload=function(){var t,e,n={status:h.status,statusText:h.statusText,headers:(t=h.getAllResponseHeaders()||"",e=new c,t.replace(/\r?\n[\t ]+/g," ").split(/\r?\n/).forEach(function(t){var n=t.split(":"),r=n.shift().trim();if(r){var o=n.join(":").trim();e.append(r,o) }}),e)};n.url="responseURL"in h?h.responseURL:n.headers.get("X-Request-URL");var r="response"in h?h.response:h.responseText;setTimeout(function(){i(new v(r,n))},0)},h.onerror=function(){setTimeout(function(){a(new TypeError("Network request failed"))},0)},h.ontimeout=function(){setTimeout(function(){a(new TypeError("Network request failed"))},0)},h.onabort=function(){setTimeout(function(){a(new t.DOMException("Aborted","AbortError"))},0)},h.open(u.method,function(t){try{return""===t&&e.location.href?e.location.href:t}catch(e){return t }}(u.url),!0),"include"===u.credentials?h.withCredentials=!0:"omit"===u.credentials&&(h.withCredentials=!1),"responseType"in h&&(n.blob?h.responseType="blob":n.arrayBuffer&&u.headers.get("Content-Type")&&-1!==u.headers.get("Content-Type").indexOf("application/octet-stream")&&(h.responseType="arraybuffer")),!o||"object"!=typeof o.headers||o.headers instanceof c?u.headers.forEach(function(t,e){h.setRequestHeader(e,t)}):Object.getOwnPropertyNames(o.headers).forEach(function(t){h.setRequestHeader(t,s(o.headers[t]))}),u.signal&&(u.signal.addEventListener("abort",f),h.onreadystatechange=function(){4===h.readyState&&u.signal.removeEventListener("abort",f)}),h.send(void 0===u._bodyInit?null:u._bodyInit)})}g.polyfill=!0,e.fetch||(e.fetch=g,e.Headers=c,e.Request=m,e.Response=v),t.Headers=c,t.Request=m,t.Response=v,t.fetch=g,Object.defineProperty(t,"__esModule",{value:!0})}),"function"!=typeof Object.assign&&(Object.assign=function(t,e){"use strict";if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(t),r=1;r<arguments.length;r++){var o=arguments[r];if(null!=o)for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])}return n}),!1 in window&&function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.ResizeObserver=e()}(this,function(){"use strict";var t=function(){if("undefined"!=typeof Map)return Map;function t(t,e){var n=-1;return t.some(function(t,r){return t[0]===e&&(n=r,!0)}),n}return function(){function e(){this.__entries__=[]}return Object.defineProperty(e.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),e.prototype.get=function(e){var n=t(this.__entries__,e),r=this.__entries__[n];return r&&r[1]},e.prototype.set=function(e,n){var r=t(this.__entries__,e);~r?this.__entries__[r][1]=n:this.__entries__.push([e,n])},e.prototype.delete=function(e){var n=this.__entries__,r=t(n,e);~r&&n.splice(r,1)},e.prototype.has=function(e){return!!~t(this.__entries__,e)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(t,e){void 0===e&&(e=null);for(var n=0,r=this.__entries__;n<r.length;n++){var o=r[n];t.call(e,o[1],o[0]) }},e}()}(),e="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,n="undefined"!=typeof global&&global.Math===Math?global:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),r="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(n):function(t){return setTimeout(function(){return t(Date.now())},1e3/60)},o=2,i=20,s=["top","right","bottom","left","width","height","size","weight"],a="undefined"!=typeof MutationObserver,c=function(){function t(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(t,e){var n=!1,i=!1,s=0;function a(){n&&(n=!1,t()),i&&u()}function c(){r(a)}function u(){var t=Date.now();if(n){if(t-s<o)return;i=!0}else n=!0,i=!1,setTimeout(c,e);s=t}return u}(this.refresh.bind(this),i)}return t.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},t.prototype.removeObserver=function(t){var e=this.observers_,n=e.indexOf(t);~n&&e.splice(n,1),!e.length&&this.connected_&&this.disconnect_()},t.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},t.prototype.updateObservers_=function(){var t=this.observers_.filter(function(t){return t.gatherActive(),t.hasActive()});return t.forEach(function(t){return t.broadcastActive()}),t.length>0},t.prototype.connect_=function(){e&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),a?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},t.prototype.disconnect_=function(){e&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},t.prototype.onTransitionEnd_=function(t){var e=t.propertyName,n=void 0===e?"":e;s.some(function(t){return!!~n.indexOf(t)})&&this.refresh()},t.getInstance=function(){return this.instance_||(this.instance_=new t),this.instance_},t.instance_=null,t}(),u=function(t,e){for(var n=0,r=Object.keys(e);n<r.length;n++){var o=r[n];Object.defineProperty(t,o,{value:e[o],enumerable:!1,writable:!1,configurable:!0})}return t},h=function(t){return t&&t.ownerDocument&&t.ownerDocument.defaultView||n},f=y(0,0,0,0);function l(t){return parseFloat(t)||0}function d(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return e.reduce(function(e,n){return e+l(t["border-"+n+"-width"])},0)}var p="undefined"!=typeof SVGGraphicsElement?function(t){return t instanceof h(t).SVGGraphicsElement}:function(t){return t instanceof h(t).SVGElement&&"function"==typeof t.getBBox};function m(t){return e?p(t)?function(t){var e=t.getBBox();return y(0,0,e.width,e.height)}(t):function(t){var e=t.clientWidth,n=t.clientHeight;if(!e&&!n)return f;var r=h(t).getComputedStyle(t),o=function(t){for(var e={},n=0,r=["top","right","bottom","left"];n<r.length;n++){var o=r[n],i=t["padding-"+o];e[o]=l(i)}return e}(r),i=o.left+o.right,s=o.top+o.bottom,a=l(r.width),c=l(r.height);if("border-box"===r.boxSizing&&(Math.round(a+i)!==e&&(a-=d(r,"left","right")+i),Math.round(c+s)!==n&&(c-=d(r,"top","bottom")+s)),!function(t){return t===h(t).document.documentElement}(t)){var u=Math.round(a+i)-e,p=Math.round(c+s)-n;1!==Math.abs(u)&&(a-=u),1!==Math.abs(p)&&(c-=p)}return y(o.left,o.top,a,c)}(t):f}function y(t,e,n,r){return{x:t,y:e,width:n,height:r }}var v=function(){function t(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=y(0,0,0,0),this.target=t}return t.prototype.isActive=function(){var t=m(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},t.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},t}(),b=function(t,e){var n,r,o,i,s,a,c,h=(r=(n=e).x,o=n.y,i=n.width,s=n.height,a="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,c=Object.create(a.prototype),u(c,{x:r,y:o,width:i,height:s,top:o,right:r+i,bottom:s+o,left:r}),c);u(this,{target:t,contentRect:h})},g=function(){function e(e,n,r){if(this.activeObservations_=[],this.observations_=new t,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=n,this.callbackCtx_=r}return e.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof h(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)||(e.set(t,new v(t)),this.controller_.addObserver(this),this.controller_.refresh()) }},e.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof h(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)&&(e.delete(t),e.size||this.controller_.removeObserver(this)) }},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(e){e.isActive()&&t.activeObservations_.push(e)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,e=this.activeObservations_.map(function(t){return new b(t.target,t.broadcastRect())});this.callback_.call(t,e,t),this.clearActive() }},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),_="undefined"!=typeof WeakMap?new WeakMap:new t,w=function t(e){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=c.getInstance(),r=new g(e,n,this);_.set(this,r)};return["observe","unobserve","disconnect"].forEach(function(t){w.prototype[t]=function(){var e;return(e=_.get(this))[t].apply(e,arguments) }}),void 0!==n.ResizeObserver?n.ResizeObserver:w}),function(t,e){"use strict";if(!("IntersectionObserver"in t&&"IntersectionObserverEntry"in t&&"intersectionRatio"in t.IntersectionObserverEntry.prototype)){var n=[];o.prototype.THROTTLE_TIMEOUT=100,o.prototype.POLL_INTERVAL=null,o.prototype.USE_MUTATION_OBSERVER=!0,o.prototype.observe=function(t){if(!this._observationTargets.some(function(e){return e.element==t})){if(!t||1!=t.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:t,entry:null}),this._monitorIntersections(),this._checkForIntersections() }},o.prototype.unobserve=function(t){this._observationTargets=this._observationTargets.filter(function(e){return e.element!=t}),this._observationTargets.length||(this._unmonitorIntersections(),this._unregisterInstance())},o.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorIntersections(),this._unregisterInstance()},o.prototype.takeRecords=function(){var t=this._queuedEntries.slice();return this._queuedEntries=[],t},o.prototype._initThresholds=function(t){var e=t||[0];return Array.isArray(e)||(e=[e]),e.sort().filter(function(t,e,n){if("number"!=typeof t||isNaN(t)||t<0||t>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return t!==n[e-1]})},o.prototype._parseRootMargin=function(t){var e=(t||"0px").split(/\s+/).map(function(t){var e=/^(-?\d*\.?\d+)(px|%)$/.exec(t);if(!e)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(e[1]),unit:e[2] }});return e[1]=e[1]||e[0],e[2]=e[2]||e[0],e[3]=e[3]||e[1],e},o.prototype._monitorIntersections=function(){this._monitoringIntersections||(this._monitoringIntersections=!0,this.POLL_INTERVAL?this._monitoringInterval=setInterval(this._checkForIntersections,this.POLL_INTERVAL):(i(t,"resize",this._checkForIntersections,!0),i(e,"scroll",this._checkForIntersections,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in t&&(this._domObserver=new MutationObserver(this._checkForIntersections),this._domObserver.observe(e,{attributes:!0,childList:!0,characterData:!0,subtree:!0}))))},o.prototype._unmonitorIntersections=function(){this._monitoringIntersections&&(this._monitoringIntersections=!1,clearInterval(this._monitoringInterval),this._monitoringInterval=null,s(t,"resize",this._checkForIntersections,!0),s(e,"scroll",this._checkForIntersections,!0),this._domObserver&&(this._domObserver.disconnect(),this._domObserver=null))},o.prototype._checkForIntersections=function(){var e=this._rootIsInDom(),n=e?this._getRootRect():{top:0,bottom:0,left:0,right:0,width:0,height:0};this._observationTargets.forEach(function(o){var i=o.element,s=a(i),c=this._rootContainsTarget(i),u=o.entry,h=e&&c&&this._computeTargetAndRootIntersection(i,n),f=o.entry=new r({time:t.performance&&performance.now&&performance.now(),target:i,boundingClientRect:s,rootBounds:n,intersectionRect:h});u?e&&c?this._hasCrossedThreshold(u,f)&&this._queuedEntries.push(f):u&&u.isIntersecting&&this._queuedEntries.push(f):this._queuedEntries.push(f)},this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)},o.prototype._computeTargetAndRootIntersection=function(n,r){if("none"!=t.getComputedStyle(n).display){for(var o,i,s,c,h,f,l,d,p=a(n),m=u(n),y=!1;!y;){var v=null,b=1==m.nodeType?t.getComputedStyle(m):{};if("none"==b.display)return;if(m==this.root||m==e?(y=!0,v=r):m!=e.body&&m!=e.documentElement&&"visible"!=b.overflow&&(v=a(m)),v&&(o=v,i=p,s=Math.max(o.top,i.top),c=Math.min(o.bottom,i.bottom),h=Math.max(o.left,i.left),d=c-s,!(p=(l=(f=Math.min(o.right,i.right))-h)>=0&&d>=0&&{top:s,bottom:c,left:h,right:f,width:l,height:d})))break;m=u(m)}return p }},o.prototype._getRootRect=function(){var t;if(this.root)t=a(this.root);else{var n=e.documentElement,r=e.body;t={top:0,left:0,right:n.clientWidth||r.clientWidth,width:n.clientWidth||r.clientWidth,bottom:n.clientHeight||r.clientHeight,height:n.clientHeight||r.clientHeight }}return this._expandRectByRootMargin(t)},o.prototype._expandRectByRootMargin=function(t){var e=this._rootMarginValues.map(function(e,n){return"px"==e.unit?e.value:e.value*(n%2?t.width:t.height)/100}),n={top:t.top-e[0],right:t.right+e[1],bottom:t.bottom+e[2],left:t.left-e[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},o.prototype._hasCrossedThreshold=function(t,e){var n=t&&t.isIntersecting?t.intersectionRatio||0:-1,r=e.isIntersecting?e.intersectionRatio||0:-1;if(n!==r)for(var o=0;o<this.thresholds.length;o++){var i=this.thresholds[o];if(i==n||i==r||i<n!=i<r)return!0 }},o.prototype._rootIsInDom=function(){return!this.root||c(e,this.root)},o.prototype._rootContainsTarget=function(t){return c(this.root||e,t)},o.prototype._registerInstance=function(){n.indexOf(this)<0&&n.push(this)},o.prototype._unregisterInstance=function(){var t=n.indexOf(this);-1!=t&&n.splice(t,1)},t.IntersectionObserver=o,t.IntersectionObserverEntry=r}function r(t){this.time=t.time,this.target=t.target,this.rootBounds=t.rootBounds,this.boundingClientRect=t.boundingClientRect,this.intersectionRect=t.intersectionRect||{top:0,bottom:0,left:0,right:0,width:0,height:0};try{this.isIntersecting=!!t.intersectionRect}catch(t){}var e=this.boundingClientRect,n=e.width*e.height,r=this.intersectionRect,o=r.width*r.height;this.intersectionRatio=n?Number((o/n).toFixed(4)):this.isIntersecting?1:0}function o(t,e){var n,r,o,i=e||{};if("function"!=typeof t)throw new Error("callback must be a function");if(i.root&&1!=i.root.nodeType)throw new Error("root must be an Element");this._checkForIntersections=(n=this._checkForIntersections.bind(this),r=this.THROTTLE_TIMEOUT,o=null,function(){o||(o=setTimeout(function(){n(),o=null},r))}),this._callback=t,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(i.rootMargin),this.thresholds=this._initThresholds(i.threshold),this.root=i.root||null,this.rootMargin=this._rootMarginValues.map(function(t){return t.value+t.unit}).join(" ")}function i(t,e,n,r){"function"==typeof t.addEventListener?t.addEventListener(e,n,r||!1):"function"==typeof t.attachEvent&&t.attachEvent("on"+e,n)}function s(t,e,n,r){"function"==typeof t.removeEventListener?t.removeEventListener(e,n,r||!1):"function"==typeof t.detatchEvent&&t.detatchEvent("on"+e,n)}function a(t){var e;try{e=t.getBoundingClientRect()}catch(t){}return e?(e.width&&e.height||(e={top:e.top,right:e.right,bottom:e.bottom,left:e.left,width:e.right-e.left,height:e.bottom-e.top}),e):{top:0,bottom:0,left:0,right:0,width:0,height:0 }}function c(t,e){for(var n=e;n;){if(n==t)return!0;n=u(n)}return!1}function u(t){var e=t.parentNode;return e&&11==e.nodeType&&e.host?e.host:e&&e.assignedSlot?e.assignedSlot.parentNode:e }}(window,document),"DateTimeFormat"in Intl&&function(){return function t(e,n,r){function o(s,a){if(!n[s]){if(!e[s]){var c="function"==typeof require&&require;if(!a&&c)return c(s,!0);if(i)return i(s,!0);var u=new Error("Cannot find module '"+s+"'");throw u.code="MODULE_NOT_FOUND",u}var h=n[s]={exports:{ }};e[s][0].call(h.exports,function(t){return o(e[s][1][t]||t)},h,h.exports,t,e,n,r)}return n[s].exports}for(var i="function"==typeof require&&require,s=0;s<r.length;s++)o(r[s]);return o }}()({1:[function(t,e,n){"use strict";function r(t,e){var n=e.split(","),r=n.length;if(r%3!=0)throw new Error("wrong length of history Array, must be multiple of 3");var o=function(t){for(var e=[],n=0;n<t.length;)e.push([t[n],t[n+1],t[n+2]]),n+=3;return e}(n).map(function(e){return function(t,e){return{until:1e3*parseInt(t.timeStamps[parseInt(e[0],32)],32),offset:t.offsets[parseInt(e[1],32)],isdst:!!parseInt(e[2],10) }}(t,e)});if(r/3!==o.length)throw new Error("failed to harvest all data!!");return o}function o(t){var e=this;if(!(t&&Array.isArray(t.zoneDefs)&&Array.isArray(t.timeStamps)&&Array.isArray(t.offsets)))throw new Error("loadTimeZoneData: rejected packedTzData, packedTzData is not in right shape.");var n={timeStamps:t.timeStamps,offsets:t.offsets};t.zoneDefs.forEach(function(t){var o=t.split("||"),i=o[0].split(","),s=o[1],a=r(n,s);i.forEach(function(t){e.value[t]=a}),e.enrichMetaZoneMapWithEquivalentZones(i)})}function i(t){var e=this;if(!t||!t.locales||!Array.isArray(t.zoneNameIndex))throw new Error("loadLocaleData: rejected data, data is not in right shape.");var n=function(e){return function(t,e){var n=e.split("|"),r=function(e){return t[parseInt(e,32)]};return{long:{standard:n[0].split(",").filter(function(t){return!!t}).map(r).join(""),daylight:n[1].split(",").filter(function(t){return!!t}).map(r).join("")},short:{standard:n[2].split(",").filter(function(t){return!!t}).map(r).join(""),daylight:n[3].split(",").filter(function(t){return!!t}).map(r).join("") }}}(t.zoneNameIndex,e)};Object.keys(t.locales).forEach(function(r){var o=t.locales[r].metazone;Object.keys(o).forEach(function(t){o[t]=n(o[t])});var i=t.locales[r].zone;Object.keys(i).forEach(function(t){i[t]=n(i[t])}),e.value[r]=t.locales[r]}),Object.keys(t.locales).forEach(function(t){for(var n=t.split("-"),r=0;r<n.length-1;r++){var o=n.slice(0,n.length-r-1).join("-");e.value[o]||(e.value[o]=e.value[t]) }})}function s(t){if(this.get(t))return this.get(t);for(var e=t.split("-"),n=[],r=0;r<e.length-1;r++)n.push(e.slice(0,e.length-r-1).join("-"));for(var o=0;o<n.length;o++)if(this.get(n[o]))return this.get(n[o])}Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(t){t.Intl&&(t.Intl._metaZoneData=new a,t.Intl._localeData=new a(i,{getLocale:s}),t.Intl._timeZoneData=new a(o,{enrichMetaZoneMapWithEquivalentZones:function(t){var e=this.globalSpace;if(e&&e.Intl._metaZoneData){var n=e.Intl._metaZoneData,r=null;if(t.forEach(function(t){n.get(t)&&(r=n.get(t))}),r){var o=n.get();t.forEach(function(t){n.get(t)||(o[t]=r)}) }}}.bind({globalSpace:t})}))};var a=function t(e,n){var r=this;(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")})(this,t),this.value={},this.load=e||function(t){this.value=t},n&&Object.keys(n).forEach(function(t){return r[t]=n[t]}),this.get=function(t){return void 0===t?this.value:this.value[t] }}},{}],2:[function(t,e,n){"use strict";function r(t,e,n){var r=t<0?"-":"+",o=Math.floor(Math.abs(t/60)),i=Math.abs(t%60),s=Intl.NumberFormat(n,{minimumIntegerDigits:e?1:2}).format(o),a=Intl.NumberFormat(n,{minimumIntegerDigits:2}).format(i);return 0===t?"":(e&&0===i&&(a=""),r+s+(a?":":"")+a)}function o(t,e,n){if(!t&&n.long){if(e&&n.long.daylight)return n.long.daylight;if(!e&&n.long.standard)return n.long.standard}else if(n.short){if(e&&n.short.daylight)return n.short.daylight;if(!e&&n.short.standard)return n.short.standard}return!1}Object.defineProperty(n,"__esModule",{value:!0});n.buildCachedCheckTimeZoneSupport=function(t){var e={};return function(n){if(void 0!==e[n])return e[n];try{new t.Intl._DateTimeFormat("en",{timeZone:n}),e[n]=!0}catch(t){e[n]=!1}return e[n] }},n.getTimeZoneOffsetInfo=function(t,e){var n=e.getTime();return t.reduce(function(t,e){return e.until>=n&&null===t?e:t},null)||t[t.length-1]},n.getZoneNameForLocale=function(t){var e=t.locale,n=t.ianaTimeZone,i=t.offset,s=t.isdst,a=t.isShort,c=t.timeStamp,u=function(t,e,n){if(!Array.isArray(t))return null;if(1===t.length)return t[0].mzone;var r=-1e3*Math.pow(2,31),o=1e3*Math.pow(2,31),i=null;return t.forEach(function(t){var s=t.from?new Date(t.from+n).getTime():r,a=t.to?new Date(t.to+n).getTime():o;s<=e&&e<=a&&(i=t.mzone)}),i}(Intl._metaZoneData.get(n),c,r(i)),h=Intl._localeData.getLocale(e),f=u&&h&&h.metazone[u],l=h&&h.zone&&h.zone[n];if(l&&o(a,s,l))return o(a,s,l);if(f&&o(a,s,f))return o(a,s,f);if(h&&h.gmtFormat&&i)h.gmtFormat.replace("{0}",r(i,a,e));else if(h&&h.gmtZeroFormat&&!i)return h.gmtZeroFormat;return i&&["GMT",r(i,a,e)].join("")||"GMT" }},{}],3:[function(t,e,n){"use strict";function r(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var i=Object.getPrototypeOf(t);if(null===i)return;return r(i,e,n)}if("value"in o)return o.value;var s=o.get;return void 0!==s?s.call(n):void 0}function o(t){return Object.getPrototypeOf?Object.getPrototypeOf(t):t.__proto__}function i(t,e){return Object.setPrototypeOf?Object.setPrototypeOf(t,e):(t.__proto__=e,t)}function s(t){function e(){var e=o(this).constructor,n=[null];n.push.apply(n,arguments);var r=new(Function.bind.apply(t,n));return e&&i(r,e.prototype),r}return function(t){return-1!==Function.toString.call(t).indexOf("[native code]")}(t)?(e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0 }}),i(e,t)):t}function a(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+(void 0===e?"undefined":c(e)));t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0 }}),e&&i(t,e)}Object.defineProperty(n,"__esModule",{value:!0});var c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};n.default=function(t){function e(t,i){if(!(this instanceof e))return new e(t,i);var s=void 0,a=i&&i.timeZone||"UTC";if(void 0===i)return(s=f.call(this,t,i)).formatToParts&&(s._nativeObject=new f(t,i)),s;if(c(a))return(s=f.call(this,t,i)).formatToParts&&(s._nativeObject=new f(t,i)),s;var u=n._timeZoneData.get(a);if(!u)throw new RangeError("invalid time zone in DateTimeFormat():  "+a);var l=h(i);l.timeZone="UTC",s=f.call(this,t,l);var d=r(o(e.prototype),"resolvedOptions",s).call(s).locale;if(!(void 0===i.timeZoneName||n._localeData.getLocale(d)&&Intl._metaZoneData.get(a)))throw new RangeError('unsupported value "'+i.timeZoneName+'" for timeZone '+a+". requires locale data for "+d);return s._dateTimeFormatPolyfill={optionTimeZone:a,optionTimeZoneName:i.timeZoneName,timeZoneData:u},s}if(t.Intl&&t.Intl.DateTimeFormat&&!t.Intl._DateTimeFormatTimeZone){var n=t.Intl,i=t.Date,c=(0,u.buildCachedCheckTimeZoneSupport)(t),h=function(t){return JSON.parse(JSON.stringify(t))},f=s(n.DateTimeFormat);n._DateTimeFormat=f,n._DateTimeFormatTimeZone={checkTimeZoneSupport:c},a(e,f),Object.defineProperty(e.prototype,"format",{configurable:!0,value:function(t){var n=r(o(e.prototype),"format",this),i=r(o(e.prototype),"resolvedOptions",this);if(!this._dateTimeFormatPolyfill)return n.call(this,t);null!=t||(t=new Date),t instanceof Date||(t=new Date(t));var s=this._dateTimeFormatPolyfill,a=(0,u.getTimeZoneOffsetInfo)(s.timeZoneData,t),c=6e4*a.offset,h=new Date(t.getTime()+c),f=n.call(this,h),l=i.call(this).locale;if(void 0!==s.optionTimeZoneName){var d="short"===s.optionTimeZoneName,p=(0,u.getZoneNameForLocale)({locale:l,ianaTimeZone:s.optionTimeZone,isdst:a.isdst,offset:a.offset,timeStamp:t.getTime(),isShort:d}),m=(0,u.getZoneNameForLocale)({locale:l,ianaTimeZone:"UTC",isdst:!1,offset:0,timeStamp:t.getTime(),isShort:d});return f.indexOf(m)<0?f.trim()+" "+p:f.replace(m,p)}return f }});var l=r(o(e.prototype),"formatToParts",this);l&&Object.defineProperty(e.prototype,"formatToParts",{configurable:!0,value:function(t){var n=r(o(e.prototype),"resolvedOptions",this);if(!this._dateTimeFormatPolyfill&&this._nativeObject)return this._nativeObject.formatToParts(t);null!=t||(t=new Date),t instanceof Date||(t=new Date(t));var i=this._dateTimeFormatPolyfill,s=(0,u.getTimeZoneOffsetInfo)(i.timeZoneData,t),a=6e4*s.offset,c=new Date(t.getTime()+a),h=l.call(this,c),f=n.call(this).locale;if(void 0!==i.optionTimeZoneName){var d="short"===i.optionTimeZoneName,p=(0,u.getZoneNameForLocale)({locale:f,ianaTimeZone:i.optionTimeZone,isdst:s.isdst,offset:s.offset,timeStamp:t.getTime(),isShort:d}),m=h.map(function(t){return t.type}).indexOf("timeZoneName");m>=0&&(h[m]={type:"timeZoneName",value:p})}return h }}),Object.defineProperty(e.prototype,"resolvedOptions",{writable:!0,configurable:!0,value:function(){var t=r(o(e.prototype),"resolvedOptions",this);if(this._dateTimeFormatPolyfill){var n=h(t.call(this));return n.timeZone=this._dateTimeFormatPolyfill.optionTimeZone,n}return t.call(this) }}),n.DateTimeFormat=e,i.prototype.toLocaleString=function(t,e){var r={day:"numeric",month:"numeric",year:"numeric",hour:"numeric",minute:"numeric",second:"numeric"};return void 0===e&&(e=h(r)),void 0===e.day&&void 0===e.month&&void 0===e.year&&void 0===e.hour&&void 0===e.minute&&void 0===e.second&&((e=h(e)).day=r.day,e.month=r.month,e.year=r.year,e.hour=r.hour,e.minute=r.minute,e.second=r.second),new n.DateTimeFormat(t,e).format(this)},i.prototype.toLocaleDateString=function(t,e){var r={day:"numeric",month:"numeric",year:"numeric"};return void 0===e&&(e=h(r)),void 0===e.day&&void 0===e.month&&void 0===e.year&&((e=h(e)).day=r.day,e.month=r.month,e.year=r.year),new n.DateTimeFormat(t,e).format(this)},i.prototype.toLocaleTimeString=function(t,e){var r={hour:"numeric",minute:"numeric",second:"numeric"};return void 0===e&&(e=h(r)),void 0===e.hour&&void 0===e.minute&&void 0===e.second&&((e=h(e)).hour=r.hour,e.minute=r.minute,e.second=r.second),new n.DateTimeFormat(t,e).format(this) }}};var u=t("./lookup-utill.js")},{"./lookup-utill.js":2}],4:[function(t,e,n){(function(n){var r=void 0!==n&&"[object global]"==={}.toString.call(n)?n:window;t("./code/polyfill.js").default(r),t("./code/data-loader.js").default(r),e.exports=r.Intl.DateTimeFormat}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./code/data-loader.js":1,"./code/polyfill.js":3}]},{},[4]);