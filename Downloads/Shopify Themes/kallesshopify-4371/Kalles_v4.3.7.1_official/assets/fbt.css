.t4s-fbt__wrap {
    margin-top: 55px;
    margin-bottom: 50px;
}

h3.t4s-fbt__title {
    font-size: 18px;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.t4s-fbt__wrap .t4s-section-fbt {
    justify-content: space-between;
    align-items: flex-start;
}

.t4s-fbt__img .t4s-fbt__img-wrap {
    margin: 0px 10px;
}

.t4s-fbt__img img {
    width: 115px;
}

.t4s-fbt__img .t4s-svg--plus {
    position: absolute;
    top: 50%;
    width: 10px;
    display: inline-block;
    vertical-align: middle;
    left: -5px;
}

.t4s-fbt_img-0 {
    margin-right: 10px;
}

.t4s-fbt__text-total-price {
    margin-bottom: 10px;
}

.t4s-fbt__total-text {
    font-size: 14px;
    margin-right: 5px;
}

.t4s-fbt__total-price {
    color: var(--primary-price-color);
    font-size: 18px;
    font-weight: 500;
}

.t4s-fbt__price {
    font-size: 15px;
    margin: 0 5px;
    display: inline-block;
    color: var(--primary-price-color);

}

.t4s-fbt__total-price del,
.t4s-fbt__price del {
    font-weight: 400;
    font-size: 16px;
    color: var(--secondary-price-color);
}

ul.t4s-fbt__list-img {
    padding: 0;
    margin-bottom: 0;
}

ul.t4s-fbt__swatches {
    padding: 0;
    margin: 30px 0;
}

.t4s-fbt__item {
    display: flex;
    align-items: center;
    line-height: 1;
    flex-wrap: wrap;
    gap: 1rem;
}

.t4s-fbt__item:not(:last-child) {
    margin-bottom: 20px;
}

.t4s-fbt__item select {
    width: auto;
    border-radius: 5px;
    color: var(--secondary-color);
    height: 36px;
    line-height: 36px;
    margin-left: 7px;
}

.t4s-fbt__item input[type="checkbox"] {
    display: none;
}

.t4s-fbt__item label:before {
    position: relative;
    top: 2px;
    content: "";
    left: -1rem;
    display: inline-block;
    margin-right: 4px;
    width: 24px;
    height: 24px;
    min-width: 14px;
    border: 1px solid #d4d6d8;
    box-shadow: none;
    background-size: 0;
    background: #fff no-repeat 50%;
    transition: all .2s ease-in-out;
    -webkit-transition: all .2s ease-in-out;
    border-radius: 2px;
    -webkit-appearance: none;
}

.t4s-fbt__item svg {
    display: block;
    width: 13px;
    height: 10px;
    fill: var(--t4s-light-color);
    position: absolute;
    top: 9px;
    left: -4px;
    pointer-events: none;
    transform: scale(0);
    -webkit-transform: scale(0);
    -webkit-transition: all .25s ease-in-out;
    transition: all .25s ease-in-out;
}

.t4s-fbt__item input[type="checkbox"]:checked+label svg {
    transform: scale(1);
    -webkit-transform: scale(1);
}

.t4s-fbt__item input[type="checkbox"]:checked+label:before {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.t4s-fbt__item input[type="checkbox"]:not(:checked)~* {
    opacity: .2;
}

.t4s-fbt__item input[type="checkbox"]:not(:checked)+label {
    opacity: 1;
}

button.t4s-fbt__submit {
    background-color: var(--accent-color);
    border: none;
    color: var(--t4s-light-color);
    text-transform: uppercase;
    position: relative;
    border-radius: var(--btn-radius);
    font-weight: 600;
    padding: 11px 25px;
}

.t4s-fbt__wrap .t4s-fbt__total-price {
    line-height: 1;
}

.t4s-fbt__wrap .is--col-fbt-total-price {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-right: calc(var(--ts-gutter-x)* .5);
}

.t4s-fbt__wrap .t4s-fbt__item-checkbox {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding-left: 1rem;
}

.t4s-fbt__item-select-price {
    padding-left: 3.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

@media (max-width:1023px) {
    .t4s-fbt__wrap .t4s-fbt__item-checkbox {}
}

@media (max-width:767px) {
    .t4s-fbt__img img {
        width: 70px;
    }

    .t4s-fbt__wrap .is--col-fbt-total-price {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 30px;
    }

    .t4s-fbt__wrap .t4s-fbt__total-price {
        display: flex;
        gap: 10px;
        line-height: 1;
    }

    .t4s-fbt__wrap .is--col-fbt-total-price * {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

@media (min-width: 1024px) {
    .is--col-fbt-total-price {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 15px;
    }
}

@media (max-width: 574px) {
    .is--col-fbt-img ul:first-child {
        display: flex !important;
    }

    .is--col-fbt-img ul:first-child li {
        display: flex;
        align-items: center;
    }

    .is--col-fbt-img ul li {
        display: flex;
        align-items: center;
        justify-content: start;
        flex-wrap: wrap;
    }

    .t4s-fbt__swatches {
        display: block !important;
    }
}