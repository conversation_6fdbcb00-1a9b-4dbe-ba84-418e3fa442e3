.is--scrollbar_true {
  --scrollbar-secondary : #ddd;
  --scrollbar-primary   : #000;
  --bottom-dt           : 40px;
  --bottom-tb           : 20px;
  --bottom-mb           : 20px;
  --scrollbar-raidus    : 5px;
  --scrollbar-height    : 3px;
}
.is--scrollbar_true {
  --bottom-scrollbar: var(--bottom-mb);
}
@media (min-width: 768px) {
  .is--scrollbar_true {
    --bottom-scrollbar: var(--bottom-tb);
  }
}
@media (min-width: 1025px) {
  .is--scrollbar_true {
    --bottom-scrollbar: var(--bottom-dt);
  }
  .t4s-section-inline-true .t4s-section-head + .flickityt4s .t4s-carousel-scrollbar {
     --bottom-scrollbar: 0px;
  }
}

.t4s-carousel-scrollbar.is--hidden {
  display: none;
}
.t4s-carousel-scrollbar:not(.is--hidden) + .flickityt4s-viewport {
    margin-bottom: var(--bottom-scrollbar);
}
.t4s-carousel-scrollbar {
    position: absolute;
    overflow: hidden;
    border-radius: var(--scrollbar-raidus);
    max-width: calc( 100% - var(--ts-gutter-x));
    padding: 0;
    margin: 0 auto;
    bottom: calc( -1 * var(--bottom-scrollbar));
    left: 0;
    right: 0;
    pointer-events: none;
}
.t4s-carousel-scrollbar__bg {
    background-color: var(--scrollbar-secondary);
    height: var(--scrollbar-height);
}
.t4s-carousel-scrollbar__drag {
    position: absolute;
    min-width: 10%;
    max-width: 100%;
    background-color: var(--scrollbar-primary);
    height: 100%;
    top: 0;
    width: var(--width, 19%);
    left: var(--left, 0);
    will-change: left;
}