{{ 'customer.min.css' | asset_url | stylesheet_tag }}
{{ 'button-style.css' | asset_url | stylesheet_tag }}
<link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid 
assign sid            = section.id
assign se_stts = section.settings -%}

<div class="t4s-customer is--register t4s-text-{{ se_stts.content_align }}">
  <svg style="display: none">
    <symbol id="icon-error" viewBox="0 0 13 13">
      <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
      <circle cx="6.5" cy="6.5" r="5.5" fill="var(--t4s-error-color)" stroke="var(--t4s-error-color)" stroke-width="0.7"/>
      <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
      <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="var(--t4s-error-color)" stroke-width="0.7">
    </symbol>
  </svg>

  {%- form 'create_customer', novalidate: 'novalidate',class: 't4s-w-100' -%}
    {%- if form.errors -%}
      <h2 class="form__message" tabindex="-1" autofocus>
        <svg aria-hidden="true" focusable="false" role="presentation">
          <use href="#icon-error" />
        </svg>
        {{ 'customer.form.error_heading' | t }}
      </h2>
      <ul> 
        {%- for field in form.errors -%}
          <li>
            {%- if field == 'form' -%}
              {{ form.errors.messages[field] }}
            {%- else -%}
              <a href="#RegisterForm-{{ field }}">
                {{ form.errors.translated_fields[field] | capitalize }}
                {{ form.errors.messages[field] }}
              </a>
            {%- endif -%}
          </li>
        {%- endfor -%}
      </ul>
    {%- endif -%}
    <div class="t4s_field t4s-pr t4s_mb_30">      
      <input class="t4s_frm_input" 
        type="text"
        name="customer[first_name]"
        id="RegisterForm-FirstName"
        {% if form.first_name %}value="{{ form.first_name }}"{% endif %}
        autocomplete="given-name"
        placeholder="{{ 'customer.register.first_name' | t }}"
      >
      <label for="RegisterForm-FirstName">
        {{ 'customer.register.first_name' | t }}
      </label>
    </div>
    <div class="t4s_field t4s-pr t4s_mb_30">
      <input class="t4s_frm_input" 
        type="text"
        name="customer[last_name]"
        id="RegisterForm-LastName"
        {% if form.last_name %}value="{{ form.last_name }}"{% endif %}
        autocomplete="family-name"
        placeholder="{{ 'customer.register.last_name' | t }}"
      >
      <label for="RegisterForm-LastName">
        {{ 'customer.register.last_name' | t }}
      </label>
    </div>
    <div class="t4s_field t4s-pr t4s_mb_30">
      <input class="t4s_frm_input" 
        type="email"
        name="customer[email]"
        id="RegisterForm-email"
        {% if form.email %} value="{{ form.email }}"{% endif %}
        spellcheck="false"
        autocapitalize="off"
        autocomplete="email"
        aria-required="true"
        {% if form.errors contains 'email' %}
          aria-invalid="true"
          aria-describedby="RegisterForm-email-error"
        {% endif %}
        placeholder="{{ 'customer.register.email' | t }}"
      >
      <label for="RegisterForm-email">
        {{ 'customer.register.email' | t }} <span class="required">*</span>
      </label>
    </div>
    {%- if form.errors contains 'email' -%}
      <span id="RegisterForm-email-error" class="form__message">
        <svg aria-hidden="true" focusable="false" role="presentation">
          <use href="#icon-error" />
        </svg>
        {{ form.errors.translated_fields['email'] | capitalize }} {{ form.errors.messages['email'] }}.
      </span>
    {%- endif -%}
    <div class="t4s_field t4s-pr t4s_mb_30">     
      <input class="t4s_frm_input" 
        type="password"
        name="customer[password]"
        id="RegisterForm-password"
        aria-required="true"
        {% if form.errors contains 'password' %}
          aria-invalid="true"
          aria-describedby="RegisterForm-password-error"
        {% endif %}
        placeholder="{{ 'customer.register.password' | t }}"
      >
      <label for="RegisterForm-password">
        {{ 'customer.register.password' | t }} <span class="required">*</span>
      </label>
    </div>
    {%- if form.errors contains 'password' -%}
      <span id="RegisterForm-password-error" class="form__message">
        <svg aria-hidden="true" focusable="false" role="presentation">
          <use href="#icon-error" />
        </svg>
        {{ form.errors.translated_fields['password'] | capitalize }} {{ form.errors.messages['password'] }}.
      </span>
    {%- endif -%}
    {%- if settings.use_privacy_policy -%}
      <p class="t4s-privacy-policy">{%- if settings.privacy_policy_link != blank -%}{{ 'customer.register.privacy_policy_html' | t: link: settings.privacy_policy_link }}{%- else -%}{{ 'customer.register.privacy_policy' | t }}{%- endif -%}</p> 
    {%- endif -%}
    <div class="t4s_field t4s_mb_20">
      <button class="t4s_btn_submmit t4s-btn t4s-btn-base t4s-btn-{{ se_stts.btn_width }}-width t4s-btn-style-{{ se_stts.button_style }} t4s-btn-size-{{ se_stts.btns_size }} t4s-btn-color-{{ se_stts.btns_cl }} {% if se_stts.button_style == 'default' or se_stts.button_style == 'outline' %}t4s-btn-effect-{{ se_stts.button_effect }}{% endif %}">{{ 'customer.register.submit' | t }}</button>
    </div>
    <a class="t4s-d-inline-block" href="{{ routes.storefront_login_url }}"> {{ 'customer.register.login_here' | t }}</a>

  {%- endform -%}

</div>

{%- schema -%}
  {
  "name": "Register",
  "tag": "section",
  "class": "t4s-section t4s-section-customers t4s-container",
    "settings": [
      {
        "type": "header",
        "content": "+ General options"
      },
      {
        "type": "select",
        "id": "content_align",
        "label": "Content align",
        "default": "start",
        "options": [
          {
            "value": "start",
            "label": "Default"
          },
          {
            "value": "center",
            "label": "Center"
          }
        ]
      },
      {
        "type": "header",
        "content": "+ Button options"
      },
      {
        "type": "select",
        "id": "btn_width",
        "label": "Button width",
        "default": "full",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "full",
            "label": "Full width"
          }
        ]
      },
      {
        "type": "select",
        "id": "button_style",
        "label": "Button style",
        "options": [
            {
                "label": "Default",
                "value": "default"
            },
            {
                "label": "Outline",
                "value": "outline"
            },
            {
                "label": "Bordered bottom",
                "value": "bordered"
            },
            {
                "label": "Link",
                "value": "link"
            }
        ]
      },
      {
        "type": "select",
        "id": "btns_size",
        "label": "Button size",
        "default":"medium",
        "options": [
            {
                "label": "Extra small",
                "value": "small"
            },
            {
                "label": "Small",
                "value": "extra-small"
            },
            {
                "label": "Medium",
                "value": "medium"
            },
            {
                "label": "Large",
                "value": "extra-medium"
            },
            {
                "label": "Extra large",
                "value": "large"
            },
            {
                "label": "Extra extra large",
                "value": "extra-large"
            }
        ]
      },
      {
        "type": "select",
        "id": "btns_cl",
        "label": "Button color",
        "default": "dark",
        "options": [
          {
              "value": "light",
              "label": "Light"
          },
          {
              "value": "dark",
              "label": "Dark"
          },
          {
              "value": "primary",
              "label": "Primary"
          },
          {
              "value": "custom1",
              "label": "Custom color 1"
          },
          {
              "value": "custom2",
              "label": "Custom color 2"
          }
        ]
      },
       {
          "type":"select",
          "id":"button_effect",
          "label":"Button hover effect",
          "default":"default",
          "info":"Only working button style default, outline",
          "options":[
              {
                  "label":"Default",
                  "value":"default"
              },
              {
                  "label":"Fade",
                  "value":"fade"
              },
              {
                  "label":"Rectangle out",
                  "value":"rectangle-out"
              },
              {
                  "label":"Sweep to right",
                  "value":"sweep-to-right"
              },
              {
                  "label":"Sweep to left",
                  "value":"sweep-to-left"
              },
              {
                  "label":"Sweep to bottom",
                  "value":"sweep-to-bottom"
              },
              {
                  "label":"Sweep to top",
                  "value":"sweep-to-top"
              },
              {
                  "label":"Shutter out horizontal",
                  "value":"shutter-out-horizontal"
              },
              {
                  "label":"Outline",
                  "value":"outline"
              },
              {
                  "label":"Shadow",
                  "value":"shadow"
              },
              {
                  "label":"Overlay run (Only working with button default)",
                  "value":"overlay-run"
              }
          ]
      }
    ],
    "presets": [
      {
        "name": "Register"
      }
    ]
  }
{% endschema %}