<!-- sections/newsletter.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'newsletter.css' | asset_url | stylesheet_tag }}
{{ 'parallax.min.css' | asset_url | stylesheet_tag }}
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign arr_space = se_blocks | where: "type", 'space'

  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif 
  assign news_overlay_cl  = se_stts.news_overlay_opacity | divided_by: 100.0
 -%}
{%- capture append_style -%}
  --news-overlay : {{ se_stts.news_overlay | color_modify: 'alpha', news_overlay_cl }};
{%- endcapture -%}
{{ news_overlay }}
<div class="t4s-section-inner t4s_se_{{ sid }} {{ stt_layout }} t4s-parallax {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}  data-parallax-t4strue data-speed="0.2" {% render 'section_style', se_stts: se_stts, append_style: append_style -%} >
  {{- html_layout[0] -}}
  {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
    {%- render 'section_tophead', se_stts: se_stts -%}
    {%- if se_stts.parallax == true -%}
      {%- assign image = se_stts.image_bg -%}
      <img class="t4s-parallax-img lazyloadt4s" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="1.2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
    {% endif %}
    <div class="{%- if se_stts.parallax == true -%} t4s-parallax-inner {% endif %} t4s-newsletter-wrap content-{{ se_stts.content_align }}">
        {%- if se_blocks.size > 0 -%}
          <div timeline hdt-reveal="slide-in" class="t4s-top-heading" timeline hdt-reveal="slide-in">
          {%- for block in se_blocks -%}
            {%- assign bk_stts = block.settings -%}
            {%- case block.type -%}
              {%- when 'custom_text' -%}
                {% if bk_stts.text != blank %}
                  {%- assign general_block = true -%}
                  <{{ bk_stts.tag }} data-lh="{{ bk_stts.text_lh_mb }}" data-lh-md="{{ bk_stts.text_lh }}" data-lh-lg="{{ bk_stts.text_lh }}" class="t4s-text-bl t4s-fnt-fm-{{ bk_stts.fontf }} t4s-font-italic-{{ bk_stts.font_italic }} t4s-hidden-mobile-{{ bk_stts.hidden_mobile }} t4s-br-mb-{{ bk_stts.remove_br_tag }} t4s-text-shadow-{{ bk_stts.text_shadow }}" id="b_{{ block.id }}" {%- render 'bk_cus_style', type: 'custom_text', bk_stts: bk_stts -%}>{{ bk_stts.text }}</{{ bk_stts.tag }}>
                {% endif %}
              {%- when 'space_html' -%}
                {%- assign general_block = true -%}
                <div class="t4s-space-html" {%- render 'bk_cus_style', type: 'space_html' , bk_stts: bk_stts -%}></div>
              {%- when 'html' -%}
                {% if bk_stts.html_content != blank %}
                  {%- assign general_block = true -%}
                    <div class=" t4s-raw-html t4s-rte--list t4s-hidden-mobile-{{ bk_stts.hidden_mobile }}" id="b_{{ block.id }}" {%- render 'bk_cus_style', type: 'html', bk_stts: bk_stts -%}>{{ bk_stts.html_content }}</div>
                  {% endif %}
              {%- when 'image' -%}
                {%- assign general_block = true -%}
                {%- assign image = bk_stts.image_child -%}
                {%- if image -%}
                  <div class="t4s-img-child t4s-hidden-mobile-{{ bk_stts.hidden_mobile }}" id="b_{{ block.id }}" {%- render 'bk_cus_style', type: 'image', bk_stts: bk_stts -%}>
                      <img data-maxw="{{ bk_stts.img_width_mb }}" data-maxw-md="{{ bk_stts.img_width }}" data-maxw-lg="{{ bk_stts.img_width }}" class="lazyloadt4s t4s-lz--fadeIn" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                      <span class="lazyloadt4s-loader"></span>
                  </div>
                {%- endif -%}
              {%- when "countdown" -%}
                {%- if bk_stts.date != blank -%}
                {%- assign countdown = true -%}
                  <div class="t4s-countdown sepr_coun_dt_wrap t4s-countdown-des-{{ bk_stts.cdt_des }} t4s-countdown-size-{{ bk_stts.cdt_size }} t4s-hidden-mobile-{{ bk_stts.hidden_mobile }}" id="b_{{ block.id }}" {%- render 'bk_cus_style', type: 'countdown', bk_stts: bk_stts -%}>
                    <div class="time" data-countdown-t4s data-date='{{ bk_stts.date }}' data-keyid='#countdown-{{ sid }}'></div>
                  </div>
                {% endif %}
              {%- when 'custom_button' -%}
                {%- if bk_stts.button_link != blank and bk_stts.button_text != blank -%}
                  {%- assign custom_button = true -%}
                  {%- assign  button_style = bk_stts.button_style -%}
                  <a href="{{ bk_stts.button_link }}" target="{{ bk_stts.target_link }}" class="t4s-btn t4s-btn-custom t4s-pe-auto t4s-fnt-fm-{{ bk_stts.fontf }} t4s-hidden-mobile-{{ bk_stts.hidden_mobile }} t4s-btn-style-{{ button_style }} {% if button_style == 'default' or button_style == 'outline' %}t4s-btn-effect-{{ bk_stts.button_effect }}{% endif %}" id="b_{{ block.id }}" {{ block.shopify_attributes }} {%- render 'bk_cus_style', type: 'custom_button', bk_stts: bk_stts -%}>{{ bk_stts.button_text }} {%- if bk_stts.button_icon_w > 0 -%}<svg class="t4s-btn-icon" width="14"><use xlink:href="#t4s-icon-btn"></use></svg>{%- endif -%}</a>
                {%- endif -%}
              {%- when 'newsletter' -%}
                {%- assign newsletter = true -%}
                {%- assign custom_button = true -%}
                </div>
                <div id="b_{{ block.id }}" class="t4s-newsletter-parent t4s_newsletter_se t4s-newsl-des-{{ bk_stts.newl_des }} t4s-newsl-{{ bk_stts.newl_size }} t4s-text-{{ bk_stts.news_align }} t4s-hidden-mobile-{{ bk_stts.hidden_mobile }}" {%- render 'bk_cus_style', type: 'newsletter', bk_stts: bk_stts -%}>
                  {%- render 'newsletter', form_id: block.id, buttonIcon: bk_stts.btn_icon, bk_stts: bk_stts -%}
                </div>
            {%- endcase -%}
          {%- endfor -%}
        {%- endif -%}
    </div>
  {{- html_layout[1] -}}
</div>
{%- if general_block or custom_button or newsletter -%}
  {{ 'general-block.css' | asset_url | stylesheet_tag }}
{%- endif -%}
{%- if custom_button -%}
  {{ 'button-style.css' | asset_url | stylesheet_tag }}
  <link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- endif -%}
{%- if newsletter -%}
  {{ 'newsletter.css' | asset_url | stylesheet_tag }}
{%- endif -%}
{%- if countdown -%} 
  {{ 'countdown.css' | asset_url | stylesheet_tag }}
  <template id="countdown-{{ sid }}">
      <span class="countdown-days">
          <span class="cd_timet4 cd-number">%-D</span>
          <span class="cd_txtt4 cd-text">%!D:{{ "sections.countdown_text.day" | t }},{{ "sections.countdown_text.day_plural" | t }};</span>
      </span>
      <span class="countdown-hours">
          <span class="cd_timet4 cd-number">%H</span> 
          <span class="cd_txtt4 cd-text">%!H:{{ "sections.countdown_text.hr" | t }},{{ "sections.countdown_text.hr_plural" | t }};</span>
      </span>
      <span class="countdown-min">
          <span class="cd_timet4 cd-number">%M</span> 
          <span class="cd_txtt4 cd-text">%!M:{{ "sections.countdown_text.min" | t }},{{ "sections.countdown_text.min_plural" | t }};</span>
      </span>
      <span class="countdown-sec">
          <span class="cd_timet4 cd-number">%S</span> 
          <span class="cd_txtt4 cd-text">%!S:{{ "sections.countdown_text.sec" | t }},{{ "sections.countdown_text.sec_plural" | t }};</span>
      </span>
  </template>
{%- endif -%}
{%- schema -%}
  {
    "name": "Newsletter",
    "tag": "section",
    "class": "t4s-section t4s-section-all t4s_tp_parallax t4s_tp_cdt t4s-newsletter",
    "settings": [
      {
          "type": "header",
          "content": "1. Heading options"
      },
      {
          "type": "select",
          "id": "design_heading",
          "label": "+ Design heading",
          "default": "1",
          "options": [
              {
                  "value": "1",
                  "label": "Design 01"
              },
              {
                  "value": "2",
                  "label": "Design 02"
              },
              {
                  "value": "3",
                  "label": "Design 03"
              },
              {
                  "value": "4",
                  "label": "Design 04"
              },
              {
                  "value": "5",
                  "label": "Design 05"
              },
              {
                  "value": "6",
                  "label": "Design 06 (width line-awesome)"
              },
              {
                  "value": "7",
                  "label": "Design 07"
              },
              {
                  "value": "8",
                  "label": "Design 08"
              },
              {
                  "value": "9",
                  "label": "Design 09"
              },
              {
                  "value": "10",
                  "label": "Design 10"
              },
              {
                  "value": "11",
                  "label": "Design 11"
              },
              {
                  "value": "12",
                  "label": "Design 12"
              },
              {
                  "value": "13",
                  "label": "Design 13"
              },
              {
                  "value": "14",
                  "label": "Design 14"
              },
              {
                "value": "15",
                "label": "Design 15"
              },
              {
                "value": "16",
                "label": "Design 16"
              }
          ]
      },
      {
          "type": "select",
          "id": "heading_align",
          "label": "+ Heading align",
          "default": "t4s-text-center",
          "options": [
              {
                  "value": "t4s-text-start",
                  "label": "Left"
              },
              {
                  "value": "t4s-text-center",
                  "label": "Center"
              },
              {
                  "value": "t4s-text-end",
                  "label": "Right"
              }
          ]
      },
      {
          "type": "text",
          "id": "top_heading",
          "label": "+ Heading"
      },
      {
          "type": "text",
          "id": "icon_heading",
          "label": "Enter a name icon [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
          "info": "Only used for design 6",
          "default": "las la-gem"
      },
      {
          "type": "textarea",
          "id": "top_subheading",
          "label": "+ Subheading"
      },
      {
        "type": "number",
        "id": "tophead_mb",
        "label": "+ Space bottom (px)",
        "info": "The vertical spacing between heading and content",
        "default": 30
      },
      {
        "type": "header",
        "content": "2. General options"
      },
      {
          "type": "select",
          "id": "content_align",
          "label": "+ Content align",
          "default": "all-center",
          "options": [
              {
                  "value": "all-center",
                  "label": "One column"
              },
              {
                  "value": "space-between",
                  "label": "Two columns"
              }
          ]
      },
      {
        "type": "color",
        "id": "news_overlay",
        "label": "Overlay",
        "default": "#000000"
      },
      {
        "type": "range",
        "id": "news_overlay_opacity",
        "label": "Overlay opacity",
        "default": 0,
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "%"
      },
      {
        "type": "header",
        "content": "3. Design options"
      },
      {
        "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped Container"},
            { "value": "t4s-container-fluid", "label": "Full Width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background",
        "default": "#7cdff1"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background Image"
      },
      {
        "type": "checkbox",
        "id": "parallax",
        "label": "Enable parallax scroll",
        "default": false
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "default": "50px,,50px,",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Mobile Options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "4. Custom css"
      },
      {
        "id": "use_cus_css",
        "type": "checkbox",
        "label": "Use custom css",
        "default":false,
        "info": "If you want custom style for this section."
      },
      { 
        "id": "code_cus_css",
        "type": "textarea",
        "label": "Code custom css",
        "info": "Use selector .SectionID to style css"
        
      }
    ],
    "blocks": [
      {
        "type":"custom_text",
        "name":"Text",
        "settings":[
            {
                "type":"textarea",
                "id":"text",
                "label":"Input text",
                "default":"Text",
                "info":"If you want to line break, please add a <br> tag in the text"
            },
            {
                "type":"checkbox",
                "id":"remove_br_tag",
                "label":"Remove <br> tag on mobile",
                "default":false
            },
            {
              "type": "select",
              "id": "tag",
              "default": "p",
              "options": [
                 {
                    "value": "h2",
                    "label": "H2"
                 },
                 {
                    "value": "h3",
                    "label": "H3"
                 },
                 {
                    "value": "h4",
                    "label": "H4"
                 },
                 {
                    "value": "h5",
                    "label": "H5"
                 },
                 {
                    "value": "h6",
                    "label": "H6"
                 },
                 {
                    "value": "p",
                    "label": "P"
                  },
                 {
                    "value": "div",
                    "label": "DIV"
                  }
              ],
              "label": "Html tag"
            },
            {
                "type": "select",
                "id": "fontf",
                "default":"inherit",
                "label": "Font family",
                "options": [
                    {
                        "label": "Inherit",
                        "value": "inherit"
                    },
                    {
                        "label": "Font family #1",
                        "value": "1"
                    },
                    {
                        "label": "Font family #2",
                        "value": "2"
                    },
                    {
                        "label": "Font family #3",
                        "value": "3"
                    }
                ]
            },
            
            {
                "type":"color",
                "id":"text_cl",
                "label":"Color text",
                "default":"#fff"
            },
            {
                "type":"range",
                "id":"text_fs",
                "label":"Font size",
                "max":100,
                "min":10,
                "step":1,
                "unit":"px",
                "default":16
            },
            {
                "type":"range",
                "id":"text_lh",
                "label":"Line height",
                "max":100,
                "min":0,
                "step":1,
                "default":0,
                "unit":"px",
                "info":"Set is '0' use to default"            
            },
            {
                "type":"range",
                "id":"text_fw",
                "label":"Font weight",
                "min":100,
                "max":900,
                "step":100,
                "default":400
            },
            {
              "type": "number",
              "id": "text_ls",
              "label": "Letter spacing (in pixel)",
              "default": 0
            },
            {
                "type":"checkbox",
                "id":"font_italic",
                "label": "Enable font style italic",
                "default":false
            },
            {
                "type":"checkbox",
                "id":"text_shadow",
                "label": "Enable text shadow",
                "default":false
            },
            {
                "type": "number",
                "id": "text_mgb",
                "label": "Margin bottom",
                "default": 15
            },
            {
                "type":"header",
                "content":"===== Option mobile ====="
            },
            {
                "type":"checkbox",
                "id":"hidden_mobile",
                "label":"Hidden on mobile ",
                "default":false
            },
            {
                "type":"range",
                "id":"text_fs_mb",
                "label":"Font size (Mobile)",
                "max":60,
                "min":10,
                "step":1,
                "unit":"px",
                "default":16
            },
            {
                "type":"range",
                "id":"text_lh_mb",
                "label":"Line height (Mobile)",
                "max":70,
                "min":0,
                "step":1,
                "default":0,
                "unit":"px",
                "info":"Set is '0' use to default"            
            },
            {
              "type": "number",
              "id": "text_ls_mb",
              "label": "Letter spacing (Mobile)",
              "default": 0
            },
            {
                "type": "number",
                "id": "text_mgb_mobile",
                "label": "Margin bottom (Mobile)",
                "default": 10
            }
        ]
      },
      {
        "type": "html",
        "name": "HTML",
        "settings": [
          {
            "type": "html",
            "id": "html_content",
            "label": "Type html"
          },
          {
            "type":"checkbox",
            "id":"hidden_mobile",
            "label":"Hidden on mobile ",
            "default":false
          }
        ]
      },
      {
        "type": "image",
        "name": "Image (Child)",
        "settings": [
          {
            "type": "image_picker",
            "id": "image_child",
            "label": "Image (Child)"
          },
          {
            "type": "number",
            "id": "img_width",
            "label": "Image width (Unit: px)",
            "info": "Set 0 to use width default of image",
            "default": 0
          },
          {
            "type": "number",
            "id": "img_width_mb",
            "label": "Image width on mobile (Unit: px)",
            "info": "Set 0 to use width default of image",
            "default": 0
          },
          {
            "type":"checkbox",
            "id":"hidden_mobile",
            "label":"Hidden on mobile ",
            "default":false
          },
          {
            "type": "number",
            "id": "mgb",
            "label": "Margin bottom (Unit: px)",
            "default": 20
          },
          {
            "type": "number",
            "id": "mgb_mb",
            "label": "Margin bottom on mobile(Unit: px)",
            "default": 20
          }
        ]
      },
      {
        "type":"custom_button",
        "name":"Button",
        "settings":[
            {
                "type":"text",
                "id":"button_text",
                "label":"Button label",
                "default":"Button label",
                "info":"If set blank will not show"
            },
            {
                "type":"url",
                "id":"button_link",
                "label":"Button link",
                "info":"If set blank will not show"
            },
            {
                "type":"select",
                "id":"target_link",
                "label":"Open link in", 
                "default":"_self",
                "options":[
                    {
                        "value": "_self",
                        "label": "Current window"
                    },
                    {
                        "value": "_blank",
                        "label": "New window"
                    }
                ]
            },
            {
                "type": "select",
                "id": "fontf",
                "default":"inherit",
                "label": "Font family",
                "options": [
                    {
                        "label": "Inherit",
                        "value": "inherit"
                    },
                    {
                        "label": "Font family #1",
                        "value": "1"
                    },
                    {
                        "label": "Font family #2",
                        "value": "2"
                    },
                    {
                        "label": "Font family #3",
                        "value": "3"
                    }
                ]
            },
            {
                "type":"range",
                "id":"button_icon_w",
                "label":"Button icon width",
                "min":0,
                "max":50,
                "step":1,
                "unit":"px",
                "default":0
            },
            {
                "type": "select",
                "id": "button_style",
                "label": "Button style",
                "options": [
                    {
                        "label": "Default",
                        "value": "default"
                    },
                    {
                        "label": "Outline",
                        "value": "outline"
                    },
                    {
                        "label": "Bordered bottom",
                        "value": "bordered"
                    },
                    {
                        "label": "Link",
                        "value": "link"
                    }
                ]
            },
            {
              "type":"select",
              "id":"button_effect",
              "label":"Button hover effect",
              "default":"default",
              "info":"Only working button style default, outline",
              "options":[
                  {
                      "label":"Default",
                      "value":"default"
                  },
                  {
                      "label":"Fade",
                      "value":"fade"
                  },
                  {
                      "label":"Rectangle out",
                      "value":"rectangle-out"
                  },
                  {
                      "label":"Sweep to right",
                      "value":"sweep-to-right"
                  },
                  {
                      "label":"Sweep to left",
                      "value":"sweep-to-left"
                  },
                  {
                      "label":"Sweep to bottom",
                      "value":"sweep-to-bottom"
                  },
                  {
                      "label":"Sweep to top",
                      "value":"sweep-to-top"
                  },
                  {
                      "label":"Shutter out horizontal",
                      "value":"shutter-out-horizontal"
                  },
                  {
                      "label":"Outline",
                      "value":"outline"
                  },
                  {
                      "label":"Shadow",
                      "value":"shadow"
                  }
              ]
            },
            {
                "type":"color",
                "id":"pri_cl",
                "label":"Primary color",
                "default":"#222"
            },
            {
                "type":"color",
                "id":"second_cl",
                "label":"Secondary color"
            },
            {
                "type":"color",
                "id":"pri_cl_hover",
                "label":"Primary color hover",
                "default":"#56cfe1"
            },
            {
                "type":"color",
                "id":"second_cl_hover",
                "label":"Secondary color hover",
                "info":"Only working button style default, outline",
                "default":"#fff"
            },
            {
                "type":"range",
                "id":"fsbutton",
                "label":"Font size",
                "max":50,
                "min":10,
                "step":1,
                "unit":"px",
                "default":14
            },
            {
                "type":"range",
                "id":"fwbutton",
                "label":"Font weight",
                "min":100,
                "max":900,
                "step":100,
                "default":400
            },
            {
                "type":"range",
                "id":"button_ls",
                "label":"Letter spacing",
                "min":0,
                "max":10,
                "step":0.1,
                "unit":"px",
                "default":0
            },
            {
                "type":"range",
                "id":"button_mh",
                "label":"Min height",
                "min":20,
                "max":80,
                "step":1,
                "unit":"px",
                "default":42,
                "info":"Only working button style default, outline"
            },
            {
                "type":"range",
                "id":"button_bdr",
                "label":"Border radius",
                "min":0,
                "max":40,
                "step":1,
                "unit":"px",
                "default":0,
                "info":"Only working button style default, outline"
            },
            {
                "type":"range",
                "id":"button_pd_lr",
                "label":"Padding left/right",
                "min":0,
                "max":70,
                "step":1,
                "unit":"px",
                "default":30,
                "info":"Only working button style default, outline"
            },
            {
                "type": "number",
                "id": "button_mgb",
                "label": "Margin bottom",
                "default": 0
            },
            {
                "type":"header",
                "content":"+ Option mobile"
            },
            {
                "type":"checkbox",
                "id":"hidden_mobile",
                "label":"Hidden on mobile ",
                "default":false
            },
            {
                "type":"range",
                "id":"button_icon_w_mb",
                "label":"Button icon width (Mobile)",
                "min":0,
                "max":50,
                "step":1,
                "unit":"px",
                "default":0
            },
            {
                "type":"range",
                "id":"fsbutton_mb",
                "label":"Font size (Mobile)",
                "max":50,
                "min":0,
                "step":1,
                "unit":"px",
                "default":12
            },
            {
                "type":"range",
                "id":"button_mh_mb",
                "label":"Min height (Mobile)",
                "min":10,
                "max":50,
                "step":1,
                "unit":"px",
                "default":36,
                "info":"Only working button style default, outline"
            },
            {
                "type":"range",
                "id":"button_pd_lr_mb",
                "label":"Padding left/right (Mobile)",
                "min":0,
                "max":50,
                "step":1,
                "unit":"px",
                "default":15,
                "info":"Only working button style default, outline"
            },
            {
              "type":"range",
              "id":"button_ls_mb",
              "label":"Letter spacing (Mobile)",
              "min":0,
              "max":10,
              "step":0.1,
              "unit":"px",
              "default":0
          },
            {
                "type": "number",
                "id": "button_mgb_mb",
                "label": "Margin bottom (Mobile)",
                "default": 0
            }
        ]
      },
      {
        "type": "countdown",
        "name": "Countdown timer",
        "limit": 4,
        "settings":[
          {
            "type": "text",
            "id": "date",
            "label": "Date countdown",
            "default": "2023\/12\/26",
            "info": "Countdown to the end sale date will be shown"
          },
          {
            "type": "select",
            "id": "cdt_des",
            "label": "Countdown design",
            "default": "1",
            "options": [
              {
                  "value": "1",
                  "label": "Design 1"
              },
              {
                  "value": "2",
                  "label": "Design 2"
              }
            ]
          },
          {
            "type": "select",
            "id": "cdt_size",
            "label": "Countdown size",
            "default": "medium",
            "options": [
              {
              "value": "small",
              "label": "Small"
              },
              {
                  "value": "medium",
                  "label": "Medium"
              },
              {
                  "value": "large",
                  "label": "Large"
              },
              {
                  "value": "extra_large",
                  "label": "Extra large"
              }
            ]
          },
          {
            "type": "range",
            "id": "box_bdr",
            "label": "Border radius",
            "default": 0,
            "min": 0,
            "max": 50,
            "step": 1,
            "unit": "%"
          },
          {
            "type": "range",
            "id": "bd_width",
            "label": "Border width",
            "default": 0,
            "min": 0,
            "max": 5,
            "step": 1,
            "unit": "px"
          },
          {
            "type": "range",
            "id": "space_item",
            "label": "Space between items",
            "default": 10,
            "min": 0,
            "max": 30,
            "step": 1,
            "unit": "px"
          },
          {
            "type": "color",
            "id": "number_cl",
            "label": "Number color",
            "default": "#fff"
          },
          {
            "type": "color",
            "id": "text_cl",
            "label": "Text color",
            "default": "#fff"
          },
          {
            "type": "color",
            "id": "border_cl",
            "label": "Border color item time",
            "default": "#000"
          },
          {
            "type": "color",
            "id": "bg_cl",
            "label": "Background item time",
            "default": "#000"
          },
          {
            "type":"checkbox",
            "id":"hidden_mobile",
            "label":"Hidden on mobile ",
            "default":false
          },
          {
            "type": "number",
            "id": "mgb",
            "label": "Margin bottom",
            "default": 15
          },
          {
            "type": "number",
            "id": "mgb_mb",
            "label": "Margin bottom (Mobile)",
            "default": 10
          }
        ]
      },
      {
        "type": "newsletter",
        "name": "Form newsletter",
        "limit": 1,
        "settings": [
          {
            "type": "number",
            "id": "form_width",
            "label": "Maximum form width",
            "info": "Default is 100% when you set to \"0\" (Unit:px)",
            "default":0
          },
          {
            "type": "number",
            "id": "form_width_mb",
            "label": "Maximum form width (Mobile)",
            "info": "Default is 100% when you set to \"0\" (Unit:px)",
             "default":0
          },
          {
            "type": "select",
            "id": "newl_des",
            "label": "Newsletter design",
            "info": "Design 11 always show icon",
            "default": "1",
            "options": [
                {
                  "value": "1",
                  "label": "Design 1"
                },
                {
                  "value": "2",
                  "label": "Design 2"
                },
                {
                  "value": "3",
                  "label": "Design 3"
                },
                {
                  "value": "4",
                  "label": "Design 4"
                },
                {
                  "value": "5",
                  "label": "Design 5"
                },
                {
                  "value": "6",
                  "label": "Design 6"
                },
                {
                  "value": "7",
                  "label": "Design 7"
                },
                {
                  "value": "8",
                  "label": "Design 8"
                },
                {
                  "value": "9",
                  "label": "Design 9"
                },
                {
                  "value": "10",
                  "label": "Design 10"
                },
                {
                  "value": "11",
                  "label": "Design 11"
                },
                {
                  "value": "12",
                  "label": "Design 12"
                },
                {
                  "value": "13",
                  "label": "Design 13"
                },
                {
                  "value": "14",
                  "label": "Design 14"
                },
                {
                  "value": "15",
                  "label": "Design 15"
                }
              ]
          },
          {
            "type": "select",
            "id": "news_align",
            "label": "Newsletter input align",
            "default": "center",
            "options":[
                {
                  "label":"Default",
                  "value":"start"
                },
                {
                  "label":"Center",
                  "value":"center"
                }
            ]
          },
          {
          "type": "select",
          "id": "newl_size",
          "label": "Newsletter size",
          "default": "small",
          "options": [
              {
                "value": "small",
                "label": "Small"
              },
              {
                "value": "medium",
                "label": "Medium"
              },
              {
                "value": "large",
                "label": "Large"
              }
            ]
          },
          {
            "type": "checkbox",
            "id": "btn_icon",
            "label": "Show button icon",
            "default": false
          },
          {
            "type": "color",
            "id": "input_cl",
            "label": "Input color",
            "default": "#878787"
          },
          {
            "type": "color",
            "id": "border_cl",
            "label": "Background/Border color",
            "default": "#000"
          },
          {
            "type": "color",
            "id": "btn_cl",
            "label": "Button color",
            "default": "#ffffff"
          },
          {
            "type": "color",
            "id": "btn_bg_cl",
            "label": "Button background color",
            "default": "#222222"
          },
          {
            "type": "color",
            "id": "btn_hover_cl",
            "label": "Button hover color",
            "default": "#ffffff"
          },
          {
            "type": "color",
            "id": "btn_hover_bg_cl",
            "label": "Button hover background color",
            "default": "#56CFE1"
          },
          {
            "type":"checkbox",
            "id":"hidden_mobile",
            "label":"Hidden on mobile ",
            "default":false
          },
          {
            "type": "number",
            "id": "mgb",
            "label": "Margin bottom",
            "default": 15
          },
          {
            "type": "number",
            "id": "mgb_mb",
            "label": "Margin bottom (Mobile)",
            "default": 10
          }
        ]
      },
      {
        "type": "space_html",
        "name": "Space HTML",
        "settings":[
          {
            "type": "color",
            "id": "color",
            "label": "Color",
            "default": "#fff"
          },
          {
            "type": "range",
            "id": "width",
            "min": 30,
            "max": 150,
            "step": 5,
            "label": "Width",
            "unit": "px",
            "default": 40
          },
          {
            "type": "range",
            "id": "height",
            "min": 1,
            "max": 5,
            "step": 0.5,
            "label": "Height",
            "unit": "px",
            "default": 2
          },
          {
            "type": "number",
            "id": "mgb",
            "label": "Margin bottom (Unit: px)",
            "default": 20
          },
          {
            "type": "paragraph",
            "content": "————————————————"
          },
          {
            "type": "range",
            "id": "width_mb",
            "min": 30,
            "max": 130,
            "step": 5,
            "label": "Width",
            "unit": "px",
            "default": 40
          },
          {
            "type":"checkbox",
            "id":"hidden_mobile",
            "label":"Hidden on mobile ",
            "default":false
          },
          {
            "type": "range",
            "id": "height_mb",
            "min": 1,
            "max": 5,
            "step": 0.5,
            "label": "Height (Mobile)",
            "default": 2
          },
          {
            "type": "number",
            "id": "mgb_mb",
            "label": "Margin bottom on mobile(Unit: px)",
            "default": 20
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Newsletter",
        "category": "Homepage",
        "blocks": [
          { "type": "custom_text",
            "settings": {
              "text": "Subscribe to our newsletter",
              "text_fs": 24,
              "text_cl": "#000",
              "text_lh": 0,
              "text_fw": 600,
              "text_mgb": 0
            }
          },
          { "type": "custom_text",
            "settings": {
              "text": "A short sentence describing what someone will receive by subscribing",
              "text_fs": 14,
              "text_cl": "#878787",
              "text_lh": 24,
              "text_fw": 400,
              "text_mgb": 30
            }
          },
          { "type": "newsletter" }
        ]
      }
    ]
  }
{% endschema %}