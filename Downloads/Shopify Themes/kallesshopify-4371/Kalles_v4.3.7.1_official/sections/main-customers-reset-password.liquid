{{ 'customer.min.css' | asset_url | stylesheet_tag }}

<div class="t4s-customer is--reset-password">
  <svg style="display: none">
    <symbol id="icon-error" viewBox="0 0 13 13">
      <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
      <circle cx="6.5" cy="6.5" r="5.5" fill="var(--t4s-error-color)" stroke="var(--t4s-error-color)" stroke-width="0.7"/>
      <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
      <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="var(--t4s-error-color)" stroke-width="0.7">
    </symbol>
  </svg>

  <p class="reset_password_subtext">{{ 'customer.reset_password.subtext' | t: email: email }}</p>
  {%- form 'reset_customer_password',class: 't4s-w-100' -%}
    {%- if form.errors -%}
      <h2 class="form__message" tabindex="-1" autofocus>
        <svg aria-hidden="true" focusable="false" role="presentation">
          <use href="#icon-error" />
        </svg>
        {{ 'customer.form.error_heading' | t }}
      </h2>
      <ul>
        {%- for field in form.errors -%}
          <li>
            {%- if field == 'form' -%}
              {{ form.errors.messages[field] }}
            {%- else -%}
              <a href="#{{ field }}">
                {{ form.errors.translated_fields[field] | capitalize }}
                {{ form.errors.messages[field] }}
              </a>
            {%- endif -%}
          </li>
        {%- endfor -%}
      </ul>
    {%- endif -%}

    <div class="t4s_field t4s-pr t4s_mb_30">      
      <input class="t4s_frm_input" 
        type="password"
        name="customer[password]"
        id="password"
        autocomplete="new-password"
        {% if form.errors contains 'password' %}
          aria-invalid="true"
          aria-describedby="password-error"
        {% endif %}
        placeholder="{{ 'customer.reset_password.password' | t }}"
      >
      <label for="password">
        {{ 'customer.reset_password.password' | t }}
      </label>
    </div>
   {%- if form.errors contains 'password' -%}
     <small id="password-error" class="form__message">
       <svg aria-hidden="true" focusable="false" role="presentation">
         <use href="#icon-error" />
       </svg>
       {{ form.errors.translated_fields['password'] | capitalize }} {{ form.errors.messages['password'] }}
     </small>
   {%- endif -%}

    <div class="t4s_field t4s-pr t4s_mb_30">      
      <input class="t4s_frm_input" 
        type="password"
        name="customer[password_confirmation]"
        id="password_confirmation"
        autocomplete="new-password"
        {% if form.errors contains 'password_confirmation' %}
          aria-invalid="true"
          aria-describedby="password_confirmation-error"
        {% endif %}
        placeholder="{{ 'customer.reset_password.password_confirm' | t }}"
      >
      <label for="password_confirmation">
        {{ 'customer.reset_password.password_confirm' | t }}
      </label>
    </div>
   {%- if form.errors contains 'password_confirmation' -%}
     <small id="password_confirmation-error" class="form__message">
       <svg aria-hidden="true" focusable="false" role="presentation">
         <use href="#icon-error" />
       </svg>
       {{ form.errors.translated_fields['password_confirmation'] | capitalize }} {{ form.errors.messages['password_confirmation'] }}
     </small>
   {%- endif -%}

    <button class="t4s_btn_submmit t4s_btn_black t4s_mb_20">{{ 'customer.reset_password.submit' | t }}</button>

  {%- endform -%}

</div>

{%- schema -%}
{
  "name": "Register",
  "tag": "section",
  "class": "t4s-section t4s-section-customers t4s-container"
}
{% endschema %}