<!-- section/banner-with-text.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'content-position.css' | asset_url | stylesheet_tag }}
{{ 'banner.css' | asset_url | stylesheet_tag }}
<link href="{{ 't4s-animation.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  assign general_block = false
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign image_ratio = se_stts.image_ratio
  if image_ratio == "ratioadapt"
    assign imgatt = ''
   else 
    assign imgatt = 'data-'
  endif
  assign index = 0
  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif 
 -%} 
<div class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }} {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
  {{- html_layout[0] -}} 
  {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
  <div class="t4s-banner-holder t4s-pr isotopet4s t4s_position_{{ se_stts.image_position }} t4s_{{ se_stts.image_size }} t4s-equal-height-false t4s-row t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }} {% if se_stts.hidden_content_first %}t4s_hidden_content_first{% endif %}" data-isotopet4s-js='{ "itemSelector": ".t4s-banner-wrap", "layoutMode": "packery" }'>
    {%liquid
      assign bk_stts = block.settings 
      assign se_stts = section.settings
      assign index = index | plus: 1
      assign image_1 = se_stts.image_1
      assign image_2 = se_stts.image_2
      assign bg_content_op = se_stts.bg_content_op | divided_by: 100.0 
      assign bg_content = se_stts.bg_content_cl | color_modify: 'alpha', bg_content_op 
      assign ani_delay = 0   
      assign percent_delay = se_stts.animation_delay | divided_by: 100.0
      assign time_ani_delay = se_stts.time_animation | times: percent_delay
    %}
      {%- capture append_bg_content_style -%}
        --bg-content:{{ bg_content }};--content-pd:{{ se_stts.content_pd_tb }}px {{ se_stts.content_pd_lr }}px;--content-pd-mb:{{ se_stts.content_pd_tb_mb }}px {{ se_stts.content_pd_lr_mb }}px;
      {%- endcapture -%}
    <div class="t4s-col-item t4s-banner-wrap t4s-col-lg-6 t4s-col-md-6 t4s-col-12">  
      <div data-t4s-animate class="t4s-banner-item t4s-eff t4s-eff-{{ se_stts.b_effect }} t4s-eff-img-{{ se_stts.img_effect }} t4s_{{ image_ratio }} t4scuspx1_{{ se_stts.custom_mb }} t4scuspx2_{{ se_stts.custom_tb }} t4scuspx3_{{ se_stts.custom_dk }}" {% if image_ratio != "ratio_fh" %} style="--aspect-ratioapt: {{ image_parallax.aspect_ratio | default: 2 }};--aspect-ratioaptmb: {{ image_mb.aspect_ratio | default: 2 }};--aspect-ratio-cusdt : {{ se_stts.height_dk }}px;--aspect-ratio-custb : {{ se_stts.height_tb }}px;--aspect-ratio-cusmb :{{ se_stts.height_mb }}px;"{%- endif %} timeline hdt-reveal="slide-in">
        <div class="t4s-banner-inner">
          {%- if se_stts.b_link_1 != blank -%}
            {%- assign ARRhtml = 'a,,' | split: ',' -%}
          {%- else -%}
            {%- assign ARRhtml = 'div,data-,data-' | split: ',' -%}
          {%- endif -%}
          <{{ ARRhtml[0] }} {{ ARRhtml[1] }}href="{{ se_stts.b_link_1 }}"  {{ ARRhtml[2] }}target="{{ se_stts.open_link }}" class="t4s-d-block t4s_ratio t4s_ratio_hasmb" {{ imgatt }}style="--aspect-ratioapt: {{ image_1.aspect_ratio | default: 2 }};--aspect-ratioaptmb: {{ image_mb.aspect_ratio | default: 2 }};">
            {%- if image_1 != blank -%}
              <img {% if image.presentation.focal_point != '50.0% 50.0%' %} style="object-position: {{ image_1.presentation.focal_point }}"{% endif %} class="lazyloadt4s t4s-lz--fadeIn t4s-obj-eff t4s-d-block" data-src="{{ image_1 | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image_1.width, h: image_1.height %}" width="{{ image_1.width }}" height="{{ image_1.height }}" alt="{{ image_1.alt | escape }}">
              <span class="lazyloadt4s-loader is-bg-img t4s-d-block" style="background: url({{ image_1 | image_url: width: 1 }})"></span>
            {%- else -%}
              {{ 'image' | placeholder_svg_tag: 't4s-placeholder-svg t4s-svg-bg1 t4s-obj-eff' }}
            {%- endif -%}
          </{{ ARRhtml[0] }}>
        </div>
      </div>
    </div>
    <div class="t4s-col-item t4s-banner-wrap t4s-col-lg-6 t4s-col-md-6 t4s-col-12">
      <div data-t4s-animate class="t4s-banner-item t4s-eff t4s-eff-{{ se_stts.b_effect }} t4s-eff-img-{{ se_stts.img_effect }} t4s_{{ image_ratio }} t4scuspx1_{{ se_stts.custom_mb }} t4scuspx2_{{ se_stts.custom_tb }} t4scuspx3_{{ se_stts.custom_dk }}" {% if image_ratio != "ratio_fh" %} style="--aspect-ratioapt: {{ image_parallax.aspect_ratio | default: 2 }};--aspect-ratioaptmb: {{ image_mb.aspect_ratio | default: 2 }};--aspect-ratio-cusdt : {{ se_stts.height_dk }}px;--aspect-ratio-custb : {{ se_stts.height_tb }}px;--aspect-ratio-cusmb :{{ se_stts.height_mb }}px;"{%- endif %} timeline hdt-reveal="slide-in">
        <div class="t4s-banner-inner">
          {%- if se_stts.b_link_2 != blank -%}
            {%- assign ARRhtml = 'a,,' | split: ',' -%}
          {%- else -%}
            {%- assign ARRhtml = 'div,data-,data-' | split: ',' -%}
          {%- endif -%}
          <{{ ARRhtml[0] }} {{ ARRhtml[1] }}href="{{ se_stts.b_link_2 }}"  {{ ARRhtml[2] }}target="{{ se_stts.open_link }}" class="t4s-d-block t4s_ratio t4s_ratio_hasmb" {{ imgatt }}style="--aspect-ratioapt: {{ image_2.aspect_ratio | default: 2 }};--aspect-ratioaptmb: {{ image_mb.aspect_ratio | default: 2 }};">
            {%- if image_2 != blank -%}
              <img {% if image.presentation.focal_point != '50.0% 50.0%' %} style="object-position: {{ image_2.presentation.focal_point }}"{% endif %} class="lazyloadt4s t4s-lz--fadeIn t4s-obj-eff t4s-d-block" data-src="{{ image_2 | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image_2.width, h: image_2.height %}" width="{{ image_2.width }}" height="{{ image_2.height }}" alt="{{ image_2.alt | escape }}">
              <span class="lazyloadt4s-loader is-bg-img t4s-d-block" style="background: url({{ image_2 | image_url: width: 1 }})"></span>
            {%- else -%}
              {{ 'image' | placeholder_svg_tag: 't4s-placeholder-svg t4s-svg-bg1 t4s-obj-eff' }}
            {%- endif -%}
          </{{ ARRhtml[0] }}>
        </div>
      </div>
    </div>
    <div class="t4s-banner-content t4s-content-position t4s-pa t4s-{{ se_stts.content_width }} t4s-pe-none t4s-text-md-{{ se_stts.content_align }} t4s-text-{{ se_stts.content_align_mobile }} t4s-bg-content-true t4s_animated" style="--time-animation:{{ se_stts.time_animation }}s; {%- render 'position_content', ch_pos: 50 , cv_pos: 50, ch_pos_mb: 50, cv_pos_mb: 50, append_bg_content_style: append_bg_content_style -%}">
      {% for block in se_blocks %}
        {%- assign bk_stts = block.settings -%}
        {%- case block.type -%}
          {%- when 'custom_text' -%}
            {% if bk_stts.text != blank %}
              <{{ bk_stts.tag }} data-lh="{{ bk_stts.text_lh_mb }}" data-lh-md="{{ bk_stts.text_lh }}" data-lh-lg="{{ bk_stts.text_lh }}" class="t4s-bl-item t4s-pe-none t4s-animation-{{ bk_stts.animation }} t4s-text-bl t4s-fnt-fm-{{ bk_stts.fontf }} t4s-font-italic-{{ bk_stts.font_italic }} t4s-hidden-mobile-{{ bk_stts.hidden_mobile }} t4s-br-mb-{{ bk_stts.remove_br_tag }} t4s-text-shadow-{{ bk_stts.text_shadow }}" id="b_{{ block.id }}" {%- render 'bk_cus_style', type: 'custom_text', bk_stts: bk_stts, ani_delay: ani_delay -%}>{{ bk_stts.text }}</{{ bk_stts.tag }}>
            {% endif %}
          {%- when 'custom_button' -%}
            {%- if bk_stts.button_link != blank and bk_stts.button_text != blank -%}
              {%- assign  button_style = bk_stts.button_style -%}
              <a href="{{ bk_stts.button_link }}" target="{{ bk_stts.target_link }}" class="t4s-bl-item t4s-animation-{{ bk_stts.animation }} t4s-btn t4s-btn-custom  t4s-pe-auto t4s-fnt-fm-{{ bk_stts.fontf }} t4s-animation-{{ bk_stts.animation }} t4s-hidden-mobile-{{ bk_stts.hidden_mobile }} t4s-btn-style-{{ button_style }} {% if button_style == 'default' or button_style == 'outline' %}t4s-btn-effect-{{ bk_stts.button_effect }}{% endif %}" id="b_{{ block.id }}" {{ block.shopify_attributes }} {%- render 'bk_cus_style', type: 'custom_button', bk_stts: bk_stts, ani_delay: ani_delay -%}>{{ bk_stts.button_text }} {%- if bk_stts.button_icon_w > 0 -%}<svg class="t4s-btn-icon" width="14"><use xlink:href="#t4s-icon-btn"></use></svg>{%- endif -%}</a>
            {%- endif -%}
        {%- endcase -%}
        {%- if bk_stts.animation != 'none' %}
          {% assign ani_delay = ani_delay | plus: time_ani_delay %}
        {% endif -%}
      {% endfor %}
    </div>
  </div>
 {{- html_layout[1] -}} 
</div>
{% schema %}
  {
    "name": "Banner with text",
    "tag":"section",
    "class": "t4s-section t4s-section-all t4s_tp_cdt t4s_tp_cd t4s_tp_istope t4s-banner-with-text",
    "settings": [
      {
        "type": "header",
        "content": "1. General options"
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Banner ratio",
        "default": "ratioadapt",
        "options": [
          {
            "value": "ratio_fh",
            "label": "Full screen"
          },
          {
            "value": "ratioadapt",
            "label": "Adapt to image"
          },
          {
            "value": "ratio_cuspx",
            "label": "Custom height"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "custom_dk",
        "label": "Use custom height (Desktop)",
        "default": true
      },
      {
        "type": "number",
        "id": "height_dk",
        "label": "Banner height (Desktop)",
        "default": 600
      },
      {
        "type": "checkbox",
        "id": "custom_tb",
        "label": "Use custom height (Tablet)",
        "default": true
      },
      {
        "type": "number",
        "id": "height_tb",
        "label": "Banner height (Tablet)",
        "default": 400
      },
      {
        "type": "checkbox",
        "id": "custom_mb",
        "label": "Use custom height (Mobile)",
        "default": true
      },
      {
        "type": "number",
        "id": "height_mb",
        "label": "Banner height (Mobile)",
        "default": 300
      },
      {
        "type": "image_picker",
        "id": "image_1",
        "label": "Image first"
      },
      {
        "type": "image_picker",
        "id": "image_2",
        "label": "Image last"
      },
      {
        "type": "url",
        "id": "b_link_1",
        "label": "Image frist link"
      },
      {
        "type": "url",
        "id": "b_link_2",
        "label": "Image last link"
      },
      {
        "type": "select",
        "id": "open_link",
        "options": [
          {
            "value": "_self",
            "label": "Current window"
          },
         {
            "value": "_blank",
            "label": "New window"
          }
        ],
        "label": "Open link in",
        "default": "_self"
      },
      {
        "type": "select",
        "id": "img_effect",
        "label": "Image hover effect",
        "info": "Waring: Hovering effect will resize your images",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "zoom",
            "label": "Zoom in"
          },
          {
            "value": "rotate",
            "label": "Rotate"
          },
          {
            "value": "translateToTop",
            "label": "Move to top"
          },
          {
            "value": "translateToRight",
            "label": "Move to right"
          },
          {
            "value": "translateToBottom",
            "label": "Move to bottom"
          },
          {
            "value": "translateToLeft",
            "label": "Move to left"
          },
          {
            "value": "filter",
            "label": "Filter"
          },
          {
            "value": "bounceIn",
            "label": "BounceIn"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_size",
        "label": "Image size",
        "default": "cover",
        "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "cover",
            "label": "Full"
          },
          {
            "value": "contain",
            "label": "Auto"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "info": "The first value is the horizontal position and the second value is the vertical. These settings apply only if the image ratio is not set to 'Adapt to image', it also does not work when using 'Focal point' on the image.",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "1",
            "label": "Left top"
          },
          {
            "value": "2",
            "label": "Left center"
          },
          {
            "value": "3",
            "label": "Left bottom"
          },
          {
            "value": "4",
            "label": "Right top"
          },
          {
            "value": "5",
            "label": "Right center"
          },
          {
            "value": "6",
            "label": "Right bottom"
          },
          {
            "value": "7",
            "label": "Center top"
          },
          {
            "value": "8",
            "label": "Center center"
          },
          {
            "value": "9",
            "label": "Center bottom"
          }
        ],
        "label": "Image position",
        "default": "8"
      },
      // {
      //   "type":"checkbox",
      //   "id":"equal_height",
      //   "label":"Enable equal items height",
      //   "info": "Use when the item widths are not equal. Only working on desktop and banner height = \"Adapt to image\"",
      //   "default":false
      // },
      {
        "type": "select",
        "id": "space_h_item",
        "options": [
          {
            "value": "0", 
            "label": "0"
          },
          {
            "value": "2", 
            "label": "2px"
          },
          {
            "value": "4", 
            "label": "4px"
          },
          {
            "value": "6", 
            "label": "6px"
          },
          {
            "value": "8", 
            "label": "8px"
          },
          {
            "value": "10", 
            "label": "10px"
          },
          {
            "value": "15",
            "label": "15px"
          },
          {
            "value": "20",
            "label": "20px"
          },
          {
            "value": "30",
            "label": "30px"
          }
        ],
        "label": "Space horizontal items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_v_item",
        "options": [
          {
            "value": "0", 
            "label": "0"
          },
          {
            "value": "2", 
            "label": "2px"
          },
          {
            "value": "4", 
            "label": "4px"
          },
          {
            "value": "6", 
            "label": "6px"
          },
          {
            "value": "8", 
            "label": "8px"
          },
          {
            "value": "10", 
            "label": "10px"
          },
          {
            "value": "15",
            "label": "15px"
          },
          {
            "value": "20",
            "label": "20px"
          },
          {
            "value": "30",
            "label": "30px"
          }
        ],
        "label": "Space vertical items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_h_item_mb",
        "options": [
          {
            "value": "0", 
            "label": "0"
          },
          {
            "value": "2", 
            "label": "2px"
          },
          {
            "value": "4", 
            "label": "4px"
          },
          {
            "value": "6", 
            "label": "6px"
          },
          {
            "value": "8", 
            "label": "8px"
          },
          {
            "value": "10", 
            "label": "10px"
          },
          {
            "value": "15",
            "label": "15px"
          },
          {
            "value": "20",
            "label": "20px"
          },
          {
            "value": "30",
            "label": "30px"
          }
        ],
        "label": "Space horizontal items (Mobile)",
        "default": "10"
      },
      {
        "type": "select",
        "id": "space_v_item_mb",
        "options": [
          {
            "value": "0", 
            "label": "0"
          },
          {
            "value": "2", 
            "label": "2px"
          },
          {
            "value": "4", 
            "label": "4px"
          },
          {
            "value": "6", 
            "label": "6px"
          },
          {
            "value": "8", 
            "label": "8px"
          },
          {
            "value": "10", 
            "label": "10px"
          },
          {
            "value": "15",
            "label": "15px"
          },
          {
            "value": "20",
            "label": "20px"
          },
          {
            "value": "30",
            "label": "30px"
          }
        ],
        "label": "Space vertical items (Mobile)",
        "default": "10"
      },
      {
        "type":"header",
        "content":"2. Content option"
      },
      {
        "type": "select",
        "id": "content_align",
        "label": "Content align",
        "default": "center",
        "options":[
            {
              "label":"Left",
              "value":"start"
            },
            {
              "label":"Center",
              "value":"center"
            },
            {
              "label":"Right",
              "value":"end"
            }
        ]
      },
      {
        "type":"select",
        "id":"content_align_mobile",
        "label":"Content align (Mobile)",
        "default":"center",
        "options":[
          {
            "label":"Left",
            "value":"start"
          },
          {
            "label":"Center",
            "value":"center"
          },
          {
            "label":"Right",
            "value":"end"
          }
        ]
      },
      {
        "type": "select",
        "id": "content_width",
        "options": [
          {
            "label": "Auto",
            "value": "auto"
          },
          {
            "label": "Fullwidth",
            "value": "fullwidth"
          },
          {
            "label": "Container (Only choose when banner fullwidth)",
            "value": "container"
          }
        ],
        "label": "Content width",
        "default": "auto"
      },
      {
        "type": "header",
        "content": "+ Content background, color options"
      },
      {
          "type": "color",
          "id": "bg_content_cl",
          "label": "Background color",
          "default": "#fff"
      },
      {
          "type": "range",
          "id": "bg_content_op",
          "label": "Background color opacity",
          "default": 50,
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%"
      },
      {
          "type": "number",
          "id": "content_pd_tb",
          "label": "Content padding top/bottom (px)",
          "default":20,
          "info":"Working on the desktop"   
      },
      {
          "type": "number",
          "id": "content_pd_lr",
          "label": "Content padding left/right (px)",
          "default":20 ,
          "info":"Working on the desktop"   
      },
      {
          "type": "number",
          "id": "content_pd_tb_mb",
          "label": "Content padding top/bottom (px)",
          "default":10,
          "info":"Working on the mobile"    
      },
      {
          "type": "number",
          "id": "content_pd_lr_mb",
          "label": "Content padding left/right (px)",
          "default":10,
          "info":"Working on the mobile"
      },
      {
        "type": "header",
        "content": "--Animation options--"
      },
      {
        "type":"range",
        "id":"time_animation",
        "label":"Duration animation each block",
        "max":5,
        "min":1,
        "default":1,
        "unit":"s",
        "step":0.5
      },
      {
        "type":"range",
        "id":"animation_delay",
        "label":"Time animation delay",
        "max":110,
        "min":10,
        "step":10,
        "unit":"%",
        "default":40,
        "info":"Defines the number of time to wait when the animation previous end, before the animation next will start."
      },
      {
        "type": "header",
        "content": "3. Design options"
      },
      {
        "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
        "options": [
          { "value": "t4s-se-container", "label": "Container"},
          { "value": "t4s-container-wrap", "label": "Wrapped container"},
          { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
        "type": "text",
        "id": "mg",
        "label": "Margin",
        "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
        "default": ",,50px,",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd",
        "label": "Padding",
        "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
        "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      }, 
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
        "type": "text",
        "id": "mg_mb",
        "label": "Margin",
        "default": ",,30px,",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_mb",
        "label": "Padding",
        "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "4. Custom css"
      },
      {
        "id": "use_cus_css",
        "type": "checkbox",
        "label": "Use custom css",
        "default":false,
        "info": "If you want custom style for this section."
      },
      { 
        "id": "code_cus_css",
        "type": "textarea",
        "label": "Code custom css",
        "info": "Use selector .SectionID to style css"
        
      }
    ],
    "blocks":[
      {
        "type":"custom_text",
        "name":"Text",
        "settings":[
            {
                "type":"textarea",
                "id":"text",
                "label":"Input text",
                "default":"Text",
                "info":"If you want to line break, please add a <br> tag in the text"
            },
            {
                "type":"checkbox",
                "id":"remove_br_tag",
                "label":"Remove <br> tag on mobile",
                "default":false
            },
            {
              "type": "select",
              "id": "tag",
              "default": "p",
              "options": [
                 {
                    "value": "h2",
                    "label": "H2"
                 },
                 {
                    "value": "h3",
                    "label": "H3"
                 },
                 {
                    "value": "h4",
                    "label": "H4"
                 },
                 {
                    "value": "h5",
                    "label": "H5"
                 },
                 {
                    "value": "h6",
                    "label": "H6"
                 },
                 {
                    "value": "p",
                    "label": "P"
                  },
                 {
                    "value": "div",
                    "label": "DIV"
                  }
              ],
              "label": "Html tag"
            },
            {
                "type": "select",
                "id": "fontf",
                "default":"inherit",
                "label": "Font family",
                "options": [
                    {
                        "label": "Inherit",
                        "value": "inherit"
                    },
                    {
                        "label": "Font family #1",
                        "value": "1"
                    },
                    {
                        "label": "Font family #2",
                        "value": "2"
                    },
                    {
                        "label": "Font family #3",
                        "value": "3"
                    }
                ]
            },
            
            {
                "type":"color",
                "id":"text_cl",
                "label":"Color text",
                "default":"#fff"
            },
            {
                "type":"range",
                "id":"text_fs",
                "label":"Font size",
                "max":100,
                "min":10,
                "step":1,
                "unit":"px",
                "default":16
            },
            {
                "type":"range",
                "id":"text_lh",
                "label":"Line height",
                "max":100,
                "min":0,
                "step":1,
                "default":0,
                "unit":"px",
                "info":"Set is '0' use to default"            
            },
            {
                "type":"range",
                "id":"text_fw",
                "label":"Font weight",
                "min":100,
                "max":900,
                "step":100,
                "default":400
            },
            {
                "type":"range",
                "id":"text_ls",
                "label":"Letter spacing",
                "max":10,
                "min":0,
                "default":0,
                "step":0.1,
                "unit":"px"
            },
            {
                "type":"checkbox",
                "id":"font_italic",
                "label": "Enable font style italic",
                "default":false
            },
            {
                "type":"checkbox",
                "id":"text_shadow",
                "label": "Enable text shadow",
                "default":false
            },
            {
                "type": "number",
                "id": "text_mgb",
                "label": "Margin bottom",
                "default": 15
            },
            {
                "type":"header",
                "content":"===== Option mobile ====="
            },
            {
                "type":"checkbox",
                "id":"hidden_mobile",
                "label":"Hidden on mobile ",
                "default":false
            },
            {
                "type":"range",
                "id":"text_fs_mb",
                "label":"Font size (Mobile)",
                "max":60,
                "min":10,
                "step":1,
                "unit":"px",
                "default":16
            },
            {
                "type":"range",
                "id":"text_lh_mb",
                "label":"Line height (Mobile)",
                "max":70,
                "min":0,
                "step":1,
                "default":0,
                "unit":"px",
                "info":"Set is '0' use to default"            
            },
            {
              "type": "number",
              "id": "text_ls_mb",
              "label": "Letter spacing (Mobile)",
              "default": 0
            },
            {
                "type": "number",
                "id": "text_mgb_mobile",
                "label": "Margin bottom (Mobile)",
                "default": 10
            },
            {
              "type": "paragraph",
              "content": "————————————————"
            },
            {
              "type":"select",
              "id":"animation",
              "label":"Animation",
              "default":"none",
              "options":[
                {
                    "label":"None",
                    "value":"none"
                },
                {
                    "label":"fadeIn",
                    "value":"fadeIn"
                },
                {
                    "label":"fadeInDown",
                    "value":"fadeInDown"
                },
                {
                    "label":"fadeInDownBig",
                    "value":"fadeInDownBig"
                },
                {
                    "label":"fadeInLeft",
                    "value":"fadeInLeft"
                },
                {
                    "label":"fadeInLeftBig",
                    "value":"fadeInLeftBig"
                },
                {
                    "label":"fadeInRight",
                    "value":"fadeInRight"
                },
                {
                    "label":"fadeInRightBig",
                    "value":"fadeInRightBig"
                },
                {
                    "label":"fadeInUp",
                    "value":"fadeInUp"
                },
                {
                    "label":"fadeInUpBig",
                    "value":"fadeInUpBig"
                },
                {
                    "label":"fadeInTopLeft",
                    "value":"fadeInTopLeft"
                },
                {
                    "label":"fadeInTopRight",
                    "value":"fadeInTopRight"
                },
                {
                    "label":"fadeInBottomLeft",
                    "value":"fadeInBottomLeft"
                },
                {
                    "label":"fadeInBottomRight",
                    "value":"fadeInBottomRight"
                },
                {
                    "label":"bounceIn",
                    "value":"bounceIn"
                },
                {
                    "label":"bounceInDown",
                    "value":"bounceInDown"
                },
                {
                    "label":"bounceInLeft",
                    "value":"bounceInLeft"
                },
                {
                    "label":"bounceInRight",
                    "value":"bounceInRight"
                },
                {
                    "label":"bounceInUp",
                    "value":"bounceInUp"
                },
                {
                    "label":"zoomIn",
                    "value":"zoomIn"
                },
                {
                    "label":"zoomInDown",
                    "value":"zoomInDown"
                },
                {
                    "label":"zoomInLeft",
                    "value":"zoomInLeft"
                },
                {
                    "label":"zoomInRight",
                    "value":"zoomInRight"
                },
                {
                    "label":"zoomInUp",
                    "value":"zoomInUp"
                },
                {
                    "label":"slideInDown",
                    "value":"slideInDown"
                },
                {
                    "label":"slideInLeft",
                    "value":"slideInLeft"
                },
                {
                    "label":"slideInRight",
                    "value":"slideInRight"
                },
                {
                    "label":"slideInUp",
                    "value":"slideInUp"
                },
                {
                    "label":"lightSpeedInRight",
                    "value":"lightSpeedInRight"
                },
                {
                    "label":"lightSpeedInLeft",
                    "value":"lightSpeedInLeft"
                },
                {
                    "label":"lightSpeedOutRight",
                    "value":"lightSpeedOutRight"
                },
                {
                    "label":"lightSpeedOutLeft",
                    "value":"lightSpeedOutLeft"
                },
                {
                    "label":"Jello",
                    "value":"ello"
                },
                {
                    "label":"Tada",
                    "value":"tada"
                },
                {
                    "label":"Pulse",
                    "value":"pulse"
                }
              ]
            }
        ]
      },
      {
        "type":"custom_button",
        "name":"Button",
        "settings":[
            {
                "type":"text",
                "id":"button_text",
                "label":"Button label",
                "default":"Button label",
                "info":"If set blank will not show"
            },
            {
                "type":"url",
                "id":"button_link",
                "label":"Button link",
                "info":"If set blank will not show"
            },
            {
                "type":"select",
                "id":"target_link",
                "label":"Open link in",
                "default":"_self",
                "options":[
                    {
                        "value": "_self",
                        "label": "Current window"
                    },
                    {
                        "value": "_blank",
                        "label": "New window"
                    }
                ]
            },
            {
                "type": "select",
                "id": "fontf",
                "default":"inherit",
                "label": "Font family",
                "options": [
                    {
                        "label": "Inherit",
                        "value": "inherit"
                    },
                    {
                        "label": "Font family #1",
                        "value": "1"
                    },
                    {
                        "label": "Font family #2",
                        "value": "2"
                    },
                    {
                        "label": "Font family #3",
                        "value": "3"
                    }
                ]
            },
            {
                "type":"range",
                "id":"button_icon_w",
                "label":"Button icon width",
                "min":0,
                "max":50,
                "step":1,
                "unit":"px",
                "default":0
            },
            {
                "type": "select",
                "id": "button_style",
                "label": "Button style",
                "options": [
                    {
                        "label": "Default",
                        "value": "default"
                    },
                    {
                        "label": "Outline",
                        "value": "outline"
                    },
                    {
                        "label": "Bordered bottom",
                        "value": "bordered"
                    },
                    {
                        "label": "Link",
                        "value": "link"
                    }
                ]
            },
             {
                "type":"select",
                "id":"button_effect",
                "label":"Button hover effect",
                "default":"default",
                "info":"Only working button style default, outline",
                "options":[
                    {
                        "label":"Default",
                        "value":"default"
                    },
                    {
                        "label":"Fade",
                        "value":"fade"
                    },
                    {
                        "label":"Rectangle out",
                        "value":"rectangle-out"
                    },
                    {
                        "label":"Sweep to right",
                        "value":"sweep-to-right"
                    },
                    {
                        "label":"Sweep to left",
                        "value":"sweep-to-left"
                    },
                    {
                        "label":"Sweep to bottom",
                        "value":"sweep-to-bottom"
                    },
                    {
                        "label":"Sweep to top",
                        "value":"sweep-to-top"
                    },
                    {
                        "label":"Shutter out horizontal",
                        "value":"shutter-out-horizontal"
                    },
                    {
                        "label":"Outline",
                        "value":"outline"
                    },
                    {
                        "label":"Shadow",
                        "value":"shadow"
                    }
                ]
            },
            {
                "type":"color",
                "id":"pri_cl",
                "label":"Primary color",
                "default":"#222"
            },
            {
                "type":"color",
                "id":"second_cl",
                "label":"Secondary color"
            },
            {
                "type":"color",
                "id":"pri_cl_hover",
                "label":"Primary color hover",
                "default":"#56cfe1"
            },
            {
                "type":"color",
                "id":"second_cl_hover",
                "label":"Secondary color hover",
                "info":"Only working button style default, outline",
                "default":"#fff"
            },
            {
                "type":"range",
                "id":"fsbutton",
                "label":"Font size",
                "max":50,
                "min":10,
                "step":1,
                "unit":"px",
                "default":14
            },
            {
                "type":"range",
                "id":"fwbutton",
                "label":"Font weight",
                "min":100,
                "max":900,
                "step":100,
                "default":400
            },
            {
                "type":"range",
                "id":"button_ls",
                "label":"Letter spacing",
                "min":0,
                "max":10,
                "step":0.1,
                "unit":"px",
                "default":0
            },
            {
                "type":"range",
                "id":"button_mh",
                "label":"Min height",
                "min":30,
                "max":80,
                "step":1,
                "unit":"px",
                "default":42,
                "info":"Only working button style default, outline"
            },
            {
                "type":"range",
                "id":"button_bdr",
                "label":"Border radius",
                "min":0,
                "max":40,
                "step":1,
                "unit":"px",
                "default":0,
                "info":"Only working button style default, outline"
            },
            {
                "type":"range",
                "id":"button_pd_lr", 
                "label":"Padding left/right",
                "min":0,
                "max":100,
                "step":1,
                "unit":"px",
                "default":30,
                "info":"Only working button style default, outline"
            },
            {
                "type": "number",
                "id": "button_mgb",
                "label": "Margin bottom",
                "default": 0
            },
            {
                "type":"header",
                "content":"+ Option mobile"
            },
            {
                "type":"checkbox",
                "id":"hidden_mobile",
                "label":"Hidden on mobile ",
                "default":false
            },
            {
                "type":"range",
                "id":"button_icon_w_mb",
                "label":"Button icon width (Mobile)",
                "min":0,
                "max":50,
                "step":1,
                "unit":"px",
                "default":0
            },
            {
                "type":"range",
                "id":"fsbutton_mb",
                "label":"Font size (Mobile)",
                "max":50,
                "min":0,
                "step":1,
                "unit":"px",
                "default":12
            },
            {
                "type":"range",
                "id":"button_mh_mb",
                "label":"Min height (Mobile)",
                "min":10,
                "max":50,
                "step":1,
                "unit":"px",
                "default":36,
                "info":"Only working button style default, outline"
            },
            {
                "type":"range",
                "id":"button_pd_lr_mb",
                "label":"Padding left/right (Mobile)",
                "min":0,
                "max":60,
                "step":1,
                "unit":"px",
                "default":15,
                "info":"Only working button style default, outline"
            },
            {
              "type":"range",
              "id":"button_ls_mb",
              "label":"Letter spacing (Mobile)",
              "min":0,
              "max":10,
              "step":0.1,
              "unit":"px",
              "default":0
          },
            {
                "type": "number",
                "id": "button_mgb_mb",
                "label": "Margin bottom (Mobile)",
                "default": 0
            },
            {
              "type": "paragraph",
              "content": "————————————————"
            },
            {
              "type":"select",
              "id":"animation",
              "label":"Animation",
              "default":"none",
              "options":[
                  {
                      "label":"None",
                      "value":"none"
                  },
                  {
                      "label":"fadeIn",
                      "value":"fadeIn"
                  },
                  {
                      "label":"fadeInDown",
                      "value":"fadeInDown"
                  },
                  {
                      "label":"fadeInDownBig",
                      "value":"fadeInDownBig"
                  },
                  {
                      "label":"fadeInLeft",
                      "value":"fadeInLeft"
                  },
                  {
                      "label":"fadeInLeftBig",
                      "value":"fadeInLeftBig"
                  },
                  {
                      "label":"fadeInRight",
                      "value":"fadeInRight"
                  },
                  {
                      "label":"fadeInRightBig",
                      "value":"fadeInRightBig"
                  },
                  {
                      "label":"fadeInUp",
                      "value":"fadeInUp"
                  },
                  {
                      "label":"fadeInUpBig",
                      "value":"fadeInUpBig"
                  },
                  {
                      "label":"fadeInTopLeft",
                      "value":"fadeInTopLeft"
                  },
                  {
                      "label":"fadeInTopRight",
                      "value":"fadeInTopRight"
                  },
                  {
                      "label":"fadeInBottomLeft",
                      "value":"fadeInBottomLeft"
                  },
                  {
                      "label":"fadeInBottomRight",
                      "value":"fadeInBottomRight"
                  },
                  {
                      "label":"bounceIn",
                      "value":"bounceIn"
                  },
                  {
                      "label":"bounceInDown",
                      "value":"bounceInDown"
                  },
                  {
                      "label":"bounceInLeft",
                      "value":"bounceInLeft"
                  },
                  {
                      "label":"bounceInRight",
                      "value":"bounceInRight"
                  },
                  {
                      "label":"bounceInUp",
                      "value":"bounceInUp"
                  },
                  {
                      "label":"zoomIn",
                      "value":"zoomIn"
                  },
                  {
                      "label":"zoomInDown",
                      "value":"zoomInDown"
                  },
                  {
                      "label":"zoomInLeft",
                      "value":"zoomInLeft"
                  },
                  {
                      "label":"zoomInRight",
                      "value":"zoomInRight"
                  },
                  {
                      "label":"zoomInUp",
                      "value":"zoomInUp"
                  },
                  {
                      "label":"slideInDown",
                      "value":"slideInDown"
                  },
                  {
                      "label":"slideInLeft",
                      "value":"slideInLeft"
                  },
                  {
                      "label":"slideInRight",
                      "value":"slideInRight"
                  },
                  {
                      "label":"slideInUp",
                      "value":"slideInUp"
                  },
                  {
                      "label":"lightSpeedInRight",
                      "value":"lightSpeedInRight"
                  },
                  {
                      "label":"lightSpeedInLeft",
                      "value":"lightSpeedInLeft"
                  },
                  {
                      "label":"lightSpeedOutRight",
                      "value":"lightSpeedOutRight"
                  },
                  {
                      "label":"lightSpeedOutLeft",
                      "value":"lightSpeedOutLeft"
                  },
                  {
                      "label":"jello",
                      "value":"jello"
                  },
                  {
                      "label":"tada",
                      "value":"tada"
                  },
                  {
                      "label":"pulse",
                      "value":"pulse"
                  }
              ]
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Banner with text",
        "category": "Homepage"
      }
    ]
  }
{% endschema %}
