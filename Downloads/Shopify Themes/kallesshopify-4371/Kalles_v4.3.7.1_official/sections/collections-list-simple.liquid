<!-- sections/collections-list-simple.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'collection-list-simple.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
{%- liquid
    assign sid = section.id
    assign se_stts = section.settings
    assign se_blocks = section.blocks
    assign stt_layout = se_stts.layout
    assign stt_image_bg = se_stts.image_bg
    if stt_layout == 't4s-se-container' 
      assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
    elsif stt_layout == 't4s-container-wrap'
      assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
    else
      assign html_layout = '__' | split: '__'
    endif
 -%}
  <div class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }}{%- if stt_image_bg != blank and stt_layout != 't4s-se-container' -%} t4s-has-imgbg lazyloadt4s{%- endif -%}" {%- if stt_image_bg != blank and stt_layout != 't4s-se-container' -%} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto" data-optimumx="2"{%- endif -%} {% render 'section_style', se_stts: se_stts %}>
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {%- if stt_image_bg != blank -%} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{%- endif -%}>{%- endif -%}
    <div class="t4s-list-collections-simple t4s-row t4s-g-0 t4s-flicky-slider flickityt4s t4s-text-center t4s-slider-btn-style-simple t4s-slider-btn-none t4s-slider-btn-small t4s-slider-btn-vi-always" 
    data-flickityt4s-js='{"isSimple": true,"setPrevNextButtons":true,"imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%","freeScroll": true, "dragThreshold" : 5, "cellAlign": "left","wrapAround": 0,"prevNextButtons": 1,"percentPosition": 1,"pageDots": 0, "autoPlay" : 0, "pauseAutoPlayOnHover" : 1 }' 
    style="--title-cl-pri: {{ se_stts.title_cl }};--title-cl-pri-hover: {{ se_stts.title_cl_hover }};--slider-btn-color: {{ se_stts.title_cl }};--item-pd: {{ se_stts.item_pd }}px;--space-bottom: {{ se_stts.space_bottom }}px;--space-bottom-mb: {{ se_stts.space_bottom_mb }}px; --space-item-dk:{{ se_stts.space_item_dk }}px; --space-item-tb:{{ se_stts.space_item_tb }}px; --space-item-mb:{{ se_stts.space_item_mb }}px;">
        {%- for block in se_blocks -%}
            {%- liquid
                assign bk_stts = block.settings
                assign collection = collections[bk_stts.collection]
                assign title = bk_stts.collection_title | default: collection.title
                assign collection_link = bk_stts.collection_link | default: collection.url
                assign open_link = bk_stts.open_link
               -%}
            <div class="t4s-collection-item-simple t4s-pr t4s-col-auto t4s-col-item">
                <a href="{{ collection_link }}" target="{{ se_stts.open_link }}" timeline hdt-reveal="{% if section.index < 3 %}fade-in{% else %}slide-in{% endif %}">{{ title }}</a>
            </div>
        {%- endfor -%}
    {{- html_layout[1] -}}
</div>

{%- schema -%}
{
    "name": "Collections list simple",
    "tag": "section",
    "class": "t4s-section t4s_bk_flickity t4s-section-all t4s_tp_cdt t4s-collections-list-simple",
    "settings": [
        {
            "type": "header",
            "content": "1. General options"
        },
        {
            "type": "color",
            "id": "title_cl",
            "label": "Title color",
            "default": "#ffffff"
        },
        {
            "type": "color",
            "id": "title_cl_hover",
            "label": "Title hover color",
            "default": "#222222"
        },
        {
            "type": "select",
            "id": "open_link",
            "info": "Works when the item has a link",
            "options": [
            {
                "value": "_self",
                "label": "Current window"
            },
            {
                "value": "_blank",
                "label": "New window"
            }
            ],
            "label": "Open link in",
            "default": "_self"
        },
        {
            "type":"range",
            "id":"space_item_dk",
            "label":"Space between items (Desktop)",
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "px",
            "default":70
        },
        {
            "type":"range",
            "id":"space_item_tb",
            "label":"Space between items (Tablet)",
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "px",
            "default":35
        },
        {
            "type":"range",
            "id":"space_item_mb",
            "label":"Space between items (Mobile)",
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "px",
            "default":25
        },
        {
            "type": "header",
            "content": "2. Design options"
        },
        {
            "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
            "options": [
                { "value": "t4s-se-container", "label": "Container"},
                { "value": "t4s-container-wrap", "label": "Wrapped container"},
                { "value": "t4s-container-fluid", "label": "Full width"}
            ]
        },
        {
            "type": "color",
            "id": "cl_bg",
            "label": "Background",
            "default": "#A20401"
        },
        {
            "type": "color_background",
            "id": "cl_bg_gradient",
            "label": "Background gradient"
        },
        {
            "type": "image_picker",
            "id": "image_bg",
            "label": "Background image"
        },
        {
            "type": "text",
            "id": "mg",
            "label": "Margin",
            "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
            "default": ",,50px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd",
            "label": "Padding",
            "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
            "default": "10px,,10px,"
        },
        {
            "type": "header",
            "content": "+ Design Tablet Options"
        },
        {
            "type": "text",
            "id": "mg_tb",
            "label": "Margin",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_tb",
            "label": "Padding",
            "placeholder": ",,50px,"
        }, 
        {
            "type": "header",
            "content": "+ Design mobile options"
        },
        {
            "type": "text",
            "id": "mg_mb",
            "label": "Margin",
            "default": ",,30px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_mb",
            "label": "Padding",
            "default": "10px,,10px,"
        }
    ],
    "blocks": [
        {
            "type": "collection_item",
            "name": "Collection",
            "settings": [
                {
                    "id": "collection",
                    "type": "collection",
                    "label": "Collection"
                },{
                    "type": "text",
                    "id": "collection_title",
                    "label": "Collection label",
                    "info" : "Leave empty to use 'Collection label'.",
                    "default": "Collection "
                },{
                    "type": "url",
                    "id": "collection_link",
                    "label": "Link (optional)",
                    "info" : "Leave empty to use 'collection url'."
                }
            ]
        }
    ],
    "presets": [
        {
            "name": "Collection list simple",
            "blocks": [
                {
                    "type": "collection_item"
                },
                {
                    "type": "collection_item"
                },
                {
                    "type": "collection_item"
                },
                {
                    "type": "collection_item"
                },
                {
                    "type": "collection_item"
                },
                {
                    "type": "collection_item"
                }
            ]
        }
    ]
}
{%- endschema -%}