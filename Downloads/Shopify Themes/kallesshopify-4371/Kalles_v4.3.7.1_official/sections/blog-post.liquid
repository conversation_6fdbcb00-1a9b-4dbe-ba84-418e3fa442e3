<!-- sections/blog-post.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'blog.css' | asset_url | stylesheet_tag }}
{{ 'slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
<link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
<link href="{{ 'loading.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign layout_des = se_stts.layout_des
  
  assign blog = blogs[se_stts.blog]
  if se_stts.btn_owl == "outline"
    assign arrow_icon = 1
  else
    assign arrow_icon = 2
  endif
  assign blog_url = blog.url
  assign limit = se_stts.limit
  assign post_des = se_stts.post_des
  assign by_txt = 'blogs.article.by' | t
  assign on_txt = 'blogs.article.on' | t
  assign readmore_txt = 'blogs.article.read_more' | t
  assign date = se_stts.date
  assign b_effect = se_stts.b_effect
  assign img_effect = se_stts.img_effect
  assign show_cate = se_stts.show_cate
  assign show_tags = se_stts.show_tags
  assign show_cnt = se_stts.show_cnt
  assign show_au = se_stts.show_au
  assign show_cm = se_stts.show_cm
  assign show_dt = se_stts.show_dt
  assign show_rm = se_stts.show_rm
  assign show_irm = se_stts.show_irm

  assign col_dk = se_stts.col_dk
  assign col_tb = se_stts.col_tb
  assign col_mb = se_stts.col_mb
  if post_des == '4'
    if col_dk != '1'
       assign col_dk = '2'
    endif
    if col_tb != '1'
       assign col_tb = '2'
    endif
  endif
  
  assign use_pagination = se_stts.use_pagination 
  assign isLoadmore = false
  if layout_des != "3"
    if use_pagination == "load-more"
      assign isLoadmore = true
      assign typeAjax = 'LmDefault'
    else
      assign typeAjax = 'AjaxDefault'
    endif
  else
     if use_pagination == "load-more"
      assign isLoadmore = true
      assign typeAjax = 'LmIsotope'
    else
      assign typeAjax = 'AjaxIsotope'
    endif
  endif
  
  assign enable_bar_lm = se_stts.enable_bar_lm 
  assign results_count = blog.articles_count

  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif 
 -%} 
{%- paginate blog.articles by limit -%}
<div  data-not-main data-ntajax-options='{"id":"{{ sid }}","type":"{{ typeAjax }}","isProduct":false}' class="t4s-section-inner {{ t4s_se_class }} t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
    <div class="t4s-top-head{% if se_stts.design_heading == "20" %} t4s-d-flex t4s-justify-content-between t4s-align-items-end t4s-flex-wrap{%endif %}">
      {%- render 'section_tophead', se_stts: se_stts -%}
      {%- if se_stts.design_heading == "20" -%}
        {% if blog_url != blank %}
        <div class="t4s-blog-button" style="--tophead_mb:{{ se_stts.tophead_mb }}px">
          <a href="{{ blog_url }}" class="t4s-blog-button-heading">{{ se_stts.bt_lb }}</a>
        </div>		
        {% endif %}	
      {%- endif -%}
    </div>
    {% if layout_des == "1" %}
      {{ 'button-style.css' | asset_url | stylesheet_tag }}
      <link href="{{ 't4s-custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
      <div data-contentlm-replace class="blog_grid has_post_des_{{ post_des }} t4s_{{ se_stts.image_ratio }} t4s_position_{{ se_stts.image_position }} t4s_{{ se_stts.image_size }} {{ se_stts.content_align }} t4s-row t4s-row-cols-lg-{{ col_dk }} t4s-row-cols-md-{{ col_tb }} t4s-row-cols-{{ col_mb }} t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }}">
    {% elsif layout_des == "2" %}
      <div class="t4s-flicky-slider blog_slider has_post_des_{{ post_des }} t4s_{{ se_stts.image_ratio }} t4s_position_{{ se_stts.image_position }} t4s_{{ se_stts.image_size }} {{ se_stts.content_align }} {% if se_stts.nav_btn %}  t4s-slider-btn-style-{{ se_stts.btn_owl }} t4s-slider-btn-{{ se_stts.btn_shape }} t4s-slider-btn-{{ se_stts.btn_size }} t4s-slider-btn-cl-{{ se_stts.btn_cl }} t4s-slider-btn-vi-{{ se_stts.btn_vi }} t4s-slider-btn-hidden-mobile-{{ se_stts.btn_hidden_mobile }} {% endif %} 
      {% if se_stts.nav_dot == true %}  t4s-dots-style-{{ se_stts.dot_owl }} t4s-dots-cl-{{ se_stts.dots_cl }} t4s-dots-round-{{ se_stts.dots_round }} t4s-dots-hidden-mobile-{{ se_stts.dots_hidden_mobile }}{% endif %} t4s-row t4s-row-cols-lg-{{ col_dk }} t4s-row-cols-md-{{ col_tb }} t4s-row-cols-{{ col_mb }} t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }}  flickityt4s" data-flickityt4s-js='{"setPrevNextButtons":true,"arrowIcon":"{{ arrow_icon }}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": {{ se_stts.loop }},"prevNextButtons": {{ se_stts.nav_btn }},"percentPosition": 1,"pageDots": {{ se_stts.nav_dot }}, "autoPlay" : {{ se_stts.au_time | times: 1000 }}, "pauseAutoPlayOnHover" : {{ se_stts.au_hover }} }' style="--space-dots: {{ se_stts.dots_space }}px;--flickity-btn-pos: {{ se_stts.space_h_item }}px;--flickity-btn-pos-mb: {{ se_stts.space_h_item_mb }}px;">
    {% else %}
      {{ 'button-style.css' | asset_url | stylesheet_tag }}
      <link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
      <div data-contentlm-replace class="isotopet4s blog_masonry has_post_des_{{ post_des }} t4s_{{ se_stts.image_ratio }} t4s_position_{{ se_stts.image_position }} t4s_{{ se_stts.image_size }} {{ se_stts.content_align }} t4s-row t4s-row-cols-lg-{{ col_dk }} t4s-row-cols-md-{{ col_tb }} t4s-row-cols-{{ col_mb }} t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }}" data-isotopet4s-js='{ "itemSelector": ".t4s-post-item", "layoutMode": "masonry" }'>
    {% endif %}
      {%- if blog != blank -%} 
        {%- case post_des -%} 
          {%-  when '1' -%}
            {%- render 'post_loop_1' for blog.articles as post,
               , blog: blog, blog_url: blog_url, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_dt: show_dt, show_au: show_au, show_cm: show_cm, show_cate: show_cate, show_tags: show_tags, show_cnt: show_cnt, show_rm: show_rm, show_irm: show_irm, date: date, readmore_txt: readmore_txt -%}
          {%-  when '2' -%}
            {%- render 'post_loop_2' for blog.articles as post,
               , blog: blog, blog_url: blog_url, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_dt: show_dt, show_au: show_au, show_cm: show_cm, show_cate: show_cate, show_tags: show_tags, show_cnt: show_cnt, show_rm: show_rm, show_irm: show_irm, date: date, readmore_txt: readmore_txt -%}
          {%-  when '3' -%}
            {%- render 'post_loop_3' for blog.articles as post,
               , blog: blog, blog_url: blog_url, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_dt: show_dt, show_au: show_au, show_cm: show_cm, show_cate: show_cate, show_tags: show_tags, show_cnt: show_cnt, show_rm: show_rm, show_irm: show_irm, date: date, readmore_txt: readmore_txt -%}
          {%-  when '4' -%}
            {%- render 'post_loop_4' for blog.articles as post,
               , blog: blog, blog_url: blog_url, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_dt: show_dt, show_au: show_au, show_cm: show_cm, show_cate: show_cate, show_tags: show_tags, show_cnt: show_cnt, show_rm: show_rm, show_irm: show_irm, date: date, readmore_txt: readmore_txt -%}
          {%-  when '5' -%}
            {%- render 'post_loop_5' for blog.articles as post,
               , blog: blog, blog_url: blog_url, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_dt: show_dt, show_au: show_au, show_cm: show_cm, show_cate: show_cate, show_tags: show_tags, show_cnt: show_cnt, show_rm: show_rm, show_irm: show_irm, date: date, readmore_txt: readmore_txt -%}
          {%-  when '6' -%}
            {%- render 'post_loop_6' for blog.articles as post,
               , blog: blog, blog_url: blog_url, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_dt: show_dt, show_au: show_au, show_cm: show_cm, show_cate: show_cate, show_tags: show_tags, show_cnt: show_cnt, show_rm: show_rm, show_irm: show_irm, date: date, readmore_txt: readmore_txt -%}
          {%-  when '7' -%}
            {%- render 'post_loop_7' for blog.articles as post,
               , blog: blog, blog_url: blog_url, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_dt: show_dt, show_au: show_au, show_cm: show_cm, show_cate: show_cate, show_tags: show_tags, show_cnt: show_cnt, show_rm: show_rm, show_irm: show_irm, date: date, readmore_txt: readmore_txt -%}
          {%-  when '8' -%}
            {%- render 'post_loop_8' for blog.articles as post,
               , blog: blog, blog_url: blog_url, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_dt: show_dt, show_au: show_au, show_cm: show_cm, show_cate: show_cate, show_tags: show_tags, show_cnt: show_cnt, show_rm: show_rm, show_irm: show_irm, date: date, readmore_txt: readmore_txt -%}
        {%- endcase -%}
      {%- else -%}
        {%- for i in (1..8) limit: limit -%}
          <article class="t4s-col-item post t4s-post-item t4s-post-des-{{ post_des }}">
            <div class="t4s-post-inner">
              <a class="t4s-post-thumb t4s-eff t4s-eff-{{ b_effect }} t4s-eff-img-{{ img_effect }} t4s-d-block" href="/admin/blogs" data-cacl-slide >
                {% capture current %}{% cycle 1, 2 %}{% endcapture %}
                <div class="t4s_ratio" style="--aspect-ratioapt: {{ image.aspect_ratio | default: 2 }}">
                  {{ 'lifestyle-' | append: current | placeholder_svg_tag: 't4s-placeholder-svg t4s-obj-eff' }}  
                </div>
              </a>
              <div class="t4s-post-info">
                <div class="t4s-post-info-inner">
                 <h4 class="t4s-post-title"><a class="" href="/admin/blogs">{{ 'onboarding.blog_title' | t }}</a></h4>
                 <div class="t4s-post-metas">
                    {% if show_au %}
                      <span class="t4s-post-author"><span class="t4s-post-author__text t4s-dib">{{ by_txt }}</span>
                        <span class="t4s-author-name">{{ 'onboarding.blog_author' | t }}</span>
                      </span>
                    {% endif %}
                    {% if show_dt %}
                      <span class="t4s-post-time"> <span class="t4s-post-time__text t4s-dib">{{ on_txt }}</span>  <span> <time class="entry-date published updated">April 29, 2022</time></span></span>
                    {% endif %}
                    {% if show_cm %}
                      <span class="t4s-post-comment"> {{ 'blogs.comments.with_count' | t: count: 19 }}</span>
                    {% endif %}
                 </div>
              </div>
              {% if show_cnt %}
                <div class="t4s-post-content t4s-rte">{{ 'onboarding.blog_excerpt' | t }}</div>
              {% endif %}
              {% if show_rm %}
                {%- if show_rm %}<a href="/admin/blogs" class="t4s-post-readmore">{{ 'blogs.article.read_more' | t }}</a>{% endif -%}
              {% endif %}
               </div>
            </div>
          </article>
        {%- endfor -%}
      {%- endif -%}
    </div>
    {%- if paginate.pages > 1 -%}
      <div class="t4s-blog-footer t4s-has-btn-{{ use_pagination }} {{ se_stts.btn_pos }}">
        {%- if isLoadmore -%} 
            {%- if paginate.next.is_link -%}
              <div data-wrap-lm class="t4s-pagination-wrapper t4s-w-100">
               {%- if enable_bar_lm -%}
                  <div data-wrap-lm-bar class="t4s-lm-bar t4s-btn-color-{{ se_stts.btns_cl }}">
                    {%- assign current_pr_size = blog.articles.size | plus: paginate.current_offset -%}
                    <span class="t4s-lm-bar--txt">{{ 'blogs.pagination.bar_with_count_html' | t: current_count: current_pr_size, total_count: results_count }}</span>
                    <div class="t4s-lm-bar--progress t4s-pr t4s-oh"><span class="t4s-lm-bar--current t4s-pa t4s-l-0 t4s-r-0 t4s-t-0 t4s-b-0" style="width: {{ current_pr_size | times: 100.0 | divided_by: results_count }}%"></span></div>
                  </div>
               {%- endif -%}
                <a data-load-more timeline hdt-reveal="slide-in" href="{{ paginate.next.url }}" class="t4s-pr t4s-loadmore-btn t4s-btn-loading__svg t4s-btn t4s-btn-base t4s-btn-style-{{ se_stts.button_style }} t4s-btn-size-{{ se_stts.btns_size }} t4s-btn-icon-{{ se_stts.btn_icon }} t4s-btn-color-{{ se_stts.btns_cl }} {% if se_stts.button_style == 'default' or se_stts.button_style == 'outline' %}t4s-btn-effect-{{ se_stts.button_effect }}{% endif %}">
                  <span class="t4s-btn-atc_text">{{ 'blogs.pagination.load_more' | t }}</span> 
                  {% if se_stts.btn_icon %}
                    <svg class="t4s-btn-icon" viewBox="0 0 32 32" width="32"><path d="M 15 4 L 15 24.0625 L 8.21875 17.28125 L 6.78125 18.71875 L 15.28125 27.21875 L 16 27.90625 L 16.71875 27.21875 L 25.21875 18.71875 L 23.78125 17.28125 L 17 24.0625 L 17 4 Z"/></svg>
                  {% endif %}
                  <div class="t4s-loading__spinner t4s-dn">
                    <svg  width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
                  </div> 
                </a>
              </div>
            {%- endif -%}
        {%- else -%}
          <a class="t4s-btn t4s-btn-base t4s-viewall-btn t4s-btn-base t4s-btn-style-{{ se_stts.button_style }} t4s-btn-size-{{ se_stts.btns_size }} t4s-btn-icon-{{ se_stts.btn_icon }} t4s-btn-color-{{ se_stts.btns_cl }} {% if se_stts.button_style == 'default' or se_stts.button_style == 'outline' %}t4s-btn-effect-{{ se_stts.button_effect }} {% endif %}" href="{{ blog_url }}">{{ 'blogs.pagination.view_all' | t }}{%- if se_stts.btn_icon -%} <svg class="t4s-btn-icon" viewBox="0 0 14 10"><use xlink:href="#t4s-icon-btn"></use></svg>{%- endif -%}</a>
         {%- endif -%} 
      </div>
    {%- endif -%} 
    {{- html_layout[1] -}}
  </div>
{%- endpaginate -%}

{%- schema -%}
  {
    "name": "Blog post",
    "tag": "section",
    "class": "t4s-section t4s_bk_flickity t4s-section-all t4s_tp_cdt t4s-blog-post t4s_tp_istope",
    "settings": [
      {
          "type": "header",
          "content": "1. Heading options"
      },
      {
          "type": "select",
          "id": "design_heading",
          "label": "+ Design heading",
          "default": "1",
          "options": [
              {
                  "value": "1",
                  "label": "Design 01"
              },
              {
                  "value": "2",
                  "label": "Design 02"
              },
              {
                  "value": "3",
                  "label": "Design 03"
              },
              {
                  "value": "4",
                  "label": "Design 04"
              },
              {
                  "value": "5",
                  "label": "Design 05"
              },
              {
                  "value": "6",
                  "label": "Design 06 (width line-awesome)"
              },
              {
                  "value": "7",
                  "label": "Design 07"
              },
              {
                  "value": "8",
                  "label": "Design 08"
              },
              {
                  "value": "9",
                  "label": "Design 09"
              },
              {
                  "value": "10",
                  "label": "Design 10"
              },
              {
                  "value": "11",
                  "label": "Design 11"
              },
              {
                  "value": "12",
                  "label": "Design 12"
              },
              {
                  "value": "13",
                  "label": "Design 13"
              },
              {
                  "value": "14",
                  "label": "Design 14"
              },
              {
                "value": "15",
                "label": "Design 15"
              },
              {
                "value": "16",
                "label": "Design 16"
              },
              {
                  "value": "20",
                  "label": "Design button heading"
              }
          ]
      },
      {
          "type": "select",
          "id": "heading_align",
          "label": "+ Heading align",
          "default": "t4s-text-center",
          "options": [
              {
                  "value": "t4s-text-start",
                  "label": "Left"
              },
              {
                  "value": "t4s-text-center",
                  "label": "Center"
              },
              {
                  "value": "t4s-text-end",
                  "label": "Right"
              }
          ]
      },
      {
          "type": "text",
          "id": "top_heading",
          "label": "+ Heading",
          "default": "Blog post"
      },
      {
          "type": "text",
          "id": "bt_lb",
          "label": "+ Button label",
          "info":"Only used for design 15",
          "default": "Button label"
      },
      {
        "type": "text",
        "id": "icon_heading",
        "label": "Enter a icon name on heading",
        "info": "Only used for design 6 [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
        "default": "las la-gem"
      },
      {
          "type": "textarea",
          "id": "top_subheading",
          "label": "+ Subheading"
      }, 
      {
        "type": "number",
        "id": "tophead_mb",
        "label": "+ Space bottom (px)",
        "info": "The vertical spacing between heading and content",
        "default": 30
      },
      {
        "type": "header",
        "content": "2. General options"
      },
      {
          "id": "blog",
          "type": "blog",
          "label": "Blog post"
      },
      {
        "type": "select",
        "id": "post_des",
        "options": [
          {
            "value": "1",
            "label": "Design 1"
          },
          {
            "value": "2",
            "label": "Design 2"
          },
          {
            "value": "3",
            "label": "Design 3"
          },
          {
            "value": "4",
            "label": "Design 4 (Always show <= 2 items per row)"
          },
          {
            "value": "5",
            "label": "Design 5"
          },
          {
            "value": "6",
            "label": "Design 6"
          },
          {
            "value": "7",
            "label": "Design 7"
          },
          {
            "value": "8",
            "label": "Design 8"
          }
        ],
        "label": "Post item design",
        "default": "1"
      },
      {
        "type": "checkbox",
        "id": "show_cate",
        "label": "Show categories",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "show_tags",
        "label": "Show tags",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "show_cnt",
        "label": "Show short content",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_au",
        "label": "Show author",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_dt",
        "label": "Show date",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_cm",
        "label": "Show comment",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "show_rm",
        "label": "Show readmore",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "show_irm",
        "label": "Show icon readmore",
        "default": false
      },
      {
        "type": "select",
        "id": "date",
        "options": [
          {
            "value": "abbreviated_date",
            "label": "Apr 19, 1994"
          },
          {
            "value": "basic",
            "label": "4/19/1994"
          },
          {
            "value": "date",
            "label": "April 19, 1994"
          },
          {
            "value": "%b %d",
            "label": "Apr 19"
          }
        ],
        "label": "Date format",
        "info":"different format options display for various languages.",
        "default": "date"
      },
      {
        "type": "select",
        "id": "content_align",
        "label": "Content align",
        "default": "t4s-text-start",
        "options": [
          {
            "value": "t4s-text-start",
            "label": "Default"
          },
          {
            "value": "t4s-text-center",
            "label": "Center"
          }
        ]
      },
      {
        "type": "range",
        "id": "limit",
        "min": 1,
        "max": 50,
        "step": 1,
        "label": "Maximum posts to show",
        "default": 8 
      },
      {
        "type": "select",
        "id": "img_effect",
        "label": "Image hover effect",
            "info": "Waring: Hovering effect will resize your images",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "zoom",
            "label": "Zoom in"
          },
          {
            "value": "rotate",
            "label": "Rotate"
          },
          {
            "value": "translateToTop",
            "label": "Move to top "
          },
          {
            "value": "translateToRight",
            "label": "Move to right"
          },
          {
            "value": "translateToBottom",
            "label": "Move to bottom"
          },
          {
            "value": "translateToLeft",
            "label": "Move to left"
          }
        ]
      },
      {
        "type": "select",
        "id": "b_effect",
        "label": "Blog effect when hover",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "border-run",
            "label": "Border run"
          },
          {
            "value": "pervasive-circle",
            "label": "Pervasive circle"
          },
          {
            "value": "plus-zoom-overlay",
            "label": "Plus zoom overlay"
          },
          {
            "value": "dark-overlay",
            "label": "Dark overlay"
          },
          {
            "value": "light-overlay",
            "label": "Light overlay"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Image ratio",
        "default": "ratioadapt",
        "info": "Aspect ratio custom will settings in general panel",
        "options": [
          {
            "group": "Natural",
            "value": "ratioadapt",
            "label": "Adapt to image"
          },
          {
            "group": "Landscape",
            "value": "ratio2_1",
            "label": "2:1"
          },
          {
            "group": "Landscape",
            "value": "ratio16_9",
            "label": "16:9"
          },
          {
            "group": "Landscape",
            "value": "ratio8_5",
            "label": "8:5"
          },
          {
            "group": "Landscape",
            "value": "ratio3_2",
            "label": "3:2"
          },
          {
            "group": "Landscape",
            "value": "ratio4_3",
            "label": "4:3"
          },
          {
            "group": "Landscape",
            "value": "rationt",
            "label": "Ratio ASOS"
          },
          {
            "group": "Squared",
            "value": "ratio1_1",
            "label": "1:1"
          },
          {
            "group": "Portrait",
            "value": "ratio2_3",
            "label": "2:3"
          },
          {
            "group": "Portrait",
            "value": "ratio1_2",
            "label": "1:2"
          },
          {
            "group": "Custom",
            "value": "ratiocus1",
            "label": "Ratio custom 1"
          },
          {
            "group": "Custom",
            "value": "ratiocus2",
            "label": "Ratio custom 2"
          },
          {
            "group": "Custom",
            "value": "ratio_us3",
            "label": "Ratio custom 3"
          },
          {
            "group": "Custom",
            "value": "ratiocus4",
            "label": "Ratio custom 4"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_size",
        "label": "Image size",
        "default": "cover",
        "info": "This settings apply only if the image ratio is not set to 'Adapt to image'.",
        "options": [
          {
            "value": "cover",
            "label": "Full"
          },
          {
            "value": "contain",
            "label": "Auto"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'.",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "1",
            "label": "Left top"
          },
          {
            "value": "2",
            "label": "Left center"
          },
          {
            "value": "3",
            "label": "Left bottom"
          },
          {
            "value": "4",
            "label": "Right top"
          },
          {
            "value": "5",
            "label": "Right center"
          },
          {
            "value": "6",
            "label": "Right bottom"
          },
          {
            "value": "7",
            "label": "Center top"
          },
          {
            "value": "8",
            "label": "Center center"
          },
          {
            "value": "9",
            "label": "Center bottom"
          }
        ],
        "label": "Image position",
        "default": "8"
      },
      {
        "type": "select",
        "id": "col_dk",
        "label": "Items per row",
        "default": "3",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_tb",
        "label": "Items per row (Tablet)",
        "default": "2",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_mb",
        "label": "Items per row (Mobile)",
        "default": "1",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          }
        ]
      },
      {
        "type": "select",
        "id": "space_h_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_v_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_h_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items (Mobile)",
        "default": "10"
      },
      {
        "type": "select",
        "id": "space_v_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items (Mobile)",
        "default": "10"
      },
      {
        "type": "header",
        "content": "--Box options--"
      },
      {
        "type": "select",
        "id": "layout_des",
        "options": [
          {
            "value": "1",
            "label": "Grid"
          },
          {
            "value": "2",
            "label": "Carousel"
          },
          {
            "value": "3",
            "label": "Masonry"
          }
        ],
        "label": "Layout design",
        "default": "2"
      },
      {
        "type": "header",
        "content": "+Options for carousel layout"
      },
      {
        "type": "checkbox",
        "id": "loop",
        "label": "Enable loop",
        "info": "At the end of cells, wrap-around to the other end for infinite scrolling",
        "default": true
      },
      {
        "type": "range",
        "id": "au_time",
        "min": 0,
        "max": 30,
        "step": 0.5,
        "label": "Autoplay speed in second.",
        "info": "Set is '0' to disable autoplay",
        "unit": "s",
        "default": 0
      },
      {
        "type": "checkbox",
        "id": "au_hover",
        "label": "Pause autoplay on hover",
        "info": "Auto-playing will pause when the user hovers over the carousel",
        "default": true
      },
      {
        "type": "paragraph",
        "content": "—————————————————"
      },
      {
        "type": "paragraph",
        "content": "Prev next button"
      },
      {
        "type": "checkbox",
        "id": "nav_btn",
        "label": "Use prev next button",
        "info": "Creates and show previous & next buttons",
        "default": false
      },
      {
        "type": "select",
        "id": "btn_vi",
        "label": "Visible",
        "default": "hover",
        "options": [
          {
            "value": "always",
            "label": "Always"
          },
          {
            "value": "hover",
            "label": "Only hover"
          }
        ]
      },
      {
        "type": "select",
        "id": "btn_owl",
        "label": "Button style",
        "default": "default",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "outline",
            "label": "Outline"
          },
          {
            "value": "simple",
            "label": "Simple"
          }
        ]
      },
      {
        "type": "select",
        "id": "btn_shape",
        "label": "Button shape",
        "info": "Not working with button style 'Simple'",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "Default"
          },
          {
            "value": "round",
            "label": "Round"
          },
          {
            "value": "rotate",
            "label": "Rotate"
          }
        ]
      },
      {
          "type": "select",
          "id": "btn_cl",
          "label": "Button color",
          "default": "dark",
          "options": [
              {
                  "value": "light",
                  "label": "Light"
              },
              {
                  "value": "dark",
                  "label": "Dark"
              },
              {
                  "value": "primary",
                  "label": "Primary"
              },
              {
                  "value": "custom1",
                  "label": "Custom color 1"
              },
              {
                  "value": "custom2",
                  "label": "Custom color 2"
              }
          ]
      },
      {
        "type": "select",
        "id": "btn_size",
        "label": "Button size",
        "default": "small",
        "options": [
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ]
      },
      {
        "type":"checkbox",
        "id":"btn_hidden_mobile",
        "label":"Hidden buttons on mobile ",
        "default": true
      },
      {
        "type": "paragraph",
        "content": "—————————————————"
      },
      {
        "type": "paragraph",
        "content": "Page dots"
      },
      {
        "type": "checkbox",
        "id": "nav_dot",
        "label": "Use page dots",
        "info": "Creates and show page dots",
        "default": false
      },
      {
        "type": "select",
        "id": "dot_owl",
        "label": "Dots style",
        "default": "default",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "outline",
            "label": "Outline"
          },
          {
            "value": "elessi",
            "label": "Elessi"
          }
        ]
      },
      {
        "type": "select",
        "id": "dots_cl",
        "label": "Dots color",
        "default": "dark",
        "options": [
          {
              "value": "light",
              "label": "Light (Best on dark background)"
          },
          {
              "value": "dark",
              "label": "Dark"
          },
          {
              "value": "primary",
              "label": "Primary"
          },
          {
              "value": "custom1",
              "label": "Custom color 1"
          },
          {
              "value": "custom2",
              "label": "Custom color 2"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "dots_round",
        "label": "Enable dots round",
        "default": true
      },
      {
        "type": "range",
        "id": "dots_space",
        "min": 2,
        "max": 20,
        "step": 1,
        "label": "Dot between horizontal",
        "unit": "px",
        "default": 10
      },
      {
        "type":"checkbox",
        "id":"dots_hidden_mobile",
        "label":"Hidden dots on mobile ",
        "default": false
      },
      {
        "type": "header",
        "content": "+Options for grid or masonry layout"
      },
      {
        "type": "select",
        "id": "use_pagination",
        "label": "Pagination",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "load-more",
            "label": "'Load more' button"
          },
          {
            "value": "view-all",
            "label": "'View all' button"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "enable_bar_lm",
        "label": "Enable progress bar",
        "info": "Only active when you use 'Load more' or 'Infinit scrolling'",
        "default": true
      },
      {
        "type": "paragraph",
        "content": "Page-loading speed is everything for good user experience. Multiple researches have shown that slow load times result in people leaving your site or delete your app which result in low conversion rates. And that’s bad news for those who use an infinite-scrolling. The more users scroll down a page, more content has to load on the same page. As a result, the page performance will increasingly slow down."
      },
      {
        "type": "paragraph",
        "content": "Another problem is limited resources of the user’s device. On many infinite scrolling sites, especially those with many images, devices with limited resources (such as mobile devices or tablets with dated hardware) can start slowing down because of the sheer number of assets it has loaded."
      },
      {
        "type": "paragraph",
        "content": "Therefore, we recommend that you only use 'Load more', 'Infinite scrolling' for when your collection is less than or equal to 400 products"
      },
      {
        "type":"checkbox",
        "id":"btn_icon",
        "label":"Enable button icon",
        "default":false
      },
      {
        "type": "select",
        "id": "button_style",
        "label": "Button style",
        "options": [
            {
                "label": "Default",
                "value": "default"
            },
            {
                "label": "Outline",
                "value": "outline"
            },
            {
                "label": "Bordered bottom",
                "value": "bordered"
            },
            {
                "label": "Link",
                "value": "link"
            }
        ]
      },
      {
        "type": "select",
        "id": "btns_size",
        "label": "Button size",
        "default":"large",
        "options": [
            {
                "label": "Extra small",
                "value": "small"
            },
            {
                "label": "Small",
                "value": "extra-small"
            },
            {
                "label": "Medium",
                "value": "medium"
            },
            {
                "label": "Large",
                "value": "extra-medium"
            },
            {
                "label": "Extra large",
                "value": "large"
            },
            {
                "label": "Extra extra large",
                "value": "extra-large"
            }
        ]
      },
      {
        "type": "select",
        "id": "btns_cl",
        "label": "Button color",
        "default": "dark",
        "options": [
          {
              "value": "light",
              "label": "Light"
          },
          {
              "value": "dark",
              "label": "Dark"
          },
          {
              "value": "primary",
              "label": "Primary"
          },
          {
              "value": "custom1",
              "label": "Custom color 1"
          },
          {
              "value": "custom2",
              "label": "Custom color 2"
          }
        ]
      },
       {
          "type":"select",
          "id":"button_effect",
          "label":"Button hover effect",
          "default":"default",
          "info":"Only working button style default, outline",
          "options":[
              {
                  "label":"Default",
                  "value":"default"
              },
              {
                  "label":"Fade",
                  "value":"fade"
              },
              {
                  "label":"Rectangle out",
                  "value":"rectangle-out"
              },
              {
                  "label":"Sweep to right",
                  "value":"sweep-to-right"
              },
              {
                  "label":"Sweep to left",
                  "value":"sweep-to-left"
              },
              {
                  "label":"Sweep to bottom",
                  "value":"sweep-to-bottom"
              },
              {
                  "label":"Sweep to top",
                  "value":"sweep-to-top"
              },
              {
                  "label":"Shutter out horizontal",
                  "value":"shutter-out-horizontal"
              },
              {
                  "label":"Outline",
                  "value":"outline"
              },
              {
                  "label":"Shadow",
                  "value":"shadow"
              }
          ]
      },
      {
        "type": "select",
        "id": "btn_pos",
        "label": "Button position",
        "default": "t4s-text-center",
        "options": [
          {
            "value": "t4s-text-start",
            "label": "Left"
          },
          {
            "value": "t4s-text-center",
            "label": "Center"
          },
          {
            "value": "t4s-text-end",
            "label": "Right"
          }
        ]
      },
      {
        "type": "header",
        "content": "3. Design options"
      },
      {
        "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped container"},
            { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      { 
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      }, 
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "4. Custom css"
      },
      {
        "id": "use_cus_css",
        "type": "checkbox",
        "label": "Use custom css",
        "default":false,
        "info": "If you want custom style for this section."
      },
      { 
        "id": "code_cus_css",
        "type": "textarea",
        "label": "Code custom css",
        "info": "Use selector .SectionID to style css"
        
      }
    ],
    "presets": [
      {
        "name": "Blog post",
        "category": "Homepage"
      }
    ]
  }
{% endschema %}

{%- javascript -%}
{%- endjavascript -%} 