<!-- sections/about_us.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'about-us.css' | asset_url | stylesheet_tag }}
{{ 'video-load.css' | asset_url | stylesheet_tag }}
{%- liquid
    assign image_fix = image_nt | image_tag
    assign sid = section.id
    assign se_stts = section.settings
    assign stt_layout = se_stts.layout
    assign stt_image_bg = se_stts.image_bg
    assign img_ratio = se_stts.image_ratio
    assign image_position = se_stts.image_position
    if stt_layout == 't4s-se-container' 
        assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
    elsif stt_layout == 't4s-container-wrap'
        assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
    else
        assign html_layout = '__' | split: '__'
    endif
    assign image_ratio = se_stts.image_ratio
    if image_ratio == "ratioadapt"
      assign imgatt = ''
     else 
      assign imgatt = 'data-'
    endif
    assign use_button = false
    assign t4s_se_class = 't4s_nt_se_' | append: sid
    if se_stts.use_cus_css and se_stts.code_cus_css != blank
        render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
    endif 
 -%}
<div class="t4s-section-inner {{ t4s_se_class }} t4s_nt_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}
    <div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
        {%- render 'section_tophead', se_stts: se_stts -%}
        <div class="t4s-row t4s-justify-content-center">
            <div class="t4s-col-item t4s-col-md-8 t4s-col-12 t4s-text-{{ se_stts.txt_align }} t4s_{{ img_ratio }} t4s_position_{{ image_position }}" timeline hdt-reveal="slide-in">
                {%- if section.blocks.size > 0 -%}
                    {%- for block in section.blocks -%}
                        {%- assign bk_stts = block.settings -%}
                        {%- case block.type -%}
                            {%- when 'bl_image' -%}
                                {%- assign image = bk_stts.image -%}
                                <div class="t4s-about-us-img t4s-border__{{ bk_stts.use_border }} t4s-pr t4s-oh" style="--mgb:{{ bk_stts.mgb }}px;--mgb-mb:{{ bk_stts.mgb_mb }}px;">
                                    {%- if image != blank -%}
                                        <div class="t4s-d-block t4s_ratio t4s-bg-11" style="background: url({{ image | image_url: width: 1 }});{{ imgatt }}--aspect-ratioapt: {{ image.aspect_ratio | default: 2 }}">
                                            <img class="lazyloadt4s t4s-lz--fadeIn" data-src="{{ image | image_url: width: 1 }}" {% if image.presentation.focal_point != '50.0% 50.0%' %} style="object-position: {{ image.presentation.focal_point }}"{% endif %} data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                                        </div>
                                    {%- else -%} 
                                        <div class="t4s_ratio" style="--aspect-ratioapt:2">
                                            {{ 'image' | placeholder_svg_tag: 't4s-placeholder-svg' }}
                                        </div>      
                                    {%- endif -%}
                                    <span class="t4s_br_1"></span><span class="t4s_br_2"></span><span class="t4s_br_3"></span><span class="t4s_br_4"></span>
                                    {%- if bk_stts.video_url != blank -%}
                                        <div class="t4s-pa t4s-t-0 t4s-l-0 t4s-r-0 t4s-b-0 t4s-d-flex t4s-justify-content-center t4s-align-items-center">
                                            <a data-open-mfp-iframe class="t4s-d-flex t4s-justify-content-center t4s-align-items-center t4s-video-control__play" href="{{ bk_stts.video_url }}" target="_blank" >
                                                <svg aria-hidden="true" focusable="false" role="presentation" class="t4s-icon-play" viewBox="0 0 20 40" width="20"><path fill-rule="evenodd" clip-rule="evenodd" d="M23.6 16.8L7 1.5C5.6.3 4 .2 2.9.7 1.6 1.4.8 2.7.7 3.8v31.7c0 1.8 1.1 3.3 3.1 3.3 1.5 0 2.5-.5 3.4-1.4l16.4-15.7c1.5-1.4 1.5-3.6 0-4.9z"></path></svg>
                                            </a>
                                        </div> 
                                    {%- endif -%}
                                </div>   
                            {%- when 'bl_text' -%}
                                <div class="t4s-about-us-txt t4s-rte" style="--mgb:{{ bk_stts.mgb }}px;--mgb-mb:{{ bk_stts.mgb_mb }}px;">{{ bk_stts.text }}</div>
                            {%- when 'bl_sig' -%} 
                                {%- assign image_sig = bk_stts.image_sig -%}
                                <div class="t4s-about_us_sig t4s-pr t4s-d-inline-block" style="--mgb:{{ bk_stts.mgb }}px;--mgb-mb:{{ bk_stts.mgb_mb }}px;">
                                    {%- if image_sig != blank -%}
                                        <img {% if image_sig.presentation.focal_point != '50.0% 50.0%' %} style="object-position: {{ image_sig.presentation.focal_point }}"{% endif %} class="lazyloadt4s t4s-lz--fadeIn" data-src="{{ image_sig | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image_sig.width, h: image_sig.height %}" width="{{ image_sig.width }}" height="{{ image_sig.height }}" alt="{{ image_sig.alt | escape }}">
                                        <span class="lazyloadt4s-loader"></span>
                                    {%- else -%}
                                        <img class="lazyloadt4s t4s-lz--fadeIn" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ "signature.png" | asset_url }}" alt="">    
                                        <span class="lazyloadt4s-loader"></span>
                                    {%- endif -%}
                                </div>  
                            {%- when 'bl_button' -%}
                                {%- if bk_stts.button_link != blank and bk_stts.button_label != blank %}
                                    {%- assign use_button = true -%}
                                    {%- assign button_style = bk_stts.button_style -%}
                                    <a class="t4s-about-us-btn t4s-btn t4s-btn-base t4s-btn-style-{{ button_style }} t4s-btn-size-{{ bk_stts.btn_size }} t4s-btn-color-{{ bk_stts.btn_cl }} {% if button_style == 'default' or button_style == 'outline' %}t4s-btn-effect-{{ bk_stts.button_effect }} t4s-btn-radius-{{ bk_stts.btn_bdr }}{% endif %}" href="{{ bk_stts.button_link }}"  target="{{ bk_stts.open_link }}" style="--mgb:{{ bk_stts.mgb }}px;--mgb-mb:{{ bk_stts.mgb_mb }}px;">{{ bk_stts.button_label }} {%- if bk_stts.btn_icon -%}<svg class="t4s-btn-icon" width="14"><use xlink:href="#t4s-icon-btn"></use></svg>{%- endif -%}</a>
                                {%- endif -%} 
                        {%- endcase -%}
                    {%- endfor -%}
                {%- endif -%}
            </div>    
        </div> 
    {{- html_layout[1] -}}
</div>
{%- if use_button -%}
    {{ 'button-style.css' | asset_url | stylesheet_tag }}
    <link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- endif -%}
{%- schema -%}
    {
        "name": "About us",
        "tag": "section",
        "class": "t4s-section t4s-section-all t4s_tp_mfps t4s-about-us",
        "settings": [
            {
                "type": "header",
                "content": "1. Heading options"
            },
            {
                "type": "select",
                "id": "design_heading",
                "label": "+ Design heading",
                "default": "1",
                "options": [
                    {
                        "value": "1",
                        "label": "Design 01"
                    },
                    {
                        "value": "2",
                        "label": "Design 02"
                    },
                    {
                        "value": "3",
                        "label": "Design 03"
                    },
                    {
                        "value": "4",
                        "label": "Design 04"
                    },
                    {
                        "value": "5",
                        "label": "Design 05"
                    },
                    {
                        "value": "6",
                        "label": "Design 06 (width line-awesome)"
                    },
                    {
                        "value": "7",
                        "label": "Design 07"
                    },
                    {
                        "value": "8",
                        "label": "Design 08"
                    },
                    {
                        "value": "9",
                        "label": "Design 09"
                    },
                    {
                        "value": "10",
                        "label": "Design 10"
                    },
                    {
                        "value": "11",
                        "label": "Design 11"
                    },
                    {
                        "value": "12",
                        "label": "Design 12"
                    },
                    {
                        "value": "13",
                        "label": "Design 13"
                    },
                    {
                        "value": "14",
                        "label": "Design 14"
                    },
                    {
                        "value": "15",
                        "label": "Design 15"
                    },
                    {
                      "value": "16",
                      "label": "Design 16"
                    }
                ]
            },
            {
                "type": "select",
                "id": "heading_align",
                "label": "+ Heading align",
                "default": "t4s-text-center",
                "options": [
                    {
                        "value": "t4s-text-start",
                        "label": "Left"
                    },
                    {
                        "value": "t4s-text-center",
                        "label": "Center"
                    },
                    {
                        "value": "t4s-text-end",
                        "label": "Right"
                    }
                ]
            },
            {
                "type": "text",
                "id": "top_heading",
                "label": "+ Heading"
            },
            {
                "type": "text",
                "id": "icon_heading",
                "label": "Enter a name icon [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
                "info": "Only used for design 6",
                "default": "las la-gem"
            },
            {
                "type": "textarea",
                "id": "top_subheading",
                "label": "+ Subheading"
            },
            {
                "type": "number",
                "id": "tophead_mb",
                "label": "+ Space bottom (px)",
                "info": "The vertical spacing between heading and content.",
                "default": 30
            },
            {
                "type": "header",
                "content": "2. General options"
            },
            {
                "type": "select",
                "id": "txt_align",
                "label": "Text alignment",
                "default": "center",
                "options": [
                    {
                        "value": "start",
                        "label": "Left"
                    },
                    {
                        "value": "center",
                        "label": "Center"
                    },
                    {
                        "value": "end",
                        "label": "Right"
                    }
                ]
            },
            {
                "type": "select",
                "id": "image_ratio",
                "label": "Image ratio",
                "default": "ratio16_9",
                "info": "Aspect Ratio Custom will settings in General panel.",
                "options": [
                {
                    "group": "Natural",
                    "value": "ratioadapt",
                    "label": "Adapt to image"
                },
                {
                    "group": "Landscape",
                    "value": "ratio2_1",
                    "label": "2:1"
                },
                {
                    "group": "Landscape",
                    "value": "ratio16_9",
                    "label": "16:9"
                },
                {
                    "group": "Landscape",
                    "value": "ratio8_5",
                    "label": "8:5"
                },
                {
                    "group": "Landscape",
                    "value": "ratio3_2",
                    "label": "3:2"
                },
                {
                    "group": "Landscape",
                    "value": "ratio4_3",
                    "label": "4:3"
                },
                {
                    "group": "Landscape",
                    "value": "rationt",
                    "label": "Ratio ASOS"
                },
                {
                    "group": "Squared",
                    "value": "ratio1_1",
                    "label": "1:1"
                },
                {
                    "group": "Portrait",
                    "value": "ratio2_3",
                    "label": "2:3"
                },
                {
                    "group": "Portrait",
                    "value": "ratio1_2",
                    "label": "1:2"
                },
                {
                    "group": "Custom",
                    "value": "ratiocus1",
                    "label": "Ratio custom 1"
                },
                {
                    "group": "Custom",
                    "value": "ratiocus2",
                    "label": "Ratio custom 2"
                },
                {
                    "group": "Custom",
                    "value": "ratio_us3",
                    "label": "Ratio custom 3"
                },
                {
                    "group": "Custom",
                    "value": "ratiocus4",
                    "label": "Ratio custom 4"
                }
                ]
            },
            {
                "type": "select",
                "id": "image_size",
                "label": "Image size",
                "default": "cover",
                "info": "This settings apply only if the image ratio is not set to 'Adapt to image'.",
                "options": [
                    {
                        "value": "cover",
                        "label": "Full"
                    },
                    {
                        "value": "contain",
                        "label": "Auto"
                    }
                ]
            },
            {
                "type": "select",
                "id": "image_position",
                "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'.And 'Not works when use 'Focal point' on image'",
                "label": "Image position",
                "default": "8",
                "options": [
                    {
                        "value": "default",
                        "label": "Default"
                    },
                    {
                        "value": "1",
                        "label": "Left top"
                    },
                    {
                        "value": "2",
                        "label": "Left center"
                    },
                    {
                        "value": "3",
                        "label": "Left bottom"
                    },
                    {
                        "value": "4",
                        "label": "Right top"
                    },
                    {
                        "value": "5",
                        "label": "Right center"
                    },
                    {
                        "value": "6",
                        "label": "Right bottom"
                    },
                    {
                        "value": "7",
                        "label": "Center top"
                    },
                    {
                        "value": "8",
                        "label": "Center center"
                    },
                    {
                        "value": "9",
                        "label": "Center bottom"
                    }
                ]
            },
            {
                "type": "header",
                "content": "3. Design options"
            },
            {
                "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
                "options": [
                    { "value": "t4s-se-container", "label": "Container"},
                    { "value": "t4s-container-wrap", "label": "Wrapped container"},
                    { "value": "t4s-container-fluid", "label": "Full width"}
                ]
            },
            {
                "type": "color",
                "id": "cl_bg",
                "label": "Background"
            },
            {
                "type": "color_background",
                "id": "cl_bg_gradient",
                "label": "Background gradient"
            },
            {
                "type": "image_picker",
                "id": "image_bg",
                "label": "Background Image"
            },
            {
                "type": "text",
                "id": "mg",
                "label": "Margin",
                "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
                "default": ",,50px,",
                "placeholder": ",,50px,"
            },
            {
                "type": "text",
                "id": "pd",
                "label": "Padding",
                "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
                "placeholder": "50px,,50px,"
            },
            {
                "type": "header",
                "content": "+ Design Tablet Options"
            },
            {
                "type": "text",
                "id": "mg_tb",
                "label": "Margin",
                "placeholder": ",,50px,"
            },
            {
                "type": "text",
                "id": "pd_tb",
                "label": "Padding",
                "placeholder": ",,50px,"
            },  
            {
                "type": "header",
                "content": "+ Design mobile options"
            },
            {
                "type": "text",
                "id": "mg_mb",
                "label": "Margin",
                "default": ",,30px,",
                "placeholder": ",,50px,"
            },
            {
                "type": "text",
                "id": "pd_mb",
                "label": "Padding",
                "placeholder": ",,50px,"
            },
            {
                "type": "header",
                "content": "4. Custom css"
            },
            {
                "id": "use_cus_css",
                "type": "checkbox",
                "label": "Use custom css",
                "default":false,
                "info": "If you want custom style for this section."
            },
            { 
                "id": "code_cus_css",
                "type": "textarea",
                "label": "Code custom css",
                "info": "Use selector .SectionID to style css"
                
            }
        ],
        "blocks":[
            {
                "name":"Image",
                "type":"bl_image",
                "limit": 1,
                "settings":[
                    {
                        "type":"image_picker",
                        "id":"image",
                        "label":"Image"
                    },
                    {
                        "type": "checkbox",
                        "id": "use_border",
                        "label": "Enable border",
                        "default": true
                    },
                    {
                        "id": "video_url",
                        "type": "video_url",
                        "label": "Video url",
                        "accept": ["youtube", "vimeo"],
                        "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc"
                    },
                    {
                        "type": "number",
                        "id": "mgb",
                        "label": "Margin bottom",
                        "default": 15
                    },
                    {
                        "type": "number",
                        "id": "mgb_mb",
                        "label": "Margin bottom (Mobile)",
                        "default": 10
                    }
                ]
            },
            {
                "type": "bl_text",
                "name": "Content",
                "settings": [
                    {
                        "type":"richtext",
                        "id":"text",
                        "label":"Content",
                        "default":"<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
                    },
                    {
                        "type": "number",
                        "id": "mgb",
                        "label": "Margin bottom",
                        "default": 25
                    },
                    {
                        "type": "number",
                        "id": "mgb_mb",
                        "label": "Margin bottom (Mobile)",
                        "default": 10
                    }
                ]
            },
            {
                "type": "bl_sig",
                "name": "Your signature",
                "limit": 1,
                "settings": [
                    {
                        "type":"image_picker",
                        "id":"image_sig",
                        "label":"Image your signature"
                    },
                    {
                        "type": "number",
                        "id": "mgb",
                        "label": "Margin bottom",
                        "default": 15
                    },
                    {
                        "type": "number",
                        "id": "mgb_mb",
                        "label": "Margin bottom (Mobile)",
                        "default": 10
                    }
                ]
            },
            {
                "type":"bl_button",
                "name":"Button",
                "settings":[
                    {
                        "type": "text",
                        "id": "button_label",
                        "label": "Button label",
                        "info":"If set blank will not show",
                        "default": "Button label"
                    },
                    {
                        "type": "url",
                        "id": "button_link",
                        "label": "Button link",
                        "info":"If set blank will not show"
                    },
                    {
                        "type": "select",
                        "id": "open_link",
                        "label": "Open link in",
                        "default": "_blank",
                        "info":"Only working when has a link",
                        "options": [
                            {
                                "value": "_self",
                                "label": "Current window"
                            },
                            {
                                "value": "_blank",
                                "label": "New window"
                            }
                        ]
                    },
                    {
                        "type":"checkbox",
                        "id":"btn_icon",
                        "label":"Button icon",
                        "default":false
                    },
                    {
                        "type": "select",
                        "id": "button_style",
                        "label": "Button style",
                        "options": [
                            {
                                "label": "Default",
                                "value": "default"
                            },
                            {
                                "label": "Outline",
                                "value": "outline"
                            },
                            {
                                "label": "Bordered bottom",
                                "value": "bordered"
                            },
                            {
                                "label": "Link",
                                "value": "link"
                            }
                        ]
                    },
                    {
                        "type": "select",
                        "id": "btn_size",
                        "label": "Button size",
                        "default":"large",
                        "options": [
                            {
                                "label": "Extra small",
                                "value": "small"
                            },
                            {
                                "label": "Small",
                                "value": "extra-small"
                            },
                            {
                                "label": "Medium",
                                "value": "medium"
                            },
                            {
                                "label": "Large",
                                "value": "extra-medium"
                            },
                            {
                                "label": "Extra large",
                                "value": "large"
                            },
                            {
                                "label": "Extra extra large",
                                "value": "extra-large"
                            }
                        ]
                    },
                    {
                        "type": "select",
                        "id": "btn_cl",
                        "label": "Button color",
                        "default": "dark",
                        "options": [
                            {
                                "value": "light",
                                "label": "Light"
                            },
                            {
                                "value": "dark",
                                "label": "Dark"
                            },
                            {
                                "value": "primary",
                                "label": "Primary"
                            },
                            {
                                "value": "custom1",
                                "label": "Custom color 1"
                            },
                            {
                                "value": "custom2",
                                "label": "Custom color 2"
                            }
                        ]
                    },
                    {
                        "type":"select",
                        "id":"button_effect",
                        "label":"Button hover effect",
                        "default":"default",
                        "info":"Only working button style default, outline",
                        "options":[
                            {
                                "label":"Default",
                                "value":"default"
                            },
                            {
                                "label":"Fade",
                                "value":"fade"
                            },
                            {
                            "label":"Rectangle out",
                            "value":"rectangle-out"
                            },
                            {
                                "label":"Sweep to right",
                                "value":"sweep-to-right"
                            },
                            {
                                "label":"Sweep to left",
                                "value":"sweep-to-left"
                            },
                            {
                                "label":"Sweep to bottom",
                                "value":"sweep-to-bottom"
                            },
                            {
                                "label":"Sweep to top",
                                "value":"sweep-to-top"
                            },
                            {
                                "label":"Shutter out horizontal",
                                "value":"shutter-out-horizontal"
                            },
                            {
                                "label":"Outline",
                                "value":"outline"
                            },
                            {
                                "label":"Shadow",
                                "value":"shadow"
                            }
                        ]
                    },
                    {
                        "type": "number",
                        "id": "mgb",
                        "label": "Margin bottom",
                        "default": 15
                    },
                    {
                        "type": "number",
                        "id": "mgb_mb",
                        "label": "Margin bottom (Mobile)",
                        "default": 10
                    }
                ]
            }
        ],
        "presets": [
            {
                "name": "About us",
                "category": "homepage",
                "blocks": [
                    {"type": "bl_image"},
                    {"type": "bl_text"},
                    {"type": "bl_sig"}
                ]
            }
        ]
    }
    
{% endschema %}