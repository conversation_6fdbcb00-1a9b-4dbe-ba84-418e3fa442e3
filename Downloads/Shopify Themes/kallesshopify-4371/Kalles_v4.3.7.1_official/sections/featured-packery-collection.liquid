<!-- sections/featured-packery-collection.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'collection-products.css' | asset_url | stylesheet_tag }}
<link href="{{ 'loading.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{{ 'button-style.css' | asset_url | stylesheet_tag }}
<link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign layout_des = se_stts.layout_des
  assign image_ratio = se_stts.image_ratio
  if image_ratio == "ratioadapt"
    assign imgatt = ''
   else 
    assign imgatt = 'data-'
  endif
  assign collection = collections[se_stts.collection]
  assign use_pagination = se_stts.use_pagination 
  assign sett_equal = se_stts.use_eq_height
  assign show_vendor = se_stts.show_vendor
  assign use_link_vendor = settings.use_link_vendor
  assign enable_rating = settings.enable_rating
  assign inc_pr = se_stts.pr_des
  assign limit = se_stts.limit
  if se_stts.btn_owl == "outline"
    assign arrow_icon = 1
  else
    assign arrow_icon = 2
  endif

  assign show_img = settings.show_img
  assign isGrowaveWishlist = false
  if settings.wishlist_mode == "3" and shop.customer_accounts_enabled
    assign isGrowaveWishlist = true
  endif
  assign enable_pr_size = settings.enable_pr_size
  assign pr_size_pos = settings.pr_size_pos
  assign show_size_type = settings.show_size_type
  assign size_ck = settings.size_ck | append: ',size,sizes,Größe' 
  assign get_size = size_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

  assign enable_pr_color = settings.enable_pr_color
  assign show_cl_type = settings.show_color_type
  assign color_ck = settings.color_ck | append: ',color,colors,couleur,colour'
  assign get_color = color_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

  assign price_varies_style = settings.price_varies_style
  assign app_review = settings.app_review
  assign use_countdown = se_stts.use_cdt
  
  assign txt_cd = 'products.grid_items.offer_end_in' | t
  assign isLoadmore = false
  if use_pagination == "load-more" or use_pagination == "infinite" 
    assign isLoadmore = true
    assign typeAjax = 'LmIsotope'
  else
    assign typeAjax = 'AjaxIsotope'
  endif
  
  assign enable_bar_lm = se_stts.enable_bar_lm 
  assign results_count = collection.products_count 

  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif 
 -%}

{%- paginate collection.products by limit -%}
<div data-not-main data-ntajax-options='{"id":"{{ sid }}","type":"{{ typeAjax }}","isProduct":true,"view":""}' class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }} {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s {% endif %}"  {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} {% render 'section_style', se_stts: se_stts %} >
  {{- html_layout[0] -}}
  {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner {% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s {% endif %} "  {% if stt_image_bg != blank %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} > {%- endif -%}
    {%- render 'section_tophead', se_stts: se_stts -%}
      
    <div{% if collection != blank %} data-collection-url="{{ collection.url }}"{% endif %} data-contentlm-replace class="isotopet4s t4s_box_pr_masonry t4s-products t4s-text-{{ se_stts.content_align }} t4s_{{ image_ratio }} t4s_position_{{ se_stts.image_position }} t4s_{{ se_stts.image_size }} t4s-row t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }} t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }}" data-isotopet4s-js='{ "itemSelector": ".t4s-product", "layoutMode": "packery" }'>
      {%- if collection != blank -%}
        {%- for product in collection.products %}
          {%- if isHasCollection -%}{% assign pr_url = product.url | within: collection %}{% else %}{%- assign pr_url = product.url %}{% endif -%}
          {%- capture col_dk %}{% cycle '3','6','3','3','3','3','3','3','3','6','3','3','3' %}{%- endcapture -%}
          {%- render 'product-packery-item', product: product, pr_url: pr_url, col_dk: col_dk, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
        {%- endfor -%}
      {%- elsif se_blocks.size > 0 -%}
        {%- for block in se_blocks limit: limit -%}
          {%- liquid
            assign bk_stts = block.settings
            assign link = bk_stts.link_product
            if link.type == "product_link" and link != blank
              assign product = link.object
            else
              continue
            endif
            if bk_stts.set_pr
              assign col_dk = 6
            else
              assign col_dk = 3
            endif
            assign pr_url = product.url | within:se_blocks
         -%}
            {%- render 'product-packery-item', product: product, pr_url: pr_url, col_dk: col_dk, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
        {%- endfor -%}
      {%- else -%}
        {%- for i in (1..18) limit: limit -%}
          <div class="t4s-col-item t4s-product t4s-pr-grid t4s-pr-style{{ product_des }} t4s-pr-item t4s-pr-des-{{ product_des }}">
            <div class="t4s-product-wrapper" data-cacl-slide >
              <div class="t4s-product-inner">
                <a class="t4s-d-block" data-cacl-slide href="/admin/products">{%- capture current -%}{%- cycle 1, 2, 3, 4, 5, 6 -%}{%- endcapture -%} 
                {{ 'product-' | append: current | placeholder_svg_tag: 't4s-placeholder-svg' }}</a>
              </div>
              <div class="t4s-product-info">
                <div class="t4s-product-info__inner">
                  <h3 class="t4s-product-title"><a href="/admin/products">{{ 'onboarding.product_title' | t }}</a></h3>
                  <span class="t4s-product-price"><del>$59.00</del><ins>$39.00</ins></span>
                </div>
              </div>
            </div>
          </div>
        {%- endfor -%}
      {%- endif -%} 
    </div>
    {%- if paginate.pages > 1 -%}
      <div class="t4s-prs-footer t4s-has-btn-{{ use_pagination }} {{ se_stts.btn_pos }}">
        {%- if isLoadmore -%} 
            {%- if paginate.next.is_link -%}
              <div data-wrap-lm class="t4s-pagination-wrapper t4s-w-100">
               {%- if enable_bar_lm -%}
               <div data-wrap-lm-bar class="t4s-lm-bar t4s-btn-color-{{ se_stts.btns_cl }}">
                 {%- assign current_pr_size = collection.products.size | plus: paginate.current_offset -%}
                  <span class="t4s-lm-bar--txt">{{ 'collections.pagination.bar_with_count_html' | t: current_count: current_pr_size, total_count: results_count }}</span>
                  <div class="t4s-lm-bar--progress t4s-pr t4s-oh"><span class="t4s-lm-bar--current t4s-pa t4s-l-0 t4s-r-0 t4s-t-0 t4s-b-0" style="width: {{ current_pr_size | times: 100.0 | divided_by: results_count }}%"></span></div>
               </div>
               {%- endif -%}
                <a data-load-more timeline hdt-reveal="slide-in" {% if use_pagination == 'infinite' %} data-load-onscroll {% endif %} href="{{ paginate.next.url }}" class="t4s-pr t4s-loadmore-btn t4s-btn-loading__svg t4s-btn t4s-btn-base t4s-btn-style-{{ se_stts.button_style }} t4s-btn-size-{{ se_stts.btns_size }} t4s-btn-icon-{{ se_stts.btn_icon }} t4s-btn-color-{{ se_stts.btns_cl }} {% if se_stts.button_style == 'default' or se_stts.button_style == 'outline' %}t4s-btn-effect-{{ se_stts.button_effect }} {% endif %}">
                  <span class="t4s-btn-atc_text">{{ 'collections.pagination.load_more' | t }}</span> 
                  {% if se_stts.btn_icon %}
                      <svg class="t4s-btn-icon" viewBox="0 0 32 32" width="32"><path d="M 15 4 L 15 24.0625 L 8.21875 17.28125 L 6.78125 18.71875 L 15.28125 27.21875 L 16 27.90625 L 16.71875 27.21875 L 25.21875 18.71875 L 23.78125 17.28125 L 17 24.0625 L 17 4 Z"/></svg>
                    {% endif %}
                    <div class="t4s-loading__spinner t4s-dn">
                      <svg  width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
                    </div> 
                </a>
              </div>
            {%- endif -%}
        {%- else -%}
          <a class="t4s-btn t4s-btn-base t4s-viewall-btn t4s-btn-base t4s-btn-style-{{ se_stts.button_style }} t4s-btn-size-{{ se_stts.btns_size }} t4s-btn-icon-{{ se_stts.btn_icon }} t4s-btn-color-{{ se_stts.btns_cl }} {% if se_stts.button_style == 'default' or se_stts.button_style == 'outline' %}t4s-btn-effect-{{ se_stts.button_effect }}{% endif %}" href="{{ collection.url }}">{{ 'collections.pagination.view_all' | t }}{%- if se_stts.btn_icon -%} <svg class="t4s-btn-icon" viewBox="0 0 14 10"><use xlink:href="#t4s-icon-btn"></use></svg>{%- endif -%}</a>
         {%- endif -%} 
      </div>
    {%- endif -%} 
    {{- html_layout[1] -}}
  </div>

{%- endpaginate -%}
{%- schema -%}
  {
    "name": "Packery collection",
    "tag": "section",
    "class": "t4s-section t4s-section-all t4s_tp_cd t4s-featured-packery-products t4s_tp_istope",
    "settings": [
      {
          "type": "header",
          "content": "1. Heading options"
      },
      {
          "type": "select",
          "id": "design_heading",
          "label": "+ Design heading",
          "default": "1",
          "options": [
              {
                  "value": "1",
                  "label": "Design 01"
              },
              {
                  "value": "2",
                  "label": "Design 02"
              },
              {
                  "value": "3",
                  "label": "Design 03"
              },
              {
                  "value": "4",
                  "label": "Design 04"
              },
              {
                  "value": "5",
                  "label": "Design 05"
              },
              {
                  "value": "6",
                  "label": "Design 06 (width line-awesome)"
              },
              {
                  "value": "7",
                  "label": "Design 07"
              },
              {
                  "value": "8",
                  "label": "Design 08"
              },
              {
                  "value": "9",
                  "label": "Design 09"
              },
              {
                  "value": "10",
                  "label": "Design 10"
              },
              {
                  "value": "11",
                  "label": "Design 11"
              },
              {
                  "value": "12",
                  "label": "Design 12"
              },
              {
                  "value": "13",
                  "label": "Design 13"
              },
              {
                  "value": "14",
                  "label": "Design 14"
              },
              {
                "value": "15",
                "label": "Design 15"
              },
              {
                "value": "16",
                "label": "Design 16"
              }
          ]
      },
      {
          "type": "select",
          "id": "heading_align",
          "label": "+ Heading align",
          "default": "t4s-text-center",
          "options": [
              {
                  "value": "t4s-text-start",
                  "label": "Left"
              },
              {
                  "value": "t4s-text-center",
                  "label": "Center"
              },
              {
                  "value": "t4s-text-end",
                  "label": "Right"
              }
          ]
      },
      {
          "type": "text",
          "id": "top_heading",
          "label": "+ Heading",
          "default": "Featured packery collection"
      },
      {
        "type": "text",
        "id": "icon_heading",
        "label": "Enter a icon name on heading",
        "info": "Only used for design 6 [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
        "default": "las la-gem"
      },
      {
          "type": "textarea",
          "id": "top_subheading",
          "label": "+ Subheading"
      }, 
      {
        "type": "number",
        "id": "tophead_mb",
        "label": "+ Space bottom (px)",
        "info": "The vertical spacing between heading and content",
        "default": 30
      },
      {
        "type": "header",
        "content": "2. General options"
      },
      {
          "id": "collection",
          "type": "collection",
          "label": "Collection" 
      },
      {
        "type": "checkbox",
        "id": "show_vendor",
        "label": "Show product vendors",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "use_cdt",
        "label": "Show product countdown",
        "default": false
      },
      {
        "type": "header",
        "content": "+ Options image products"
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Image ratio",
        "default": "ratio1_1",
        "info": "Aspect ratio custom will settings in general panel",
        "options": [
          {
            "group": "Natural",
            "value": "ratioadapt",
            "label": "Adapt to image"
          },
          {
            "group": "Landscape",
            "value": "ratio2_1",
            "label": "2:1"
          },
          {
            "group": "Landscape",
            "value": "ratio16_9",
            "label": "16:9"
          },
          {
            "group": "Landscape",
            "value": "ratio8_5",
            "label": "8:5"
          },
          {
            "group": "Landscape",
            "value": "ratio3_2",
            "label": "3:2"
          },
          {
            "group": "Landscape",
            "value": "ratio4_3",
            "label": "4:3"
          },
          {
            "group": "Landscape",
            "value": "rationt",
            "label": "Ratio ASOS"
          },
          {
            "group": "Squared",
            "value": "ratio1_1",
            "label": "1:1"
          },
          {
            "group": "Portrait",
            "value": "ratio2_3",
            "label": "2:3"
          },
          {
            "group": "Portrait",
            "value": "ratio1_2",
            "label": "1:2"
          },
          {
            "group": "Custom",
            "value": "ratiocus1",
            "label": "Ratio custom 1"
          },
          {
            "group": "Custom",
            "value": "ratiocus2",
            "label": "Ratio custom 2"
          },
          {
            "group": "Custom",
            "value": "ratio_us3",
            "label": "Ratio custom 3"
          },
          {
            "group": "Custom",
            "value": "ratiocus4",
            "label": "Ratio custom 4"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_size",
        "label": "Image size",
        "default": "cover",
        "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "cover",
            "label": "Full"
          },
          {
            "value": "contain",
            "label": "Auto"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "1",
            "label": "Left top"
          },
          {
            "value": "2",
            "label": "Left center"
          },
          {
            "value": "3",
            "label": "Left bottom"
          },
          {
            "value": "4",
            "label": "Right top"
          },
          {
            "value": "5",
            "label": "Right center"
          },
          {
            "value": "6",
            "label": "Right bottom"
          },
          {
            "value": "7",
            "label": "Center top"
          },
          {
            "value": "8",
            "label": "Center center"
          },
          {
            "value": "9",
            "label": "Center bottom"
          }
        ],
        "label": "Image position",
        "default": "8"
      },
      {
        "type": "select",
        "id": "content_align",
        "label": "Product content align",
        "default": "default",
        "options": [
          {
            "label": "Default",
            "value": "default"
          },
          {
            "label": "Center",
            "value": "center"
          }
        ]
      },
      {
        "type": "range",
        "id": "limit",
        "min": 1,
        "max": 50,
        "step": 1,
        "label": "Maximum products to show",
        "default": 14
      },
      {
        "type": "select",
        "id": "col_dk",
        "label": "Items per row",
        "default": "4",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          },
          {
            "value": "5",
            "label": "5"
          },
          {
            "value": "6",
            "label": "6"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_tb",
        "label": "Items per row (Tablet)",
        "default": "2",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_mb",
        "label": "Items per row (Mobile)",
        "default": "2",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          }
        ]
      },
      {
        "type": "select",
        "id": "space_h_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_v_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_h_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items (Mobile)",
        "default": "10"
      },
      {
        "type": "select",
        "id": "space_v_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items (Mobile)",
        "default": "10"
      },
      {
        "type": "header",
        "content": "+Options for layout"
      },
      {
        "type": "select",
        "id": "use_pagination",
        "label": "Pagination",
        "info": "Loadmore only working when choose collection on section",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "load-more",
            "label": "'Load more' button"
          },
          {
            "value": "view-all",
            "label": "'View all' button"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "enable_bar_lm",
        "label": "Enable progress bar",
        "info": "Only active when you use 'Load more' or 'Infinit scrolling'",
        "default": true
      },
      {
        "type":"checkbox",
        "id":"btn_icon",
        "label":"Enable button icon",
        "default":false
      },
      {
        "type": "select",
        "id": "button_style",
        "label": "Button style",
        "options": [
            {
                "label": "Default",
                "value": "default"
            },
            {
                "label": "Outline",
                "value": "outline"
            },
            {
                "label": "Bordered bottom",
                "value": "bordered"
            },
            {
                "label": "Link",
                "value": "link"
            }
        ]
      },
      {
        "type": "select",
        "id": "btns_size",
        "label": "Button size",
        "default":"large",
        "options": [
            {
                "label": "Extra small",
                "value": "small"
            },
            {
                "label": "Small",
                "value": "extra-small"
            },
            {
                "label": "Medium",
                "value": "medium"
            },
            {
                "label": "Large",
                "value": "extra-medium"
            },
            {
                "label": "Extra large",
                "value": "large"
            },
            {
                "label": "Extra extra large",
                "value": "extra-large"
            }
        ]
      },
      {
        "type": "select",
        "id": "btns_cl",
        "label": "Button color",
        "default": "dark",
        "options": [
          {
              "value": "light",
              "label": "Light"
          },
          {
              "value": "dark",
              "label": "Dark"
          },
          {
              "value": "primary",
              "label": "Primary"
          },
          {
              "value": "custom1",
              "label": "Custom color 1"
          },
          {
              "value": "custom2",
              "label": "Custom color 2"
          }
        ]
      },
      {
        "type":"select",
        "id":"button_effect",
        "label":"Button hover effect",
        "default":"default",
        "info":"Only working button style default, outline",
        "options":[
            {
                "label":"Default",
                "value":"default"
            },
            {
                "label":"Fade",
                "value":"fade"
            },
            {
                "label":"Rectangle out",
                "value":"rectangle-out"
            },
            {
                "label":"Sweep to right",
                "value":"sweep-to-right"
            },
            {
                "label":"Sweep to left",
                "value":"sweep-to-left"
            },
            {
                "label":"Sweep to bottom",
                "value":"sweep-to-bottom"
            },
            {
                "label":"Sweep to top",
                "value":"sweep-to-top"
            },
            {
                "label":"Shutter out horizontal",
                "value":"shutter-out-horizontal"
            },
            {
                "label":"Outline",
                "value":"outline"
            },
            {
                "label":"Shadow",
                "value":"shadow"
            }
        ]
    },
      {
        "type": "select",
        "id": "btn_pos",
        "label": "Button position",
        "default": "t4s-text-center",
        "options": [
          {
            "value": "t4s-text-start",
            "label": "Left"
          },
          {
            "value": "t4s-text-center",
            "label": "Center"
          },
          {
            "value": "t4s-text-end",
            "label": "Right"
          }
        ]
      },
      {
        "type": "header",
        "content": "3. Design options"
      },
      {
        "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped container"},
            { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      }, 
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "4. Custom css"
      },
      {
        "id": "use_cus_css",
        "type": "checkbox",
        "label": "Use custom css",
        "default":false,
        "info": "If you want custom style for this section."
      },
      { 
        "id": "code_cus_css",
        "type": "textarea",
        "label": "Code custom css",
        "info": "Use selector .SectionID to style css"
        
      }
    ],
    "blocks": [
      {
        "type": "product",
        "name": "Product",
        "settings": [
          {
            "type": "url",
            "id": "link_product",
            "info": "Only active when choose link product",
            "label": "Choose Product"
          },
          {
            "type": "checkbox",
            "id": "set_pr",
            "label": "Set is this featured product",
            "default": false
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Packery collection",
        "category": "Homepage"
      }
    ]
  }
{%- endschema -%} 