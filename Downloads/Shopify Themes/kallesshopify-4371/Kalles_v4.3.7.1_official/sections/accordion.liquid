<!-- sections/accordion.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'accordion.css' | asset_url | stylesheet_tag }}
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if se_stts.layout == 't4s-cus-width'
    assign html_layout = '<div class="t4s-container" style="--width:010693px">__</div>' | split: '__'
  elsif stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign search_url = routes.all_products_collection_url

  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif 
  assign forloop_first = true
 -%}
  <div class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }} {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s {% endif %} "  {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %}   {% render 'section_style', se_stts: se_stts %}>
    {{- html_layout[0] |replace: '010693', se_stts.custom_width -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner {% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s {% endif %} "  {%- if stt_image_bg != blank -%} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {%- endif -%} > {%- endif -%}
    <div class="t4s-accordion-style-{{ se_stts.accor_des }}" itemscope itemtype="https://schema.org/FAQPage">
      {%- render 'section_tophead', se_stts: se_stts -%}
      <div class="t4s-tabs t4s-type-accordion {{ se_stts.content_align }}" data-t4s-tabs style="--title-cl: {{ se_stts.title_cl }};--bg-title-cl: {{ se_stts.bg_title_cl }};--title-active-cl: {{ se_stts.title_active_cl }};--bg-title-active-cl: {{ se_stts.bg_title_active_cl }};--content-cl: {{ se_stts.content_cl }};--bg-content-cl: {{ se_stts.bg_content_cl }};">
        {%- for block in se_blocks -%}
          {%- assign bk_stts = block.settings -%} 
          {%- assign blockid = block.id -%}
          {%- if bk_stts.title == blank or bk_stts.content == blank -%}{% continue %}{%- endif -%}
          <div class="t4s-tab-wrapper {% if forloop_first and se_stts.auto_active %} t4s-active {% endif %}" data-t4s-tab-wrapper {{ block.shopify_attributes }} itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
            <a id="b_{{ block.id }}" class="t4s-accor-title" href="#t4s-{{ blockid }}" rel="nofollow" data-t4s-tab-item data-no-instant>
              <span class="t4s-accor-text">
                <span class="t4s-accor-icon">{%- render 'icon_accordion',icon: bk_stts.icon -%} </span>
                <span itemprop="name">{{ bk_stts.title }}</span>
              </span>
              <span class="t4s-accor-item-nav"></span>
            </a>
            <div id="t4s-{{ blockid }}" class="t4s-tab-content t4s-rte" data-t4s-tab-content {% if se_stts.auto_active == false %} style="display: none;"{%- endif -%} itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
              <div itemprop="text">
                {{ bk_stts.content }}
              </div>
            </div>
          </div>
          {%- assign forloop_first = false -%}
        {%- endfor -%}
      </div>
    </div>
    {{- html_layout[1] -}}
</div>
{% schema %}
{
  "name": "Accordion",
  "tag": "section",
  "class": "t4s-section t4s-section-all t4s_tp_cdt t4s-accordion t4s_tp_tab",
  "settings": [
    {
      "type": "header",
      "content": "1. Heading options"
    },
    {
      "type": "select",
      "id": "design_heading",
      "label": "+ Design heading",
      "default": "1",
      "options": [
        {
          "value": "1",
          "label": "Design 01"
        },
        {
          "value": "2",
          "label": "Design 02"
        },
        {
          "value": "3",
          "label": "Design 03"
        },
        {
          "value": "4",
          "label": "Design 04"
        },
        {
          "value": "5",
          "label": "Design 05"
        },
        {
          "value": "6",
          "label": "Design 06 (width line-awesome)"
        },
        {
          "value": "7",
          "label": "Design 07"
        },
        {
          "value": "8",
          "label": "Design 08"
        },
        {
          "value": "9",
          "label": "Design 09"
        },
        {
          "value": "10",
          "label": "Design 10"
        },
        {
          "value": "11",
          "label": "Design 11"
        },
        {
          "value": "12",
          "label": "Design 12"
        },
        {
          "value": "13",
          "label": "Design 13"
        },
        {
          "value": "14",
          "label": "Design 14"
        },
        {
          "value": "15",
          "label": "Design 15"
        },
        {
          "value": "16",
          "label": "Design 16"
        }
      ]
    },
    {
      "type": "select",
      "id": "heading_align",
      "label": "+ Heading align",
      "default": "t4s-text-center",
      "options": [
        {
          "value": "t4s-text-start",
          "label": "Left"
        },
        {
          "value": "t4s-text-center",
          "label": "Center"
        },
        {
          "value": "t4s-text-end",
          "label": "Right"
        }
      ]
    },
    {
      "type": "text",
      "id": "top_heading",
      "label": "+ Heading",
      "default": "Accordion"
    },
    {
      "type": "text",
      "id": "icon_heading",
      "label": "Enter a icon name on heading",
      "info": "Only used for design 6 [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
      "default": "las la-gem"
    },
    {
      "type": "textarea",
      "id": "top_subheading",
      "label": "+ Subheading"
    },
    {
      "type": "number",
      "id": "tophead_mb",
      "label": "+ Space bottom (px)",
      "info": "The vertical spacing between heading and content",
      "default": 30
    },
    {
      "type": "header",
      "content": "2. General options"
    },
    {
      "type": "select",
      "id": "accor_des",
      "options": [
        {
          "value": "1",
          "label": "Design 1"
        },
        {
          "value": "2",
          "label": "Design 2"
        }
      ],
      "label": "Accordion design",
      "default": "1"
    },
    {
      "type": "color",
      "id": "title_cl",
      "label": "Accordion label color",
      "default": "#222"
    },
    {
      "type": "color",
      "id": "bg_title_cl",
      "label": "Accordion label background color",
      "default": "#f6f6f8"
    },
    {
      "type": "color",
      "id": "title_active_cl",
      "label": "Accordion label active color",
      "default": "#000"
    },
    {
      "type": "color",
      "id": "bg_title_active_cl",
      "label": "Accordion label active background color",
      "default": "#f6f6f8"
    },
    {
      "type": "color",
      "id": "content_cl",
      "label": "Accordion content color",
      "default": "#878787"
    },
    {
      "type": "color",
      "id": "bg_content_cl",
      "label": "Accordion content background color",
      "default": "#fff"
    },
    {
      "type": "select",
      "id": "content_align",
      "label": "Accordion content align",
      "default": "t4s-text-start",
      "options": [
        {
          "value": "t4s-text-start",
          "label": "Left"
        },
        {
          "value": "t4s-text-center",
          "label": "Center"
        },
        {
          "value": "t4s-text-end",
          "label": "Right"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "auto_active",
      "label": "Auto active 1 items",
      "default": true
    },
    {
      "type": "header",
      "content": "3. Design options"
    },
    {
      "type": "select",
      "id": "layout",
      "default": "t4s-container-wrap",
      "label": "Layout",
      "options": [
        {
          "value": "t4s-cus-width",
          "label": "Custom width"
        },
        {
          "value": "t4s-se-container",
          "label": "Container"
        },
        {
          "value": "t4s-container-wrap",
          "label": "Wrapped container"
        },
        {
          "value": "t4s-container-fluid",
          "label": "Full width"
        }
      ]
    },
    {
      "type": "range",
      "id": "custom_width",
      "label": "Custom width for content",
      "info": "Only working when use layout custom width",
      "default": 1000,
      "min": 500,
      "max": 1000,
      "step": 10,
      "unit": "px"
    },
    {
      "type": "color",
      "id": "cl_bg",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "cl_bg_gradient",
      "label": "Background gradient"
    },
    {
      "type": "image_picker",
      "id": "image_bg",
      "label": "Background image"
    },
    {
      "type": "text",
      "id": "mg",
      "label": "Margin",
      "info": "Margin top, margin right, margin bottom, margin left. If you not use to blank",
      "default": ",,50px,",
      "placeholder": ",,50px,"
    },
    {
      "type": "text",
      "id": "pd",
      "label": "Padding",
      "info": "Padding top, padding right, padding bottom, padding left. If you not use to blank",
      "placeholder": "50px,,50px,"
    },
    {
      "type": "header",
      "content": "+ Design Tablet Options"
    },
    {
      "type": "text",
      "id": "mg_tb",
      "label": "Margin",
      "placeholder": ",,50px,"
    },
    {
      "type": "text",
      "id": "pd_tb",
      "label": "Padding",
      "placeholder": ",,50px,"
    },
    {
      "type": "header",
      "content": "+ Design mobile options"
    },
    {
      "type": "text",
      "id": "mg_mb",
      "label": "Margin",
      "default": ",,30px,",
      "placeholder": ",,50px,"
    },
    {
      "type": "text",
      "id": "pd_mb",
      "label": "Padding",
      "placeholder": ",,50px,"
    },
    {
      "type": "header",
      "content": "4. Custom css"
    },
    {
      "id": "use_cus_css",
      "type": "checkbox",
      "label": "Use custom css",
      "default": false,
      "info": "If you want custom style for this section."
    },
    {
      "id": "code_cus_css",
      "type": "textarea",
      "label": "Code custom css",
      "info": "Use selector .SectionID to style css"
    }
  ],
  "blocks": [
    {
      "type": "accor_item",
      "name": "Accordion item",
      "settings": [
        {
          "type": "select",
          "id": "icon",
          "label": "Accordion icon",
          "default": "none",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "box",
              "label": "Box"
            },
            {
              "value": "chat_bubble",
              "label": "Chat bubble"
            },
            {
              "value": "check_mark",
              "label": "Check mark"
            },
            {
              "value": "dryer",
              "label": "Dryer"
            },
            {
              "value": "eye",
              "label": "Eye"
            },
            {
              "value": "heart",
              "label": "Heart"
            },
            {
              "value": "iron",
              "label": "Iron"
            },
            {
              "value": "leaf",
              "label": "Leaf"
            },
            {
              "value": "leather",
              "label": "Leather"
            },
            {
              "value": "lock",
              "label": "Lock"
            },
            {
              "value": "map_pin",
              "label": "Map pin"
            },
            {
              "value": "pants",
              "label": "Pants"
            },
            {
              "value": "plane",
              "label": "Plane"
            },
            {
              "value": "price_tag",
              "label": "Price tag"
            },
            {
              "value": "question_mark",
              "label": "Question mark"
            },
            {
              "value": "return",
              "label": "Return"
            },
            {
              "value": "ruler",
              "label": "Ruler"
            },
            {
              "value": "shirt",
              "label": "Shirt"
            },
            {
              "value": "shoe",
              "label": "Shoe"
            },
            {
              "value": "silhouette",
              "label": "Silhouette"
            },
            {
              "value": "star",
              "label": "Star"
            },
            {
              "value": "truck",
              "label": "Truck"
            },
            {
              "value": "washing",
              "label": "Washing"
            }
          ]
        },
        {
          "type": "text",
          "id": "title",
          "label": "Accordion label",
          "default": "Accordion item"
        },
        {
          "id": "content",
          "type": "richtext",
          "label": "Accordion content"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Accordion",
      "category": "Homepage",
      "blocks": [
        {
          "type": "accor_item",
          "settings": {
            "title": "Tab 01",
            "content": "<p>We love to hear from you on our customer service, merchandise, website or any topics you want to share with us. Your comments and suggestions will be appreciated. Please complete the form below.</p>"
          }
        },
        {
          "type": "accor_item",
          "settings": {
            "title": "Tab 02",
            "content": "<p>We love to hear from you on our customer service, merchandise, website or any topics you want to share with us. Your comments and suggestions will be appreciated. Please complete the form below.</p>"
          }
        },
        {
          "type": "accor_item",
          "settings": {
            "title": "Tab 03",
            "content": "<p>We love to hear from you on our customer service, merchandise, website or any topics you want to share with us. Your comments and suggestions will be appreciated. Please complete the form below.</p>"
          }
        }
      ]
    }
  ]
}
{% endschema %}