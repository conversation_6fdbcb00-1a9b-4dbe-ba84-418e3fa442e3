<!-- sections/our_team.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{%- liquid
    assign sid = section.id
    assign se_stts = section.settings
    assign se_blocks = section.blocks
    assign stt_layout = se_stts.layout
    assign stt_image_bg = se_stts.image_bg
    if stt_layout == 't4s-se-container' 
        assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
    elsif stt_layout == 't4s-container-wrap'
        assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
    else
        assign html_layout = '__' | split: '__'
    endif
    if se_stts.btn_owl == "outline"
    assign arrow_icon = 1
    else
    assign arrow_icon = 2
    endif
    assign use_slider = false
    assign t4s_se_class = 't4s_nt_se_' | append: sid
    if se_stts.use_cus_css and se_stts.code_cus_css != blank
        render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
    endif 
 -%}     
<div class="t4s-section-inner {{ t4s_se_class }} t4s_nt_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}
      <div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
    {%- render 'section_tophead', se_stts: se_stts -%}
    {% if se_stts.layout_des == "1" %}
        <div class="t4s_ratio1_1 t4s-align-items-center t4s-row t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }} t4s-gx-md-{{ se_stts.space_item }} t4s-gy-md-{{ se_stts.space_item }} t4s-gx-{{ se_stts.space_item_mb }} t4s-gy-{{ se_stts.space_item_mb }}" id="b_{{ block.id }}" data-select-flickity {{ block.shopify_attributes }}>
    {% elsif se_stts.layout_des == "2" %}
        {%- assign use_slider = true -%}
        <div class="t4s-flicky-slider t4s_ratio1_1 t4s-row t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }} {% if se_stts.nav_btn == true %}t4s-btn-style-{{ se_stts.btn_owl }} t4s-btn-{{ se_stts.btn_shape }} t4s-btn-{{ se_stts.btn_size }} t4s-btn-cl-{{ se_stts.btn_cl }} t4s-btn-vi-{{ se_stts.btn_vi }}{% endif %} t4s-btn-hidden-mobile-{{ se_stts.btn_hidden_mobile }}{% if se_stts.nav_btn == true %}  t4s-slider-btn-style-{{ se_stts.btn_owl }} t4s-slider-btn-{{ se_stts.btn_shape }} t4s-slider-btn-{{ se_stts.btn_size }} t4s-slider-btn-cl-{{ se_stts.btn_cl }} t4s-slider-btn-vi-{{ se_stts.btn_vi }} t4s-slider-btn-hidden-mobile-{{ se_stts.btn_hidden_mobile }} {% endif %}{% if se_stts.nav_dot %} t4s-dots-style-{{ se_stts.dot_owl }} t4s-dots-cl-{{ se_stts.dots_cl }} t4s-dots-round-{{ se_stts.dots_round }} t4s-dots-hidden-mobile-{{ se_stts.dots_hidden_mobile }} {% endif %} flickityt4s" data-flickityt4s-js='{"setPrevNextButtons":true, "arrowIcon":"{{ arrow_icon }}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": {{ se_stts.loop }},"prevNextButtons": {{ se_stts.nav_btn }},"percentPosition": 1,"pageDots": {{ se_stts.nav_dot }}, "autoPlay" : {{ se_stts.au_time | times: 1000 }}, "pauseAutoPlayOnHover" : {{ se_stts.au_hover }} }' style="--space-dots: {{ se_stts.dots_space }}px;--flickity-btn-pos: {{ se_stts.space_item }}px;--flickity-btn-pos-mb: {{ se_stts.space_item_mb }}px;">
    {% endif %}
        {%- if se_blocks.size > 0 -%}
            {%- for block in se_blocks -%}
                {%- liquid
                    assign bk_stts = block.settings 
                    assign image = bk_stts.image 
                    assign social_icons = false
                    if bk_stts.social_facebook_link != blank or bk_stts.social_twitter_link != blank or bk_stts.social_instagram_link != blank or bk_stts.social_dribbble_link != blank or bk_stts.social_behance_link != blank or bk_stts.social_tiktok_link != blank
                      assign social_icons = true
                    endif
                    if social_icons
                        assign socials = "social_facebook_link social_twitter_link social_instagram_link social_dribbble_link social_behance_link social_tiktok_link" | split: ' '
                        assign socials_icon = "facebook twitter instagram dribbble behance tiktok" | split: ' '
                    endif
               -%}
                <div class="t4s-col-item t4s-member {{ click_class }}" id="b_{{ block.id }}" data-select-flickity {{ block.shopify_attributes }} >  
                    <div class="t4s-member-inner t4s-pr t4s-text-center" timeline hdt-reveal="slide-in">
                        <div class="t4s-member-info">
                            <div class="t4s_ratio" style="--aspect-ratioapt: {{ image.aspect_ratio | default: 1 }}" data-cacl-slide>
                                {%- if image != blank -%}
                                    <img class="lazyloadt4s t4s-lz--fadeIn" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                                    <span class="lazyloadt4s-loader is-bg-img" style="background: url({{ image | image_url: width: 1 }});"></span>
                                {%- else -%}
                                    {{ 'image' | placeholder_svg_tag: 't4s-placeholder-svg' }} 
                                {%- endif -%} 
                            </div> 
                            <h4 class="t4s-member-name">{{ bk_stts.name }}</h4>
                            <span>{{ bk_stts.position }}</span>
                        </div>
                        <div class="t4s-member-socials t4s-pa t4s-w-100">
                            {%- for social_link in socials -%}
                                {% if bk_stts[social_link] != blank -%}
                                    <a href="{{ bk_stts[social_link] | escape }}" target="_blank">{%- render 'icon_socials', icon_name: socials_icon[forloop.index0] -%} </a>
                                {%- endif %}
                            {% endfor -%}
                        </div>  
                    </div>
                </div>
            {%- endfor -%}
        {%- endif -%}
    </div>
    {{- html_layout[1] -}}
</div>
{%- if use_slider -%}
    {{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
    {{ 'slider-settings.css' | asset_url | stylesheet_tag }}
{%- endif -%}
<style>
.t4s-member-name{margin-top:15px;}
.t4s-member-info { padding-bottom: 15px;}
.t4s-member-info::before{content: '';position: absolute;background: rgba(246,246,246,.9);left: 0;top: 0;width: 100%;height: 100%;opacity: 0;transition: all .3s;z-index: 1;}
.t4s-member-inner:hover .t4s-member-info::before{opacity: 1;}
.t4s-member-socials{bottom: 30px;z-index: 2;opacity:0;transition: .3s;}
.t4s-member-socials a svg {width: 18px;height: 18px;fill:currentColor;}
.t4s-member-socials a {margin: 0px 8px;}
.t4s-member-inner:hover .t4s-member-socials{opacity:1;}
</style>
{%- schema -%}
{
    "name": "Our team",
    "tag": "section",
    "class": "t4s-section t4s-section-all t4s_bk_flickity",
    "settings": [
        {
            "type": "header",
            "content": "1. Heading options"
        },
        {
            "type": "select",
            "id": "design_heading",
            "label": "+ Design heading",
            "default": "1",
            "options": [
                {
                    "value": "1",
                    "label": "Design 01"
                },
                {
                    "value": "2",
                    "label": "Design 02"
                },
                {
                    "value": "3",
                    "label": "Design 03"
                },
                {
                    "value": "4",
                    "label": "Design 04"
                },
                {
                    "value": "5",
                    "label": "Design 05"
                },
                {
                    "value": "6",
                    "label": "Design 06 (width line-awesome)"
                },
                {
                    "value": "7",
                    "label": "Design 07"
                },
                {
                    "value": "8",
                    "label": "Design 08"
                },
                {
                    "value": "9",
                    "label": "Design 09"
                },
                {
                    "value": "10",
                    "label": "Design 10"
                },
                {
                    "value": "11",
                    "label": "Design 11"
                },
                {
                    "value": "12",
                    "label": "Design 12"
                },
                {
                    "value": "13",
                    "label": "Design 13"
                },
                {
                    "value": "14",
                    "label": "Design 14"
                },
                {
                    "value": "15",
                    "label": "Design 15"
                },
                {
                  "value": "16",
                  "label": "Design 16"
                }
            ]
        },
        {
            "type": "select",
            "id": "heading_align",
            "label": "+ Heading align",
            "default": "t4s-text-center",
            "options": [
                {
                    "value": "t4s-text-start",
                    "label": "Left"
                },
                {
                    "value": "t4s-text-center",
                    "label": "Center"
                },
                {
                    "value": "t4s-text-end",
                    "label": "Right"
                }
            ]
        },
        {
            "type": "text",
            "id": "top_heading",
            "label": "+ Heading"
        },
        {
            "type": "text",
            "id": "icon_heading",
            "label": "Enter a name icon [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
            "info": "Only used for design 6",
            "default": "las la-gem"
        },
        {
            "type": "textarea",
            "id": "top_subheading",
            "label": "+ Subheading"
        },
        {
            "type": "number",
            "id": "tophead_mb",
            "label": "+ Space bottom (px)",
            "info": "The vertical spacing between heading and content.",
            "default": 30
        },
        {
            "type": "header",
            "content": "2. General options"
        },
        {
            "type": "select",
            "id": "layout_des",
            "options": [
                {
                    "value": "1",
                    "label": "Grid"
                },
                {
                    "value": "2",
                    "label": "Carousel"
                }
            ],
            "label": "Layout design",
            "default": "1"
        },
        {
            "type": "select",
            "id": "col_dk",
            "label": "Member per row",
            "default": "3",
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                },
                {
                    "value": "3",
                    "label": "3"
                },
                {
                    "value": "4",
                    "label": "4"
                },
                {
                    "value": "5",
                    "label": "5"
                },
                {
                    "value": "6",
                    "label": "6"
                }
            ]
        },
        {
            "type": "select",
            "id": "col_tb",
            "label": "Member per row (Tablet)",
            "default": "2",
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                },
                {
                    "value": "3",
                    "label": "3"
                },
                {
                    "value": "4",
                    "label": "4"
                }
            ]
        },
        {
            "type": "select",
            "id": "col_mb",
            "label": "Member per row (Mobile)",
            "default": "2",
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                }
            ]
        },
        {
            "type": "select",
            "id": "space_item",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                }
            ],
            "label": "Space between items",
            "default": "30"
        },
        {
            "type": "select",
            "id": "space_item_mb",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                }
            ],
            "label": "Space between items (Mobile)",
            "default": "30"
        },
        {
            "type": "header",
            "content": "+Options for carousel layout"
        },
        {
            "type": "checkbox",
            "id": "loop",
            "label": "Enable loop",
            "info": "At the end of cells, wrap-around to the other end for infinite scrolling",
            "default": true
        },
        {
            "type": "range",
            "id": "au_time",
            "min": 0,
            "max": 30,
            "step": 0.5,
            "label": "Autoplay speed in second.",
            "info": "Set is '0' to disable autoplay",
            "unit": "s",
            "default": 0
        },
        {
            "type": "checkbox",
            "id": "au_hover",
            "label": "Pause autoplay on hover",
            "info": "Auto-playing will pause when the user hovers over the carousel",
            "default": true
        },
        {
            "type": "paragraph",
            "content": "—————————————————"
        },
        {
            "type": "paragraph",
            "content": "Prev next button"
        },
        {
            "type": "checkbox",
            "id": "nav_btn",
            "label": "Use prev next button",
            "info": "Creates and show previous & next buttons",
            "default": false
        },
        {
            "type": "select",
            "id": "btn_vi",
            "label": "Visible",
            "default": "hover",
            "options": [
                {
                    "value": "always",
                    "label": "Always"
                },
                {
                    "value": "hover",
                    "label": "Only hover"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_owl",
            "label": "Button style",
            "default": "default",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "outline",
                    "label": "Outline"
                },
                {
                    "value": "simple",
                    "label": "Simple"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_shape",
            "label": "Button shape",
            "info": "Not working with button style 'Simple'",
            "default": "none",
            "options": [
                {
                    "value": "none",
                    "label": "Default"
                },
                {
                    "value": "round",
                    "label": "Round"
                },
                {
                    "value": "rotate",
                    "label": "Rotate"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_cl",
            "label": "Button color",
            "default": "dark",
            "options": [
                {
                    "value": "light",
                    "label": "Light"
                },
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "primary",
                    "label": "Primary"
                },
                {
                    "value": "custom1",
                    "label": "Custom color 1"
                },
                {
                    "value": "custom2",
                    "label": "Custom color 2"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_size",
            "label": "Buttons size",
            "default": "small",
            "options": [
                {
                    "value": "small",
                    "label": "Small"
                },
                {
                    "value": "medium",
                    "label": "Medium"
                },
                {
                    "value": "large",
                    "label": "Large"
                }
            ]
        },
        {
            "type":"checkbox",
            "id":"btn_hidden_mobile",
            "label":"Hidden buttons on mobile ",
            "default": true
        },
        {
            "type": "paragraph",
            "content": "—————————————————"
        },
        {
            "type": "paragraph",
            "content": "Page dots"
        },
        {
            "type": "checkbox",
            "id": "nav_dot",
            "label": "Use page dots",
            "info": "Creates and show page dots",
            "default": false
        },
        {
            "type": "select",
            "id": "dot_owl",
            "label": "Dots style",
            "default": "default",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "outline",
                    "label": "Outline"
                },
                {
                    "value": "elessi",
                    "label": "Elessi"
                }
            ]
        },
        {
            "type": "select",
            "id": "dots_cl",
            "label": "Dots color",
            "default": "dark",
            "options": [
                {
                    "value": "light",
                    "label": "Light (Best on dark background)"
                },
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "primary",
                    "label": "Primary"
                },
                {
                    "value": "custom1",
                    "label": "Custom color 1"
                },
                {
                    "value": "custom2",
                    "label": "Custom color 2"
                }
            ]
        },
        {
            "type": "checkbox",
            "id": "dots_round",
            "label": "Enable dots round",
            "default": true
        },
        {
            "type": "range",
            "id": "dots_space",
            "min": 2,
            "max": 20,
            "step": 1,
            "label": "Dot between horizontal",
            "unit": "px",
            "default": 10
        },
        {
            "type":"checkbox",
            "id":"dots_hidden_mobile",
            "label":"Hidden dots on mobile ",
            "default": false
        },
        {
            "type": "header",
            "content": "3.Design options"
        },
        {
            "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
            "options": [
                { "value": "t4s-se-container", "label": "Container"},
                { "value": "t4s-container-wrap", "label": "Wrapped container"},
                { "value": "t4s-container-fluid", "label": "Full width"}
            ]
        },
        {
            "type": "color",
            "id": "cl_bg",
            "label": "Background"
        },
        {
            "type": "color_background",
            "id": "cl_bg_gradient",
            "label": "Background gradient"
        },
        {
            "type": "image_picker",
            "id": "image_bg",
            "label": "Background image"
        },
        {
            "type": "text",
            "id": "mg",
            "label": "Margin",
            "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
            "default": ",,50px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd",
            "label": "Padding",
            "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
            "placeholder": "50px,,50px,"
        },
        {
          "type": "header",
          "content": "+ Design Tablet Options"
        },
        {
          "type": "text",
          "id": "mg_tb",
          "label": "Margin",
          "placeholder": ",,50px,"
        },
        {
          "type": "text",
          "id": "pd_tb",
          "label": "Padding",
          "placeholder": ",,50px,"
        },
        {
            "type": "header",
            "content": "+ Design mobile options"
        },
        {
            "type": "text",
            "id": "mg_mb",
            "label": "Margin",
            "default": ",,30px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_mb",
            "label": "Padding",
            "placeholder": ",,50px,"
        },
        {
            "type": "header",
            "content": "4. Custom css"
        },
        {
            "id": "use_cus_css",
            "type": "checkbox",
            "label": "Use custom css",
            "default":false,
            "info": "If you want custom style for this section."
        },
        { 
            "id": "code_cus_css",
            "type": "textarea",
            "label": "Code custom css",
            "info": "Use selector .SectionID to style css"
            
        }
    ],
    "blocks": [
        {
            "type": "member",
            "name": "Member",
            "settings": [
                {
                    "type": "image_picker",
                    "id": "image",
                    "label": "Image"
                },
                {
                    "type": "text",
                    "id": "name",
                    "label": "Name",
                    "default": "Add a name"
                },
                {
                    "type": "text",
                    "id": "position",
                    "label": "Position",
                    "default": "Developer"
                },
                {
                    "type": "header",
                    "content": "+ Social"
                },
                {
                    "type": "text",
                    "id": "social_facebook_link",
                    "label": "Facebook",
                    "info": "https:\/\/facebook.com\/shopify"
                },
                {
                    "type": "text",
                    "id": "social_twitter_link",
                    "label": "Twitter",
                    "info": "https:\/\/twitter.com\/shopify"
                },
                {
                    "type": "text",
                    "id": "social_instagram_link",
                    "label": "Instagram",
                    "info": "https:\/\/instagram.com\/shopify"
                },
                {
                    "type": "text",
                    "id": "social_dribbble_link",
                    "label": "Dribbble",
                    "info": "https:\/\/dribbble.com\/shopify"
                },
                {
                    "type": "text",
                    "id": "social_behance_link",
                    "label": "Behance",
                    "info": "https:\/\/behance.net\/user"
                },
                {
                    "type": "text",
                    "id": "social_tiktok_link",
                    "label": "Tiktok",
                    "info": "https:\/\/tiktok.com\/shopify"
                }
            ]
        }
    ],
    "presets": [
      {
        "name": "Our team",
        "category": "Homepage",
        "blocks": [
            {"type": "member"},
            {"type": "member"},
            {"type": "member"}
        ]
      }
    ]
}
{% endschema %}
