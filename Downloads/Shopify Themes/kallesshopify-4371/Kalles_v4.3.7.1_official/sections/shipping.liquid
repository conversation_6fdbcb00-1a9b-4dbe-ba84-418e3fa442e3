<!-- sections/shipping.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'shipping.css' | asset_url | stylesheet_tag }}
{{ 'slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
<link href="{{ 't4s-animation.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if stt_layout == 't4s-se-container'
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif

  assign carousel_mobile = se_stts.carousel_mobile
  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif

  assign cl_ic_lightness = se_stts.cl_ic | color_extract: 'lightness'
  if cl_ic_lightness < 85
    assign cl_ic2 = '#ffffff'
  else
    assign cl_ic2 = '#222222'
  endif
-%}
<div
  class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}"
  {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %}
    data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto" data-optimumx="2"
  {% endif %}
  {% render 'section_style', se_stts: se_stts -%}
  timeline
  hdt-reveal="slide-in"
>
  {{- html_layout[0] -}}
  {%- if stt_layout == 't4s-se-container' -%}
    <div
      class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}"
      {% if stt_image_bg != blank %}
        data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto" data-optimumx="2"
      {% endif %}
    >
  {% endif -%}
  {%- render 'section_tophead', se_stts: se_stts -%}
  <div
    class="t4s-shipping-list {% if carousel_mobile %} carousel-disable-md t4s-flicky-slider flickityt4s t4s-dots-style-default t4s-dots-cl-{{ se_stts.dots_cl }} t4s-dots-round-{{ se_stts.dots_round }} {% endif %} t4s-shipping-padding-{{ se_stts.design_padding }} t4s-shipping-icon-{{ se_stts.icon_des }} t4s-shipping-icon-{{ se_stts.icon_size }} t4s-{{ se_stts.content_align }} t4s-ver-center-{{ se_stts.align_vertical }} use_border_{{ se_stts.border }} t4s-row t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }}  t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }}"
    style="--icon-cl: {{ se_stts.cl_ic }};--icon-cl2: {{ cl_ic2 }};--bd-cl: {{ se_stts.cl_bd }}; --bg-cl: {{ se_stts.bg_item }};--title-cl: {{ se_stts.cl_hd }};--content-cl: {{ se_stts.cl_cot }};--hd-fs: {{ se_stts.hd_fs }}px; --txt-fs: {{ se_stts.txt_fs }}px; --hd-fw: {{ se_stts.hd_fw }};"
    data-flickityt4s-js='{"watchCSS":1,"setPrevNextButtons":false,"arrowIcon":"1","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": false,"prevNextButtons": false,"percentPosition": 1,"pageDots": true, "autoPlay" : false, "pauseAutoPlayOnHover" : true }'
  >
    {%- if section.blocks.size > 0 -%}
      {%- for block in section.blocks -%}
        {%- assign bk_stts = block.settings -%}
        {%- assign image = bk_stts.image_icon -%}
        <div class="t4s-shipping t4s-shipping-item t4s-col-item">
          <div class="t4s-shipping-inner">
            {%- if se_stts.source == 'themes_icon' -%}
              {%- if bk_stts.icon_themes != blank and bk_stts.icon_themes != 'none' -%}
                <div class="t4s-col-auto t4s-shipping-icon t4s-shipping-icon-themes">
                  {%- render 'icon_shipping', icon_name: bk_stts.icon_themes %}
                </div>
              {%- endif -%}
            {%- elsif se_stts.source == 'get_image' -%}
              {%- if image != blank -%}
                <div
                  class="t4s-col-auto t4s-shipping-icon t4s-shipping-icon-img t4s-ship-img lazyloadt4s"
                  data-bgset="{{ image | image_url: width: 1 }}"
                  data-sizes="auto"
                  data-optimumx="2"
                ></div>
              {%- endif -%}
            {%- else -%}
              {%- if bk_stts.icon != blank -%}
                <div class="t4s-col-auto t4s-shipping-icon t4s-shipping-icon-line">
                  <i class="{{ bk_stts.icon }}"></i>
                </div>
              {%- endif -%}
            {%- endif -%}
            <div class="t4s-col t4s-shipping-content">
              <h3 class="t4s-shipping-title">{{ bk_stts.title }}</h3>
              <div class="t4s-shipping-des t4s-rte--list">{{ bk_stts.text }}</div>
              <div class="t4s-shipping-des t4s-rte--list">{{ bk_stts.html }}</div>
            </div>
          </div>
        </div>
      {%- endfor -%}
    {%- endif -%}
  </div>
  {{- html_layout[1] -}}
</div>
{%- schema -%}
{
  "name": "Shipping",
  "tag": "section",
  "class": "t4s-section t4s-section-all t4s_tp_cdt t4s-shipping t4s_bk_flickity",
  "settings": [
    {
      "type": "header",
      "content": "1. Heading options"
    },
    {
      "type": "select",
      "id": "design_heading",
      "label": "+ Design heading",
      "default": "1",
      "options": [
        {
          "value": "1",
          "label": "Design 01"
        },
        {
          "value": "2",
          "label": "Design 02"
        },
        {
          "value": "3",
          "label": "Design 03"
        },
        {
          "value": "4",
          "label": "Design 04"
        },
        {
          "value": "5",
          "label": "Design 05"
        },
        {
          "value": "6",
          "label": "Design 06 (width line-awesome)"
        },
        {
          "value": "7",
          "label": "Design 07"
        },
        {
          "value": "8",
          "label": "Design 08"
        },
        {
          "value": "9",
          "label": "Design 09"
        },
        {
          "value": "10",
          "label": "Design 10"
        },
        {
          "value": "11",
          "label": "Design 11"
        },
        {
          "value": "12",
          "label": "Design 12"
        },
        {
          "value": "13",
          "label": "Design 13"
        },
        {
          "value": "14",
          "label": "Design 14"
        },
        {
          "value": "15",
          "label": "Design 15"
        },
        {
          "value": "16",
          "label": "Design 16"
        }
      ]
    },
    {
      "type": "select",
      "id": "heading_align",
      "label": "+ Heading align",
      "default": "t4s-text-center",
      "options": [
        {
          "value": "t4s-text-start",
          "label": "Left"
        },
        {
          "value": "t4s-text-center",
          "label": "Center"
        },
        {
          "value": "t4s-text-end",
          "label": "Right"
        }
      ]
    },
    {
      "type": "text",
      "id": "top_heading",
      "label": "+ Heading"
    },
    {
      "type": "text",
      "id": "icon_heading",
      "label": "Enter a icon name on heading",
      "info": "Only used for design 6 [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
      "default": "las la-gem"
    },
    {
      "type": "textarea",
      "id": "top_subheading",
      "label": "+ Subheading"
    },
    {
      "type": "number",
      "id": "tophead_mb",
      "label": "+ Space bottom (px)",
      "info": "The vertical spacing between heading and content",
      "default": 30
    },
    {
      "type": "header",
      "content": "2. General options"
    },
    {
      "type": "select",
      "id": "content_align",
      "label": "Content align",
      "default": "text-start",
      "options": [
        {
          "label": "Left",
          "value": "text-start"
        },
        {
          "label": "Center",
          "value": "text-center"
        },
        {
          "label": "Right",
          "value": "text-end"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "align_vertical",
      "label": "Content align vertical center",
      "default": false
    },
    {
      "type": "select",
      "id": "design_padding",
      "label": "Space padding",
      "default": "1",
      "options": [
        {
          "value": "1",
          "label": "Space 01"
        },
        {
          "value": "2",
          "label": "Space 02"
        }
      ]
    },
    {
      "type": "select",
      "id": "source",
      "label": "Source icon",
      "default": "themes_icon",
      "options": [
        {
          "value": "themes_icon",
          "label": "Themes icon"
        },
        {
          "value": "get_image",
          "label": "Use image"
        },
        {
          "value": "line_awe",
          "label": "Line awesome"
        }
      ]
    },
    {
      "type": "select",
      "id": "icon_des",
      "label": "Icon design",
      "default": "default",
      "options": [
        {
          "label": "Default",
          "value": "default"
        },
        {
          "label": "Circle",
          "value": "circle"
        }
      ]
    },
    {
      "type": "select",
      "id": "icon_size",
      "label": "Icon size",
      "default": "medium",
      "options": [
        {
          "label": "Extra Small",
          "value": "extra-small"
        },
        {
          "label": "Small",
          "value": "small"
        },
        {
          "label": "Medium",
          "value": "medium"
        },
        {
          "label": "Large",
          "value": "large"
        }
      ]
    },
    {
      "type": "select",
      "id": "col_dk",
      "label": "Items per row",
      "default": "4",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        },
        {
          "value": "3",
          "label": "3"
        },
        {
          "value": "4",
          "label": "4"
        },
        {
          "value": "5",
          "label": "5"
        },
        {
          "value": "6",
          "label": "6"
        }
      ]
    },
    {
      "type": "select",
      "id": "col_tb",
      "label": "Items per row (Tablet)",
      "default": "2",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        },
        {
          "value": "3",
          "label": "3"
        },
        {
          "value": "4",
          "label": "4"
        }
      ]
    },
    {
      "type": "select",
      "id": "col_mb",
      "label": "Items per row (Mobile)",
      "default": "1",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "border",
      "label": "Use border",
      "default": false
    },
    {
      "type": "color",
      "id": "cl_bd",
      "label": "Border color",
      "default": "#ddd"
    },
    {
      "type": "color",
      "id": "cl_ic",
      "label": "Icon color",
      "default": "#9e9e9e"
    },
    {
      "type": "color",
      "id": "cl_hd",
      "label": "Heading color",
      "default": "#222222"
    },
    {
      "type": "color",
      "id": "cl_cot",
      "label": "Content color",
      "default": "#878787"
    },
    {
      "type": "color",
      "id": "bg_item",
      "label": "Item background color",
      "info": "Only choose when has border",
      "default": "#fff"
    },
    {
      "type": "range",
      "label": "Heading font size",
      "id": "hd_fs",
      "min": 10,
      "max": 30,
      "step": 1,
      "unit": "px",
      "default": 16
    },
    {
      "type": "range",
      "label": "Heading font weight",
      "id": "hd_fw",
      "min": 300,
      "max": 800,
      "step": 100,
      "default": 500
    },
    {
      "type": "range",
      "label": "Text font size",
      "id": "txt_fs",
      "min": 10,
      "max": 30,
      "step": 1,
      "unit": "px",
      "default": 13
    },
    {
      "type": "select",
      "id": "space_h_item",
      "options": [
        {
          "value": "0",
          "label": "0"
        },
        {
          "value": "2",
          "label": "2px"
        },
        {
          "value": "4",
          "label": "4px"
        },
        {
          "value": "6",
          "label": "6px"
        },
        {
          "value": "8",
          "label": "8px"
        },
        {
          "value": "10",
          "label": "10px"
        },
        {
          "value": "15",
          "label": "15px"
        },
        {
          "value": "20",
          "label": "20px"
        },
        {
          "value": "30",
          "label": "30px"
        }
      ],
      "label": "Space horizontal items",
      "default": "30"
    },
    {
      "type": "select",
      "id": "space_v_item",
      "options": [
        {
          "value": "0",
          "label": "0"
        },
        {
          "value": "2",
          "label": "2px"
        },
        {
          "value": "4",
          "label": "4px"
        },
        {
          "value": "6",
          "label": "6px"
        },
        {
          "value": "8",
          "label": "8px"
        },
        {
          "value": "10",
          "label": "10px"
        },
        {
          "value": "15",
          "label": "15px"
        },
        {
          "value": "20",
          "label": "20px"
        },
        {
          "value": "30",
          "label": "30px"
        }
      ],
      "label": "Space vertical items",
      "default": "30"
    },
    {
      "type": "select",
      "id": "space_h_item_mb",
      "options": [
        {
          "value": "0",
          "label": "0"
        },
        {
          "value": "2",
          "label": "2px"
        },
        {
          "value": "4",
          "label": "4px"
        },
        {
          "value": "6",
          "label": "6px"
        },
        {
          "value": "8",
          "label": "8px"
        },
        {
          "value": "10",
          "label": "10px"
        },
        {
          "value": "15",
          "label": "15px"
        },
        {
          "value": "20",
          "label": "20px"
        },
        {
          "value": "30",
          "label": "30px"
        }
      ],
      "label": "Space horizontal items (Mobile)",
      "default": "10"
    },
    {
      "type": "select",
      "id": "space_v_item_mb",
      "options": [
        {
          "value": "0",
          "label": "0"
        },
        {
          "value": "2",
          "label": "2px"
        },
        {
          "value": "4",
          "label": "4px"
        },
        {
          "value": "6",
          "label": "6px"
        },
        {
          "value": "8",
          "label": "8px"
        },
        {
          "value": "10",
          "label": "10px"
        },
        {
          "value": "15",
          "label": "15px"
        },
        {
          "value": "20",
          "label": "20px"
        },
        {
          "value": "30",
          "label": "30px"
        }
      ],
      "label": "Space vertical items (Mobile)",
      "default": "10"
    },
    {
      "type": "header",
      "content": "Carousel options"
    },
    {
      "type": "checkbox",
      "id": "carousel_mobile",
      "label": "Enable carousel on mobile",
      "default": true
    },
    {
      "type": "select",
      "id": "dots_cl",
      "label": "Dots color",
      "default": "dark",
      "options": [
        {
          "value": "light",
          "label": "Light (Best on dark background)"
        },
        {
          "value": "dark",
          "label": "Dark"
        },
        {
          "value": "primary",
          "label": "Primary"
        },
        {
          "value": "custom1",
          "label": "Custom color 1"
        },
        {
          "value": "custom2",
          "label": "Custom color 2"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "dots_round",
      "label": "Enable dots round",
      "default": true
    },
    {
      "type": "header",
      "content": "3. Design options"
    },
    {
      "type": "select",
      "id": "layout",
      "default": "t4s-container-wrap",
      "label": "Layout",
      "options": [
        { "value": "t4s-se-container", "label": "Container" },
        { "value": "t4s-container-wrap", "label": "Wrapped container" },
        { "value": "t4s-container-fluid", "label": "Full width" }
      ]
    },
    {
      "type": "color",
      "id": "cl_bg",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "cl_bg_gradient",
      "label": "Background gradient"
    },
    {
      "type": "image_picker",
      "id": "image_bg",
      "label": "Background image"
    },
    {
      "type": "text",
      "id": "mg",
      "label": "Margin",
      "info": "Margin top, margin right, margin bottom, margin left. If you not use to blank",
      "default": ",,50px,",
      "placeholder": ",,50px,"
    },
    {
      "type": "text",
      "id": "pd",
      "label": "Padding",
      "info": "Padding top, padding right, padding bottom, padding left. If you not use to blank",
      "placeholder": "50px,,50px,"
    },
    {
      "type": "header",
      "content": "+ Design Tablet Options"
    },
    {
      "type": "text",
      "id": "mg_tb",
      "label": "Margin",
      "default": ",,50px,",
      "placeholder": ",,50px,"
    },
    {
      "type": "text",
      "id": "pd_tb",
      "label": "Padding",
      "placeholder": ",,50px,"
    },
    {
      "type": "header",
      "content": "+ Design mobile options"
    },
    {
      "type": "text",
      "id": "mg_mb",
      "label": "Margin",
      "default": ",,30px,",
      "placeholder": ",,50px,"
    },
    {
      "type": "text",
      "id": "pd_mb",
      "label": "Padding",
      "placeholder": ",,50px,"
    },
    {
      "type": "header",
      "content": "4. Custom css"
    },
    {
      "id": "use_cus_css",
      "type": "checkbox",
      "label": "Use custom css",
      "default": false,
      "info": "If you want custom style for this section."
    },
    {
      "id": "code_cus_css",
      "type": "textarea",
      "label": "Code custom css",
      "info": "Use selector .SectionID to style css"
    }
  ],
  "blocks": [
    {
      "type": "shipping",
      "name": "Shipping",
      "settings": [
        {
          "type": "header",
          "content": "+ Icon options"
        },
        {
          "type": "select",
          "id": "icon_themes",
          "label": "Select icon",
          "info": "Only used for source  theme icon",
          "default": "car",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "car",
              "label": "Car"
            },
            {
              "value": "truck",
              "label": "Truck"
            },
            {
              "value": "diamond",
              "label": "Diamond"
            },
            {
              "value": "door-lock",
              "label": "Door lock"
            },
            {
              "value": "gym",
              "label": "Gym"
            },
            {
              "value": "hammer",
              "label": "Hammer"
            },
            {
              "value": "headphones",
              "label": "Headphones"
            },
            {
              "value": "helm",
              "label": "Helm"
            },
            {
              "value": "hourglass",
              "label": "Hourglass"
            },
            {
              "value": "map",
              "label": "Map"
            },
            {
              "value": "piggy",
              "label": "Piggy"
            },
            {
              "value": "refesh",
              "label": "Refesh"
            },
            {
              "value": "rocket",
              "label": "Rocket"
            },
            {
              "value": "shield",
              "label": "Shield"
            },
            {
              "value": "shield2",
              "label": "Shield 2"
            },
            {
              "value": "smile",
              "label": "Smile"
            },
            {
              "value": "scissors",
              "label": "Scissors"
            },
            {
              "value": "shuffle",
              "label": "Shuffle"
            },
            {
              "value": "cloud_upload",
              "label": "Cloud upload"
            },
            {
              "value": "cash",
              "label": "Cash"
            },
            {
              "value": "way",
              "label": "Way"
            },
            {
              "value": "wristwatch",
              "label": "Wristwatch"
            },
            {
              "value": "world",
              "label": "World"
            },
            {
              "value": "wallet",
              "label": "Wallet"
            },
            {
              "value": "unlock",
              "label": "Unlock"
            },
            {
              "value": "umbrella",
              "label": "Umbrella"
            },
            {
              "value": "repeat",
              "label": "Repeat"
            },
            {
              "value": "refesh-2",
              "label": "Refesh 2"
            },
            {
              "value": "medal",
              "label": "Medal"
            },
            {
              "value": "portfolio",
              "label": "Portfolio"
            },
            {
              "value": "like",
              "label": "Like"
            },
            {
              "value": "plance",
              "label": "Plance"
            },
            {
              "value": "map-maker",
              "label": "Map maker"
            },
            {
              "value": "help",
              "label": "Help"
            },
            {
              "value": "gift",
              "label": "Gift"
            },
            {
              "value": "cart",
              "label": "Cart"
            },
            {
              "value": "box",
              "label": "Box"
            },
            {
              "value": "back",
              "label": "Back"
            },
            {
              "value": "back2",
              "label": "Back2"
            }
          ]
        },
        {
          "type": "image_picker",
          "id": "image_icon",
          "label": "Choose image",
          "info": "Only used for source image"
        },

        {
          "type": "text",
          "id": "icon",
          "label": "Enter icon",
          "info": "Only used for source line awesome icon",
          "default": "las la-shipping-fast"
        },
        {
          "type": "paragraph",
          "content": "[LineAwesome](https://kalles.the4.co/font-lineawesome/)"
        },
        {
          "type": "header",
          "content": "+Text options"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Add a heading"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Use this text to describe.<\/p>"
        },
        {
          "type": "textarea",
          "id": "html",
          "label": "HTML"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Shipping",
      "category": "Homepage",
      "blocks": [
        {
          "type": "shipping",
          "settings": {
            "icon_themes": "car",
            "icon": "las la-shipping-fast",
            "title": "FREE SHIPPING",
            "text": "<p>Free shipping on all US order or order above $100<\/p>"
          }
        },
        {
          "type": "shipping",
          "settings": {
            "icon_themes": "help",
            "icon": "las la-life-ring",
            "title": "SUPPORT 24\/7",
            "text": "<p>Contact us 24 hours a day, 7 days a week<\/p>"
          }
        },
        {
          "type": "shipping",
          "settings": {
            "icon_themes": "refesh",
            "icon": "las la-undo-alt",
            "title": "30 DAYS RETURN",
            "text": "<p>Simply return it within 30 days for an exchange.<\/p>"
          }
        },
        {
          "type": "shipping",
          "settings": {
            "icon_themes": "door-lock",
            "icon": "las la-lock",
            "title": "100% PAYMENT SECURE",
            "text": "<p>We ensure secure payment with PEV<\/p>"
          }
        }
      ]
    }
  ]
}
{%- endschema -%}
