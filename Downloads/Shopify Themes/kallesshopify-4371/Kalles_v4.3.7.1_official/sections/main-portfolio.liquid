<!-- sections/main-portfolio.liquid -->
{{ 'blog.css' | asset_url | stylesheet_tag }}
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'custom-effect.css' | asset_url | stylesheet_tag }}
{{ 'loading.css' | asset_url | stylesheet_tag }}
{{ 'button-style.css' | asset_url | stylesheet_tag }}
{{ 'custom-effect.css' | asset_url | stylesheet_tag }}
{%- liquid
    assign sid = section.id
    assign se_stts = section.settings  
    assign se_blocks = section.blocks  
    assign stt_layout = se_stts.layout
    assign stt_image_bg = se_stts.image_bg
    if stt_layout == 't4s-se-container' 
        assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
    elsif stt_layout == 't4s-container-wrap'
        assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
    else
        assign html_layout = '__' | split: '__'
    endif 
    assign source = se_stts.source
    assign limit = se_stts.limit
    assign col_dk = se_stts.col_dk
    assign col_tb = se_stts.col_tb
    assign col_mb = se_stts.col_mb
    assign blog_url =  blog.url
    assign all_tags = blog.all_tags
    assign use_pagination = se_stts.use_pagination
    assign layout_des = se_stts.layout_des
    assign isLoadmore = false
    if layout_des != "2"
        if use_pagination == "load-more" or use_pagination == "infinite" 
            assign isLoadmore = true
            assign typeAjax = 'LmDefault'
        else
            assign typeAjax = 'AjaxDefault'
        endif
    else
        if use_pagination == "load-more" or use_pagination == "infinite" 
            assign isLoadmore = true
            assign typeAjax = 'LmIsotope'
        else
            assign typeAjax = 'AjaxIsotope'
        endif
    endif
    assign use_bar_lm = se_stts.use_bar_lm
    assign results_count = blog.articles_count
 -%}
{%- paginate blog.articles by limit -%}
    <div class="t4s-section-inner t4s_nt_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
        {{- html_layout[0] -}}
        {%- if stt_layout == 't4s-se-container' -%}
            <div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
                <div class="t4s-row">
                    <div data-ntajax-container data-ntajax-options='{"id":"{{ sid }}","type":"{{ typeAjax }}","isProduct":false,"updateURL":true,"updateURLPrev":true}' class="t4s-col-item t4s-main-portfolio-page t4s-main-area">
                      {%- if source != '0' -%}
                          <div class="t4s-blog-tags t4s-blog-count-{{ se_stts.show_count }}" timeline hdt-reveal="slide-in">
                              <ul class="t4s-tags-ul t4s-text-center t4s-active-filters">
                                  {%- if source == '1' -%}
                                      <li class="t4s-d-inline-flex"><a class="{% unless current_tags %}is--selected{% endunless %}" href="{{ blog_url }}">{{ 'blogs.filter_all' | t }}</a></li>
                                      {%- for tag in all_tags -%}
                                          {%- if current_tags contains tag -%}
                                              <li class="t4s-d-inline-flex"><a href="{{ blog_url }}" class="is--selected">{{ tag | remove : 'category_' | strip }}<span class="t4s-blog-count"> ({{ tag.total_count }})</span></a></li>
                                          {%- else -%}
                                              <li class="t4s-d-inline-flex"><a href="{{ blog_url }}/tagged/{{ tag | handle }}">{{ tag | remove : 'category_' | strip }}<span class="t4s-blog-count"> ({{ tag.total_count }})</span></a></li>
                                          {%- endif -%}
                                      {%- endfor -%}
                                  {%- elsif source == '2' -%}
                                      <li class="t4s-d-inline-flex"><a class="{% unless current_tags %} is--selected{% endunless %}" href="{{ blog_url }}">{{ 'blogs.filter_all' | t }}</a></li>
                                      {%- for block in section.blocks -%}
                                          {%- assign tag = block.settings.title -%}
                                          {%- if current_tags contains tag -%}
                                              <li class="t4s-d-inline-flex"><a href="{{ blog_url }}" class="is--selected">{{ tag | remove : 'category_' | strip }}</a></li>
                                          {%- elsif all_tags contains tag -%}
                                              <li class="t4s-d-inline-flex"><a href="{{ blog_url }}/tagged/{{ tag | handle }}">{{ tag | remove : 'category_' | strip }}</a></li>
                                          {%- endif -%}
                                      {%- endfor -%}
                                  {%- endif -%}
                              </ul>
                          </div>
                      	{%- endif -%}
                        {%- if paginate.previous.is_link and isLoadmore -%}
                            <div data-wrap-lm-prev class="t4s-pagination-wrapper t4s-prev-head t4s-has-btn-{{ use_pagination }} {{ se_stts.btn_pos }} t4s-w-100" timeline hdt-reveal="slide-in">
                                <a data-load-more data-is-prev href="{{ paginate.previous.url }}" class="t4s-pr t4s-loadmore-btn t4s-btn t4s-btn-loading__svg t4s-btn-base t4s-btn-style-{{ se_stts.button_style }} t4s-btn-size-{{ se_stts.btn_size }} t4s-btn-icon-{{ se_stts.btn_icon }} t4s-btn-color-{{ se_stts.btn_cl }} {% if se_stts.button_style == 'default' or se_stts.button_style == 'outline' %}t4s-btn-effect-{{ se_stts.button_effect }}{% endif %}">
                                    <span class="t4s-btn-atc_text">{{ 'search.pagination.load_prev' | t }}</span> 
                                    {% if se_stts.btn_icon %}<svg class="t4s-btn-icon" viewBox="0 0 32 32"><path d="M 15 4 L 15 24.0625 L 8.21875 17.28125 L 6.78125 18.71875 L 15.28125 27.21875 L 16 27.90625 L 16.71875 27.21875 L 25.21875 18.71875 L 23.78125 17.28125 L 17 24.0625 L 17 4 Z"/></svg>{% endif %}
                                    <div class="t4s-loading__spinner t4s-dn"><svg  width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg></div> 
                                </a>
                            </div>
                        {%- endif -%} 
                        {%- if se_stts.layout_des == '1' -%}
                        <div data-contentlm-replace class="t4s-row t4s_{{ se_stts.image_ratio }} t4s_position_{{ se_stts.image_position }} t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }} t4s-row-cols-lg-{{ col_dk }} t4s-row-cols-md-{{ col_tb }} t4s-row-cols-{{ col_mb }} t4s-text-{{ se_stts.text_align }} t4s_{{ se_stts.image_size }}">
                        {%- else -%}
                        <div data-contentlm-replace class="isotopet4s t4s_portfolio_masonry t4s_{{ se_stts.image_ratio }} t4s_position_{{ se_stts.image_position }} t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }} t4s-row t4s-row-cols-lg-{{ col_dk }} t4s-row-cols-md-{{ col_tb }} t4s-row-cols-{{ col_mb }} t4s-text-{{ se_stts.text_align }} t4s_{{ se_stts.image_size }}" data-isotopet4s-js='{ "itemSelector": ".t4s-portfolio-item", "layoutMode": "masonry" }'>
                        {%- endif -%}
                            {%- for article in blog.articles -%}
                                {%- assign image = article.image -%}
                                <article class="t4s-col-item t4s-portfolio-item">
                                    <div class="t4s-portfolio-inner t4s-pr" timeline hdt-reveal="slide-in">
                                        {%- if image != blank -%}
                                            <a class="t4s-portfolio-thumb t4s-d-block" href="{{ article.url }}">
                                                <div class="t4s_ratio" style="background: url({{ image | image_url: width: 1 }});--aspect-ratioapt: {{ image.aspect_ratio | default: 2 }}">
                                                    <img class="lazyloadt4s ist4s-lz--fadeIn" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                                                </div>
                                            </a>
                                        {%- endif -%}
                                        <div class="t4s-portfolio-info t4s-pa t4s-w-100 t4s-text-center">                                      
                                            <h4 class="t4s-post-title"><a href="{{ article.url }}" class="t4s-post-text-color">{{ article.title }}</a></h4>
                                            <div class="t4s-portfolio-tags">
                                                {%- for tag in article.tags limit: 3 -%} 
                                                    <a href="{{ blog_url }}/tagged/{{ tag | handle }}" class="t4s-portfolio-text-color">{{ tag | remove : 'category_' | strip }}</a> {%- unless forloop.last -%} , {% endunless -%}
                                                {%- endfor -%} 
                                            </div>                                      
                                        </div>
                                    </div>
                                </article>
                            {%- endfor -%}
                        </div>
                        {%- if paginate.pages > 1 -%}
                            <div class="t4s-blog-footer t4s-col-item t4s-has-btn-{{ use_pagination }} {{ se_stts.btn_pos }}">
                                {%- if use_pagination == 'default' -%}
                                    {%- render 'pagination', paginate: paginate, anchor: '' -%}
                                {%- elsif paginate.next.is_link -%}
                                    <div data-wrap-lm class="t4s-pagination-wrapper t4s-w-100" timeline hdt-reveal="slide-in">
                                        {%- if use_bar_lm -%}
                                            <div data-wrap-lm-bar class="t4s-lm-bar t4s-btn-color-{{ se_stts.btn_cl }}">
                                                {%- assign current_pr_size = blog.articles.size | plus: paginate.current_offset -%}
                                                <span class="t4s-lm-bar--txt">{{ 'blogs.pagination.bar_with_count_html' | t: current_count: current_pr_size, total_count: results_count }}</span>
                                                <div class="t4s-lm-bar--progress t4s-pr t4s-oh"><span class="t4s-lm-bar--current t4s-pa t4s-l-0 t4s-r-0 t4s-t-0 t4s-b-0" style="width: {{ current_pr_size | times: 100.0 | divided_by: results_count }}%"></span></div>
                                            </div>
                                        {%- endif -%}
                                        <a data-load-more {% if use_pagination == 'infinite' %}data-load-onscroll{% endif %} href="{{ paginate.next.url }}" class="t4s-pr t4s-btn-loading__svg t4s-btn t4s-btn-base t4s-btn-style-{{ se_stts.button_style }} t4s-btn-icon-{{ se_stts.btn_icon }} t4s-btn-size-{{ se_stts.btn_size }} t4s-btn-color-{{ se_stts.btn_cl }} {% if se_stts.button_style == 'default' or se_stts.button_style == 'outline' %}t4s-btn-effect-{{ se_stts.button_effect }}{% endif %}"><span class="t4s-btn-atc_text">{{ 'blogs.pagination.load_more' | t }}</span> 
                                            {%- if se_stts.btn_icon -%}<svg class="t4s-btn-icon" viewBox="0 0 32 32"><path d="M 15 4 L 15 24.0625 L 8.21875 17.28125 L 6.78125 18.71875 L 15.28125 27.21875 L 16 27.90625 L 16.71875 27.21875 L 25.21875 18.71875 L 23.78125 17.28125 L 17 24.0625 L 17 4 Z"/></svg>{%- endif -%} 
                                            <div class="t4s-loading__spinner t4s-dn">
                                                <svg  width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
                                            </div> 
                                        </a>
                                    </div>
                                {%- endif -%}
                            </div>
                        {%- endif -%}  
                    </div>
                    <aside data-sidebar-content class="t4s-col-item t4s-col-12 t4s-col-lg-3 t4s-sidebar t4s-dn"><div class="t4s-loading--bg"></div></aside>
                </div>
        {{- html_layout[1] -}}
    </div>
{%- endpaginate -%}
<style>
    .t4s-prev-head { margin:40px 0px;}
    .t4s-main-portfolio .t4s-blog-tags {
        margin-bottom: 25px;
    }
    .t4s-main-portfolio .t4s-tags-ul{padding-left: 0px;}
    .t4s-main-portfolio .t4s-tags-ul li a {
        height: 40px;
        line-height: 40px;
        border: 1px solid transparent;
        padding: 0 20px;
        border-radius: var(--btn-radius);
        font-weight: 500;
        color: var(--text-color);
    }
    .t4s-main-portfolio .t4s-tags-ul li a:focus, .t4s-tags-ul li a:hover{
        color: var(--secondary-color);
    }
    .t4s-main-portfolio .t4s-tags-ul li a.is--selected {
        color: var(--secondary-color);
        border-color: var(--secondary-color);
    }
    .t4s-blog-count-false .t4s-blog-count{display:none;}
    .t4s-portfolio-thumb::before {
        content: '';
        position: absolute;
        background: rgba(246,246,246,.9);
        left: 0;
        top: 50%;
        width: 100%;
        height: 0;
        opacity: 0; 
        transition: all .3s;
        z-index: 1;
    }
    .t4s-portfolio-inner:hover .t4s-portfolio-thumb::before{
        top: 0;
        height: 100%;
        opacity: 1;
    }
    .t4s-portfolio-info {
        top: 50%;
        left: 50%;
        opacity: 0;
        padding: 0 30px;
        -webkit-transform: translate(-50%,-120%);
        -ms-transform: translate(-50%,-120%);
        -o-transform: translate(-50%,-120%);
        transform: translate(-50%,-120%);
        transition: .3s;
        z-index: 2;
    }
    .t4s-portfolio-inner:hover .t4s-portfolio-info{
        opacity: 1;
        -webkit-transform: translate(-50%,-50%);
        -ms-transform: translate(-50%,-50%);
        -o-transform: translate(-50%,-50%);
        transform: translate(-50%,-50%);
    }
    .t4s-portfolio-inner .t4s-post-title{font-size: 14px;text-transform: uppercase;}
</style>
{%- schema -%}
{
    "name":"Portfolio",
    "tag":"section",
    "class":"t4s-section t4s-section-main t4s_tp_istope t4s-main-portfolio",
    "settings":[
        {
            "type":"header",
            "content":"1.General options"
        },
        {
            "type": "select",
            "id": "layout_des",
            "label": "Layout design",
            "default": "2",
            "options": [
                {
                    "value": "1",
                    "label": "Grid"
                },
                {
                    "value": "2",
                    "label": "Masonry"
                }
            ]
        },
        {
            "type": "range",
            "max":50,
            "min":1,
            "step":1,
            "id": "limit",
            "label": "Maximum articles to show",
            "default": 6
        },
        {
            "type":"header",
            "content":"+ Content options"
        },
        {
            "type": "select",
            "id": "col_dk",
            "label": "Items per row",
            "default": "3",
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                },
                {
                    "value": "3",
                    "label": "3"
                },
                {
                    "value": "4",
                    "label": "4"
                }
            ]
        },
        {
            "type": "select",
            "id": "col_tb",
            "label": "Items per row (Tablet)",
            "default": "2",
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                },
                {
                    "value": "3",
                    "label": "3"
                },
                {
                    "value": "4",
                    "label": "4"
                }
            ]
        },
        {
            "type": "select",
            "id": "col_mb",
            "label": "Items per row (Mobile)",
            "default": "1",
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                }
            ]
        }, 
        {
            "type": "select",
            "id": "space_h_item",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                },
                {
                    "value": "40",
                    "label": "40px"
                }
            ],
            "label": "Space horizontal between items",
            "default": "30"
        },
        {
            "type": "select",
            "id": "space_v_item",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                },
                {
                    "value": "40",
                    "label": "40px"
                }
            ],
            "label": "Space vertical vertical items",
            "default": "30"
        },
        {
            "type": "select",
            "id": "space_h_item_mb",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                },
                {
                    "value": "40",
                    "label": "40px"
                }
            ],
            "label": "Space horizontal between items (Mobile)",
            "default": "30"
        },
        {
            "type": "select",
            "id": "space_v_item_mb",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                },
                {
                    "value": "40",
                    "label": "40px"
                }
            ],
            "label": "Space vertical vertical items (Mobile)",
            "default": "30"
        },
        {
            "type": "header",
            "content": "Pagination options"
        },
        {
            "type": "select",
            "id": "use_pagination",
            "label": "Pagination",
            "default": "default",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "load-more",
                    "label": "'Load more' button"
                },
                {
                    "value": "infinite",
                    "label": "Infinit scrolling"
                }
            ]
        },
        {
            "type": "checkbox",
            "id": "use_bar_lm",
            "label": "Use progress bar",
            "info": "Only active when you use 'Load more'",
            "default": true
        },
        {
            "type": "paragraph",
            "content": "Page-loading speed is everything for good user experience. Multiple researches have shown that slow load times result in people leaving your site or delete your app which result in low conversion rates. And that’s bad news for those who use an infinite-scrolling. The more users scroll down a page, more content has to load on the same page. As a result, the page performance will increasingly slow down."
        },
        {
            "type": "paragraph",
            "content": "Another problem is limited resources of the user’s device. On many infinite scrolling sites, especially those with many images, devices with limited resources (such as mobile devices or tablets with dated hardware) can start slowing down because of the sheer number of assets it has loaded."
        },
        {
            "type": "paragraph",
            "content": "Therefore, we recommend that you only use 'Load more', 'Infinite scrolling' for when your collection is less than or equal to 400 posts"
        },
        {
            "type":"checkbox",
            "id":"btn_icon",
            "label":"Enable button icon",
            "default":false
        },
        {
            "type": "select",
            "id": "button_style",
            "label": "Button style",
            "options": [
                {
                    "label": "Default",
                    "value": "default"
                },
                {
                    "label": "Outline",
                    "value": "outline"
                },
                {
                    "label": "Bordered bottom",
                    "value": "bordered"
                },
                {
                    "label": "Link",
                    "value": "link"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_size",
            "label": "Button size",
            "default":"medium",
            "options": [
                {
                    "label": "Extra small",
                    "value": "small"
                },
                {
                    "label": "Small",
                    "value": "extra-small"
                },
                {
                    "label": "Medium",
                    "value": "medium"
                },
                {
                    "label": "Large",
                    "value": "extra-medium"
                },
                {
                    "label": "Extra large",
                    "value": "large"
                },
                {
                    "label": "Extra extra large",
                    "value": "extra-large"
                }
            ]
        }, 
        {
            "type": "select",
            "id": "btn_cl",
            "label": "Button color",
            "default": "dark",
            "options": [
                {
                    "value": "light",
                    "label": "Light"
                },
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "primary",
                    "label": "Primary"
                },
                {
                    "value": "custom1",
                    "label": "Custom color 1"
                },
                {
                    "value": "custom2",
                    "label": "Custom color 2"
                }
            ]
        },  
        {
            "type":"select",
            "id":"button_effect",
            "label":"Button hover effect",
            "default":"default",
            "info":"Only working button style default, outline",
            "options":[
                {
                    "label":"Default",
                    "value":"default"
                },
                {
                    "label":"Fade",
                    "value":"fade"
                },
                {
                  "label":"Rectangle out",
                  "value":"rectangle-out"
                },
                {
                    "label":"Sweep to right",
                    "value":"sweep-to-right"
                },
                {
                    "label":"Sweep to left",
                    "value":"sweep-to-left"
                },
                {
                    "label":"Sweep to bottom",
                    "value":"sweep-to-bottom"
                },
                {
                    "label":"Sweep to top",
                    "value":"sweep-to-top"
                },
                {
                    "label":"Shutter out horizontal",
                    "value":"shutter-out-horizontal"
                },
                {
                    "label":"Outline",
                    "value":"outline"
                },
                {
                    "label":"Shadow",
                    "value":"shadow"
                }
            ]
        },  
        {
            "type": "select",
            "id": "btn_pos",
            "label": "Button position",
            "default": "t4s-text-center",
            "options": [
                {
                    "value": "t4s-text-start",
                    "label": "Left"
                },
                {
                    "value": "t4s-text-center",
                    "label": "Center"
                },
                {
                    "value": "t4s-text-end",
                    "label": "Right"
                }
            ]
        },
        {
            "type": "header",
            "content": "+Options for image"
        },
        {
            "type": "select",
            "id": "image_ratio",
            "label": "Aspect ratio",
            "default": "ratioadapt",
            "info": "Aspect ratio custom will settings in general panel.",
            "options": [
                {
                    "group": "Auto",
                    "value": "ratioadapt",
                    "label": "Adapt to image"
                },
                {
                    "group": "Landscape",
                    "value": "ratio2_1",
                    "label": "2:1"
                },
                {
                    "group": "Landscape",
                    "value": "ratio16_9",
                    "label": "16:9"
                },
                {
                    "group": "Landscape",
                    "value": "ratio8_5",
                    "label": "8:5"
                },
                {
                    "group": "Landscape",
                    "value": "ratio3_2",
                    "label": "3:2"
                },
                {
                    "group": "Landscape",
                    "value": "ratio4_3",
                    "label": "4:3"
                },
                {
                    "group": "Landscape",
                    "value": "rationt",
                    "label": "Ratio ASOS"
                },
                {
                    "group": "Squared",
                    "value": "ratio1_1",
                    "label": "1:1"
                },
                {
                    "group": "Portrait",
                    "value": "ratio2_3",
                    "label": "2:3"
                },
                {
                    "group": "Portrait",
                    "value": "ratio1_2",
                    "label": "1:2"
                },
                {
                    "group": "Custom",
                    "value": "ratiocus1",
                    "label": "Ratio custom 1"
                },
                {
                    "group": "Custom",
                    "value": "ratiocus2",
                    "label": "Ratio custom 2"
                },
                {
                    "group": "Custom",
                    "value": "ratiocus3",
                    "label": "Ratio custom 3"
                },
                {
                    "group": "Custom",
                    "value": "ratiocus4",
                    "label": "Ratio custom 4"
                }
            ]
        },
        {
            "type": "select",
            "id": "image_position",
            "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'.",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "1",
                    "label": "Left top"
                },
                {
                    "value": "2",
                    "label": "Left center"
                },
                {
                    "value": "3",
                    "label": "Left bottom"
                },
                {
                    "value": "4",
                    "label": "Right top"
                },
                {
                    "value": "5",
                    "label": "Right center"
                },
                {
                    "value": "6",
                    "label": "Right bottom"
                },
                {
                    "value": "7",
                    "label": "Center top"
                },
                {
                    "value": "8",
                    "label": "Center center"
                },
                {
                    "value": "9",
                    "label": "Center bottom"
                }
            ],
            "label": "Image position",
            "default": "8"
        },
        {
            "type": "select",
            "id": "image_size",
            "label": "Image size",
            "default": "cover",
            "info": "This settings apply only if the image ratio is not set to 'Adapt to image'.",
            "options": [
                {
                    "value": "cover",
                    "label": "Full"
                },
                {
                    "value": "contain",
                    "label": "Auto"
                }
            ]
        },
        {
            "type":"header",
            "content":"+ FILTER BY TAG"
        },
        {
            "type": "radio",
            "id": "source",
            "default": "0",
            "label": "Disable\/Show all \/ Manually block",
            "options": [
                {
                    "value": "0",
                    "label": "Disable"
                },
                {
                    "value": "1",
                    "label": "Show all"
                },
                {
                    "value": "2",
                    "label": "Manually block"
                }
            ]
        },
        {
            "type": "checkbox",
            "id": "show_count",
            "label": "Show count",
            "info": "Count only show all. Not working manually block",
            "default": true
        },
        {
            "type": "header",
            "content": "2.Design options"
        },
        {
            "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
            "options": [
                { "value": "t4s-se-container", "label": "Container"},
                { "value": "t4s-container-wrap", "label": "Wrapped container"},
                { "value": "t4s-container-fluid", "label": "Full width"}
            ]
        },
        {
            "type": "color",
            "id": "cl_bg",
            "label": "Background"
        },
        {
            "type": "color_background",
            "id": "cl_bg_gradient",
            "label": "Background gradient"
        },
        {
            "type": "image_picker",
            "id": "image_bg",
            "label": "Background image"
        },
        {
            "type": "text",
            "id": "mg",
            "label": "Margin",
            "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
            "default": ",,50px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd",
            "label": "Padding",
            "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
            "placeholder": "50px,,50px,"
        },
        {
          "type": "header",
          "content": "+ Design Tablet Options"
        },
        {
          "type": "text",
          "id": "mg_tb",
          "label": "Margin",
          "placeholder": ",,50px,"
        },
        {
          "type": "text",
          "id": "pd_tb",
          "label": "Padding",
          "placeholder": ",,50px,"
        },
        {
            "type": "header",
            "content": "+ Design mobile options"
        },
        {
            "type": "text",
            "id": "mg_mb",
            "label": "Margin",
            "default": ",,30px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_mb",
            "label": "Padding",
            "placeholder": ",,50px,"
        }
    ],
    "blocks": [
        {
            "type": "tag",
            "name": "Tag",
            "limit": 10,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Tag name"
                }
            ]
        }
    ]
}
{% endschema %}