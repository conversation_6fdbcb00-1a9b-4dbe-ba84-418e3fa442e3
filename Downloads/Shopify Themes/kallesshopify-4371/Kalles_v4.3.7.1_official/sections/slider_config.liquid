{%- liquid 
    assign se_stts = section.settings
    if se_stts.btn_owl == "outline"
        assign arrow_icon = 1
    else
        assign arrow_icon = 2
    endif
    assign se_blocks = section.blocks
    assign isShowHTMl = false
    if template.suffix == 'config' and request.design_mode
        assign isShowHTMl = true
    endif 
 -%}
{%- if isShowHTMl -%}
    {{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
    {{ 'slider-settings.css' | asset_url | stylesheet_tag }}
    <div class="t4s-container" style="margin-top:100px;margin-bottom: 100px;">
        <h3 class="t4s-text-center" style="margin-bottom: 30px;">Slider button, dot config</h3>
        {%- for block in se_blocks -%}
            {%- assign index = forloop.index -%}
            <div class="t4s-flicky-slider t4s_ratio16_9 t4s_position_8 t4s_cover flickityt4s t4s-row t4s-row-cols-lg-3 t4s-row-cols-md-2 t4s-row-cols-1 t4s-gx-30 t4s-gy-30 t4s-slider-btn-style-{{ se_stts.btn_owl }} t4s-slider-btn-{{ se_stts.btn_shape }} t4s-slider-btn-{{ se_stts.btn_size }} t4s-slider-btn-cl-custom{{ index }} t4s-slider-btn-vi-{{ se_stts.btn_vi }} t4s-dots-style-{{ se_stts.dot_owl }} t4s-dots-cl-custom{{ index }} t4s-dots-round-{{ se_stts.dots_round }}" data-flickityt4s-js='{"setPrevNextButtons":true,"arrowIcon":"{{ arrow_icon }}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": true,"prevNextButtons": true,"percentPosition": 1,"pageDots": true, "autoPlay" : 5000, "pauseAutoPlayOnHover" : true }'  style="--space-dots: {{ se_stts.dots_space }}px;--flickity-btn-pos: 30px;--flickity-btn-pos-mb: 30px;margin-bottom: 50px;">
                {% for i in (1..5) %}
                    <div class="t4s-col-item">
                        <div class="t4s_ratio" data-cacl-slide>
                            {{ 'image' | placeholder_svg_tag: 't4s-placeholder-svg t4s-obj-eff' }}
                        </div>
                    </div>
                {%- endfor -%}
            </div>
        {%- endfor -%} 
    </div>
{%- endif -%}
{%- for block in se_blocks -%}
    {%- liquid 
        assign bk_stts = block.settings 
        assign index = forloop.index 
        assign btn_color_primary = bk_stts.btn_color_primary
        assign btn_color_second = bk_stts.btn_color_second
        assign btn_color_primary_hover =  bk_stts.btn_color_primary_hover
        assign btn_color_second_hover =  bk_stts.btn_color_second_hover
   -%}
    {%- style -%}
        .t4s-flicky-slider.t4s-slider-btn-cl-custom{{ index }}{
            --btn-color            : {{ btn_color_second }};
            --btn-background       : {{ btn_color_primary }};
            --btn-border           : {{ btn_color_primary }};
            --btn-color-hover      : {{ btn_color_second_hover }};
            --btn-background-hover : {{ btn_color_primary_hover }};
            --btn-border-hover     :{{ btn_color_primary_hover }}; 
        }
        .t4s-flicky-slider.t4s-slider-btn-style-outline.t4s-slider-btn-cl-custom{{ index }}{
            --btn-color            : {{ btn_color_primary }};
            --btn-border           : {{ btn_color_primary }};
            --btn-color-hover      : {{ btn_color_second_hover }};
            --btn-background-hover : {{ btn_color_primary_hover }};
        }
        .t4s-slider-btn-pos-middle-border.t4s-slider-btn-style-outline.t4s-slider-btn-cl-custom{{ index }}{
            --btn-color            : {{ btn_color_primary }};
            --btn-background       : {{ btn_color_second }};
            --btn-color-hover      : {{ btn_color_second_hover }};
            --btn-background-hover : {{ btn_color_primary_hover }};
        }
        .t4s-flicky-slider.t4s-slider-btn-style-simple.t4s-slider-btn-cl-custom{{ index }}{
            --btn-color           : {{ btn_color_primary }};
            --btn-border          : {{ btn_color_primary }};
            --btn-color-hover     : {{ btn_color_primary_hover }};
            --btn-border-hover    : {{ btn_color_primary_hover }};
        }
        .t4s-flicky-slider.t4s-dots-cl-custom{{ index }}{
            --dots-background  : {{ btn_color_primary }};
            --dots-background2 : {{ btn_color_second }};
        }
        .t4s-dots-style-number.t4s-dots-cl-custom{{ index }} {
            --dots-cl    : {{ btn_color_second }};
            --bg-dots-cl : {{ btn_color_primary }};
        }
    {%- endstyle -%}
{%- endfor -%}
{%- schema -%}
{
    "name": "Carousel Color Config",
    "tag": "div",
    "class": "t4s-section t4s-section-config t4s_tp_flickity t4s-section-admn-fixed",
    "settings": [
        {
            "type": "paragraph",
            "content": "Setting section only show here to preview (Not setting for all button or dots of site)."
        },
        {
            "type": "header",
            "content": "+ Prev next button"
        },
        {
            "type": "select",
            "id": "btn_vi",
            "label": "Visible",
            "default": "hover",
            "options": [
                {
                    "value": "always",
                    "label": "Always"
                },
                {
                    "value": "hover",
                    "label": "Only hover"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_owl",
            "label": "Button style",
            "default": "default",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "outline",
                    "label": "Outline"
                },
                {
                    "value": "simple",
                    "label": "Simple"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_shape",
            "label": "Button shape",
            "info": "Not working with button style 'Simple'",
            "default": "none",
            "options": [
                {
                    "value": "none",
                    "label": "Default"
                },
                {
                    "value": "round",
                    "label": "Round"
                },
                {
                    "value": "rotate",
                    "label": "Rotate"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_size",
            "label": "Buttons size",
            "default": "small",
            "options": [
                {
                    "value": "small",
                    "label": "Small"
                },
                {
                    "value": "medium",
                    "label": "Medium"
                },
                {
                    "value": "large",
                    "label": "Large"
                }
            ]
        },
        {
            "type": "header",
            "content": "+ Dots"
        },
        {
            "type": "select",
            "id": "dot_owl",
            "label": "Dots style",
            "default": "default",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "outline",
                    "label": "Outline"
                },
                {
                    "value": "elessi",
                    "label": "Elessi"
                }
            ]
        },
        {
            "type": "checkbox",
            "id": "dots_round",
            "label": "Enable dots round",
            "default": true
        },
        {
            "type": "range",
            "id": "dots_space",
            "min": 2,
            "max": 20,
            "step": 1,
            "label": "Dot between horizontal",
            "unit": "px",
            "default": 10
        }
    ],
    "blocks":[
        {
            "type": "custom1",
            "name": "Custom color 1",
            "limit": 1,
            "settings":[
                {
                    "type":"color",
                    "id":"btn_color_primary",
                    "label":"Primary color",
                    "default":"#ffb100"
                },
                {
                    "type":"color",
                    "id":"btn_color_second",
                    "label":"Secondary color",
                    "default":"#fff",
                    "info":"Only working button style default"
                },
                {
                    "type":"color",
                    "id":"btn_color_primary_hover",
                    "label":"Primary color hover",
                    "default":"#ff4e00"
                },
                {
                    "type":"color",
                    "id":"btn_color_second_hover",
                    "label":"Secondary color hover",
                    "default":"#fff",
                    "info":"Only working button style default, outline"
                }
            ]
        },
        {
            "type": "custom2",
            "name": "Custom color 2",
            "limit": 1,
            "settings":[
                {
                    "type":"color",
                    "id":"btn_color_primary",
                    "label":"Primary color",
                    "default":"#109533"
                },
                {
                    "type":"color",
                    "id":"btn_color_second",
                    "label":"Secondary color",
                    "default":"#fff",
                    "info":"Only working button style default"
                },
                {
                    "type":"color",
                    "id":"btn_color_primary_hover",
                    "label":"Primary color hover",
                    "default":"#ff4e00"
                },
                {
                    "type":"color",
                    "id":"btn_color_second_hover",
                    "label":"Secondary color hover",
                    "default":"#fff",
                    "info":"Only working button style default, outline"
                }
            ]
        }

    ]
}
{% endschema %}