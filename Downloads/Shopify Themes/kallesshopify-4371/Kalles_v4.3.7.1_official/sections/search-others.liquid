{%- liquid
   assign results_count = search.results_count
   assign limit = 5 
 -%}

{%- if results_count > 0 -%}
   <style>
      .t4s-result-title {
          background-color: rgba(var(--text-color-rgb),.1);
          padding: 20px;
          border-top: 1px solid rgba(var(--border-color-rgb),.8);
          border-bottom: 1px solid rgba(var(--border-color-rgb),.8);
          margin: 20px -20px;
          font-weight: 600;
          font-size: 14px;
          color: var(--secondary-color);
      }
      .t4s-result__item:not(:last-child) {
          margin-bottom: 10px;
          padding-bottom: 10px;
      }
      .t4s-result-image {
          flex: 0 0 auto;
          margin-right: 15px;
          min-width: 95px;
          max-width: 95px;
          max-height: 120px;
      }
      .t4s-result__item-title {
         font-size: 14px;
         font-weight: 500;
         line-height: 1.25;
         color: var(--secondary-color);
      }
      .t4s-result__item-title:hover {
          color: var(--accent-color);
      }
      .t4s-result-svg {
          padding-bottom: 100%;
          background-color: rgba(var(--text-color-rgb),.1);
      }
      .t4s-result-svg>svg {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 44px;
          height: 44px;
          stroke-width: 1px;
          color: var(--secondary-color);
      }
      .rtl_true .t4s-result-image {
         margin-right: 0;
         margin-left: 15px;
      }
   </style>
   <div class="t4s-result-title">{{ 'search.general.results_others' | t }}</div>
   {%- for item in search.results limit: limit %}{% assign image = item.image -%}
      <div class="t4s-result__item t4s-d-flex">
         <div class="t4s-result-image"><a class="t4s-d-block t4s-pr t4s-oh{% if image %} t4s_ratio t4s-bg-11{% endif %}" href="{{ item.url }}"{% if image %} style="background: url({{ image | image_url: width: 1 }});--aspect-ratioapt: {{ image.aspect_ratio | default: 1 }}"{% endif %}>
         {%- if image -%}
            <img class="lazyloadt4s" data-src="{{ image | image_url: width: 1 }}" data-widths="[95,170]" data-optimumx="2" data-sizes="auto" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
         {%- else -%}
            <div class="t4s-result-svg t4s-pr t4s-oh t4s-w-100"><svg class="t4s-pa" viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg></div>
         {%- endif -%}
         </a></div>
         <div class="t4s-result-infos">
            {{ item.title | link_to: item.url, class: 't4s-result__item-title t4s-d-block' }}
         </div>
      </div>
   {%- endfor -%}
{%- endif -%}