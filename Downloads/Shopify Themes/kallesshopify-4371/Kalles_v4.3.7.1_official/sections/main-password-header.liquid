<style>
button.t4s-btn-sidebar {
    background-color: transparent;
    color: var(--sencondary-color);
    justify-self: flex-end;
    grid-column: 3;
} 
button.t4s-btn-sidebar svg {
    width: 14px;
}
span.t4s-btn-sidebar-icon {
    margin-right: 5px;
}
.t4s-drawer__main.t4s-current-scrollbar {
    padding: 20px;
}
.password-header .t4s-container {
    padding: 2rem 1.5rem 2.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    margin: 0 auto;
    text-align: center; 
    row-gap: 20px;
}
@media only screen and (min-width: 750px) {
  .password-header .t4s-container {
      display: grid;
      gap: 3rem;
      grid-template-columns: 1fr 1.5fr 1fr;
      padding: 2rem 5rem 2.5rem;
      text-align: left;
  }
}
.password-header h1 {
      font-size: 25px;
}
button.t4s-btn-sidebar {
    border-width: 1px;
    border-style: solid;
}

span.t4s-btn-sidebar-text:hover { 
    text-decoration-thickness: 0.2rem;
}
.t4s-close-overlay {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100vw;
    z-index: 200;
    visibility: hidden;
    pointer-events: none;
    opacity: 0;
    background: rgba(0,0,0,.7);
    transition: opacity .3s ease-in-out,visibility .3s ease-in-out;
}
.t4s-close-overlay.is--visible {
    pointer-events: auto;
    opacity: .5;
    visibility: visible;
}
.t4s-iconsvg-close {
    width: 16px;
    height: 16px;
    stroke-width: 1.5px;
    display: inline-block;
    pointer-events: none;
}
p.password-content {
    margin-top: 20px;
}
p.password-content a{
    color: #000;
}
p.password-content a:hover{
  color:var(--accent-color);
}
</style>
<div class="password-header">
  <div class="t4s-container">
    {%- if section.settings.logo -%}
      <img
        src="{{ section.settings.logo | image_url: width: 500, height: 500 }}"
        class="password-logo"
        alt="{{ section.settings.logo.alt | default: shop.name | escape }}"
        style="max-width: {{ section.settings.logo_max_width }}px"
        width="{{ section.settings.logo_max_width }}"
        height="{{ section.settings.logo_max_width | divided_by: section.settings.logo.aspect_ratio }}"
        loading="lazy"
      >
    {%- else -%}
      <h1 class="h2">{{ shop.name }}</h1>
    {%- endif -%}

    {%- if shop.password_message != blank -%}
      <div class="password-content">
        {{ shop.password_message }}
      </div>
    {%- endif -%}

    <button data-sidebar-drawer class="t4s-btn-sidebar"><span class="t4s-btn-sidebar-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Pro 6.1.1 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --><path d="M240 392C240 400.8 232.8 408 224 408C215.2 408 208 400.8 208 392V312C208 303.2 215.2 296 224 296C232.8 296 240 303.2 240 312V392zM224 0C294.7 0 352 57.31 352 128V192H368C412.2 192 448 227.8 448 272V432C448 476.2 412.2 512 368 512H80C35.82 512 0 476.2 0 432V272C0 227.8 35.82 192 80 192H96V128C96 57.31 153.3 0 224 0zM224 32C170.1 32 128 74.98 128 128V192H320V128C320 74.98 277 32 224 32zM80 224C53.49 224 32 245.5 32 272V432C32 458.5 53.49 480 80 480H368C394.5 480 416 458.5 416 432V272C416 245.5 394.5 224 368 224H80z"/></svg></span> <span class="t4s-btn-sidebar-text">{{ 'password_page.password_link' | t }}</span></button>

  </div>
</div>

 <link rel="stylesheet" href="{{ 'drawer.min.css' | asset_url }}" media="all">
 <div id="t4s-drawer-sidebar" class="t4s-drawer t4s-drawer__right" aria-hidden="true">
    <div class="t4s-drawer__header"><span>{{ 'password_page.drawer_heading' | t }}</span><button class="t4s-drawer__close" data-drawer-close aria-label="{{ 'general.sidebar.close' | t }}"><svg class="t4s-iconsvg-close" role="presentation" viewBox="0 0 16 14"><path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path></svg></button></div>
    <div class="t4s-drawer__content">
       <div class="t4s-drawer__main t4s-current-scrollbar">
              {% form 'storefront_password', class: 'form-single-field nt_mini_cart' %}
         
                    {%- assign formId = 'PasswordLoginForm' -%}
                    <p class="form-row{% if form.errors %}input-group--error{% endif %}">
                      <label for="Password">{{ 'password_page.login_form_password_label' | t }} <span class="required">*</span></label>
                      <input required="required" type="password" name="password"
                             id="Password"
                             class="input-group__field input--content-color t4s-w-100 {% if form.errors %}input--error{% endif %}"
                             placeholder="{{ 'password_page.login_form_password_placeholder' | t }}"
                             data-login-field
                             autocomplete="off"
                             aria-labelledby="PasswordLabel {% if form.errors %} {{ formId }}-password-error{% endif %}"
                             {% if form.errors %}
                             data-error="password" 
                             aria-invalid="true"
                             {%- endif -%}
                             >
                    </p>
                    <button type="submit" name="commit" class="button button_primary t4s-w-100 t4s-tu js_add_ld"> {{ 'password_page.login_form_submit' | t }} </button>
                    {%- if form.errors -%}
                    <span id="{{ formId }}-password-error" class="input-error-message">
                      <i class="facl facl-attention"></i> {{ form.errors.messages['form'] }}
                    </span>
                    {%- endif -%}
                    {% endform %}
                    <p class="password-content">{{ 'password_page.admin_link_html' | t }}</p>
       </div>
       <div class="t4s-drawer__footer"></div>
    </div>
 </div>

<script>
  function myFunctionPass() {
    var drawerSidebar = document.querySelector('#t4s-drawer-sidebar'),
    overlay = document.querySelector('.t4s-close-overlay');

    document.querySelector('[data-sidebar-drawer]').addEventListener("click", function(){
     drawerSidebar.setAttribute("aria-hidden", "false");
     overlay.classList.add('is--visible');
    });

    document.querySelector('.t4s-drawer__close').addEventListener("click", function(){
     drawerSidebar.setAttribute("aria-hidden", "true");
     overlay.classList.remove('is--visible');
    });

    overlay.addEventListener("click", function(){
     drawerSidebar.setAttribute("aria-hidden", "true");
     overlay.classList.remove('is--visible');
    });

    document.addEventListener('keyup', event => {
      if (event.keyCode === 27) {
         drawerSidebar.setAttribute("aria-hidden", "true");
         overlay.classList.remove('is--visible');
      }
    });
  };
  myFunctionPass();

  document.addEventListener('shopify:section:select', function(event) {
    myFunctionPass();
  });

</script>

{%- schema -%}
{
  "name": "Password header",
  "settings": [
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Password header"
    },
    {
      "type": "range",
      "id": "logo_max_width",
      "min": 50,
      "max": 250,
      "step": 10,
      "default": 100,
      "unit": "px",
      "label": "Custom logo width"
    }
  ]
}
{% endschema %}