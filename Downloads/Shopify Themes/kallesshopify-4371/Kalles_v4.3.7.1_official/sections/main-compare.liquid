{%- comment -%}
{%- if template == 'search.wishlist' %}{{ 'wishlist_page.meta' | t }}{%- elsif template == 'search.compare' %}{{ 'compare_page.meta' | t }}{%- endif -%}
{%- endcomment -%}
{{ 'section.css' | asset_url | stylesheet_tag }}
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign image_ratio = se_stts.image_ratio
  if image_ratio == "ratioadapt"
    assign imgatt = ''
   else 
    assign imgatt = 'data-'
  endif

  assign limit           = 6
  assign results_count   = search.results_count
  assign root_url        = routes.root_url
  assign inc_price       = settings.price_format
  assign enable_rating   = settings.enable_rating
  assign app_review      = settings.app_review
  assign btn_blocks      = section.blocks | where: "type", 'btn'
  assign on_sale_txt     = 'products.badge.on_sale' | t
  assign save_js         = 'products.badge.save_amoun_html' | t: saved_amount: 'saved_amount'
  assign remove_txt      = 'compare_page.remove' | t
  assign in_stock_txt    = 'compare_page.in_stock' | t
  assign outofstock_txt  = 'compare_page.outofstock' | t 
  assign placeholder_img = settings.placeholder_img
  assign list_id_prs = ''
  if request.design_mode
    assign arr_results   = se_stts.product_list
    assign results_count = arr_results.count
  endif  
 -%}

{{ 'main-compare.css' | asset_url | stylesheet_tag }}
{%- paginate search.results by limit -%}
{%- liquid
  unless request.design_mode
    assign arr_results = search.results
  endunless -%}
<div data-ntajax-container data-ntajax-options='{"id":"{{ sid }}","type":"{{ typeAjax }}","isProduct":true,"view":""}' class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }} {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s {% endif %}"  {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} {% render 'section_style', se_stts: se_stts %} >
  {{- html_layout[0] -}}
  {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner {% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s {% endif %} "  {% if stt_image_bg != blank %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} > {%- endif -%}

{%- if results_count > 0 %}{% assign list_id_prs = arr_results | map: 'id' | join: ',id: ' | prepend: 'id: ' -%}
   <div class="t4s_compare_page">
     <div class="t4s_compare_table t4s-text-{{ se_stts.content_align }} t4s_{{ image_ratio }}  t4s_position_{{ se_stts.image_position }} t4s_{{ se_stts.image_size }}">

        {%- for block in section.blocks -%}

           {%- if block.type == 'btn' %}{% continue %}{% endif -%}

           {%- assign block_title = block.settings.title | escape -%}
           {%- case block.type -%}
              {%- when 'basic' -%}
                  <div class="t4s_compare_row t4s_compare_basic">
                    <div class="t4s_compare_col t4s_compare_field t4s_empty_field"></div>
                    {%- for product in arr_results -%}

                    {%- liquid
                      assign pr_id = product.id
                      assign on_sale = false
                      if product.compare_at_price_min > product.price_min
                        assign on_sale = true 
                      endif 
                      assign pr_url = product.url
                      assign hd_up = 'group-' | append: pr_id
                      assign nav_up = linklists[hd_up].links | where: 'type', 'product_link'
                      assign isGrouped = false
                      assign nav_up_size = nav_up.size 
                      if nav_up.size > 0 
                         assign isGrouped = true
                      endif 

                      assign isDefault = product.has_only_default_variant
                      assign pr_variants = product.variants
                      assign current_variant = pr_variants.first
                      assign isPreoder = false

                      if product.tags contains 'isPreoder' or current_variant.inventory_policy == 'continue' and current_variant.inventory_management == 'shopify' and current_variant.inventory_quantity <= 0
                         assign isPreoder = true
                      endif

                      if current_variant.inventory_management == 'shopify'
                         assign max_qty = current_variant.inventory_quantity | default: 9999
                      else
                         assign max_qty = 9999
                      endif
                      if current_variant.quantity_rule.max and max_qty > current_variant.quantity_rule.max
                         assign max_qty = current_variant.quantity_rule.max
                      endif

                      assign meta_theme = product.metafields.theme
                      assign cus_qty = meta_theme.cus_qty | default: 1
                      assign isExternal = false
                      assign external_title = meta_theme.external_title 
                      assign external_link = meta_theme.external_link
                      if external_title != blank or external_link != blank 
                         assign isExternal = true 
                      endif
                      assign image = product.featured_media | default: placeholder_img -%}

                    <div class="t4s_compare_col t4s_compare_value t4s_compare_id_{{ pr_id }}" data-title>
                      <div class="t4s_compare_basic_content">
                         <a href="{{ root_url }}" data-no-instant rel="nofollow" data-remove-compare class="t4s_compare_remove t4s-d-inline-block" data-id="{{ pr_id }}">{{ remove_txt }}</a>
                         {%- if image != blank %}<a class="t4s-product-image t4s-d-block t4s_ratio" href="{{ pr_url }}" style="--aspect-ratioapt:{{ image.aspect_ratio }};">
                            <img src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image | image_url: width: 1 }}" data-widths="[80, 160, 250, 320, 500, 640]" data-sizes="auto" class="t4s-w-100 lazyloadt4s" alt="{{ image.alt | escape }}">
                            <span class="lazyloadt4s-loader is-bg-img" style="background: url({{ image | image_url: width: 1 }});"></span>
                          </a>{% endif -%}
                         <a class="t4s_compare_product-title t4s-d-block" href="{{ pr_url }}">{{ product.title | escape }}</a>
                         <div class="t4s_compare_price t4s-d-block">
                             {%- if product.price_varies -%}
                            {{ product.price_min | money }}–{{ product.price_max | money }}
                            {%- elsif on_sale -%}<del>{{ product.compare_at_price | money }}</del> <ins>{{ product.price | money }}</ins>{%- if settings.label_sale_style == '1' -%}<span class="t4s_compare_onsale is--text">{{ on_sale_txt }}</span>{%- else -%}{%- assign save = product.compare_at_price | minus: product.price | times: 100.0 | divided_by: product.compare_at_price | round -%}<span class="t4s_compare_onsale is--percent">{{ save_js | replace: 'saved_amount', save }}</span>{% endif -%}
                            {%- else -%}
                            {{ product.price | money }}
                            {%- endif -%}
                         </div>
                         {%- if settings.enable_quickview or settings.enable_atc %}
                         <div class="t4s-compare-group-btns">
                           {%- if settings.enable_quickview %}<a data-id="{{ pr_id }}" href="{{ pr_url }}" data-tooltip rel="nofollow" class="t4s-pr-item-btn t4s-pr-quickview" data-action-quickview><span class="t4s-svg-pr-icon"><svg viewBox="0 0 24 24"><use xlink:href="#t4s-icon-qv"></use></svg></span><span class="t4s-text-pr">{{ 'products.product_card.quick_view' | t }}</span></a>{% endif %}
                           {%- if settings.enable_atc %}{%- render 'product-atc', pr_available: product. available, pr_url: pr_url, isDefault: isDefault, isPreoder: isPreoder, isExternal: isExternal, external_title: external_title, external_link: external_link, isGrouped: isGrouped, current_variant: current_variant, pid: pr_id, cus_qty: cus_qty -%}{% endif %}
                         </div>
                         {% endif %}
                      </div>
                    </div>

                    {%- endfor -%}
                  </div>

              {%- when 'availability' -%}
                  <div class="t4s_compare_row t4s_compare_availability">
                    <div class="t4s_compare_col t4s_compare_field">{{ block_title }}</div>
                    {%- for product in arr_results -%}
                    <div class="t4s_compare_col t4s_compare_value t4s_compare_id_{{ product.id }}" data-title="{{ block_title }}">{% if product.available %}<span class="t4s_compare_stock is--in-stock">{{ in_stock_txt }}</span>{% else %}<span class="t4s_compare_stock is--out-of-stock">{{ outofstock_txt }}</span>{% endif %}</div>
                    {%- endfor -%}
                  </div>

              {%- when 'vendor' -%}
                  <div class="t4s_compare_row t4s_compare_vendor">
                    <div class="t4s_compare_col t4s_compare_field">{{ block_title }}</div>
                    {%- for product in arr_results %}<div class="t4s_compare_col t4s_compare_value t4s_compare_id_{{ product.id }}" data-title="{{ block_title }}">{{ product.vendor | default: '-' | escape }}</div>{% endfor -%}
                  </div>

              {%- when 'rating' %}{% unless enable_rating %}{% continue %}{% endunless -%}
                  <div class="t4s_compare_row t4s_compare_rating">
                    <div class="t4s_compare_col t4s_compare_field">{{ block_title }}</div>
                    {%- for product in arr_results -%}
                    <div class="t4s_compare_col t4s_compare_value t4s_compare_id_{{ product.id }}" data-title="{{ block_title }}">
                      {%- render 'product-rating', product: product, app_review: app_review -%}
                    </div>
                    {%- endfor -%}
                  </div>

              {%- else -%}
                  <div class="t4s_compare_row t4s_compare_pr_variants t4s_compare_{{ block.settings.title | handle }}">
                    <div class="t4s_compare_col t4s_compare_field">{{ block_title }}</div>
                    {%- for product in arr_results -%}
                    <div class="t4s_compare_col t4s_compare_value t4s_compare_id_{{ product.id }}" data-title="{{ block_title }}">{% if product.has_only_default_variant %}-{% else %}{{ product.options_by_name[block.settings.title].values | join: ', ' | default: '-' }}{% endif -%}</div>
                    {%- endfor -%}
                  </div>

           {%- endcase -%}
        {%- endfor -%}
     </div>
   </div>

    {%- style -%}
    .t4s_compare_table {
      {%- assign qv_cl_lightness  = se_stts.qv_cl | color_extract: 'lightness' -%}
      {%- assign qv_cl_hover_lightness  = se_stts.qv_cl_hover | color_extract: 'lightness' -%}
      {%- assign atc_cl_lightness = se_stts.atc_cl | color_extract: 'lightness' -%}
      {%- assign atc_cl_hover_lightness = se_stts.atc_cl_hover | color_extract: 'lightness' -%}

      --qv-bg-cl             : {{ se_stts.qv_cl }};
      --qv-cl            : {% if qv_cl_lightness < 85 %}#fff{% else %}#222{% endif %};
      --atc-compare-bg-cl       : {{ se_stts.atc_cl }};
      --atc-compare-cl      : {% if atc_cl_lightness < 85 %}#fff{% else %}#222{% endif %};

      --qv-bg-hover-cl             : {{ se_stts.qv_cl_hover }};
      --qv-hover-cl            : {% if qv_cl_hover_lightness < 85 %}#fff{% else %}#222{% endif %};
      --atc-compare-bg-hover-cl       : {{ se_stts.atc_cl_hover }};
      --atc-compare-hover-cl      : {% if atc_cl_hover_lightness < 85 %}#fff{% else %}#222{% endif %};
    }
     .t4s_compare_basic_content a.t4s-pr-quickview svg,
     .t4s_compare_basic_content a.t4s-pr-addtocart svg {
      max-width: 20px;
      }
    .t4s_compare_basic_content a.t4s-pr-quickview {
      border-color: var(--qv-bg-cl);
      color: var(--qv-bg-cl);
      background-color: transparent;
    }
    .t4s_compare_basic_content a.t4s-pr-quickview:hover {
      border-color: var(--qv-bg-hover-cl);
      background-color: var(--qv-bg-hover-cl);
      color: var(--qv-hover-cl)
    }
    .t4s_compare_basic_content a.t4s-pr-addtocart,
    .t4s_compare_basic_content .t4s-product-atc-qty {
      background-color: var(--atc-compare-bg-cl); 
      color: var(--atc-compare-cl);
    }
    .t4s_compare_basic_content a.t4s-pr-addtocart:hover {
      background-color: var(--atc-compare-bg-hover-cl); 
      color: var(--atc-compare-hover-cl);
    }
    .t4s_compare_basic_content .t4s_compare_price {color: {{ section.settings.cl_price }} }
    .t4s_compare_basic_content .t4s_compare_price ins {color: {{ section.settings.cl_price_sale }} }
    .t4s_compare_availability .is--in-stock {color: {{ section.settings.cl_in }} }
    .t4s_compare_availability .is--out-of-stock {color: {{ section.settings.cl_out }} }
    {%- endstyle -%}
{%- else -%}
    <div class="t4s_empty_page t4s_empty_compare_page t4s-text-center">
      <svg width="140" height="140" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M 16 4 C 10.886719 4 6.617188 7.160156 4.875 11.625 L 6.71875 12.375 C 8.175781 8.640625 11.710938 6 16 6 C 19.242188 6 22.132813 7.589844 23.9375 10 L 20 10 L 20 12 L 27 12 L 27 5 L 25 5 L 25 8.09375 C 22.808594 5.582031 19.570313 4 16 4 Z M 25.28125 19.625 C 23.824219 23.359375 20.289063 26 16 26 C 12.722656 26 9.84375 24.386719 8.03125 22 L 12 22 L 12 20 L 5 20 L 5 27 L 7 27 L 7 23.90625 C 9.1875 26.386719 12.394531 28 16 28 C 21.113281 28 25.382813 24.839844 27.125 20.375 Z"/></svg>
      <h4 class="t4s_empty_compare__heading t4s_empty_title">{{ 'compare_page.empty' | t }}</h4>
      <div class="t4s_empty_compare__txt t4s_empty_des">{{ 'compare_page.empty_html' | t }}</div>
      {%- if btn_blocks.size > 0 -%}
        {{ 'button-style.css' | asset_url | stylesheet_tag }}
        <link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
        {%- for block in btn_blocks -%}
          {%- assign bk_stts = block.settings -%}
          {% if bk_stts.title != blank and bk_stts.url != blank %}
            <a class="t4s-btn t4s-btn-base t4s_empty_compare__btn t4s-btn-base t4s-btn-style-{{ bk_stts.button_style }} t4s-btn-size-{{ bk_stts.btns_size }} t4s-btn-icon-{{ bk_stts.btn_icon }} t4s-btn-color-{{ bk_stts.btns_cl }} {% if bk_stts.button_style == 'default' or bk_stts.button_style == 'outline' %}t4s-btn-effect-{{ bk_stts.button_effect }}{% endif %}" href="{{ bk_stts.url | default: routes.all_products_collection_url }}" data-loading-bar >{{ bk_stts.title }}{%- if bk_stts.btn_icon -%}<svg class="t4s-btn-icon" width="14"><use xlink:href="#t4s-icon-btn"></use></svg>{%- endif -%}</a>
          {% endif %}
        {%- endfor -%}
      {%- endif -%}
    </div>
{%- endif -%}
 {{- html_layout[1] -}}
</div>
{%- endpaginate -%}

<script>var isPageCompare = true, isComparePerformed = {{ search.performed }}, countComparePage = {{ results_count }}, listIDPrs = {{ list_id_prs | json }}{% if results_count == 0 %}, isEmtyCompare = true{% endif %};</script>

{%- schema -%}
  {
    "name": "Compare Page",
    "class": "t4s_section_compare",
    "settings": [
      {
        "type": "header",
        "content": "Demo exist compare products on editor"
      },
      {
        "type": "product_list",
        "id": "product_list",
        "label": "Products test",
        "limit": 6,
        "info": "Only shown on editor admin"
      },
      {
        "type": "header",
        "content": "1. General options"
      },
      {
        "type": "color",
        "id": "qv_cl",
        "label": "Quick view color",
        "default": "#56CFE1"
      },
      {
        "type": "color",
        "id": "qv_cl_hover",
        "label": "Quick view hover color",
        "default": "#222"
      },
      {
        "type": "color",
        "id": "atc_cl",
        "label": "Add to cart, Quick Shop,... color",
        "default": "#56CFE1"
      },
      {
        "type": "color",
        "id": "atc_cl_hover",
        "label": "Add to cart, Quick Shop,... hover color",
        "default": "#222"
      },
      {
        "type": "color",
        "id": "cl_price",
        "label": "Price color",
        "default": "#95bf46"
      },
      {
        "type": "color",
        "id": "cl_price_sale",
        "label": "Price sale color",
        "default": "#ec0101"
      },
      {
        "type": "color",
        "id": "cl_in",
        "label": " \"In stock\" color",
        "default": "#95bf46"
      },
      {
        "type": "color",
        "id": "cl_out",
        "label": "\"Out of stock\" color",
        "default": "#e1b878"
      },
      {
        "type": "header",
        "content": "+ Options image products"
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Image ratio",
        "default": "rationt",
        "info": "Aspect ratio custom will settings in general panel",
        "options": [
          {
            "group": "Natural",
            "value": "ratioadapt",
            "label": "Adapt to image"
          },
          {
            "group": "Landscape",
            "value": "ratio2_1",
            "label": "2:1"
          },
          {
            "group": "Landscape",
            "value": "ratio16_9",
            "label": "16:9"
          },
          {
            "group": "Landscape",
            "value": "ratio8_5",
            "label": "8:5"
          },
          {
            "group": "Landscape",
            "value": "ratio3_2",
            "label": "3:2"
          },
          {
            "group": "Landscape",
            "value": "ratio4_3",
            "label": "4:3"
          },
          {
            "group": "Landscape",
            "value": "rationt",
            "label": "Ratio ASOS"
          },
          {
            "group": "Squared",
            "value": "ratio1_1",
            "label": "1:1"
          },
          {
            "group": "Portrait",
            "value": "ratio2_3",
            "label": "2:3"
          },
          {
            "group": "Portrait",
            "value": "ratio1_2",
            "label": "1:2"
          },
          {
            "group": "Custom",
            "value": "ratiocus1",
            "label": "Ratio custom 1"
          },
          {
            "group": "Custom",
            "value": "ratiocus2",
            "label": "Ratio custom 2"
          },
          {
            "group": "Custom",
            "value": "ratio_us3",
            "label": "Ratio custom 3"
          },
          {
            "group": "Custom",
            "value": "ratiocus4",
            "label": "Ratio custom 4"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_size",
        "label": "Image size",
        "default": "cover",
        "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "cover",
            "label": "Full"
          },
          {
            "value": "contain",
            "label": "Auto"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "1",
            "label": "Left top"
          },
          {
            "value": "2",
            "label": "Left center"
          },
          {
            "value": "3",
            "label": "Left bottom"
          },
          {
            "value": "4",
            "label": "Right top"
          },
          {
            "value": "5",
            "label": "Right center"
          },
          {
            "value": "6",
            "label": "Right bottom"
          },
          {
            "value": "7",
            "label": "Center top"
          },
          {
            "value": "8",
            "label": "Center center"
          },
          {
            "value": "9",
            "label": "Center bottom"
          }
        ],
        "label": "Image position",
        "default": "8"
      },
      {
        "type": "select",
        "id": "content_align",
        "label": "Product content align",
        "default": "center",
        "options": [
          {
            "label": "Default",
            "value": "default"
          },
          {
            "label": "Center",
            "value": "center"
          }
        ]
      },
      {
        "type": "header",
        "content": "2. Design options"
      },
      {
        "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped container"},
            { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      }

    ],
    "blocks": [
      {
        "type": "basic",
        "name": "Content Basic",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "image, title, price,..."
          }
        ]
      },
      {
        "type": "availability",
        "name": "Availability",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Availability"
          }
        ]
      },
      {
        "type": "vendor",
        "name": "Vendor",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Vendor"
          }
        ]
      },
      {
        "type": "rating",
        "name": "Rating",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Rating"
          }
        ]
      },
      {
        "type": "variants",
        "name": "Variants",
        "limit": 10,
        "settings": [
          {
            "type": "paragraph",
            "content": "Enter key variant name in product to it show in t4s_compare. eg: Color or Size"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Key variant name:",
            "default": "Color"
          }
        ]
      },
      {
         "type": "btn",
         "name": "Button empty",
         "limit": 4,
         "settings": [
            {
              "type": "paragraph",
              "content": "Tip: Only show when cart empty"
            },
            {
              "label": "Button text",
              "id": "title",
              "type": "text",
              "default": "RETURN TO SHOP"
            },
            {
              "label": "Button link",
              "id": "url",
              "type": "url"
            },
            {
              "type": "select",
              "id": "open_link",
              "options": [
                {
                  "value": "_self",
                  "label": "Current window"
                },
               {
                  "value": "_blank",
                  "label": "New window"
                }
              ],
              "label": "Open link in",
              "default": "_self"
            },
            {
              "type":"checkbox",
              "id":"btn_icon",
              "label":"Enable button icon",
              "default":false
            },
            {
              "type": "select",
              "id": "button_style",
              "label": "Button style",
              "options": [
                  {
                      "label": "Default",
                      "value": "default"
                  },
                  {
                      "label": "Outline",
                      "value": "outline"
                  },
                  {
                      "label": "Bordered bottom",
                      "value": "bordered"
                  },
                  {
                      "label": "Link",
                      "value": "link"
                  }
              ]
            },
            {
              "type": "select",
              "id": "btns_size",
              "label": "Button size",
              "default":"medium",
              "options": [
                  {
                      "label": "Extra small",
                      "value": "small"
                  },
                  {
                      "label": "Small",
                      "value": "extra-small"
                  },
                  {
                      "label": "Medium",
                      "value": "medium"
                  },
                  {
                      "label": "Large",
                      "value": "extra-medium"
                  },
                  {
                      "label": "Extra large",
                      "value": "large"
                  },
                  {
                      "label": "Extra extra large",
                      "value": "extra-large"
                  }
              ]
            },
            {
              "type": "select",
              "id": "btns_cl",
              "label": "Button color",
              "default": "primary",
              "options": [
                {
                    "value": "light",
                    "label": "Light"
                },
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "primary",
                    "label": "Primary"
                },
                {
                    "value": "custom1",
                    "label": "Custom color 1"
                },
                {
                    "value": "custom2",
                    "label": "Custom color 2"
                }
              ]
            },
            {
                "type":"select",
                "id":"button_effect",
                "label":"Button hover effect",
                "default":"default",
                "info":"Only working button style default, outline",
                "options":[
                    {
                        "label":"Default",
                        "value":"default"
                    },
                    {
                        "label":"Fade",
                        "value":"fade"
                    },
                    {
                        "label":"Rectangle out",
                        "value":"rectangle-out"
                    },
                    {
                        "label":"Sweep to right",
                        "value":"sweep-to-right"
                    },
                    {
                        "label":"Sweep to left",
                        "value":"sweep-to-left"
                    },
                    {
                        "label":"Sweep to bottom",
                        "value":"sweep-to-bottom"
                    },
                    {
                        "label":"Sweep to top",
                        "value":"sweep-to-top"
                    },
                    {
                        "label":"Shutter out horizontal",
                        "value":"shutter-out-horizontal"
                    },
                    {
                        "label":"Outline",
                        "value":"outline"
                    },
                    {
                        "label":"Shadow",
                        "value":"shadow"
                    }
                ]
            }
          ]
      }
   ],
    "default": {
      "blocks": [
        {"type": "basic"},
        {"type": "availability"},
        {"type": "vendor"},
        {"type": "variants"},
        {"type": "btn"} 
      ]
    }
  }
{% endschema %}