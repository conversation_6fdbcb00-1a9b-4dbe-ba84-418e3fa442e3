{%- liquid
    assign se_stts = section.settings
    assign show_txt = se_stts.show_txt
    assign img_size = 40
    assign search_url = routes.search_url 
 -%}
{%- if se_stts.enable_toolbar and section.blocks.size > 0 -%}
{{ 'toolbar_mobile.css' | asset_url | stylesheet_tag }}
<div class="t4s-toolbar t4s-toolbar-label-{{ show_txt }} t4s-pf t4s-r-0 t4s-l-0 t4s-b-0 t4s-d-flex t4s-align-items-center t4s-justify-content-between" style="--bg-color:{{ se_stts.bg_color }};--icon-color:{{ se_stts.icon_color }};--label-color:{{ se_stts.label_color }};--count-bg-color:{{ se_stts.count_bg_color }};--count-text-color:{{ se_stts.count_text_color }}">
    {%- for block in section.blocks -%}
        {%- assign bk_stts = block.settings -%}
        {%- assign block_type = block.type -%}
        {%- case block_type -%}
            {%- when 'shop' -%}
            <div class="t4s-toolbart-{{ block_type }} t4s-toolbar-item">
                <a href="{{ collections[bk_stts.link].url | default: routes.all_products_collection_url }}">
                    <span class="t4s-toolbar-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-grid"><rect x="3" y="3" width="7" height="7"/><rect x="14" y="3" width="7" height="7"/><rect x="14" y="14" width="7" height="7"/><rect x="3" y="14" width="7" height="7"/></svg>
                    </span>
                    {%- if show_txt -%}<span class="t4s-toolbar-label">{{ bk_stts.title }}</span>{%- endif -%}
                </a>
            </div>
            {%- when 'wish' %}{% if settings.wishlist_mode == '0' %}{% continue %}{% endif -%} 
            <div class="t4s-toolbart-{{ block_type }} t4s-toolbar-item">
                <a data-link-wishlist href="{% if settings.wishlist_mode != '3' %}{{ search_url }}/?view=wishlist{%- else -%}/pages/wishlist{%- endif -%}">
                    <span class="t4s-toolbar-icon t4s-pr">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-heart"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/></svg>
                        <span data-count-wishlist class="t4s-pa t4s-op-0 t4s-ts-op t4s-count-box">0</span>
                    </span>           
                    {%- if show_txt -%}<span class="t4s-toolbar-label">{{ bk_stts.title }}</span>{%- endif -%}
                </a>
            </div>
            {%- when 'compe' -%}
            {%- unless settings.enable_compe -%}{%- continue -%}{%- endunless -%} 
                <div class="t4s-toolbart-{{ block_type }} t4s-toolbar-item">
                <a data-link-compare href="{{ search_url }}/?view=compare">
                    <span class="t4s-toolbar-icon t4s-pr">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-refresh-cw"><polyline points="23 4 23 10 17 10"/><polyline points="1 20 1 14 7 14"/><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"/></svg>
                        <span data-count-compare class="t4s-pa t4s-op-0 t4s-ts-op t4s-count-box">0</span>
                    </span>  
                    {%- if show_txt -%}<span class="t4s-toolbar-label">{{ bk_stts.title }}</span>{%- endif -%}
                </a>
              </div>
            {%- when 'cart' -%}
                <div class="t4s-toolbart-{{ block_type }} t4s-toolbar-item">
                    <a href="{{ routes.cart_url }}"{% if request.page_type != 'cart' and settings.cart_type != 'disable' %} data-drawer-delay- data-drawer-options='{ "id":"#t4s-mini_cart" }'{% endif %}>
                        <span class="t4s-toolbar-icon t4s-pr">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"/><circle cx="20" cy="21" r="1"/><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/></svg>
                            <span data-cart-count class="t4s-pa t4s-op-0 t4s-ts-op t4s-count-box">{{ cart.item_count }}</span>
                        </span>
                        {%- if show_txt -%}<span class="t4s-toolbar-label">{{ bk_stts.title }}</span>{%- endif -%}
                    </a>
                </div>
            {%- when 'account' -%}{%- unless shop.customer_accounts_enabled %}{% continue %}{% endunless -%} 
                <div class="t4s-toolbart-{{ block_type }} t4s-toolbar-item">
                    <a href="{{ routes.account_url }}" {% unless customer or request.page_type contains 'customers' or settings.login_side == false %} data-drawer-delay- data-drawer-options='{ "id":"#t4s-login-sidebar" }' {% endunless %}>
                        <span class="t4s-toolbar-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-user"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>
                        </span>
                        {%- if show_txt -%}<span class="t4s-toolbar-label">{{ bk_stts.title }}</span>{%- endif -%}
                    </a>
                </div>
            {%- when 'search' -%}
                <div class="t4s-toolbart-{{ block_type }} t4s-toolbar-item">
                    <a href="{{ routes.search_url }}" data-drawer-delay- data-drawer-options='{ "id":"#t4s-search-hidden" }'>
                        <span class="t4s-toolbar-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"/><line x1="21" y1="21" x2="16.65" y2="16.65"/></svg>
                        </span>
                        {%- if show_txt -%}<span class="t4s-toolbar-label">{{ bk_stts.title }}</span>{%- endif -%}
                    </a>
                </div>
            {%- when 'blog' %}
                {% assign blog = blogs[bk_stts.blog] -%}
                <div class="t4s-toolbart-{{ block_type }} t4s-toolbar-item">
                    <a href="{{ blog.url | default: request.path }}">
                        <span class="t4s-toolbar-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-feather"><path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"/><line x1="16" y1="8" x2="2" y2="22"/><line x1="17.5" y1="15" x2="9" y2="15"/></svg>
                        </span>
                        {%- if show_txt -%}<span class="t4s-toolbar-label">{{ bk_stts.title }}</span>{%- endif -%}
                    </a>
                </div>
            {%- when 'home' -%}
            <div class="t4s-toolbart-{{ block_type }} t4s-toolbar-item">
                <a href="{{ routes.root_url }}">
                    <span class="t4s-toolbar-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-home"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9 22 9 12 15 12 15 22"/></svg>
                    </span>
                    {%- if show_txt -%}<span class="t4s-toolbar-label">{{ bk_stts.title }}</span>{%- endif -%}
                  </a>
            </div>
            {%- when 'nav' -%}
                <div class="t4s-toolbart-{{ block_type }} t4s-toolbar-item">
                    <a href="{{ routes.root_url }}" data-menu-drawer data-drawer-options='{ "id":"#t4s-menu-drawer" }' class="t4s-push-menu-btn">
                        <span class="t4s-toolbar-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-menu"><line x1="3" y1="12" x2="21" y2="12"/><line x1="3" y1="6" x2="21" y2="6"/><line x1="3" y1="18" x2="21" y2="18"/></svg>
                        </span>
                        {%- if show_txt -%}<span class="t4s-toolbar-label">{{ bk_stts.title }}</span>{%- endif -%}
                    </a>
                </div>
            {%- when 'filter' -%}
            <div class="t4s-toolbart-{{ block_type }} t4s-toolbar-item t4s-dn">
                <a href="{{ request.path }}" data-drawer-options='{ "id":"#t4s-filter-hidden" }'>
                    <span class="t4s-toolbar-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="16" height="16" fill="currentColor"><path d="M324.4 64C339.6 64 352 76.37 352 91.63C352 98.32 349.6 104.8 345.2 109.8L240 230V423.6C240 437.1 229.1 448 215.6 448C210.3 448 205.2 446.3 200.9 443.1L124.7 385.6C116.7 379.5 112 370.1 112 360V230L6.836 109.8C2.429 104.8 0 98.32 0 91.63C0 76.37 12.37 64 27.63 64H324.4zM144 224V360L208 408.3V223.1C208 220.1 209.4 216.4 211.1 213.5L314.7 95.1H37.26L140 213.5C142.6 216.4 143.1 220.1 143.1 223.1L144 224zM496 400C504.8 400 512 407.2 512 416C512 424.8 504.8 432 496 432H336C327.2 432 320 424.8 320 416C320 407.2 327.2 400 336 400H496zM320 256C320 247.2 327.2 240 336 240H496C504.8 240 512 247.2 512 256C512 264.8 504.8 272 496 272H336C327.2 272 320 264.8 320 256zM496 80C504.8 80 512 87.16 512 96C512 104.8 504.8 112 496 112H400C391.2 112 384 104.8 384 96C384 87.16 391.2 80 400 80H496z"></path></svg>
                    </span>
                    {%- if show_txt -%}<span class="t4s-toolbar-label">{{ bk_stts.title }}</span>{%- endif -%}
                </a>
            </div>
            {%- when 'sidebar' -%}
                <div class="t4s-toolbart-{{ block_type }} t4s-toolbar-item t4s-dn">
                    <a href="{{ request.path }}" data-sidebar-trigger>
                        <span class="t4s-toolbar-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-sidebar"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><line x1="9" y1="3" x2="9" y2="21"/></svg>
                        </span>
                        {%- if show_txt -%}<span class="t4s-toolbar-label">{{ bk_stts.title }}</span>{%- endif -%}
                    </a>
                </div>
            {%- else -%}
            <div class="t4s-toolbart-{{ block_type }} t4s-toolbar-item">
                {%- assign image = bk_stts.image -%}
                <a href="{{ bk_stts.link | default: routes.account_url }}">
                    <span class="t4s-toolbar-icon t4s-pr">
                        {%- if image != blank %}
                            <img class="lazyloadt4s t4s-lz--fadeIn" data-src="{{ image | image_url: height: img_size }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src=" {%- render 'img_svg', w: image.img_size, h: image.img_size -%} " width="{{ image.img_size }}" height="{{ image.img_size }}" alt="{{ image.alt | escape }}">
                            <span class="lazyloadt4s-loader"></span>
                        {%- endif -%}
                    </span>
                    {%- if show_txt -%}<span class="t4s-toolbar-label">{{ bk_stts.title }}</span>{%- endif -%}
                </a>
            </div>
        {%- endcase -%}
    {%- endfor -%}
</div>
{%- endif -%}
{% schema %}
{
    "name": "Sticky toolbar mobile",
    "class": "t4s-section t4s-toolbar-mobile t4s-section-admn-fixed-",
    "settings": [
        {
            "type": "checkbox",
            "id": "enable_toolbar",
            "label": "Enable sticky toolbar mobile",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_txt",
            "label": "Show text under icon",
            "default": true
        },
        {
            "type": "header",
            "content": "+ Color options"
        },
        {
            "type": "color",
            "id": "bg_color",
            "label": "Background color",
            "default": "#fff"
        },
        {
            "type": "color",
            "id": "icon_color",
            "label": "Icon color",
            "default": "#222"
        },
        {
            "type": "color",
            "id": "label_color",
            "label": "Label color",
            "default": "#222"
        },
        {
            "type": "color",
            "id": "count_bg_color",
            "label": "Count background color",
            "default": "#000"
        },
        {
            "type": "color",
            "id": "count_text_color",
            "label": "Count text color",
            "default": "#fff"
        }
    ],
    "blocks":[
    {
        "type": "shop",
        "name": "Shop",
        "limit": 1,
        "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Title",
                    "default": "Shop"
                },
                {
                    "type": "collection",
                    "id": "link",
                    "label": "Link (optional)"
                }
            ]
        },
        {
            "type": "wish",
            "name": "Wishlist",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Title",
                    "default": "Wishlist"
                }
            ]
        },
        {
            "type": "compe",
            "name": "Compare",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Title",
                    "default": "Compare"
                }
            ]
        },
        {
            "type": "cart",
            "name": "Cart",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Title",
                    "default": "Cart"
                }
            ]
        },
        {
            "type": "account",
            "name": "Account",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Title",
                    "default": "Account"
                }
            ]
        },
        {
            "type": "search",
            "name": "Search",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Title",
                    "default": "Search"
                }
            ]
        },
        {
            "type": "blog",
            "name": "Blog",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Title",
                    "default": "Blog"
                },
                {
                    "id": "blog",
                    "type": "blog",
                    "label": "Blog"
                }
            ]
        },
        {
            "type": "filter",
            "name": "Filter",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Title",
                    "default": "Filter"
                },
                {
                    "type": "paragraph",
                    "content": "Only show when page has 'Filter'"
            }
            ]
        },
        {
            "type": "sidebar",
            "name": "Sidebar",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Title",
                    "default": "Sidebar"
                },
                {
                    "type": "paragraph",
                    "content": "Only show when page has 'Sidebar'"
                }
            ]
        },
        {
            "type": "nav",
            "name": "Menu",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Title",
                    "default": "Menu"
                }
            ]
        },
        {
            "type": "home",
            "name": "Home",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Title",
                    "default": "Home"
                }
            ]
        },
        {
            "type": "link",
            "name": "Link Custom",
            "settings": [
            {
                "type": "text",
                "id": "title",
                "label": "Title",
                "default": "Custom"
            },
            {
                "type": "image_picker",
                "id": "image",
                "label": "Icon image"
            },
            {
                "type": "url",
                "id": "link",
                "label": "Link",
                "default": "/"
            }
            ]
        }
    ],
    "default": {
        "blocks": [
          {"type": "shop"},{"type": "wish"},{"type": "cart"},{"type": "account"},{"type": "search"}
        ]
    }
}
{% endschema %}