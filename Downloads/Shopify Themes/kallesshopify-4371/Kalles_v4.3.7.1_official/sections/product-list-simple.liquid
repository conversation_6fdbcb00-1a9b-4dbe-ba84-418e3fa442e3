<!-- sections/product-list-simple.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'top-head.css' | asset_url | stylesheet_tag }}
{{ 'tabs.css' | asset_url | stylesheet_tag }}
{{ 'collection-products.css' | asset_url | stylesheet_tag }}
{{ 'slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
<link href="{{ 'loading.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign layout_des = se_stts.layout_des
  assign tabs_des = se_stts.tabs_des
  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif 
 -%}
<div class="t4s-section-inner t4s_nt_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
    <div class=" t4s-tabs-se tabs-layout-{{ tabs_des }} t4s-tabs-{{ tabs_des }} t4s-item-rounded-{{ se_stts.item_rounded }} t4s-border-{{ se_stts.tabs_border }}" style="--border:{{ se_stts.tabs_border }};--item-cl:{{ se_stts.item_cl }};--item-bg:{{ se_stts.item_bg }};--item-cl-active:{{ se_stts.item_cl_active }};--item-bg-active:{{ se_stts.item_bg_active }};--space-between:{{ se_stts.space_between }}px;--mgb:{{ se_stts.tabslist_mb }}px;">
        {%- render 'section_tophead', se_stts: se_stts -%}
      <div class="t4s-tabs t4s-type-tabs" data-t4s-tabs2>
          <div class="t4s-tabs-head">
        {%- if layout_des == "1" -%}
            <ul data-t4s-tab-ul2 class="t4s-tabs-ul t4s-row t4s-align-items-center t4s_box_pr_grid t4s-products t4s-justify-content-center">
        {%- elsif layout_des == "2" -%} 
          <ul data-t4s-tab-ul2 class="t4s-tabs-ul t4s-align-items-center  t4s-flicky-slider t4s-slider-btn-style-simple t4s-slider-btn-none t4s-slider-btn-small t4s-slider-btn-vi-always flickityt4s" 
           data-flickityt4s-js='{"isSimple": true,"freeScroll": true, "arrowIcon":"1", "imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "minWidthLG": 992, "cellAlignLG":"{{ slide_cellAlign }}", "cellAlign":"center", "wrapAround": false,"prevNextButtons": false,"percentPosition": 0,"pageDots": false, "pauseAutoPlayOnHover" : true }'>  
         {%- endif -%} 
          {% if se_stts.products != blank %} 
          {%- for product in se_stts.products -%}
            <li class="t4s-tab-item t4s-tab-sp-vertical" style="--space_vertical:{{ se_stts.space_vertical }}px;">
                <span class="t4s-text-title data-t4s-tab-item data-no-instant {{ block.shopify_attributes }} {% if forloop.first == true %} t4s-active {% endif %}">{{ product.title | link_to: product.url }}</span>
            </li>
          {%- endfor -%}
					{% else%}
					{%- for i in (1..6) limit: limit -%}
						<li class="t4s-tab-item t4s-tab-sp-vertical" style="--space_vertical:{{ se_stts.space_vertical }}px;">
							<span class="t4s-text-title data-t4s-tab-item data-no-instant {{ block.shopify_attributes }} {% if forloop.first == true %} t4s-active {% endif %}"><a href="/">Product title</a></span>
						</li>
					{% endfor%}
          {% endif %}
        </ul>
          </div>
      </div>
      <div class="t4s-cl-button t4s-d-flex t4s-justify-content-center t4s-align-items-center">
      {%- if se_stts.button_link != blank -%}
                <a href="{{ se_stts.button_link }} "class= "t4s-cl-view-all">{{ se_stts.button_label }}</a>
        {%- endif -%}
        </div>
    </div>
    {{- html_layout[1] -}}
</div>
 <style>
  .t4s-tabs-border-bg .t4s-tabs-ul li a{
    padding:14px 24px;
    border-radius:56px;

 }
 .t4s-tab-sp-vertical{
    margin-top:var(--space_vertical);
 }
 .t4s-cl-view-all{
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: -0.01em;
    text-transform: uppercase;
    border-bottom:1px solid #000;
    color: #000;
 }
 .t4s-cl-view-all:hover{
	border-color:var(--link-color-hover);
}
 </style>

{%- schema -%}
  {
    "name": "Product list simple",
    "tag": "section",
    "class": "t4s-section t4s-section-all t4s_bk_flickity t4s_tp_cdt t4s-tabs-collection t4s_tp_tab2",
    "settings": [
      {
          "type": "header",
          "content": "1. Heading options"
      },
      {
          "type": "select",
          "id": "design_heading",
          "label": "+ Design heading",
          "default": "1",
          "options": [
              {
                  "value": "1",
                  "label": "Design 01"
              },
              {
                  "value": "2",
                  "label": "Design 02"
              },
              {
                  "value": "3",
                  "label": "Design 03"
              },
              {
                  "value": "4",
                  "label": "Design 04"
              },
              {
                  "value": "5",
                  "label": "Design 05"
              },
              {
                  "value": "6",
                  "label": "Design 06 (width line-awesome)"
              },
              {
                  "value": "7",
                  "label": "Design 07"
              },
              {
                  "value": "8",
                  "label": "Design 08"
              },
              {
                  "value": "9",
                  "label": "Design 09"
              },
              {
                  "value": "10",
                  "label": "Design 10"
              },
              {
                  "value": "11",
                  "label": "Design 11"
              },
              {
                  "value": "12",
                  "label": "Design 12"
              },
              {
                  "value": "13",
                  "label": "Design 13"
              },
              {
                  "value": "14",
                  "label": "Design 14"
              },
              {
                "value": "15",
                "label": "Design 15"
              },
              {
                "value": "16",
                "label": "Design 16"
              }
          ]
      },
      {
          "type": "select",
          "id": "heading_align",
          "label": "+ Heading align",
          "default": "t4s-text-center",
          "options": [
              {
                  "value": "t4s-text-start",
                  "label": "Left"
              },
              {
                  "value": "t4s-text-center",
                  "label": "Center"
              },
              {
                  "value": "t4s-text-end",
                  "label": "Right"
              }
          ]
      },
      {
          "type": "text",
          "id": "top_heading",
          "label": "+ Heading",
          "default": "Product list simple"
      },
      {
        "type": "text",
        "id": "icon_heading",
        "label": "Enter a icon name on heading",
        "info": "Only used for design 6 [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
        "default": "las la-gem"
      },
      {
          "type": "textarea",
          "id": "top_subheading",
          "label": "+ Subheading"
      }, 
      {
        "type": "number",
        "id": "tophead_mb",
        "label": "+ Space bottom (px)",
        "info": "The vertical spacing between heading and content",
        "default": 30
      },
      {
        "type": "header",
        "content": "2. General options"
      },
      {
        "type":"product_list",
        "id":"products",
        "label":"Product list"
      },
      {
        "type": "select",
        "id": "tabs_des",
        "options": [
					{
            "value": "border-bg",
            "label": "Has border"
          },
          {
            "value": "base",
            "label": "Base"
          },
          {
            "value": "underline",
            "label": "Has underline (when item active)"
          } 
        ],
        "label": "Product item",
        "default": "border-bg"
      },
      {
        "type": "select",
        "id": "tabs_border",
        "label": "Product item border",
        "default": "solid",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "solid",
            "label": "Solid"
          },
          {
            "value": "dashed",
            "label": "Dashed"
          },
          {
            "value": "dotted",
            "label": "Dotted"
          }
        ]
      },
      {
        "type": "color",
        "id": "item_cl",
        "label": "Color item",
        "default": "#222222"
      },
      {
        "type": "color",
        "id": "item_bg",
        "label": "Background/border item"
      },
      {
        "type": "color",
        "id": "item_cl_active",
        "label": "Color item active",
        "default": "#fff"
      },
      {
        "type": "color",
        "id": "item_bg_active",
        "label": "Background/border item active",
        "default": "#56CFE1"
      },
      {
        "type": "range",
        "id": "space_between",
        "min": 0,
        "max": 40,
        "step": 1,
        "label": "Space between items",
        "unit": "px",
        "default": 30
      },
      {
        "type": "range",
        "id": "space_vertical",
        "min": 0,
        "max": 40,
        "step": 1,
        "label": "Space vertical items",
        "unit": "px",
        "default": 30
      },
      {
        "type": "number",
        "id": "tabslist_mb",
        "label": "Product list margin bottom",
        "default": 30
      },
      {
        "type": "text",
        "id": "button_label",
        "label": "Button label",
        "default": "View all device"
      },
      {
        "type":"url",
        "id":"button_link",
        "label":"Button link"
        
      },
      {
        "type": "header",
        "content": "--Box options--"
      },
      {
        "type": "select",
        "id": "layout_des",
        "options": [
          {
            "value": "1",
            "label": "Grid"
          },
          {
            "value": "2",
            "label": "Carousel"
          }
        ],
        "label": "Layout design",
        "default": "1"
      },
      {
        "type": "header",
        "content": "3. Design options"
      },
      {
        "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped container"},
            { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "4. Custom css"
      },
      {
        "id": "use_cus_css",
        "type": "checkbox",
        "label": "Use custom css",
        "default":false,
        "info": "If you want custom style for this section."
      },
      { 
        "id": "code_cus_css",
        "type": "textarea",
        "label": "Code custom css",
        "info": "Use selector .SectionID to style css"
        
      }
    ],
  "presets": [
      {
        "name": "Product list simple",
        "category": "Homepage"
       
      }
    ]
  }
{%- endschema -%}

{%- javascript -%}
{%- endjavascript -%}