<!-- sections/image-gallery.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'custom-effect.css' | asset_url | stylesheet_tag }}
{{ 'slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
{%- liquid
  assign image_fix = image_nt | image_tag
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign image_ratio = se_stts.image_ratio
  assign b_effect = se_stts.b_effect
  assign img_effect = se_stts.img_effect
  assign open_link = se_stts.open_link
  assign click_image = se_stts.click_image
  if click_image == "data-t4s-gallery"
    assign click_class = "t4s-cursor-pointer"
  endif 
  if se_stts.btn_owl == "outline"
    assign arrow_icon = 1
  else
    assign arrow_icon = 2
  endif
  assign ARRhtml1 = 'a,,' | split: ','
  assign ARRhtml2 = 'div,data-,data-' | split: ','

  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif 
 -%} 
<div class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}"  {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} {% render 'section_style', se_stts: se_stts %}>
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner {% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s {% endif %} "  {% if stt_image_bg != blank %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} > {%- endif -%}
    {%- render 'section_tophead', se_stts: se_stts -%}
      <div {{ click_image }} data-t4s-thumb-{{ se_stts.nav_thumbs }} class="isotopet4s t4s_{{ image_ratio }} t4s_position_{{ se_stts.image_position }} t4s_{{ se_stts.image_size }} t4s-row t4s-gx-md-{{ se_stts.space_item }} t4s-gy-md-{{ se_stts.space_item }} t4s-gx-{{ se_stts.space_item_mb }} t4s-gy-{{ se_stts.space_item_mb }}" data-isotopet4s-js='{ "itemSelector": ".t4s-gallery-item", "layoutMode": "packery" }' style="--item-rd:{{ se_stts.item_rd }}%;">
      {%- if se_blocks.size > 0 -%}
          {%- for block in se_blocks -%}
            {%- liquid
              assign bk_stts = block.settings
              assign image = bk_stts.image
              if bk_stts.link == blank
                assign ARRhtml = ARRhtml2
              else
                assign ARRhtml = ARRhtml1
              endif
           -%}
            <div {{ click_image }}--item class="t4s-col-item t4s-gallery-item {{ click_class }} t4s-col-lg-{{ bk_stts.col_dk }} t4s-col-md-{{ bk_stts.col_tb }} t4s-col-{{ bk_stts.col_mb }}" id="b_{{ block.id }} " data-select-flickity {{ block.shopify_attributes }}>
              <{{ ARRhtml[0] }} {{ ARRhtml[1] }}href="{{ bk_stts.link }}" {{ ARRhtml[2] }}target="{{ open_link }}" class="t4s-eff t4s-eff-{{ b_effect }} t4s-eff-img-{{ img_effect }}" {{ click_image }}--open timeline hdt-reveal="slide-in">
                <div data-cacl-slide class="t4s_ratio t4s-img-wrap" style="--aspect-ratioapt: {{ image.aspect_ratio | default: 2 }}">
                  {%- if image != blank -%}
                    <img {% if image.presentation.focal_point != '50.0% 50.0%' %} style="object-position: {{ image.presentation.focal_point }}"{% endif %} class="lazyloadt4s t4s-lz--fadeIn t4s-obj-eff" data-pswp-src="{{ image | image_url }}" data-pswp-w="{{ image.width }}" data-pswp-h="{{ image.height }}" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                  	<span class="lazyloadt4s-loader is-bg-img" style="background: url({{ image | image_url: width: 1 }});"></span>
                  {%- else -%}
                    {{ 'image' | placeholder_svg_tag: 't4s-placeholder-svg t4s-obj-eff' }} 
                  {%- endif -%} 
                </div>
              </{{ ARRhtml[0] }}>
            </div>
          {%- endfor -%}
      {%- endif -%}
    </div>
    {{- html_layout[1] -}} 
  </div>
<style type="text/css">
  .gallery_popup_thumbnail .gallery-item {
    cursor: pointer;
  }
  .t4s-gallery-item .t4s-img-wrap {
    border-radius: var(--item-rd);
    overflow: hidden;
  }
  .t4s-gallery-item .t4s-eff-border-run::before,
  .t4s-gallery-item .t4s-eff-border-run::after {
    border-radius: var(--item-rd);
  }
</style>
{%- schema -%}
  {
    "name": "Image gallery packery",
    "tag": "section",
    "class": "t4s-section t4s_bk_flickity t4s-section-all t4s_tp_cdt t4s_tp_istope t4s-gallery",
    "settings": [
      {
          "type": "header",
          "content": "1. Heading options"
      },
      {
          "type": "select",
          "id": "design_heading",
          "label": "+ Design heading",
          "default": "1",
          "options": [
              {
                  "value": "1",
                  "label": "Design 01"
              },
              {
                  "value": "2",
                  "label": "Design 02"
              },
              {
                  "value": "3",
                  "label": "Design 03"
              },
              {
                  "value": "4",
                  "label": "Design 04"
              },
              {
                  "value": "5",
                  "label": "Design 05"
              },
              {
                  "value": "6",
                  "label": "Design 06 (width line-awesome)"
              },
              {
                  "value": "7",
                  "label": "Design 07"
              },
              {
                  "value": "8",
                  "label": "Design 08"
              },
              {
                  "value": "9",
                  "label": "Design 09"
              },
              {
                  "value": "10",
                  "label": "Design 10"
              },
              {
                  "value": "11",
                  "label": "Design 11"
              },
              {
                  "value": "12",
                  "label": "Design 12"
              },
              {
                  "value": "13",
                  "label": "Design 13"
              },
              {
                  "value": "14",
                  "label": "Design 14"
              },
              {
                "value": "15",
                "label": "Design 15"
              },
              {
                "value": "16",
                "label": "Design 16"
              }
          ]
      },
      {
          "type": "select",
          "id": "heading_align",
          "label": "+ Heading align",
          "default": "t4s-text-center",
          "options": [
              {
                  "value": "t4s-text-start",
                  "label": "Left"
              },
              {
                  "value": "t4s-text-center",
                  "label": "Center"
              },
              {
                  "value": "t4s-text-end",
                  "label": "Right"
              }
          ]
      },
      {
          "type": "text",
          "id": "top_heading",
          "label": "+ Heading",
          "default": "Packery gallery"
      },
      {
        "type": "text",
        "id": "icon_heading",
        "label": "Enter a icon name on heading",
        "info": "Only used for design 6 [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
        "default": "las la-gem"
      },
      {
          "type": "textarea",
          "id": "top_subheading",
          "label": "+ Subheading"
      }, 
      {
        "type": "number",
        "id": "tophead_mb",
        "label": "+ Space bottom (px)",
        "info": "The vertical spacing between heading and content",
        "default": 30
      },
      {
        "type": "header",
        "content": "2. General options"
      },
      {
        "type": "select",
        "id": "click_image",
        "label": "Click action",
        "default": "data-t4s-gallery",
        "info": "Select style when you click to image",
        "options": [
          {
            "value": "data-goto",
            "label": "Go to link"
          },
          {
            "value": "data-t4s-gallery",
            "label": "Photoswise popup"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "nav_thumbs",
        "label": "Enable Navigation Thumbnail",
        "info": "Works when use Photoswise Popup",
        "default": true
      },
      {
        "type": "select",
        "id": "open_link",
        "info": "Works when the item has a link",
        "options": [
          {
            "value": "_self",
            "label": "Current window"
          },
         {
            "value": "_blank",
            "label": "New window"
          }
        ],
        "label": "Open link in",
        "default": "_blank"
      },
      {
        "type": "range",
        "id": "item_rd",
        "min": 0,
        "max": 50,
        "step": 1,
        "label": "Item image rounded",
        "unit": "%",
        "default": 0
      },
      {
        "type": "select",
        "id": "img_effect",
        "label": "Image hover effect",
            "info": "Waring: Hovering effect will resize your images",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "zoom",
            "label": "Zoom in"
          },
          {
            "value": "rotate",
            "label": "Rotate"
          },
          {
            "value": "translateToTop",
            "label": "Move to top "
          },
          {
            "value": "translateToRight",
            "label": "Move to right"
          },
          {
            "value": "translateToBottom",
            "label": "Move to bottom"
          },
          {
            "value": "translateToLeft",
            "label": "Move to left"
          },
          {
            "value": "filter",
            "label": "Filter"
          },
          {
            "value": "bounceIn",
            "label": "BounceIn"
          }
        ]
      },
      {
        "type": "select",
        "id": "b_effect",
        "label": "Item effect when hover",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "border-run",
            "label": "Border run"
          },
          {
            "value": "pervasive-circle",
            "label": "Pervasive circle"
          },
          {
            "value": "plus-zoom-overlay",
            "label": "Plus zoom overlay"
          },
          {
            "value": "dark-overlay",
            "label": "Dark overlay"
          },
          {
            "value": "light-overlay",
            "label": "Light overlay"
          }
        ]
      },
      {
        "type": "header",
        "content": "+ Options image"
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Image ratio",
        "default": "ratioadapt",
        "info": "Aspect ratio custom will settings in general panel",
        "options": [
          {
            "group": "Natural",
            "value": "ratioadapt",
            "label": "Adapt to image"
          },
          {
            "group": "Landscape",
            "value": "ratio2_1",
            "label": "2:1"
          },
          {
            "group": "Landscape",
            "value": "ratio16_9",
            "label": "16:9"
          },
          {
            "group": "Landscape",
            "value": "ratio8_5",
            "label": "8:5"
          },
          {
            "group": "Landscape",
            "value": "ratio3_2",
            "label": "3:2"
          },
          {
            "group": "Landscape",
            "value": "ratio4_3",
            "label": "4:3"
          },
          {
            "group": "Landscape",
            "value": "rationt",
            "label": "Ratio ASOS"
          },
          {
            "group": "Squared",
            "value": "ratio1_1",
            "label": "1:1"
          },
          {
            "group": "Portrait",
            "value": "ratio2_3",
            "label": "2:3"
          },
          {
            "group": "Portrait",
            "value": "ratio1_2",
            "label": "1:2"
          },
          {
            "group": "Custom",
            "value": "ratiocus1",
            "label": "Ratio custom 1"
          },
          {
            "group": "Custom",
            "value": "ratiocus2",
            "label": "Ratio custom 2"
          },
          {
            "group": "Custom",
            "value": "ratio_us3",
            "label": "Ratio custom 3"
          },
          {
            "group": "Custom",
            "value": "ratiocus4",
            "label": "Ratio custom 4"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_size",
        "label": "Image size",
        "default": "cover",
        "info": "This settings apply only if the image ratio is not set to 'Adapt to image'.",
        "options": [
          {
            "value": "cover",
            "label": "Full"
          },
          {
            "value": "contain",
            "label": "Auto"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "info": "The first value is the horizontal position and the second value is the vertical. These settings apply only if the image ratio is not set to 'Adapt to image', it also does not work when using 'Focal point' on the image.",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "1",
            "label": "Left top"
          },
          {
            "value": "2",
            "label": "Left center"
          },
          {
            "value": "3",
            "label": "Left bottom"
          },
          {
            "value": "4",
            "label": "Right top"
          },
          {
            "value": "5",
            "label": "Right center"
          },
          {
            "value": "6",
            "label": "Right bottom"
          },
          {
            "value": "7",
            "label": "Center top"
          },
          {
            "value": "8",
            "label": "Center center"
          },
          {
            "value": "9",
            "label": "Center bottom"
          }
        ],
        "label": "Image position",
        "default": "8"
      },
      {
        "type": "select",
        "id": "space_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
  		  {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space between items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
    	  {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space between items (Mobile)",
        "default": "10"
      },
      {
        "type": "header",
        "content": "3. Design options"
      },
      {
        "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped container"},
            { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background", 
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
          "type": "text",
          "id": "mg", 
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
          "type": "text",
          "id": "mg_tb",
          "label": "Margin",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_tb",
          "label": "Padding",
          "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "4. Custom css"
      },
      {
        "id": "use_cus_css",
        "type": "checkbox",
        "label": "Use custom css",
        "default":false,
        "info": "If you want custom style for this section."
      },
      { 
        "id": "code_cus_css",
        "type": "textarea",
        "label": "Code custom css",
        "info": "Use selector .SectionID to style css"
        
      }
    ],
    "blocks": [
      {
        "type": "image_item",
        "name": "Image",
        "settings": [
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Link"
          },
          {
            "type": "select",
            "id": "col_dk",
            "label": "Item width",
            "default": "6",
            "options": [
              {
                "value": "12",
                "label": "100%"
              },
              {
                "value": "9",
                "label": "75%"
              },
              {
                "value": "8",
                "label": "66.66%"
              },
              {
                "value": "7",
                "label": "58.33%"
              },
              {
                "value": "6",
                "label": "50%"
              },
              {
                "value": "5",
                "label": "41.66%"
              },
              {
                "value": "4",
                "label": "33.33%"
              },
              {
                "value": "3",
                "label": "25%"
              },
              {
                "value": "2",
                "label": "16.67%"
              }
            ]
          },
          {
            "type": "select",
            "id": "col_tb",
            "label": "Item width (Tablet)",
            "default": "6",
            "options": [
              {
                "value": "12",
                "label": "100%"
              },
              {
                "value": "9",
                "label": "75%"
              },
              {
                "value": "8",
                "label": "66.66%"
              },
              {
                "value": "7",
                "label": "58.33%"
              },
              {
                "value": "6",
                "label": "50%"
              },
              {
                "value": "5",
                "label": "41.66%"
              },
              {
                "value": "4",
                "label": "33.33%"
              },
              {
                "value": "3",
                "label": "25%"
              },
              {
                "value": "2",
                "label": "16.67%"
              }
            ]
          },
          {
            "type": "select",
            "id": "col_mb",
            "label": "Item width (Mobile)",
            "default": "12",
            "options": [
              {
                "value": "12",
                "label": "100%"
              },
              {
                "value": "9",
                "label": "75%"
              },
              {
                "value": "8",
                "label": "66.66%"
              },
              {
                "value": "7",
                "label": "58.33%"
              },
              {
                "value": "6",
                "label": "50%"
              },
              {
                "value": "5",
                "label": "41.66%"
              },
              {
                "value": "4",
                "label": "33.33%"
              },
              {
                "value": "3",
                "label": "25%"
              }
            ]
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Image gallery packery",
        "category": "Homepage",
        "blocks": [
          {"type": "image_item"},
          {"type": "image_item"},
          {"type": "image_item"},
          {"type": "image_item"}
        ]
      }
    ]
  }
 {%- endschema -%} 