<!-- sections/instagram_feed.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'instagram.css' | asset_url | stylesheet_tag }}
{%- liquid
    assign image_fix = image_nt | image_tag
    assign sid = section.id
    assign se_stts = section.settings
    assign se_blocks = section.blocks
    assign block = section.blocks.first
    assign bk_stts = block.settings
    assign b_type = block.type
    assign arr_img = se_blocks | where: "type", "img"
    assign stt_layout = se_stts.layout
    assign stt_image_bg = se_stts.image_bg
    if stt_layout == 't4s-se-container' 
        assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
    elsif stt_layout == 't4s-container-wrap'
        assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
    else
        assign html_layout = '__' | split: '__'
    endif
    if se_stts.btn_owl == "outline"
    assign arrow_icon = 1
    else
    assign arrow_icon = 2
    endif
    assign ARRhtml1 = 'a,,' | split: ','
    assign ARRhtml2 = 'div,data-,data-' | split: ','
    assign b_effect = se_stts.b_effect
    assign img_effect = se_stts.img_effect
    assign use_slider = false
    assign icon_ins_id = 'icon-instagram' | append: sid
    assign t4s_se_class = 't4s_nt_se_' | append: sid
    if se_stts.use_cus_css and se_stts.code_cus_css != blank
        render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
    endif 
 -%}   
{%- if se_stts.layout_des == "2" -%}
{{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
{{ 'slider-settings.css' | asset_url | stylesheet_tag }}
{%- endif -%}
{%- if b_effect != "none" or img_effect != "nome" -%}
{{ 'custom-effect.css' | asset_url | stylesheet_tag }}
{%- endif -%}
<svg style="display: none">
    <symbol  id="{{ icon_ins_id }}" class="t4s-svg-ins-image"  viewBox="0 0 24 24"  fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M22,15.5,18.55,11a2,2,0,0,0-3.14,0L12,15.27a2,2,0,0,1-3,.18l-1.1-1.07a2,2,0,0,0-2.81,0L2,17.5V20a2,2,0,0,0,2,2H20a2,2,0,0,0,2-2Z"></path><rect x="2" y="2" width="20" height="20" rx="2"></rect><line x1="6.99" y1="7" x2="7" y2="7" stroke-linecap="round" stroke-width="2.5"></line></symbol>
</svg>  
<div class="t4s-section-inner {{ t4s_se_class }} t4s-pr ins-is--loaded t4s_nt_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}
      <div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
        {%- if b_type == '1' -%}
            {%- render 'block_tophead', bk_stts: bk_stts -%}
        {%- elsif b_type != blank and b_type != 'img' -%}
            <div class="t4s-ins-content {{ bk_stts.pos }} t4s-t-0 t4s-l-0 t4s-r-0 t4s-b-0 t4s-d-flex t4s-justify-content-center t4s-align-items-center t4s-pe-none t4s-op-0 t4s-ins-title-type-{{ b_type }}">
                <div class="t4s-ins-content-wrap t4s-ins-content-style{{ bk_stts.des }} t4s-text-center t4s-rte" timeline hdt-reveal="slide-in">
                    {%- if b_type == '2' -%}
                        <p></p>
                        <h4 class="t4s-ins-title">{{ bk_stts.title }}</h4>
                        <h6 class="t4s-ins-subtitle">{{ bk_stts.sub_title }}</h6>
                        {{ bk_stts.content }}
                        <p></p>
                    {%- elsif b_type == '3' -%}
                        <p></p>
                        <h4 class="t4s-ins-title">{{ bk_stts.title }}</h4>
                        <div class="t4s-hr-border"></div>
                        {{ bk_stts.content }}
                        <p></p>
                    {%- else -%}
                        <p class="t4s-ins-subtitle t4s-fnt-fm-3">{{ bk_stts.content }}</p>
                        <h4 class="t4s-ins-title">{{ bk_stts.title }}</h4>
                    {%- endif -%}
                </div>
            </div>
        {%- endif -%}
    {% if se_stts.layout_des == "1" %}
        <div class="t4s-align-items-center t4s-row t4s_{{ se_stts.image_size }} t4s_{{ se_stts.image_ratio }} t4s_position_{{ se_stts.image_position }} t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }} t4s-gx-md-{{ se_stts.space_item }} t4s-gy-md-{{ se_stts.space_item }} t4s-gx-{{ se_stts.space_item_mb }} t4s-gy-{{ se_stts.space_item_mb }}" id="b_{{ block.id }}" data-select-flickity {{ block.shopify_attributes }}>
    {% else %}
        {%- assign use_slider = true -%}
        <div class="t4s-flicky-slider t4s-row t4s_{{ se_stts.image_size }} t4s_{{ se_stts.image_ratio }} t4s_position_{{ se_stts.image_position }} t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }} t4s-gx-md-{{ se_stts.space_item }} t4s-gy-md-{{ se_stts.space_item }} t4s-gx-{{ se_stts.space_item_mb }} t4s-gy-{{ se_stts.space_item_mb }}{% if se_stts.nav_btn == true %}  t4s-slider-btn-style-{{ se_stts.btn_owl }} t4s-slider-btn-{{ se_stts.btn_shape }} t4s-slider-btn-{{ se_stts.btn_size }} t4s-slider-btn-cl-{{ se_stts.btn_cl }} t4s-slider-btn-vi-{{ se_stts.btn_vi }} t4s-slider-btn-hidden-mobile-{{ se_stts.btn_hidden_mobile }} {% endif %}{% if se_stts.nav_dot %} t4s-dots-style-{{ se_stts.dot_owl }} t4s-dots-cl-{{ se_stts.dots_cl }} t4s-dots-round-{{ se_stts.dots_round }} t4s-dots-hidden-mobile-{{ se_stts.dots_hidden_mobile }} {% endif %} flickityt4s" data-flickityt4s-js='{ "arrowIcon":"{{ arrow_icon }}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": {{ se_stts.loop }},"prevNextButtons": {{ se_stts.nav_btn }},"percentPosition": 1,"pageDots": {{ se_stts.nav_dot }}, "autoPlay" : {{ se_stts.au_time | times: 1000 }}, "pauseAutoPlayOnHover" : {{ se_stts.au_hover }} }' style="--space-dots: {{ se_stts.dots_space }}px;--flickity-btn-pos: {{ se_stts.space_item }}px;--flickity-btn-pos-mb: {{ se_stts.space_item_mb }}px;">
    {% endif %}
        {%- if se_blocks.size > 0 -%}
            {%- for block in arr_img -%}
                {%- liquid
                    assign bk_stts = block.settings 
                    assign image = bk_stts.image 
                    assign url = bk_stts.url
                    if url == blank 
                        assign ARRhtml = ARRhtml2
                    else 
                        assign ARRhtml = ARRhtml1 
                    endif 
               -%}
                <div class="t4s-col-item t4s-instagram-item t4s-col-ins" id="b_{{ block.id }}" data-select-flickity {{ block.shopify_attributes }} >
                    <{{ ARRhtml[0] }} {{ ARRhtml[1] }}href="{{ url }}" {{ ARRhtml[2] }}target="_blank" class="t4s-eff t4s-eff-{{ b_effect }} t4s-eff-img-{{ img_effect }}" timeline hdt-reveal="slide-in"> 
                        <div class="t4s_ratio" style="background: url({{ image | image_url: width: 1 }});--aspect-ratioapt: {{ image.aspect_ratio | default: 1.7776 }}" data-cacl-slide>
                            {%- if image != blank -%}
                                <img {% if image.presentation.focal_point != '50.0% 50.0%' %} style="object-position: {{ image.presentation.focal_point }}"{% endif %} class="lazyloadt4s t4s-lz--fadeIn t4s-obj-eff" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                            {%- else -%}
                                {{ 'image' | placeholder_svg_tag: 't4s-placeholder-svg t4s-obj-eff' }} 
                            {%- endif -%} 
                        </div>   
                        <div class="t4s-ins-info">
                            <div class="t4s-ins-icon">
                                <svg aria-hidden="true" focusable="false" role="presentation"><use href="#{{ icon_ins_id }}" /></svg>
                            </div>
                        </div>  
                    </{{ ARRhtml[0] }}> 
                </div>
            {%- endfor -%}
        {%- endif -%}
    </div>
    {{- html_layout[1] -}}
</div>
{%- schema -%}
{
    "name": "Instagram feed",
    "tag": "section",
    "class": "t4s-section t4s-section-all t4s_bk_flickity",
    "settings": [
        {
            "type": "header",
            "content": "1. General options"
        },
        {
            "type": "select",
            "id": "layout_des",
            "options": [
                {
                    "value": "1",
                    "label": "Grid"
                },
                {
                    "value": "2",
                    "label": "Carousel"
                }
            ],
            "label": "Layout design",
            "default": "2"
        },
        {
            "type": "select",
            "id": "image_ratio",
            "label": "Aspect ratio",
            "default": "ratio1_1",
            "info": "Aspect ratio custom will settings in general panel.",
            "options": [
                {
                    "group": "Auto",
                    "value": "ratioadapt",
                    "label": "Adapt to image"
                },
                {
                    "group": "Landscape",
                    "value": "ratio2_1",
                    "label": "2:1"
                },
                {
                    "group": "Landscape",
                    "value": "ratio16_9",
                    "label": "16:9"
                },
                {
                    "group": "Landscape",
                    "value": "ratio8_5",
                    "label": "8:5"
                },
                {
                    "group": "Landscape",
                    "value": "ratio3_2",
                    "label": "3:2"
                },
                {
                    "group": "Landscape",
                    "value": "ratio4_3",
                    "label": "4:3"
                },
                {
                    "group": "Landscape",
                    "value": "rationt",
                    "label": "Ratio ASOS"
                },
                {
                    "group": "Squared",
                    "value": "ratio1_1",
                    "label": "1:1"
                },
                {
                    "group": "Portrait",
                    "value": "ratio2_3",
                    "label": "2:3"
                },
                {
                    "group": "Portrait",
                    "value": "ratio1_2",
                    "label": "1:2"
                },
                {
                    "group": "Custom",
                    "value": "ratiocus1",
                    "label": "Ratio custom 1"
                },
                {
                    "group": "Custom",
                    "value": "ratiocus2",
                    "label": "Ratio custom 2"
                },
                {
                    "group": "Custom",
                    "value": "ratiocus3",
                    "label": "Ratio custom 3"
                },
                {
                    "group": "Custom",
                    "value": "ratiocus4",
                    "label": "Ratio custom 4"
                }
            ]
        },
        {
            "type": "select",
            "id": "image_position",
            "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'.And 'Not works when use 'Focal point' on image'",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "1",
                    "label": "Left top"
                },
                {
                    "value": "2",
                    "label": "Left center"
                },
                {
                    "value": "3",
                    "label": "Left bottom"
                },
                {
                    "value": "4",
                    "label": "Right top"
                },
                {
                    "value": "5",
                    "label": "Right center"
                },
                {
                    "value": "6",
                    "label": "Right bottom"
                },
                {
                    "value": "7",
                    "label": "Center top"
                },
                {
                    "value": "8",
                    "label": "Center center"
                },
                {
                    "value": "9",
                    "label": "Center bottom"
                }
            ],
            "label": "Image position",
            "default": "8"
        },
        {
            "type": "select",
            "id": "image_size",
            "label": "Image size",
            "default": "cover",
            "info": "This settings apply only if the image ratio is not set to 'Adapt to image'.",
            "options": [
                {
                    "value": "cover",
                    "label": "Full"
                },
                {
                    "value": "contain",
                    "label": "Auto"
                }
            ]
        },
        {
            "type": "select",
            "id": "img_effect",
            "label": "Photos effect when hover ",
            "default": "zoom",
            "options": [
                {
                    "value": "none",
                    "label": "None"
                },
                {
                    "value": "zoom",
                    "label": "Zoom in"
                },
                {
                    "value": "filter",
                    "label": "Filter"
                },
                {
                    "value": "bounceIn",
                    "label": "BounceIn"
                }
            ]
        },
        {
            "type": "select",
            "id": "b_effect",
            "label": "Photos effect",
            "default": "dark-overlay",
            "options": [
                {
                    "value": "none",
                    "label": "None"
                },
                {
                    "value": "border-run",
                    "label": "Border run"
                },
                {
                    "value": "pervasive-circle",
                    "label": "Pervasive circle"
                },
                {
                    "value": "plus-zoom-overlay",
                    "label": "Plus zoom overlay"
                },
                {
                    "value": "dark-overlay",
                    "label": "Dark overlay"
                },
                {
                    "value": "light-overlay",
                    "label": "Light overlay"
                } 
            ]
        },
        {
            "type": "select",
            "id": "col_dk",
            "label": "Items per row",
            "default": "6",
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                },
                {
                    "value": "3",
                    "label": "3"
                },
                {
                    "value": "4",
                    "label": "4"
                },
                {
                    "value": "5",
                    "label": "5"
                },
                {
                    "value": "6",
                    "label": "6"
                }
            ]
        },
        {
            "type": "select",
            "id": "col_tb",
            "label": "Items per row (Tablet)",
            "default": "2",
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                },
                {
                    "value": "3",
                    "label": "3"
                },
                {
                    "value": "4",
                    "label": "4"
                }
            ]
        },
        {
            "type": "select",
            "id": "col_mb",
            "label": "Items per row (Mobile)",
            "default": "2",
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                }
            ]
        },
        {
            "type": "select",
            "id": "space_item",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "3", 
                    "label": "3px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                }
            ],
            "label": "Spaces between photos",
            "default": "0"
        },
        {
            "type": "select",
            "id": "space_item_mb",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "3", 
                    "label": "3px"
                  },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                }
            ],
            "label": "Spaces between photos (Mobile)",
            "default": "0"
        },
        {
            "type": "header",
            "content": "+Options for carousel layout"
        },
        {
            "type": "checkbox",
            "id": "loop",
            "label": "Enable loop",
            "info": "At the end of cells, wrap-around to the other end for infinite scrolling",
            "default": true
        },
        {
            "type": "range",
            "id": "au_time",
            "min": 0,
            "max": 30,
            "step": 0.5,
            "label": "Autoplay speed in second.",
            "info": "Set is '0' to disable autoplay",
            "unit": "s",
            "default": 0
        },
        {
            "type": "checkbox",
            "id": "au_hover",
            "label": "Pause autoplay on hover",
            "info": "Auto-playing will pause when the user hovers over the carousel",
            "default": true
        },
        {
            "type": "paragraph",
            "content": "—————————————————"
        },
        {
            "type": "paragraph",
            "content": "Prev next button"
        },
        {
            "type": "checkbox",
            "id": "nav_btn",
            "label": "Use prev next button",
            "info": "Creates and show previous & next buttons",
            "default": false
        },
        {
            "type": "select",
            "id": "btn_vi",
            "label": "Visible",
            "default": "hover",
            "options": [
                {
                    "value": "always",
                    "label": "Always"
                },
                {
                    "value": "hover",
                    "label": "Only hover"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_owl",
            "label": "Button style",
            "default": "default",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "outline",
                    "label": "Outline"
                },
                {
                    "value": "simple",
                    "label": "Simple"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_shape",
            "label": "Button shape",
            "info": "Not working with button style 'Simple'",
            "default": "none",
            "options": [
                {
                    "value": "none",
                    "label": "Default"
                },
                {
                    "value": "round",
                    "label": "Round"
                },
                {
                    "value": "rotate",
                    "label": "Rotate"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_cl",
            "label": "Button color",
            "default": "dark",
            "options": [
                {
                    "value": "light",
                    "label": "Light"
                },
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "primary",
                    "label": "Primary"
                },
                {
                    "value": "custom1",
                    "label": "Custom color 1"
                },
                {
                    "value": "custom2",
                    "label": "Custom color 2"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_size",
            "label": "Buttons size",
            "default": "small",
            "options": [
                {
                    "value": "small",
                    "label": "Small"
                },
                {
                    "value": "medium",
                    "label": "Medium"
                },
                {
                    "value": "large",
                    "label": "Large"
                }
            ]
        },
        {
            "type":"checkbox",
            "id":"btn_hidden_mobile",
            "label":"Hidden buttons on mobile ",
            "default": true
        },
        {
            "type": "paragraph",
            "content": "—————————————————"
        },
        {
            "type": "paragraph",
            "content": "Page dots"
        },
        {
            "type": "checkbox",
            "id": "nav_dot",
            "label": "Use page dots",
            "info": "Creates and show page dots",
            "default": false
        },
        {
            "type": "select",
            "id": "dot_owl",
            "label": "Dots style",
            "default": "default",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "outline",
                    "label": "Outline"
                },
                {
                    "value": "elessi",
                    "label": "Elessi"
                }
            ]
        },
        {
            "type": "select",
            "id": "dots_cl",
            "label": "Dots color",
            "default": "dark",
            "options": [
                {
                    "value": "light",
                    "label": "Light (Best on dark background)"
                },
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "primary",
                    "label": "Primary"
                },
                {
                    "value": "custom1",
                    "label": "Custom color 1"
                },
                {
                    "value": "custom2",
                    "label": "Custom color 2"
                }
            ]
        },
        {
            "type": "checkbox",
            "id": "dots_round",
            "label": "Enable dots round",
            "default": true
        },
        {
            "type": "range",
            "id": "dots_space",
            "min": 2,
            "max": 20,
            "step": 1,
            "label": "Dot between horizontal",
            "unit": "px",
            "default": 10
        },
        {
            "type":"checkbox",
            "id":"dots_hidden_mobile",
            "label":"Hidden dots on mobile ",
            "default": false
        },
        {
            "type": "header",
            "content": "2.Design options"
        },
        {
            "type": "select","id": "layout","default": "t4s-container-fluid","label": "Layout",
            "options": [
                { "value": "t4s-se-container", "label": "Container"},
                { "value": "t4s-container-wrap", "label": "Wrapped container"},
                { "value": "t4s-container-fluid", "label": "Full width"}
            ]
        },
        {
            "type": "color",
            "id": "cl_bg",
            "label": "Background"
        },
        {
            "type": "color_background",
            "id": "cl_bg_gradient",
            "label": "Background gradient"
        },
        {
            "type": "image_picker",
            "id": "image_bg",
            "label": "Background image"
        },
        {
            "type": "text",
            "id": "mg",
            "label": "Margin",
            "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
            "default": ",,50px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd",
            "label": "Padding",
            "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
            "placeholder": "50px,,50px,"
        },
        {
            "type": "header",
            "content": "+ Design Tablet Options"
        },
        {
            "type": "text",
            "id": "mg_tb",
            "label": "Margin",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_tb",
            "label": "Padding",
            "placeholder": ",,50px,"
        }, 
        {
            "type": "header",
            "content": "+ Design mobile options"
        },
        {
            "type": "text",
            "id": "mg_mb",
            "label": "Margin",
            "default": ",,30px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_mb",
            "label": "Padding",
            "placeholder": ",,50px,"
        },
        {
            "type": "header",
            "content": "3. Custom css"
        },
        {
            "id": "use_cus_css",
            "type": "checkbox",
            "label": "Use custom css",
            "default":false,
            "info": "If you want custom style for this section."
        },
        { 
            "id": "code_cus_css",
            "type": "textarea",
            "label": "Code custom css",
            "info": "Use selector .SectionID to style css"
            
        }
    ],
    "blocks": [
        {
            "type": "img",
            "name": "Image",
            "settings": [
                {
                    "type": "image_picker",
                    "id": "image",
                    "label": "Instagram image",
                    "info": "1080 x 1080px .jpg recommended"
                },
                {
                    "type": "url",
                    "id": "url",
                    "label": "Link to"
                }
            ]
        },
        {
            "type": "1",
            "name": "Title type 1",
            "limit":1,
            "settings":[
                {
                    "type": "select",
                    "id": "design_heading",
                    "label": "+ Design heading",
                    "default": "1",
                    "options": [
                        {
                            "value": "1",
                            "label": "Design 01"
                        },
                        {
                            "value": "2",
                            "label": "Design 02"
                        },
                        {
                            "value": "3",
                            "label": "Design 03"
                        },
                        {
                            "value": "4",
                            "label": "Design 04"
                        },
                        {
                            "value": "5",
                            "label": "Design 05"
                        },
                        {
                            "value": "6",
                            "label": "Design 06 (width line-awesome)"
                        },
                        {
                            "value": "7",
                            "label": "Design 07"
                        },
                        {
                            "value": "8",
                            "label": "Design 08"
                        },
                        {
                            "value": "9",
                            "label": "Design 09"
                        },
                        {
                            "value": "10",
                            "label": "Design 10"
                        },
                        {
                            "value": "11",
                            "label": "Design 11"
                        },
                        {
                            "value": "12",
                            "label": "Design 12"
                        },
                        {
                            "value": "13",
                            "label": "Design 13"
                        },
                        {
                            "value": "14",
                            "label": "Design 14"
                        },
                        {
                            "value": "15",
                            "label": "Design 15"
                        },
                        {
                          "value": "16",
                          "label": "Design 16"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "heading_align",
                    "label": "+ Heading align",
                    "default": "t4s-text-center",
                    "options": [
                        {
                            "value": "t4s-text-start",
                            "label": "Left"
                        },
                        {
                            "value": "t4s-text-center",
                            "label": "Center"
                        },
                        {
                            "value": "t4s-text-end",
                            "label": "Right"
                        }
                    ]
                },
                {
                    "type": "text",
                    "id": "top_heading",
                    "label": "+ Heading",
                    "default":"@ FOLLOW US ON INSTAGRAM"
                },
                {
                    "type": "text",
                    "id": "icon_heading",
                    "label": "Enter a name icon [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
                    "info": "Only used for design 6",
                    "default": "las la-gem"
                },
                {
                    "type": "textarea",
                    "id": "top_subheading",
                    "label": "+ Subheading"
                },
                {
                    "type": "number",
                    "id": "tophead_mb",
                    "label": "+ Space bottom (px)",
                    "info": "The vertical spacing between heading and content.",
                    "default": 30
                }
            ]
        },
        {
            "type": "2",
            "name": "Title type 2",
            "limit":1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Heading",
                    "default": "INSTAGRAM"
                },
                {
                    "type": "text",
                    "id": "sub_title",
                    "label": "Sub text",
                    "default": "@NAME_ACCOUNT"
                },
                {
                    "type": "richtext",
                    "id": "content",
                    "label": "Content",
                    "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor.</p>"
                },
                {
                    "type": "select",
                    "id": "des",
                    "options": [
                    {
                        "value": "1",
                        "label": "Design 1"
                    },
                    {
                        "value": "2",
                        "label": "Design 2"
                    }
                    ],
                    "label": "Design"
                },
                {
                    "type": "select",
                    "id": "pos",
                    "options": [
                        {
                            "value": "t4s-pa",
                            "label": "Center"
                        },
                        {
                            "value": "t4s-pa-md",
                            "label": "Top"
                        }
                    ],
                    "label": "Position mobile"
                }
            ]
        },
        {
            "type": "3",
            "name": "Title type 3",
            "limit":1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Heading",
                    "default": "INSTAGRAM"
                },
                {
                    "type": "richtext",
                    "id": "content",
                    "label": "Content",
                    "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor.</p>"
                },
                {
                    "type": "select",
                    "id": "des",
                    "options": [
                    {
                        "value": "1",
                        "label": "Design 1"
                    },
                    {
                        "value": "2",
                        "label": "Design 2"
                    }
                    ],
                    "label": "Design"
                },
                {
                    "type": "select",
                    "id": "pos",
                    "options": [
                        {
                            "value": "t4s-pa",
                            "label": "Center"
                        },
                        {
                            "value": "t4s-pa-md",
                            "label": "Top"
                        }
                    ],
                    "label": "Position mobile"
                }
            ]
        },
        {
            "type": "4",
            "name": "Title type 4",
            "limit":1,
            "settings": [
                {
                    "type": "text",
                    "id": "title",
                    "label": "Heading",
                    "default": "@THE4STUDIO"
                },
                {
                    "type": "textarea",
                    "id": "content",
                    "label": "Sub heading",
                    "default": "Find us on Instagram"
                },
                {
                    "type": "select",
                    "id": "des",
                    "options": [
                    {
                        "value": "1",
                        "label": "Design 1"
                    },
                    {
                        "value": "2",
                        "label": "Design 2"
                    }
                    ],
                    "label": "Design"
                },
                {
                    "type": "select",
                    "id": "pos",
                    "options": [
                        {
                            "value": "t4s-pa",
                            "label": "Center"
                        },
                        {
                            "value": "t4s-pa-md",
                            "label": "Top"
                        }
                    ],
                    "label": "Position mobile"
                }
            ]
        }
    ],
    "presets": [
      {
        "name": "Instagram feed",
        "category": "homepage",
        "blocks": [
            {"type": "img"},
            {"type": "img"},
            {"type": "img"},
            {"type": "img"},
            {"type": "img"},
            {"type": "img"}
        ]
      }
    ]
}
{% endschema %}
