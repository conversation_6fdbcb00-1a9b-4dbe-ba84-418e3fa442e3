<!-- sections/instagram-shop.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
{{ 'instagram-pin.css' | asset_url | stylesheet_tag }}
{{ 'collection-products.css' | asset_url | stylesheet_tag }}
{{ 'slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'button-style.css' | asset_url | stylesheet_tag }}
<link href="{{ 'base_drop.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid
    assign image_fix = image_nt | image_tag
    assign sid = section.id
    assign se_stts = section.settings
    assign se_blocks = section.blocks
    assign open_link = se_stts.open_link 
    assign sett_equal = se_stts.use_eq_height
    assign layout_des = se_stts.layout_des
    assign stt_layout = se_stts.layout
    if stt_layout == 't4s-se-container' 
        assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
    elsif stt_layout == 't4s-container-wrap'
        assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
    else
        assign html_layout = '__' | split: '__'
    endif
    if se_stts.btn_owl == "outline"
        assign arrow_icon = 1
    else
        assign arrow_icon = 2  
    endif
    assign ARRhtml1 = 'a,,' | split: ','
    assign ARRhtml2 = 'div,data-,data-' | split: ','
    assign root_url = routes.root_url
    if root_url != '/'
    assign root_url = root_url | append: '/'
    endif
 -%}
<div class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }}" {% render 'section_style', se_stts: se_stts -%} >  
   {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner">{%- endif -%}
     {%- render 'section_tophead', se_stts: se_stts -%}
    {% if se_stts.layout_des == "1" %}
        <div class="t4s-ins-shop t4s-row t4s_{{ se_stts.image_size }} t4s_{{ se_stts.image_ratio }} t4s_position_{{ se_stts.image_position }} t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }} t4s-gx-md-{{ se_stts.space }} t4s-gy-md-{{ se_stts.space }} t4s-gx-{{ se_stts.space_mb }} t4s-gy-{{ se_stts.space_mb }}">
    {% else %}
        {%- assign use_slider = true -%}
        <div class="t4s-ins-shop t4s-flicky-slider t4s-row t4s_{{ se_stts.image_size }} t4s_{{ se_stts.image_ratio }} t4s_position_{{ se_stts.image_position }} t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }} t4s-gx-md-{{ se_stts.space }} t4s-gy-md-{{ se_stts.space }} t4s-gx-{{ se_stts.space_mb }} t4s-gy-{{ se_stts.space_mb }} {% if se_stts.nav_btn == true %}  t4s-slider-btn-style-{{ se_stts.btn_owl }} t4s-slider-btn-{{ se_stts.btn_shape }} t4s-slider-btn-{{ se_stts.btn_size }} t4s-slider-btn-cl-{{ se_stts.btn_cl }} t4s-slider-btn-vi-{{ se_stts.btn_vi }} t4s-slider-btn-hidden-mobile-{{ se_stts.btn_hidden_mobile }} {% endif %} {% if se_stts.nav_dot == true %}t4s-dots-style-{{ se_stts.dot_owl }} t4s-dots-cl-{{ se_stts.dots_cl }} t4s-dots-round-{{ se_stts.dots_round }} t4s-dots-hidden-mobile-{{ se_stts.dots_hidden_mobile }} {% endif %}  flickityt4s" data-flickityt4s-js='{ "arrowIcon":"{{ arrow_icon }}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": {{ se_stts.loop }},"prevNextButtons": {{ se_stts.nav_btn }},"percentPosition": 1,"pageDots": {{ se_stts.nav_dot }}, "autoPlay" : {{ se_stts.au_time | times: 1000 }}, "pauseAutoPlayOnHover" : {{ se_stts.au_hover }} }' style="--space-dots: {{ se_stts.dots_space }}px;--flickity-btn-pos: {{ se_stts.space }}px;--flickity-btn-pos-mb: {{ se_stts.space_mb }}px;">
    {% endif %}
    {%- if se_blocks.size > 0 -%}
        {%- for block in se_blocks -%}
            {%- liquid
                assign bk_stts = block.settings  
                assign image = bk_stts.image 
                assign ratio = image.aspect_ratio  
                assign url = bk_stts.url 
                if url == blank 
                    assign ARRhtml = ARRhtml2
                else  
                    assign ARRhtml = ARRhtml1 
                endif 
                assign index = 0 
           -%}
            <div class="t4s-col-item t4s-ins-item" id="b_{{ block.id }}" data-select-flickity {{ block.shopify_attributes }}>    
                <div class="t4s-ins-wrap t4s-pr t4s-oh" timeline hdt-reveal="slide-in">         
                    <{{ ARRhtml[0] }} {{ ARRhtml[1] }}href="{{ url }}" {{ ARRhtml[2] }}target="_blank"> 
                        <div class="t4s_ratio" {{ bk_stts.url }} style="background: url({{ image | image_url: width: 1 }});--aspect-ratioapt: {{ image.aspect_ratio | default: 1.7776 }}; --br-rdu: {{ se_stts.image_bdr }}%;">
                            {%- if image != blank -%}
                                <img {% if image.presentation.focal_point != '50.0% 50.0%' %}style="object-position: {{ image.presentation.focal_point }}"{% endif %} class="lazyloadt4s t4s-lz--fadeIn" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                            {%- else -%}
                                {{ 'image' | placeholder_svg_tag: 't4s-placeholder-svg' }} 
                            {%- endif -%} 
                        </div>     
                    </{{ ARRhtml[0] }}> 
                    {%- for i in (1..3) -%}
                        {%- liquid
                            assign product   = 'product_' | append: i
                            assign pos_t     = 'pos_t_' | append: i
                            assign pos_l     = 'pos_l_' | append: i
                            assign pos_popup = 'pos_popup_' | append: i
                            assign cl_pin    = 'cl_pin_' | append: i
                            assign product_id   = bk_stts[product]
                            assign pos_t_id     = bk_stts[pos_t]
                            assign pos_l_id     = bk_stts[pos_l]
                            assign pos_popup_id = bk_stts[pos_popup]
                            assign cl_pin_id    = bk_stts[cl_pin]
                          -%}
                        {%- if product_id != blank -%}{%- assign index = index | plus: 1 -%}
                        <span data-bid="t4s_{{ bk_stts.id }}{{ product_id.id }}" data-pin-popup data-position="{{ pos_popup_id }}" data-is-pr data-href="{{ product_id.url }}" data-sid="render-pr_lb{{ se_stts.pr_pin_des }}" class="t4s-lookbook-pin t4s-ins-pin is-type__pr" {{ 
                        block.shopify_attributes }} style="--ps-top: {{ pos_t_id }}%;--ps-left: {{ pos_l_id }}%;"> 
                            <span class="t4s-zoompin"></span>
                            <span class="t4s-hotspot-ins t4s-hotspot-{{ cl_pin_id }}">{{ index }}</span>
                        </span>
                        {%- endif -%} 
                    {%- endfor -%}      
                </div>        
            </div>
        {%- endfor -%}
      {%- else -%}{%- render 'no-blocks' -%}{%- endif -%}
    </div>
    {{- html_layout[1] -}}
</div>
{%- schema -%}
  {
    "name": "Instagram shop",
    "tag":"section",
    "class": "t4s-section t4s-section-all t4s_tp_lb t4s_tp_flickity t4s_bk_flickity type_pin_owl",
    "settings": [
        {
                "type": "header",
                "content": "1. Heading options"
            },
            {
                "type": "select",
                "id": "design_heading",
                "label": "+ Design heading",
                "default": "1",
                "options": [
                    {
                        "value": "1",
                        "label": "Design 01"
                    },
                    {
                        "value": "2",
                        "label": "Design 02"
                    },
                    {
                        "value": "3",
                        "label": "Design 03"
                    },
                    {
                        "value": "4",
                        "label": "Design 04"
                    },
                    {
                        "value": "5",
                        "label": "Design 05"
                    },
                    {
                        "value": "6",
                        "label": "Design 06 (width line-awesome)"
                    },
                    {
                        "value": "7",
                        "label": "Design 07"
                    },
                    {
                        "value": "8",
                        "label": "Design 08"
                    },
                    {
                        "value": "9",
                        "label": "Design 09"
                    },
                    {
                        "value": "10",
                        "label": "Design 10"
                    },
                    {
                        "value": "11",
                        "label": "Design 11"
                    },
                    {
                        "value": "12",
                        "label": "Design 12"
                    },
                    {
                        "value": "13",
                        "label": "Design 13"
                    },
                    {
                        "value": "14",
                        "label": "Design 14"
                    },
                    {
                      "value": "15",
                      "label": "Design 15"
                    },
                    {
                      "value": "16",
                      "label": "Design 16"
                    }
                ]
            },
            {
                "type": "select",
                "id": "heading_align",
                "label": "+ Heading align",
                "default": "t4s-text-center",
                "options": [
                    {
                        "value": "t4s-text-start",
                        "label": "Left"
                    },
                    {
                        "value": "t4s-text-center",
                        "label": "Center"
                    },
                    {
                        "value": "t4s-text-end",
                        "label": "Right"
                    }
                ]
            },
            {
                "type": "text",
                "id": "top_heading",
                "label": "+ Heading"
            },
            {
                "type": "text",
                "id": "icon_heading",
                "label": "Enter a name icon [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
                "info": "Only used for design 6",
                "default": "las la-gem"
            },
            {
                "type": "textarea",
                "id": "top_subheading",
                "label": "+ Subheading"
            },
            {
                "type": "number",
                "id": "tophead_mb",
                "label": "+ Space bottom (px)",
                "info": "The vertical spacing between heading and content.",
                "default": 30
            },
        {
            "type": "header",
            "content": "2. General Settings"
        },
        {
            "type": "select",
            "id": "layout_des",
            "options": [
            {
                "value": "1",
                "label": "Grid"
            },
            {
                "value": "2",
                "label": "Carousel"
            }
            ],
            "label": "Layout design",
            "default": "2"
        },
        {
            "type": "select",
            "id": "pr_pin_des",
            "options": [
                {
                    "value": "1",
                    "label": "Pin product design 1"
                },
                {
                    "value": "2",
                    "label": "Pin product design 2"
                },
                {
                    "value": "3",
                    "label": "Pin product design 3"
                },
                {
                    "value": "4",
                    "label": "Pin product design 4"
                },
                {
                    "value": "5",
                    "label": "Pin product design 5"
                },
                {
                    "value": "6",
                    "label": "Pin product design 6"
                }
            ],
            "label": "Pin product design",
            "default": "1"
        },
        {
            "type": "select",
            "id": "open_link",
            "options": [
            {
                "value": "_self",
                "label": "Current window"
            },
            {
                "value": "_blank",
                "label": "New window"
            }
            ],
            "label": "Open link in",
            "default": "_self"
        },
        {
        "type": "select",
        "id": "space",
        "options": [
            {
            "value": "30",
            "label": "30px"
            },
            {
            "value": "25",
            "label": "25px"
            },
            {
            "value": "20",
            "label": "20px"
            },
            {
            "value": "15",
            "label": "15px"
            },
            {
            "value": "10",
            "label": "10px"
            },
            {
            "value": "6",
            "label": "6px"
            },
            {
            "value": "3",
            "label": "3px"
            },
            {
            "value": "2",
            "label": "2px"
            },
            {
            "value": "0",
            "label": "0px"
            }
        ],
        "label": "Spaces between photos", 
        "default": "0" 
        },
        {
           "type": "select",
           "id": "space_mb",
           "options": [
             {
               "value": "30",
               "label": "30"
             },
             {
               "value": "25",
               "label": "25"
             },
             {
               "value": "20",
               "label": "20"
             },
             {
               "value": "15",
               "label": "15"
             },
             {
               "value": "10",
               "label": "10"
             },
             {
               "value": "6",
               "label": "6"
             },
             {
               "value": "3",
               "label": "3"
             },
             {
               "value": "2",
               "label": "2"
             },
             {
               "value": "0",
               "label": "0"
             }
           ],
           "label": "Spaces between photos mobile",
           "default": "20" 
          },
        {
            "type": "select",
            "id": "col_dk",
            "label": "Photos per row",
            "info": "How many photos you want to show per row",
            "default": "6",
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                },
                {
                    "value": "3",
                    "label": "3"
                },
                {
                    "value": "4",
                    "label": "4"
                },
                {
                    "value": "5",
                    "label": "5"
                },
                {
                    "value": "6",
                    "label": "6"
                }
            ]
        },
        {
            "type": "select",
            "id": "col_tb",
            "label": "Photos per row Tablet",
            "default": "3",
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                },
                {
                    "value": "3",
                    "label": "3"
                },
                {
                    "value": "4",
                    "label": "4"
                }
            ]
        },
        {
            "type": "select",
            "id": "col_mb",
            "label": "Photos per row Mobile",
            "default": "2",
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                }
            ]
        },
        {
            "type": "header",
            "content": "+Options image"
        },
        {
            "type": "select",
            "id": "image_ratio",
            "label": "Image ratio",
            "default": "ratio1_1",
            "info": "Aspect ratio custom will settings in general panel",
            "options": [
            {
                "group": "Natural",
                "value": "ratioadapt",
                "label": "Adapt to image"
            },
            {
                "group": "Landscape",
                "value": "ratio2_1",
                "label": "2:1"
            },
            {
                "group": "Landscape",
                "value": "ratio16_9",
                "label": "16:9"
            },
            {
                "group": "Landscape",
                "value": "ratio8_5",
                "label": "8:5"
            },
            {
                "group": "Landscape",
                "value": "ratio3_2",
                "label": "3:2"
            },
            {
                "group": "Landscape",
                "value": "ratio4_3",
                "label": "4:3"
            },
            {
                "group": "Landscape",
                "value": "rationt",
                "label": "Ratio ASOS"
            },
            {
                "group": "Squared",
                "value": "ratio1_1",
                "label": "1:1"
            },
            {
                "group": "Portrait",
                "value": "ratio2_3",
                "label": "2:3"
            },
            {
                "group": "Portrait",
                "value": "ratio1_2",
                "label": "1:2"
            },
            {
                "group": "Custom",
                "value": "ratiocus1",
                "label": "Ratio custom 1"
            },
            {
                "group": "Custom",
                "value": "ratiocus2",
                "label": "Ratio custom 2"
            },
            {
                "group": "Custom",
                "value": "ratio_us3",
                "label": "Ratio custom 3"
            },
            {
                "group": "Custom",
                "value": "ratiocus4",
                "label": "Ratio custom 4"
            }
            ]
        },
        {
            "type": "select",
            "id": "image_size",
            "label": "Image size",
            "default": "cover",
            "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
            {
                "value": "cover",
                "label": "Full"
            },
            {
                "value": "contain",
                "label": "Auto"
            }
            ]
        },
        {
            "type": "select",
            "id": "image_position",
            "info": "The first value is the horizontal position and the second value is the vertical. These settings apply only if the image ratio is not set to 'Adapt to image', it also does not work when using 'Focal point' on the image.",
            "options": [
            {
                "value": "default",
                "label": "Default"
            },
            {
                "value": "1",
                "label": "Left top"
            },
            {
                "value": "2",
                "label": "Left center"
            },
            {
                "value": "3",
                "label": "Left bottom"
            },
            {
                "value": "4",
                "label": "Right top"
            },
            {
                "value": "5",
                "label": "Right center"
            },
            {
                "value": "6",
                "label": "Right bottom"
            },
            {
                "value": "7",
                "label": "Center top"
            },
            {
                "value": "8",
                "label": "Center center"
            },
            {
                "value": "9",
                "label": "Center bottom"
            }
            ],
            "label": "Image position",
            "default": "8"
        },
        {
            "type":"range",
            "id":"image_bdr",
            "label":"Image border radius",
            "min":0,
            "max":50,
            "step":1,
            "unit":"%",
            "default":0 
        },
          {
            "type": "header",
            "content": "+Options for carousel layout"
          },
          {
            "type": "checkbox",
            "id": "loop",
            "label": "Enable loop",
            "info": "At the end of cells, wrap-around to the other end for infinite scrolling",
            "default": true
          },
          {
            "type": "range",
            "id": "au_time",
            "min": 0,
            "max": 30,
            "step": 0.5,
            "label": "Autoplay speed in second.",
            "info": "Set is '0' to disable autoplay",
            "unit": "s",
            "default": 0
          },
          {
            "type": "checkbox",
            "id": "au_hover",
            "label": "Pause autoplay on hover",
            "info": "Auto-playing will pause when the user hovers over the carousel",
            "default": true
          },
          {
            "type": "paragraph",
            "content": "—————————————————"
          },
          {
            "type": "paragraph",
            "content": "Prev next button"
          },
          {
            "type": "checkbox",
            "id": "nav_btn",
            "label": "Use prev next button",
            "info": "Creates and show previous & next buttons",
            "default": false
          },
          {
            "type": "select",
            "id": "btn_vi",
            "label": "Visible",
            "default": "hover",
            "options": [
              {
                "value": "always",
                "label": "Always"
              },
              {
                "value": "hover",
                "label": "Only hover"
              }
            ]
          },
          {
            "type": "select",
            "id": "btn_owl",
            "label": "Button style",
            "default": "default",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "outline",
                "label": "Outline"
              },
              {
                "value": "simple",
                "label": "Simple"
              }
            ]
          },
          {
            "type": "select",
            "id": "btn_shape",
            "label": "Button shape",
            "info": "Not working with button style 'Simple'",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "Default"
              },
              {
                "value": "round",
                "label": "Round"
              },
              {
                "value": "rotate",
                "label": "Rotate"
              }
            ]
          },
          {
              "type": "select",
              "id": "btn_cl",
              "label": "Button color",
              "default": "dark",
              "options": [
                  {
                      "value": "default",
                      "label": "Default"
                  },
                  {
                      "value": "light",
                      "label": "Light"
                  },
                  {
                      "value": "dark",
                      "label": "Dark"
                  },
                  {
                      "value": "primary",
                      "label": "Primary"
                  },
                  {
                        "value": "custom1",
                      "label": "Custom color 1"
                  },
                  {
                      "value": "custom2",
                      "label": "Custom color 2"
                  }
              ]
          },
          {
            "type": "select",
            "id": "btn_size",
            "label": "Buttons size",
            "default": "small",
            "options": [
              {
                "value": "small",
                "label": "Small"
              },
              {
                "value": "medium",
                "label": "Medium"
              },
              {
                "value": "large",
                "label": "Large"
              }
            ]
          },
          {
            "type":"checkbox",
            "id":"btn_hidden_mobile",
            "label":"Hidden buttons on mobile ",
            "default": true
          },
          {
            "type": "paragraph",
            "content": "—————————————————"
          },
          {
            "type": "paragraph",
            "content": "Page dots"
          },
          {
            "type": "checkbox",
            "id": "nav_dot",
            "label": "Use page dots",
            "info": "Creates and show page dots",
            "default": false
          },
          {
            "type": "select",
            "id": "dot_owl",
            "label": "Dots style",
            "default": "default",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "outline",
                "label": "Outline"
              },
              {
                "value": "elessi",
                "label": "Elessi"
              }
            ]
          },
          {
            "type": "select",
            "id": "dots_cl",
            "label": "Dots color",
            "default": "dark",
            "options": [
              {
                  "value": "default",
                  "label": "Default"
              },
              {
                  "value": "light",
                  "label": "Light (Best on dark background)"
              },
              {
                  "value": "dark",
                  "label": "Dark"
              },
              {
                  "value": "primary",
                  "label": "Primary"
              },
              {
                  "value": "custom1",
                  "label": "Custom color 1"
              },
              {
                  "value": "custom2",
                  "label": "Custom color 2"
              }
            ]
          },
          {
            "type": "checkbox",
            "id": "dots_round",
            "label": "Enable dots round",
            "default": true
          },
          {
            "type": "range",
            "id": "dots_space",
            "min": 2,
            "max": 20,
            "step": 1,
            "label": "Dot between horizontal",
            "unit": "px",
            "default": 10
          },
          {
            "type":"checkbox",
            "id":"dots_hidden_mobile",
            "label":"Hidden dots on mobile ",
            "default": false
          },
        {
            "type": "header",
            "content": "3. Design options"
        },
        {
            "type": "select","id": "layout","default": "t4s-container-fluid","label": "Layout",
            "options": [
                { "value": "t4s-se-container", "label": "Container"},
                { "value": "t4s-container-wrap", "label": "Wrapped Container"},
                { "value": "t4s-container-fluid", "label": "Full width"}
            ]
        },
        {
            "type": "color",
            "id": "cl_bg",
            "label": "Background"
        },
        {
            "type": "color_background",
            "id": "cl_bg_gradient",
            "label": "Background gradient"
        },
        {
            "type": "text",
            "id": "mg",
            "label": "Margin",
            "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
            "default": ",,50px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd",
            "label": "Padding",
            "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
            "placeholder": "50px,,50px,"
        },
        {
          "type": "header",
          "content": "+ Design Tablet Options"
        },
        {
          "type": "text",
          "id": "mg_tb",
          "label": "Margin",
          "placeholder": ",,50px,"
        },
        {
          "type": "text",
          "id": "pd_tb",
          "label": "Padding",
          "placeholder": ",,50px,"
        }, 
        {
            "type": "header",
            "content": "+ Design mobile options"
        },
        {
            "type": "text",
            "id": "mg_mb",
            "label": "Margin",
            "default": ",,30px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_mb",
            "label": "Padding",
            "placeholder": ",,50px,"
        }
    ],
    "blocks": [
      {
        "type": "img",
        "name": "Image pin",
        "limit": 16,
        "settings": [
          {
            "type": "image_picker","id": "image","label": "Choose image","info": "1080 x 1080px .jpg recommended"
          },
          {
            "type": "url","id": "url","label": "Link (optional)"
          },
          {
            "type": "header","content": "+ Pin 1 Settings"
          },
          {
             "type": "product","id": "product_1","label": "Choose product (Pin 1)"
          },
          {
            "type":"range","id":"pos_t_1","min":0,"max":100,"step":1,"unit":"%","label":"Position top (Pin 1)","default":50
          },
          {
            "type":"range","id":"pos_l_1","min":0,"max":100,"step":1,"unit":"%","label":"Position left (Pin 1)","default":50
          },
          {
           "type": "select","id": "pos_popup_1","label": "Position (Pin 1)","default": "top",
           "options": [
              { "value": "top-start", "label": "Top start"},
              { "value": "top", "label": "Top"},
              { "value": "top-end", "label": "Top end"},
              { "value": "bottom-start", "label": "Bottom start"},
              { "value": "bottom", "label": "Bottom"},
              { "value": "bottom-end", "label": "Bottom end"},
              { "value": "left-start", "label": "Left start"},
              { "value": "left", "label": "Left"},
              { "value": "left-end", "label": "Left end"},
              { "value": "right-start", "label": "Right start"},
              { "value": "right", "label": "Right"},
              { "value": "right-end", "label": "Right end"}
           ]
          },
          {
           "type": "select","id": "cl_pin_1","label": "Pin color  (Pin 1):","default": "dark",
           "options": [
              { "value": "dark", "label": "Dark"},
              { "value": "light", "label": "Light"}
           ]
          },
          {
            "type": "header","content": "+ Pin 2 Settings"
          },
          {
             "type": "product","id": "product_2","label": "Choose product (Pin 2)"
          },
          {
            "type":"range","id":"pos_t_2","min":0,"max":100,"step":1,"unit":"%","label":"Position top (Pin 2)","default":50
          },
          {
            "type":"range","id":"pos_l_2","min":0,"max":100,"step":1,"unit":"%","label":"Position left (Pin 2)","default":50
          },
          {
           "type": "select","id": "pos_popup_2","label": "Position (Pin 2)","default": "top",
           "options": [
              { "value": "top-start", "label": "Top start"},
              { "value": "top", "label": "Top"},
              { "value": "top-end", "label": "Top end"},
              { "value": "bottom-start", "label": "Bottom start"},
              { "value": "bottom", "label": "Bottom"},
              { "value": "bottom-end", "label": "Bottom end"},
              { "value": "left-start", "label": "Left start"},
              { "value": "left", "label": "Left"},
              { "value": "left-end", "label": "Left end"},
              { "value": "right-start", "label": "Right start"},
              { "value": "right", "label": "Right"},
              { "value": "right-end", "label": "Right end"}
           ]
          },
          {
           "type": "select","id": "cl_pin_2","label": "Pin color  (Pin 2):","default": "dark",
           "options": [
              { "value": "dark", "label": "Dark"},
              { "value": "light", "label": "Light"}
           ]
          },
          {
            "type": "header","content": "+ Pin 3 Settings"
          },
          {
             "type": "product","id": "product_3","label": "Choose product (Pin 3)"
          },
          {
            "type":"range","id":"pos_t_3","min":0,"max":100,"step":1,"unit":"%","label":"Position top (Pin 3)","default":50
          },
          {
            "type":"range","id":"pos_l_3","min":0,"max":100,"step":1,"unit":"%","label":"Position left (Pin 3)","default":50
          },
          {
           "type": "select","id": "pos_popup_3","label": "Position (Pin 3)","default": "top",
           "options": [
              { "value": "top-start", "label": "Top start"},
              { "value": "top", "label": "Top"},
              { "value": "top-end", "label": "Top end"},
              { "value": "bottom-start", "label": "Bottom start"},
              { "value": "bottom", "label": "Bottom"},
              { "value": "bottom-end", "label": "Bottom end"},
              { "value": "left-start", "label": "Left start"},
              { "value": "left", "label": "Left"},
              { "value": "left-end", "label": "Left end"},
              { "value": "right-start", "label": "Right start"},
              { "value": "right", "label": "Right"},
              { "value": "right-end", "label": "Right end"}
           ]
          },
          {
           "type": "select","id": "cl_pin_3","label": "Pin color  (Pin 3):","default": "light",
           "options": [
              { "value": "dark", "label": "Dark"},
              { "value": "light", "label": "Light"}
           ]
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Instagram shop",
        "category": "Social media",
        "blocks": [
          {"type": "img"},{"type": "img"},{"type": "img"},{"type": "img"},{"type": "img"},{"type": "img"}
        ]
      }
    ]
  }
{% endschema %}
