<!-- sections/tabs-collection.liquid -->
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif

  assign search_url = routes.all_products_collection_url
  if se_stts.btn_owl == "outline"
    assign arrow_icon = 1
  else
    assign arrow_icon = 2
  endif
  assign tabs_des = se_stts.tabs_des
  assign tabs_pos = se_stts.tabs_pos
  if tabs_pos == "start"
    assign slide_cellAlign = "left"
  elsif tabs_pos == "end" 
      assign slide_cellAlign = "right"
  else
    assign slide_cellAlign = "center"
  endif
  assign use_link_vendor = settings.use_link_vendor

  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif 
 -%}
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'top-head.css' | asset_url | stylesheet_tag }}
{{ 'tabs.css' | asset_url | stylesheet_tag }}
{{ 'collection-products.css' | asset_url | stylesheet_tag }}
{{ 'slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
<link href="{{ 'loading.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- assign search_terms = search.terms | split: ' ' -%}
{%- if search.terms and search_terms contains 'ntt4tag' -%}
  {%- liquid
    assign num_i = search_terms[0] | remove: '_bid' | plus: 0
    assign block = section.blocks[num_i]
    assign collection = collections[block.settings.collection]
    render 'inc_tab', ck_q: false, section: section, sid: sid, collection: collection, block: block, se_stts: se_stts, arrow_icon: arrow_icon
  -%}
{%- else -%}
<div class="t4s-section-inner t4s_nt_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
    <div class=" t4s-tabs-se tabs-layout-{{ tabs_des }} t4s-tabs-{{ tabs_des }} t4s-item-rounded-{{ se_stts.item_rounded }} t4s-border-{{ se_stts.tabs_border }}" style="--border:{{ se_stts.tabs_border }};--item-cl:{{ se_stts.item_cl }};--item-bg:{{ se_stts.item_bg }};--item-cl-active:{{ se_stts.item_cl_active }};--item-bg-active:{{ se_stts.item_bg_active }};--space-between:{{ se_stts.space_between }}px;--mgb:{{ se_stts.tabslist_mb }}px;">
      {% if tabs_des != "inline" %}
        {%- render 'section_tophead', se_stts: se_stts -%}
      {% endif %}
      <div class="t4s-tabs t4s-type-tabs t4s-text-{{ tabs_pos }}" data-t4s-tabs2>
        {% if tabs_des == "inline" %}
          <div class="t4s-tabs-head">
            {%- if se_stts.top_heading != blank -%}<h3 class="t4s-section-title t4s-title {{ se_stts.heading_align }}"><span>{{ se_stts.top_heading }}</span></h3>{%- endif -%}
        {% endif %}
          <ul data-t4s-tab-ul2 class="t4s-tabs-ul t4s-align-items-center t4s-justify-content-{{ tabs_pos }} t4s-flicky-slider t4s-slider-btn-style-simple t4s-slider-btn-none t4s-slider-btn-small t4s-slider-btn-vi-always flickityt4s" 
           data-flickityt4s-js='{"isSimple": true,"freeScroll": true, "arrowIcon":"1", "imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "minWidthLG": 992, "cellAlignLG":"{{ slide_cellAlign }}", "cellAlign":"center", "wrapAround": false,"prevNextButtons": true,"percentPosition": 0,"pageDots": false, "pauseAutoPlayOnHover" : true }'>  
          {%- for block in se_blocks -%}
            {%- assign bk_stts = block.settings -%}
            {%- assign blockid = block.id -%} 
            <li class="t4s-tab-item t4s-d-inline-flex"><a id="b_{{ block.id }}" href="#t4s-{{ blockid }}" rel="nofollow" data-t4s-tab-item data-no-instant {{ block.shopify_attributes }} {% if forloop.first == true %} class="t4s-active" {% endif %}>
              <span>
                {% if bk_stts.icon_img != blank %} 
                  {% assign image = bk_stts.icon_img %}
                  <span class="t4s-icon-title t4s-icon-img">
                    <img class="lazyloadt4s t4s-lz--fadeIn t4s-obj-eff" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                  </span>
                {% elsif bk_stts.icon_title != blank %}
                  <span class="t4s-icon-title"><i class="{{ bk_stts.icon_title }}"></i></span>
                {% endif %}
                <span class="t4s-text-title">{{ bk_stts.title }}</span>
              </span>
            </a></li>
          {%- endfor -%}
        </ul>
        {% if tabs_des == "inline" %}
          </div>
        {% endif %}
        <div class="t4s-pr t4s-tab-contents2">
          {%- if request.design_mode -%}
            {%- for block in se_blocks -%}
              {%- assign collection = collections[block.settings.collection] -%}
              <div id="t4s-{{ block.id }}" class="t4s-tab-content2 {% if forloop.first == true %} t4s-active {% endif %}" data-t4s-tab-content data-render-lazy-component >
                {%- render 'inc_tab',ck_q:true,section:section,sid: sid,collection:collection, block: block, se_stts: se_stts,arrow_icon:arrow_icon,use_link_vendor:use_link_vendor -%}
              </div>
            {%- endfor -%}
          {%- else -%}
              {%- assign block = section.blocks[0] -%}
              {%- assign collection = collections[block.settings.collection] -%}
              <div id="t4s-{{ block.id }}" class="t4s-tab-content2 t4s-active" data-t4s-tab-content data-render-lazy-component >
                {%- render 'inc_tab',ck_q:true,section:section,sid: sid,collection:collection, block: block, se_stts: se_stts,arrow_icon:arrow_icon,use_link_vendor:use_link_vendor -%}
              </div>
              {%- for block in section.blocks offset:1 -%}
              {%- assign collection = collections[block.settings.collection] -%}
                <div id="t4s-{{ block.id }}" class="t4s-tab-content2 lazyloadt4s" data-t4s-tab-content data-render-lazy-component data-rendert4s="{{ routes.search_url }}?type=article&q={{ forloop.index }}_bid+ntt4tag" data-set4surl='&section_id={{ sid }}' data-t4splitlz><div class="lds_bginfinity t4s-pr"></div></div>
              {%- endfor -%}
          {%- endif -%}
        </div>
      </div>
    </div>
    {{- html_layout[1] -}}
</div>
{%- endif -%}
{%- schema -%}
  {
    "name": "Tabs collection",
    "tag": "section",
    "class": "t4s-section t4s-section-all t4s_bk_flickity t4s_tp_cdt t4s-tabs-collection t4s_tp_tab2",
    "settings": [
      {
          "type": "header",
          "content": "1. Heading options"
      },
      {
          "type": "select",
          "id": "design_heading",
          "label": "+ Design heading",
          "default": "1",
          "options": [
              {
                  "value": "1",
                  "label": "Design 01"
              },
              {
                  "value": "2",
                  "label": "Design 02"
              },
              {
                  "value": "3",
                  "label": "Design 03"
              },
              {
                  "value": "4",
                  "label": "Design 04"
              },
              {
                  "value": "5",
                  "label": "Design 05"
              },
              {
                  "value": "6",
                  "label": "Design 06 (width line-awesome)"
              },
              {
                  "value": "7",
                  "label": "Design 07"
              },
              {
                  "value": "8",
                  "label": "Design 08"
              },
              {
                  "value": "9",
                  "label": "Design 09"
              },
              {
                  "value": "10",
                  "label": "Design 10"
              },
              {
                  "value": "11",
                  "label": "Design 11"
              },
              {
                  "value": "12",
                  "label": "Design 12"
              },
              {
                  "value": "13",
                  "label": "Design 13"
              },
              {
                  "value": "14",
                  "label": "Design 14"
              },
              {
                "value": "15",
                "label": "Design 15"
              },
              {
                "value": "16",
                "label": "Design 16"
              }
          ]
      },
      {
          "type": "select",
          "id": "heading_align",
          "label": "+ Heading align",
          "default": "t4s-text-center",
          "options": [
              {
                  "value": "t4s-text-start",
                  "label": "Left"
              },
              {
                  "value": "t4s-text-center",
                  "label": "Center"
              },
              {
                  "value": "t4s-text-end",
                  "label": "Right"
              }
          ]
      },
      {
          "type": "text",
          "id": "top_heading",
          "label": "+ Heading",
          "default": "Tabs collections"
      },
      {
        "type": "text",
        "id": "icon_heading",
        "label": "Enter a icon name on heading",
        "info": "Only used for design 6 [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
        "default": "las la-gem"
      },
      {
          "type": "textarea",
          "id": "top_subheading",
          "label": "+ Subheading"
      }, 
      {
        "type": "number",
        "id": "tophead_mb",
        "label": "+ Space bottom (px)",
        "info": "The vertical spacing between heading and content",
        "default": 30
      },
      {
        "type": "header",
        "content": "2. General options"
      },
      {
        "type": "select",
        "id": "tabs_des",
        "options": [
          {
            "value": "base",
            "label": "Base"
          },
          {
            "value": "border",
            "label": "Has border (when item active)"
          },
          {
            "value": "border-bg",
            "label": "Has border and background"
          },
          {
            "value": "underline",
            "label": "Has underline (when item active)"
          },
          {
            "value": "divider",
            "label": "Has underline (when item active) and divider"
          },
          {
            "value": "list-underline",
            "label": "Tabs list has underline"
          },
          {
            "value": "inline",
            "label": "Inline"
          }
        ],
        "label": "Tabs design",
        "default": "base"
      },
      {
        "type": "select",
        "id": "tabs_border",
        "label": "Tabs item border",
        "info": "Only working with design has border and inline",
        "default": "solid",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "solid",
            "label": "Solid"
          },
          {
            "value": "dashed",
            "label": "Dashed"
          },
          {
            "value": "dotted",
            "label": "Dotted"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "item_rounded",
        "label": "Tabs item rounded",
        "info":"Only working with design has border and background",
        "default": false
      },
      {
        "type": "color",
        "id": "item_cl",
        "label": "Color item",
        "default": "#222222"
      },
      {
        "type": "color",
        "id": "item_bg",
        "label": "Background/border item",
        "info":"Only working with design has border and background"
      },
      {
        "type": "color",
        "id": "item_cl_active",
        "label": "Color item active",
        "default": "#56CFE1"
      },
      {
        "type": "color",
        "id": "item_bg_active",
        "label": "Background/border item active",
         "info":"Only working with design has border and background",
        "default": "#56CFE1"
      },
      {
        "type": "range",
        "id": "space_between",
        "min": 0,
        "max": 40,
        "step": 1,
        "label": "Space between items",
        "unit": "px",
        "default": 30
      },
      {
        "type": "select",
        "id": "tabs_pos",
        "label": "Tabs List Position",
        "default": "center",
        "options": [
          {
            "value": "start",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "end",
            "label": "Right"
          }
        ]
      },
      {
        "type": "number",
        "id": "tabslist_mb",
        "label": "Tabs list margin bottom",
        "default": 30
      },
      {
        "type": "header",
        "content": "3. Design options"
      },
      {
        "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped container"},
            { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "4. Custom css"
      },
      {
        "id": "use_cus_css",
        "type": "checkbox",
        "label": "Use custom css",
        "default":false,
        "info": "If you want custom style for this section."
      },
      { 
        "id": "code_cus_css",
        "type": "textarea",
        "label": "Code custom css",
        "info": "Use selector .SectionID to style css"
        
      }
    ],
    "blocks": [
      {
        "type": "tab_item",
        "name": "Tab item",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Tab title",
            "default": "Tab title"
          },
          {
            "type": "text",
            "id": "icon_title",
            "label": "Enter a icon name on tab title",
            "info": "[LineAwesome](https://kalles.the4.co/font-lineawesome/)"
          },
          {
            "type": "image_picker",
            "id": "icon_img",
            "label": "Icon image"
          },
          {
              "id": "collection",
              "type": "collection",
              "label": "Collection"
          },
          {
            "type": "select",
            "id": "product_des",
            "options": [
              {
                "value": "1",
                "label": "Design 1"
              },
              {
                "value": "2",
                "label": "Design 2"
              },
              {
                "value": "3",
                "label": "Design 3"
              },
              {
                "value": "4",
                "label": "Design 4"
              },
              {
                "value": "5",
                "label": "Design 5"
              },
              {
                "value": "6",
                "label": "Design 6"
              },
              {
                "value": "7",
                "label": "Design 7"
              },
              {
                "value": "8",
                "label": "Design 8"
              },
              {
                "value": "9",
                "label": "Design 9"
              }
            ],
            "label": "Product item design",
            "default": "1"
          },
          {
            "type": "checkbox",
            "id": "show_vendor",
            "label": "Show product vendors",
            "default": false
          },
          {
            "type": "checkbox",
            "id": "use_cdt",
            "label": "Show product countdown",
            "default": false
          },
          {
            "type": "header",
            "content": "+ Options image products"
          },
          {
            "type": "select",
            "id": "image_ratio",
            "label": "Image ratio",
            "default": "ratioadapt",
            "info": "Aspect ratio custom will settings in general panel",
            "options": [
              {
                "group": "Natural",
                "value": "ratioadapt",
                "label": "Adapt to image"
              },
              {
                "group": "Landscape",
                "value": "ratio2_1",
                "label": "2:1"
              },
              {
                "group": "Landscape",
                "value": "ratio16_9",
                "label": "16:9"
              },
              {
                "group": "Landscape",
                "value": "ratio8_5",
                "label": "8:5"
              },
              {
                "group": "Landscape",
                "value": "ratio3_2",
                "label": "3:2"
              },
              {
                "group": "Landscape",
                "value": "ratio4_3",
                "label": "4:3"
              },
              {
                "group": "Landscape",
                "value": "rationt",
                "label": "Ratio ASOS"
              },
              {
                "group": "Squared",
                "value": "ratio1_1",
                "label": "1:1"
              },
              {
                "group": "Portrait",
                "value": "ratio2_3",
                "label": "2:3"
              },
              {
                "group": "Portrait",
                "value": "ratio1_2",
                "label": "1:2"
              },
              {
                "group": "Custom",
                "value": "ratiocus1",
                "label": "Ratio custom 1"
              },
              {
                "group": "Custom",
                "value": "ratiocus2",
                "label": "Ratio custom 2"
              },
              {
                "group": "Custom",
                "value": "ratio_us3",
                "label": "Ratio custom 3"
              },
              {
                "group": "Custom",
                "value": "ratiocus4",
                "label": "Ratio custom 4"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_size",
            "label": "Image size",
            "default": "cover",
            "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "cover",
                "label": "Full"
              },
              {
                "value": "contain",
                "label": "Auto"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_position",
            "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "1",
                "label": "Left top"
              },
              {
                "value": "2",
                "label": "Left center"
              },
              {
                "value": "3",
                "label": "Left bottom"
              },
              {
                "value": "4",
                "label": "Right top"
              },
              {
                "value": "5",
                "label": "Right center"
              },
              {
                "value": "6",
                "label": "Right bottom"
              },
              {
                "value": "7",
                "label": "Center top"
              },
              {
                "value": "8",
                "label": "Center center"
              },
              {
                "value": "9",
                "label": "Center bottom"
              }
            ],
            "label": "Image position",
            "default": "8"
          },
          {
            "type": "select",
            "id": "content_align",
            "label": "Product content align",
            "default": "start",
            "options": [
              {
                "label": "Default",
                "value": "start"
              },
              {
                "label": "Center",
                "value": "center"
              }
            ]
          },
          {
            "type": "range",
            "id": "limit",
            "min": 1,
            "max": 50,
            "step": 1,
            "label": "Maximum products to show",
            "default": 8
          },
          {
            "type": "select",
            "id": "col_dk",
            "label": "Items per row",
            "default": "4",
            "options": [
              {
                "value": "1",
                "label": "1"
              },
              {
                "value": "2",
                "label": "2"
              },
              {
                "value": "3",
                "label": "3"
              },
              {
                "value": "4",
                "label": "4"
              },
              {
                "value": "5",
                "label": "5"
              },
              {
                "value": "6",
                "label": "6"
              }
            ]
          },
          {
            "type": "select",
            "id": "col_tb",
            "label": "Items per row (Tablet)",
            "default": "2",
            "options": [
              {
                "value": "1",
                "label": "1"
              },
              {
                "value": "2",
                "label": "2"
              },
              {
                "value": "3",
                "label": "3"
              },
              {
                "value": "4",
                "label": "4"
              }
            ]
          },
          {
            "type": "select",
            "id": "col_mb",
            "label": "Items per row (Mobile)",
            "default": "2",
            "options": [
              {
                "value": "1",
                "label": "1"
              },
              {
                "value": "2",
                "label": "2"
              }
            ]
          },
          {
            "type": "select",
            "id": "space_h_item",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15", 
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space horizontal items",
            "default": "30"
          },
          {
            "type": "select",
            "id": "space_v_item",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15", 
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space vertical items",
            "default": "30"
          },
          {
            "type": "select",
            "id": "space_h_item_mb",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15", 
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space horizontal items (Mobile)",
            "default": "10"
          },
          {
            "type": "select",
            "id": "space_v_item_mb",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15", 
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space vertical items (Mobile)",
            "default": "10"
          },
          {
            "type": "header",
            "content": "--Box options--"
          },
          {
            "type": "select",
            "id": "layout_des",
            "options": [
              {
                "value": "1",
                "label": "Grid"
              },
              {
                "value": "2",
                "label": "Carousel"
              },
              {
                "value": "3",
                "label": "Masonry"
              }
            ],
            "label": "Layout design",
            "default": "2"
          },
          {
            "type": "header",
            "content": "+Options for carousel layout"
          },
          {
            "type": "checkbox",
            "id": "loop",
            "label": "Enable loop",
            "info": "At the end of cells, wrap-around to the other end for infinite scrolling",
            "default": true
          },
          {
            "type": "range",
            "id": "au_time",
            "min": 0,
            "max": 30,
            "step": 0.5,
            "label": "Autoplay speed in second.",
            "info": "Set is '0' to disable autoplay",
            "unit": "s",
            "default": 0
          },
          {
            "type": "checkbox",
            "id": "au_hover",
            "label": "Pause autoplay on hover",
            "info": "Auto-playing will pause when the user hovers over the carousel",
            "default": true
          },
          {
            "type": "paragraph",
            "content": "—————————————————"
          },
          {
            "type": "paragraph",
            "content": "Prev next button"
          },
          {
            "type": "checkbox",
            "id": "nav_btn",
            "label": "Use prev next button",
            "info": "Creates and show previous & next buttons",
            "default": false
          },
          {
            "type": "select",
            "id": "btns_pos",
            "label": "Buttons position",
            "default": "default",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "middle-border",
                "label": "Middle border"
              }
            ]
          },
          {
            "type": "select",
            "id": "btn_vi",
            "label": "Visible",
            "default": "hover",
            "options": [
              {
                "value": "always",
                "label": "Always"
              },
              {
                "value": "hover",
                "label": "Only hover"
              }
            ]
          },
          {
            "type": "select",
            "id": "btn_owl",
            "label": "Button style",
            "default": "default",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "outline",
                "label": "Outline"
              },
              {
                "value": "simple",
                "label": "Simple"
              }
            ]
          },
          {
            "type": "select",
            "id": "btn_shape",
            "label": "Button shape",
            "info": "Not working with button style 'Simple'",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "Default"
              },
              {
                "value": "round",
                "label": "Round"
              },
              {
                "value": "rotate",
                "label": "Rotate"
              }
            ]
          },
          {
              "type": "select",
              "id": "btn_cl",
              "label": "Button color",
              "default": "dark",
              "options": [
                  {
                      "value": "light",
                      "label": "Light"
                  },
                  {
                      "value": "dark",
                      "label": "Dark"
                  },
                  {
                      "value": "primary",
                      "label": "Primary"
                  },
                  {
                      "value": "custom1",
                      "label": "Custom color 1"
                  },
                  {
                      "value": "custom2",
                      "label": "Custom color 2"
                  }
              ]
          },
          {
            "type": "select",
            "id": "btn_size",
            "label": "Button size",
            "default": "small",
            "options": [
              {
                "value": "small",
                "label": "Small"
              },
              {
                "value": "medium",
                "label": "Medium"
              },
              {
                "value": "large",
                "label": "Large"
              }
            ]
          },
          {
            "type":"checkbox",
            "id":"btn_hidden_mobile",
            "label":"Hidden buttons on mobile ",
            "default": true
          },
          {
            "type": "paragraph",
            "content": "—————————————————"
          },
          {
            "type": "paragraph",
            "content": "Page dots"
          },
          {
            "type": "checkbox",
            "id": "nav_dot",
            "label": "Use page dots",
            "info": "Creates and show page dots",
            "default": false
          },
          {
            "type": "select",
            "id": "dot_owl",
            "label": "Dots style",
            "default": "default",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "outline",
                "label": "Outline"
              },
              {
                "value": "elessi",
                "label": "Elessi"
              }
            ]
          },
          {
            "type": "select",
            "id": "dots_cl",
            "label": "Dots color",
            "default": "dark",
            "options": [
              {
                  "value": "light",
                  "label": "Light (Best on dark background)"
              },
              {
                  "value": "dark",
                  "label": "Dark"
              },
              {
                  "value": "primary",
                  "label": "Primary"
              },
              {
                  "value": "custom1",
                  "label": "Custom color 1"
              },
              {
                  "value": "custom2",
                  "label": "Custom color 2"
              }
            ]
          },
          {
            "type": "checkbox",
            "id": "dots_round",
            "label": "Enable dots round",
            "default": true
          },
          {
            "type": "range",
            "id": "dots_space",
            "min": 2,
            "max": 20,
            "step": 1,
            "label": "Dot between horizontal",
            "unit": "px",
            "default": 10
          },
          {
            "type":"checkbox",
            "id":"dots_hidden_mobile",
            "label":"Hidden dots on mobile ",
            "default": false
          },
          {
            "type": "header",
            "content": "+Options for grid or masonry layout"
          },
          {
            "type": "select",
            "id": "use_pagination",
            "label": "Pagination",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "None"
              },
              {
                "value": "load-more",
                "label": "'Load more' button"
              },
              {
                "value": "view-all",
                "label": "'View all' button"
              }
            ]
          },
          {
            "type": "checkbox",
            "id": "enable_bar_lm",
            "label": "Enable progress bar",
            "info": "Only active when you use 'Load more' or 'Infinit scrolling'",
            "default": true
          },
          {
            "type":"checkbox",
            "id":"btn_icon",
            "label":"Enable button icon",
            "default":false
          },
          {
            "type": "select",
            "id": "button_style",
            "label": "Button style",
            "options": [
                {
                    "label": "Default",
                    "value": "default"
                },
                {
                    "label": "Outline",
                    "value": "outline"
                },
                {
                    "label": "Bordered bottom",
                    "value": "bordered"
                },
                {
                    "label": "Link",
                    "value": "link"
                }
            ]
          },
          {
            "type": "select",
            "id": "btns_size",
            "label": "Button size",
            "default":"large",
            "options": [
                {
                    "label": "Extra small",
                    "value": "small"
                },
                {
                    "label": "Small",
                    "value": "extra-small"
                },
                {
                    "label": "Medium",
                    "value": "medium"
                },
                {
                    "label": "Large",
                    "value": "extra-medium"
                },
                {
                    "label": "Extra large",
                    "value": "large"
                },
                {
                    "label": "Extra extra large",
                    "value": "extra-large"
                }
            ]
          },
          {
            "type": "select",
            "id": "btns_cl",
            "label": "Button color",
            "default": "dark",
            "options": [
              {
                  "value": "light",
                  "label": "Light"
              },
              {
                  "value": "dark",
                  "label": "Dark"
              },
              {
                  "value": "primary",
                  "label": "Primary"
              },
              {
                  "value": "custom1",
                  "label": "Custom color 1"
              },
              {
                  "value": "custom2",
                  "label": "Custom color 2"
              }
            ]
          },
          {
            "type":"select",
            "id":"button_effect",
            "label":"Button hover effect",
            "default":"default",
            "info":"Only working button style default, outline",
            "options":[
                {
                    "label":"Default",
                    "value":"default"
                },
                {
                    "label":"Fade",
                    "value":"fade"
                },
                {
                    "label":"Rectangle out",
                    "value":"rectangle-out"
                },
                {
                    "label":"Sweep to right",
                    "value":"sweep-to-right"
                },
                {
                    "label":"Sweep to left",
                    "value":"sweep-to-left"
                },
                {
                    "label":"Sweep to bottom",
                    "value":"sweep-to-bottom"
                },
                {
                    "label":"Sweep to top",
                    "value":"sweep-to-top"
                },
                {
                    "label":"Shutter out horizontal",
                    "value":"shutter-out-horizontal"
                },
                {
                    "label":"Outline",
                    "value":"outline"
                },
                {
                    "label":"Shadow",
                    "value":"shadow"
                }
            ]
          },
          {
            "type": "select",
            "id": "btn_pos",
            "label": "Button position",
            "default": "t4s-text-center",
            "options": [
              {
                "value": "t4s-text-start",
                "label": "Left"
              },
              {
                "value": "t4s-text-center",
                "label": "Center"
              },
              {
                "value": "t4s-text-end",
                "label": "Right"
              }
            ]
          }
        ]
      }
    ],
  "presets": [
      {
        "name": "Tabs collection",
        "category": "Homepage",
        "blocks": [
          { "type": "tab_item",
            "settings": {
              "title": "Tab 01"
            }
          },
          { "type": "tab_item",
            "settings": {
              "title": "Tab 02"
            }
          },
          { "type": "tab_item",
            "settings": {
              "title": "Tab 03"
            }
          }
        ]
      }
    ]
  }
{%- endschema -%}

{%- javascript -%}
{%- endjavascript -%}