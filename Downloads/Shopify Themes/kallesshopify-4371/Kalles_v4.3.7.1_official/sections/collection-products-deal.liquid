<!-- sections/featured-products-deal.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'collection-products.css' | asset_url | stylesheet_tag }}
{{ 'slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'products-deal.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  assign image_ratio = se_stts.image_ratio
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif

  assign image_ratio = se_stts.image_ratio
  if image_ratio == "ratioadapt"
    assign imgatt = ''
   else 
    assign imgatt = 'data-'
  endif
  assign collection = collections[se_stts.collection]
  assign use_pagination = se_stts.use_pagination 
  assign sett_equal = se_stts.use_eq_height
  assign show_vendor = se_stts.show_vendor
  assign use_link_vendor = settings.use_link_vendor
  assign enable_rating = settings.enable_rating
  assign inc_pr = se_stts.pr_des
  assign limit = se_stts.limit
  assign product_des = se_stts.product_des
  if se_stts.btn_owl == "outline"
    assign arrow_icon = 1
  else
    assign arrow_icon = 2
  endif

  assign section_layout = se_stts.section_layout

  assign show_img = settings.show_img
  assign isGrowaveWishlist = false
  if settings.wishlist_mode == "3" and shop.customer_accounts_enabled
    assign isGrowaveWishlist = true
  endif
  assign enable_pr_size = settings.enable_pr_size
  assign pr_size_pos = settings.pr_size_pos
  assign show_size_type = settings.show_size_type
  assign size_ck = settings.size_ck | append: ',size,sizes,Größe' 
  assign get_size = size_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

  assign enable_pr_color = settings.enable_pr_color
  assign show_cl_type = settings.show_color_type
  assign color_ck = settings.color_ck | append: ',color,colors,couleur,colour'
  assign get_color = color_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

  assign price_varies_style = settings.price_varies_style
  assign app_review = settings.app_review
  assign use_countdown = se_stts.use_cdt
  
  assign txt_cd = 'products.grid_items.offer_end_in' | t 

  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif 
 -%} 
<div class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }} {%- if stt_image_bg != blank and stt_layout != 't4s-se-container' -%}  t4s-has-imgbg lazyloadt4s {%- endif -%} "  {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %}  {% render 'section_style', se_stts: se_stts %}>
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner {% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s {% endif %} "  {% if stt_image_bg != blank %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} > {%- endif -%}
    <div class="t4s-box-wrap t4s-box-products-deals t4s-layout-{{ section_layout }} border-{{ se_stts.layout_border }}" style="--tophead_mb: {{ se_stts.tophead_mb }}px;--bd-cl:{{ se_stts.box_border_cl }};--bdr:{{ se_stts.box_border_r }}px;--time-cl: {{ se_stts.cl_text }};--time-bg-cl: {{ se_stts.bg_cdt }};">
      <div timeline hdt-reveal="slide-in" class="t4s-top-heading {{ se_stts.heading_align }}"  style="--tophead_mb: {{ se_stts.tophead_mb }}px;" timeline hdt-reveal="slide-in">
          <div class="top-inner">
             {%- if se_stts.top_heading != blank -%} 
              <h3 class="t4s-section-title t4s-title"><span>{{ se_stts.top_heading }}</span></h3>
             {%- endif -%} 
             {%- if se_stts.date != blank -%} 
              <div class="t4s-time-box">
                {% if  section_layout != "3" %}
                  {%- if se_stts.cd_txt != blank -%} 
                    <span class="t4s-countdown-title">{{ se_stts.cd_txt }}</span>
                  {%- endif -%} 
                  <div class="t4s-countdown-sale">
                    <div class="time" data-countdown-t4s data-date='{{ se_stts.date }}'>
                      <span class="countdown-days">
                        <span class="cd_timet4 cd-number">%-D</span>
                        <span class="cd_txtt4 cd-text">%!D:{{ "sections.countdown_text.day" | t }},{{ "sections.countdown_text.day_plural" | t }}; </span>
                      </span>
                      <span class="countdown-hours">
                        <span class="cd_timet4 cd-number">%H:</span>
                      </span>
                      <span class="countdown-min">
                        <span class="cd_timet4 cd-number">%M:</span> 
                      </span>
                      <span class="countdown-sec">
                        <span class="cd_timet4 cd-number">%S</span> 
                      </span>
                    </div>
                  </div>
                {% else %}
                  {%- if se_stts.cd_txt != blank -%} 
                    <span class="t4s-countdown-title">{{ se_stts.cd_txt }}</span>
                  {%- endif -%} 
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-clock"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  <div class="t4s-countdown-sale">
                    <div class="time" data-countdown-t4s data-date='{{ se_stts.date }}'>
                      <span class="countdown-hours">
                        <span class="cd_timet4 cd-number">%Hh:</span>
                      </span>
                      <span class="countdown-min">
                        <span class="cd_timet4 cd-number">%Mm:</span> 
                      </span>
                      <span class="countdown-sec">
                        <span class="cd_timet4 cd-number">%Ss</span> 
                      </span>
                    </div>
                  </div>
                {% endif %}
              </div>
             {%- endif -%} 
          </div>
      </div>
     {%- if se_stts.layout_des == "1" -%} 
      <div data-collection-url="{{ collection.url }}" class="t4s_box_pr_grid t4s-products  t4s-justify-content-center t4s-text-{{ se_stts.content_align }} t4s_{{ image_ratio }} t4s_position_{{ se_stts.image_position }} t4s_{{ se_stts.image_size }}  t4s-row t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }} t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }}">
     {%- else -%} 
      <div data-collection-url="{{ collection.url }}" data-t4s-resizeobserver class="t4s-flicky-slider t4s_box_pr_slider t4s-products t4s-text-{{ se_stts.content_align }} t4s_{{ image_ratio }} t4s_position_{{ se_stts.image_position }} t4s_{{ se_stts.image_size }} {% if se_stts.nav_btn %}  t4s-slider-btn-style-{{ se_stts.btn_owl }} t4s-slider-btn-{{ se_stts.btn_shape }} t4s-slider-btn-{{ se_stts.btn_size }} t4s-slider-btn-cl-{{ se_stts.btn_cl }} t4s-slider-btn-vi-{{ se_stts.btn_vi }} t4s-slider-btn-hidden-mobile-{{ se_stts.btn_hidden_mobile }} {% endif %} {% if se_stts.nav_dot == true %} t4s-dots-style-{{ se_stts.dot_owl }} t4s-dots-cl-{{ se_stts.dots_cl }} t4s-dots-round-{{ se_stts.dots_round }} t4s-dots-hidden-mobile-{{ se_stts.dots_hidden_mobile }} {% endif %}  t4s-row t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }} t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }}  flickityt4s" data-flickityt4s-js='{"setPrevNextButtons":true,"arrowIcon":"{{ arrow_icon }}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": {{ se_stts.loop }},"prevNextButtons": {{ se_stts.nav_btn }},"percentPosition": 1,"pageDots": {{ se_stts.nav_dot }}, "autoPlay" : {{ se_stts.au_time | times: 1000 }}, "pauseAutoPlayOnHover" : {{ se_stts.au_hover }} }' style="--space-dots: {{ se_stts.dots_space }}px;--flickity-btn-pos: {{ se_stts.space_h_item }}px;--flickity-btn-pos-mb: {{ se_stts.space_h_item_mb }}px;">
     {%- endif -%} 
      {%- if se_blocks.size > 0 -%}
        {%- assign txt_sold = se_stts.txt_sold -%}
        {%- assign txt_avai = se_stts.txt_avai -%}
        {%- case product_des -%}
          {%- when '1' -%} 
            {%- for block in se_blocks limit:se_stts.pr_limit -%}
              {%- liquid 
                assign bk_stts = block.settings
                assign link = bk_stts.link_product
                if link.type == "product_link" and link != blank
                  assign product = link.object
                  if product.id == blank
                  continue
                  endif
                else
                  continue
                endif
                assign ck_noPr = false
                assign sold = bk_stts.total | minus: bk_stts.available | times: 100 | divided_by: bk_stts.total | ceil -%}
                {% if se_stts.show_stock_bar %}
                  {%- capture append_stock -%}
                    <div class="loop-t4s-pr-stock"  style="--stock-cl1:{{ se_stts.cl_stock_1 }};--stock-cl2:{{ se_stts.cl_stock_2 }}">
                      <div class="status-bar">
                         <div class="sold-bar" style="width: {{ sold }}%"></div>
                      </div>
                      <div class="t4s-pr-stock-status">
                        <div class="sold"> <span class="label">{{ txt_sold }} </span> <span class="value">{{ bk_stts.total | minus: bk_stts.available }}<span></span></span></div>
                        <div class="available"> <span class="label">{{ txt_avai }} </span> <span class="value">{{ bk_stts.available }}<span></span></span></div>
                      </div>
                    </div>
                  {%- endcapture -%}
                {% endif %}
              {% if isHasCollection %}
              {%- assign pr_url = product.url | within:se_blocks %}
              {% else %}{%- assign pr_url = product.url %}{% endif -%}
              {%- render 'product-deal-item1', product: product, pr_url: pr_url, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type,get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating,se_countdown: use_countdown, imgatt: imgatt, append_stock: append_stock -%}
            {%- endfor -%}
          {%- when '2' -%}
            {%- for block in se_blocks limit:se_stts.pr_limit -%}
              {%- liquid 
                assign bk_stts = block.settings
                assign link = bk_stts.link_product
                if link.type == "product_link" and link != blank
                  assign product = link.object
                  if product.id == blank
                  continue
                  endif
                else
                  continue
                endif
                assign ck_noPr = false
                assign sold = bk_stts.total | minus: bk_stts.available | times: 100 | divided_by: bk_stts.total | ceil -%}
                {% if se_stts.show_stock_bar %}
                  {%- capture append_stock -%}
                    <div class="loop-t4s-pr-stock"  style="--stock-cl1:{{ se_stts.cl_stock_1 }};--stock-cl2:{{ se_stts.cl_stock_2 }}" timeline hdt-reveal="slide-in">
                      <div class="status-bar">
                         <div class="sold-bar" style="width: {{ sold }}%"></div>
                      </div>
                      <div class="t4s-pr-stock-status">
                        <div class="sold"> <span class="label">{{ txt_sold }} </span> <span class="value">{{ bk_stts.total | minus: bk_stts.available }}<span></span></span></div>
                        <div class="available"> <span class="label">{{ txt_avai }} </span> <span class="value">{{ bk_stts.available }}<span></span></span></div>
                      </div>
                    </div>
                  {%- endcapture -%}
                {% endif %}
              {% if isHasCollection %}
              {%- assign pr_url = product.url | within:se_blocks %}
              {% else %}{%- assign pr_url = product.url %}{% endif -%}
              {%- render 'product-deal-item2',
                product: product, pr_url: pr_url, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, append_stock: append_stock -%}
            {%- endfor -%}
          {%- when '3' -%}
            {%- for block in se_blocks limit:se_stts.pr_limit -%}
              {%- liquid 
                assign bk_stts = block.settings
                assign link = bk_stts.link_product
                if link.type == "product_link" and link != blank
                  assign product = link.object
                  if product.id == blank
                  continue
                  endif
                else
                  continue
                endif
                assign ck_noPr = false
                assign sold = bk_stts.total | minus: bk_stts.available | times: 100 | divided_by: bk_stts.total | ceil -%}
              {% if se_stts.show_stock_bar %}
                  {%- capture append_stock -%}
                    <div class="loop-t4s-pr-stock"  style="--stock-cl1:{{ se_stts.cl_stock_1 }};--stock-cl2:{{ se_stts.cl_stock_2 }}" timeline hdt-reveal="slide-in">
                      <div class="status-bar">
                         <div class="sold-bar" style="width: {{ sold }}%"></div>
                      </div>
                      <div class="t4s-pr-stock-status">
                        <div class="sold"> <span class="label">{{ txt_sold }} </span> <span class="value">{{ bk_stts.total | minus: bk_stts.available }}<span></span></span></div>
                        <div class="available"> <span class="label">{{ txt_avai }} </span> <span class="value">{{ bk_stts.available }}<span></span></span></div>
                      </div>
                    </div>
                  {%- endcapture -%}
                {% endif %}
              {% if isHasCollection %}
              {%- assign pr_url = product.url | within:se_blocks %}
              {% else %}{%- assign pr_url = product.url %}{% endif -%}
              {%- render 'product-grid-item1', product: product, pr_url: pr_url, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, append_stock: append_stock -%}
            {%- endfor -%}
          {%- when '4' -%} 
            {%- for block in se_blocks limit:se_stts.pr_limit -%}
              {%- liquid 
                assign bk_stts = block.settings
                assign link = bk_stts.link_product
                if link.type == "product_link" and link != blank
                  assign product = link.object
                  if product.id == blank
                  continue
                  endif
                else
                  continue
                endif
                assign ck_noPr = false
                assign sold = bk_stts.total | minus: bk_stts.available | times: 100 | divided_by: bk_stts.total | ceil -%}
                {% if se_stts.show_stock_bar %}
                  {%- capture append_stock -%}
                    <div class="loop-t4s-pr-stock"  style="--stock-cl1:{{ se_stts.cl_stock_1 }};--stock-cl2:{{ se_stts.cl_stock_2 }}" timeline hdt-reveal="slide-in">
                      <div class="status-bar">
                         <div class="sold-bar" style="width: {{ sold }}%"></div>
                      </div>
                      <div class="t4s-pr-stock-status">
                        <div class="sold"> <span class="label">{{ txt_sold }} </span> <span class="value">{{ bk_stts.total | minus: bk_stts.available }}<span></span></span></div>
                        <div class="available"> <span class="label">{{ txt_avai }} </span> <span class="value">{{ bk_stts.available }}<span></span></span></div>
                      </div>
                    </div>
                  {%- endcapture -%}
                {% endif %}
              {% if isHasCollection %}
              {%- assign pr_url = product.url | within:se_blocks %}
              {% else %}{%- assign pr_url = product.url %}{% endif -%}
              {%- render 'product-deal-item3', product: product, pr_url: pr_url, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, append_stock: append_stock -%}
            {%- endfor -%}
        {%- endcase -%}
      {%- else -%}
        {%- for i in (1..8) limit:se_stts.pr_limit -%}
          <div class="t4s-col-item t4s-pr-item t4s-pr-des-{{ se_stts.product_des }} pr-{{ se_stts.content_align }}">
            <div class="t4s-product-wrapper">
              <div class="t4s-product-inner">
                <a class="db" href="/admin/products">{%- capture current -%}  {%- cycle 1, 2, 3, 4, 5, 6 -%}  {%- endcapture -%} {{ 'product-' | append: current | placeholder_svg_tag: 't4s-placeholder-svg' }}</a>
              </div>
              <div class="t4s-product-info">
                <h3 class="t4s-product-title"><a href="/admin/products">{{ 'onboarding.product_title' | t }}</a></h3>
                <span class="t4s-product-price"><del>$59.00</del><ins>$39.00</ins></span>
              </div>
            </div>
            {%- assign sold = bk_stts.total | minus: bk_stts.available | times: 100 | divided_by: bk_stts.total | ceil -%}
            {% if se_stts.show_stock_bar %}
              {%- capture append_stock -%}
                <div class="loop-t4s-pr-stock"  style="--stock-cl1:{{ se_stts.cl_stock_1 }};--stock-cl2:{{ se_stts.cl_stock_2 }}">
                  <div class="status-bar">
                     <div class="sold-bar" style="width: {{ sold }}%"></div>
                  </div>
                  <div class="t4s-pr-stock-status">
                    <div class="sold"> <span class="label">{{ txt_sold }} </span> <span class="value">{{ bk_stts.total | minus: bk_stts.available }}<span></span></span></div>
                    <div class="available"> <span class="label">{{ txt_avai }} </span> <span class="value">{{ bk_stts.available }}<span></span></span></div>
                  </div>
                </div>
              {%- endcapture -%}
            {% endif %}
          </div>
        {%- endfor -%}
      {%- endif -%}
    </div>
    </div>
    {{- html_layout[1] -}}
  </div>
{%- schema -%}
  {
    "name": "Products deals",
    "tag": "section",
    "class": "t4s-section t4s_bk_flickity t4s-section-all t4s_tp_cdt t4s_tp_cd t4s-products-deals",
    "settings": [
      {
        "type": "header",
        "content": "1. Heading options"
      },
      {
        "type": "select",
        "id": "heading_align",
        "label": "Heading align",
        "default": "t4s-text-center",
        "options": [
          {
            "value": "t4s-text-start",
            "label": "Left"
          },
          {
            "value": "t4s-text-center",
            "label": "Center"
          },
          {
            "value": "t4s-text-end",
            "label": "Right"
          }
        ]
      },
      {
          "type": "text",
          "id": "top_heading",
          "label": "Top Title",
          "default": "Product deals of the day"
      },
      {
        "type": "header",
        "content": "----------------------"
      },
      {
        "type": "text",
        "id": "cd_txt",
        "label": "Countdown text",
        "default": "End in:"
      },
      {
       "type": "text",
       "id": "date",
       "label": "Data countdown",
       "default": "2023\/12\/20",
       "info": "Countdown to the end sale date will be shown."
      },
      {
        "type": "color",
        "id": "cl_text", 
        "label": "Countdown content color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "id": "bg_cdt",
        "label": "Countdown background color:",
        "default": "#e4573d"
      },
      {
        "type": "number",
        "id": "tophead_mb",
        "label": "+ Space bottom (px)",
        "info": "The vertical spacing between heading and content",
        "default": 30
      },
      {
        "type": "header",
        "content": "2. General options"
      },
      {
        "type": "select",
        "id": "section_layout",
        "label": "Section layout",
        "default": "1",
        "options": [
          {
            "value": "1",
            "label": "Layout 01"
          },
          {
            "value": "2",
            "label": "Layout 02"
          },
          {
            "value": "3",
            "label": "Layout 03"
          }
        ]
      },
      {
       "type": "checkbox",
       "id": "layout_border",
       "label": "Show border layout",
       "default": true
      },
      {
        "type": "range",
        "id": "box_border_r",
        "min": 0,
        "max": 50,
        "step": 1,
        "label": "Border radius",
        "unit": "px",
        "default": 5
      },
      {
        "type": "color",
        "id": "box_border_cl", 
        "label": "Layout border color",
        "default": "#4e97fd"
      },
      {
        "type": "select",
        "id": "product_des",
        "options": [
          {
            "value": "1",
            "label": "Design 1"
          },
          {
            "value": "2",
            "label": "Design 2"
          },
          {
            "value": "3",
            "label": "Design 3"
          },
          {
            "value": "4",
            "label": "Design 4"
          }
        ],
        "label": "Product item design",
        "default": "1"
      },
      {
        "type": "checkbox",
        "id": "show_vendor",
        "label": "Show product vendors",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "use_cdt",
        "label": "Show product countdown",
        "default": false
      },
      {
        "type": "header",
        "content": "+ Options image products"
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Image ratio",
        "default": "ratioadapt",
        "info": "Aspect ratio custom will settings in general panel",
        "options": [
          {
            "group": "Natural",
            "value": "ratioadapt",
            "label": "Adapt to image"
          },
          {
            "group": "Landscape",
            "value": "ratio2_1",
            "label": "2:1"
          },
          {
            "group": "Landscape",
            "value": "ratio16_9",
            "label": "16:9"
          },
          {
            "group": "Landscape",
            "value": "ratio8_5",
            "label": "8:5"
          },
          {
            "group": "Landscape",
            "value": "ratio3_2",
            "label": "3:2"
          },
          {
            "group": "Landscape",
            "value": "ratio4_3",
            "label": "4:3"
          },
          {
            "group": "Landscape",
            "value": "rationt",
            "label": "Ratio ASOS"
          },
          {
            "group": "Squared",
            "value": "ratio1_1",
            "label": "1:1"
          },
          {
            "group": "Portrait",
            "value": "ratio2_3",
            "label": "2:3"
          },
          {
            "group": "Portrait",
            "value": "ratio1_2",
            "label": "1:2"
          },
          {
            "group": "Custom",
            "value": "ratiocus1",
            "label": "Ratio custom 1"
          },
          {
            "group": "Custom",
            "value": "ratiocus2",
            "label": "Ratio custom 2"
          },
          {
            "group": "Custom",
            "value": "ratio_us3",
            "label": "Ratio custom 3"
          },
          {
            "group": "Custom",
            "value": "ratiocus4",
            "label": "Ratio custom 4"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_size",
        "label": "Image size",
        "default": "cover",
        "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "cover",
            "label": "Full"
          },
          {
            "value": "contain",
            "label": "Auto"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "1",
            "label": "Left top"
          },
          {
            "value": "2",
            "label": "Left center"
          },
          {
            "value": "3",
            "label": "Left bottom"
          },
          {
            "value": "4",
            "label": "Right top"
          },
          {
            "value": "5",
            "label": "Right center"
          },
          {
            "value": "6",
            "label": "Right bottom"
          },
          {
            "value": "7",
            "label": "Center top"
          },
          {
            "value": "8",
            "label": "Center center"
          },
          {
            "value": "9",
            "label": "Center bottom"
          }
        ],
        "label": "Image position",
        "default": "8"
      },
      {
        "type": "select",
        "id": "content_align",
        "label": "Product content align",
        "default": "default",
        "options": [
          {
            "label": "Default",
            "value": "default"
          },
          {
            "label": "Center",
            "value": "center"
          }
        ]
      },
      {
        "type": "range",
        "id": "pr_limit",
        "min": 1,
        "max": 50,
        "step": 1,
        "label": "Maximum products to show",
        "default": 8
      },
      {
        "type": "select",
        "id": "col_dk",
        "label": "Items per row",
        "default": "4",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          },
          {
            "value": "5",
            "label": "5"
          },
          {
            "value": "6",
            "label": "6"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_tb",
        "label": "Items per row (Tablet)",
        "default": "2",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_mb",
        "label": "Items per row (Mobile)",
        "default": "2",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          }
        ]
      },
      {
        "type": "select",
        "id": "space_h_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_v_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_h_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items (Mobile)",
        "default": "10"
      },
      {
        "type": "select",
        "id": "space_v_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items (Mobile)",
        "default": "10"
      },
      {
        "type": "header",
        "content": "+ stock Bar"
      },
      {
       "type": "checkbox",
       "id": "show_stock_bar",
       "label": "Show stock bar",
       "default": true
      },
      {
        "type": "text",
        "id": "txt_sold",
        "label": "Text sold",
        "default": "Sold:"
      },
      {
        "type": "text",
        "id": "txt_avai",
        "label": "Text available",
        "default": "Available:"
      },
      {
        "type": "color",
        "id": "cl_stock_1",
        "label": "Background color 1:",
        "default": "#4e97fd"
      },
      {
        "type": "color",
        "id": "cl_stock_2",
        "label": "Background color 2:",
        "default": "#77ccfd"
      },
      {
        "type": "header",
        "content": "--Box options--"
      },

      {
        "type": "select",
        "id": "layout_des",
        "options": [
          {
            "value": "1",
            "label": "Grid"
          },
          {
            "value": "2",
            "label": "Carousel"
          }
        ],
        "label": "Layout design",
        "default": "2"
      },
      {
        "type": "header",
        "content": "+Options for carousel layout"
      },
      {
        "type": "checkbox",
        "id": "loop",
        "label": "Enable loop",
        "info": "At the end of cells, wrap-around to the other end for infinite scrolling",
        "default": true
      },
      {
        "type": "range",
        "id": "au_time",
        "min": 0,
        "max": 30,
        "step": 0.5,
        "label": "Autoplay speed in second.",
        "info": "Set is '0' to disable autoplay",
        "unit": "s",
        "default": 0
      },
      {
        "type": "checkbox",
        "id": "au_hover",
        "label": "Pause autoplay on hover",
        "info": "Auto-playing will pause when the user hovers over the carousel",
        "default": true
      },
      {
        "type": "paragraph",
        "content": "—————————————————"
      },
      {
        "type": "paragraph",
        "content": "Prev next button"
      },
      {
        "type": "checkbox",
        "id": "nav_btn",
        "label": "Use prev next button",
        "info": "Creates and show previous & next buttons",
        "default": false
      },
      {
        "type": "select",
        "id": "btn_vi",
        "label": "Visible",
        "default": "hover",
        "options": [
          {
            "value": "always",
            "label": "Always"
          },
          {
            "value": "hover",
            "label": "Only hover"
          }
        ]
      },
      {
        "type": "select",
        "id": "btn_owl",
        "label": "Button style",
        "default": "default",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "outline",
            "label": "Outline"
          },
          {
            "value": "simple",
            "label": "Simple"
          }
        ]
      },
      {
        "type": "select",
        "id": "btn_shape",
        "label": "Button shape",
        "info": "Not working with button style 'Simple'",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "Default"
          },
          {
            "value": "round",
            "label": "Round"
          },
          {
            "value": "rotate",
            "label": "Rotate"
          }
        ]
      },
      {
          "type": "select",
          "id": "btn_cl",
          "label": "Button color",
          "default": "dark",
          "options": [
              {
                  "value": "light",
                  "label": "Light"
              },
              {
                  "value": "dark",
                  "label": "Dark"
              },
              {
                  "value": "primary",
                  "label": "Primary"
              },
              {
                  "value": "custom1",
                  "label": "Custom color 1"
              },
              {
                  "value": "custom2",
                  "label": "Custom color 2"
              }
          ]
      },
      {
        "type": "select",
        "id": "btn_size",
        "label": "Button size",
        "default": "small",
        "options": [
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ]
      },
      {
        "type":"checkbox",
        "id":"btn_hidden_mobile",
        "label":"Hidden buttons on mobile ",
        "default": true
      },
      {
        "type": "paragraph",
        "content": "—————————————————"
      },
      {
        "type": "paragraph",
        "content": "Page dots"
      },
      {
        "type": "checkbox",
        "id": "nav_dot",
        "label": "Use page dots",
        "info": "Creates and show page dots",
        "default": false
      },
      {
        "type": "select",
        "id": "dot_owl",
        "label": "Dots style",
        "default": "default",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "outline",
            "label": "Outline"
          },
          {
            "value": "elessi",
            "label": "Elessi"
          }
        ]
      },
      {
        "type": "select",
        "id": "dots_cl",
        "label": "Dots color",
        "default": "dark",
        "options": [
          {
              "value": "light",
              "label": "Light (Best on dark background)"
          },
          {
              "value": "dark",
              "label": "Dark"
          },
          {
              "value": "primary",
              "label": "Primary"
          },
          {
              "value": "custom1",
              "label": "Custom color 1"
          },
          {
              "value": "custom2",
              "label": "Custom color 2"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "dots_round",
        "label": "Enable dots round",
        "default": true
      },
      {
        "type": "range",
        "id": "dots_space",
        "min": 2,
        "max": 20,
        "step": 1,
        "label": "Dot between horizontal",
        "unit": "px",
        "default": 10
      },
      {
        "type":"checkbox",
        "id":"dots_hidden_mobile",
        "label":"Hidden dots on mobile ",
        "default": false
      },
      {
        "type": "header",
        "content": "3. Design options"
      },
      {
        "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped container"},
            { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      }, 
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "4. Custom css"
      },
      {
        "id": "use_cus_css",
        "type": "checkbox",
        "label": "Use custom css",
        "default":false,
        "info": "If you want custom style for this section."
      },
      { 
        "id": "code_cus_css",
        "type": "textarea",
        "label": "Code custom css",
        "info": "Use selector .SectionID to style css"
        
      }
    ],
    "blocks": [
      {
        "type": "product",
        "name": "Product",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "info": "Only show on admin editor, a key word reminiscent of the selected products.",
            "label": "Title (optional)"
          },
          {
            "type": "url",
            "id": "link_product",
            "info": "Only active when choose link product",
            "label": "== Choose Product (recommended)"
          },
          {
            "type": "range",
            "id": "total",
            "min": 1,
            "max": 100,
            "step": 1,
            "label": "Total Stock:",
            "default": 100
          },
          {
            "type": "range",
            "id": "available",
            "min": 0,
            "max": 100,
            "step": 1,
            "label": "Available Stock:",
            "default": 75
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Products deals",
        "category": "Homepage"
      }
    ]
  }
 {%- endschema -%} 