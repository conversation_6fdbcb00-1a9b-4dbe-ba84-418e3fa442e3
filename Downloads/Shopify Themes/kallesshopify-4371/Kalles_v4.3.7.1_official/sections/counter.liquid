<!-- sections/counter.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'counter.css' | asset_url | stylesheet_tag }}
<link href="{{ 't4s-animation.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  assign source_icon =  se_stts.source_icon
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif

  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif 
 -%} 
<div class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
    {%- render 'section_tophead', se_stts: se_stts -%}
    <div class="t4s-counter-list t4s-counter-{{ se_stts.counter_size }} t4s-counter-border-{{ se_stts.use_border }} t4s-row t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }}  t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }}" style="--number-cl: {{ se_stts.number_cl }};--heading-cl: {{ se_stts.heading_cl }};--icon-cl: {{ se_stts.icon_cl }};--des-cl: {{ se_stts.des_cl }};--bd-cl: {{ se_stts.bd_cl }};--bg-cl: {{ se_stts.bg_cl }};">
      {%- if se_blocks.size > 0 -%}
        {%- for block in se_blocks -%}
          {%- assign bk_stts = block.settings -%}
          {%- assign content_align = bk_stts.content_align -%}
            <div class="t4s-counter t4s-counter-item t4s-col-item t4s-text-{{ content_align }} t4s-eff-img-{{ se_stts.icon_effect }}" timeline hdt-reveal="slide-in">
                <div class="t4s-counter-inner{% if bk_stts.item_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if bk_stts.item_bg != blank %}data-bgset="{{ bk_stts.item_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>
                    {%- if source_icon != 'none' -%}
                        <div class="t4s-counter-icon">
                            {%- if source_icon == 'themes_icon' -%}
                                {%- if bk_stts.icon_themes != "none" -%}
                                    <div class="t4s-counter-icon__theme t4s-d-flex">
                                        {%- render 'icon_shipping', icon_name: bk_stts.icon_themes, icon_class: 't4s-obj-eff' -%}
                                    </div>
                                {%- endif -%}
                            {%- elsif source_icon == 'get_image' -%}
                                {%- if bk_stts.image_icon != blank -%}
                                    <div class="t4s-counter-icon__image t4s-obj-eff lazyloadt4s" data-bgset="{{ bk_stts.image_icon | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"></div>
                                {%- endif -%}
                            {%- elsif source_icon == 'line_awe' -%}
                                {%- if bk_stts.icon != blank -%}<div class="t4s-counter-icon__awesome"><i class="{{ bk_stts.icon }} t4s-obj-eff"></i></div> {%- endif -%}
                            {%- endif -%}
                        </div>
                    {%- endif -%}
                    {%- if bk_stts.heading != blank -%}<h3 class="t4s-counter-title">{{ bk_stts.heading }}{%- endif -%}
                    {%- if bk_stts.number != blank -%}<h3 class="t4s-counter-number"><span data-speed="1000" data-from="0" data-to="{{ bk_stts.number }}" data-count-to data-t4s-animate>0</span>{% if bk_stts.unit != blank %}<span class="t4s-counter-unit t4s-d-inline-block">{{ bk_stts.unit }}</span>{%- endif %}</h3>{%- endif -%}
                    {%- if bk_stts.des != blank -%}<p class="t4s-counter-des">{{ bk_stts.des }}</p>{%- endif -%}
                </div>
            </div>
        {%- endfor -%}
      {%- endif -%}
    </div>
    {{- html_layout[1] -}}
</div> 

{%- schema -%}
  {
    "name": "Counter",
    "tag": "section",
    "class": "t4s-section t4s-section-all t4s_tp_cdt t4s-counter",
    "settings": [
      {
          "type": "header",
          "content": "1. Heading options"
      },
      {
          "type": "select",
          "id": "design_heading",
          "label": "+ Design heading",
          "default": "1",
          "options": [
              {
                  "value": "1",
                  "label": "Design 01"
              },
              {
                  "value": "2",
                  "label": "Design 02"
              },
              {
                  "value": "3",
                  "label": "Design 03"
              },
              {
                  "value": "4",
                  "label": "Design 04"
              },
              {
                  "value": "5",
                  "label": "Design 05"
              },
              {
                  "value": "6",
                  "label": "Design 06 (width line-awesome)"
              },
              {
                  "value": "7",
                  "label": "Design 07"
              },
              {
                  "value": "8",
                  "label": "Design 08"
              },
              {
                  "value": "9",
                  "label": "Design 09"
              },
              {
                  "value": "10",
                  "label": "Design 10"
              },
              {
                  "value": "11",
                  "label": "Design 11"
              },
              {
                  "value": "12",
                  "label": "Design 12"
              },
              {
                  "value": "13",
                  "label": "Design 13"
              },
              {
                  "value": "14",
                  "label": "Design 14"
              },
              {
                "value": "15",
                "label": "Design 15"
              },
              {
                "value": "16",
                "label": "Design 16"
              }
          ]
      },
      {
          "type": "select",
          "id": "heading_align",
          "label": "+ Heading align",
          "default": "t4s-text-center",
          "options": [
              {
                  "value": "t4s-text-start",
                  "label": "Left"
              },
              {
                  "value": "t4s-text-center",
                  "label": "Center"
              },
              {
                  "value": "t4s-text-end",
                  "label": "Right"
              }
          ]
      },
      {
          "type": "text",
          "id": "top_heading",
          "label": "+ Heading"
      },
      {
        "type": "text",
        "id": "icon_heading",
        "label": "Enter a icon name on heading",
        "info": "Only used for design 6 [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
        "default": "las la-gem"
      },
      {
          "type": "textarea",
          "id": "top_subheading",
          "label": "+ Subheading"
      }, 
      {
        "type": "number",
        "id": "tophead_mb",
        "label": "+ Space bottom (px)",
        "info": "The vertical spacing between heading and content",
        "default": 30
      },
      {
        "type": "header",
        "content": "2. General options"
      },
        {
            "type": "select",
            "id": "source_icon",
            "label": "Source icon",
            "default": "none",
            "options": [
                {
                    "value": "none",
                    "label": "None"
                },
                {
                    "value": "themes_icon",
                    "label": "Themes icon"
                },
                {
                    "value": "get_image",
                    "label": "Use image"
                },
                {
                    "value": "line_awe",
                    "label": "Line awesome"
                }
            ]
        },
        {
            "type": "select",
            "id": "icon_effect",
            "label": "Icon hover effect",
            "default": "none",
            "options": [
                {
                    "value": "none",
                    "label": "None"
                },
                {
                    "value": "zoom",
                    "label": "Zoom in"
                },
                {
                    "value": "rotate",
                    "label": "Rotate"
                },
                {
                    "value": "translateToTop",
                    "label": "Move to top"
                },
                {
                    "value": "translateToRight",
                    "label": "Move to right"
                },
                {
                    "value": "translateToBottom",
                    "label": "Move to bottom"
                },
                {
                    "value": "translateToLeft",
                    "label": "Move to left"
                },
                {
                    "value": "filter",
                    "label": "Filter"
                },
                {
                    "value": "bounceIn",
                    "label": "BounceIn"
                }
            ]
        },
      {
        "type": "select",
        "id": "counter_size",
        "label": "Counter size",
        "default": "medium",
        "options": [
         {
            "label": "Small",
            "value": "small"
          },
          {
            "label": "Medium",
            "value": "medium"
          },
          {
            "label": "Large",
            "value": "large"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "use_border",
        "label": "Enable border",
        "default": false
      },
      {
            "type": "color",
            "id": "icon_cl",
            "label": "Icon color",
            "default": "#222222"
        },
      {
        "type": "color",
        "id": "heading_cl",
        "label": "Heading color",
        "default": "#222222"
      },
      {
        "type": "color",
        "id": "number_cl",
        "label": "Number color",
        "default": "#222222"
      },
      {
        "type": "color",
        "id": "des_cl",
        "label": "Description color",
        "default": "#878787"
      },
      {
        "type": "color",
        "id": "bd_cl",
        "label": "Border color",
        "default": "#ddd"
      },
      {
        "type": "color",
        "id": "bg_cl",
        "label": "Background color"
      },
      {
        "type": "select",
        "id": "col_dk",
        "label": "Items per row",
        "default": "3",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          },
          {
            "value": "5",
            "label": "5"
          },
          {
            "value": "6",
            "label": "6"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_tb",
        "label": "Items per row (Tablet)",
        "default": "2",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_mb",
        "label": "Items per row (Mobile)",
        "default": "2",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          }
        ]
      },
      {
        "type": "select",
        "id": "space_h_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_v_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_h_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items (Mobile)",
        "default": "10"
      },
      {
        "type": "select",
        "id": "space_v_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items (Mobile)",
        "default": "10"
      },
      {
        "type": "header",
        "content": "3. Design options"
      },
      {
        "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped container"},
            { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      }, 
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "4. Custom css"
      },
      {
        "id": "use_cus_css",
        "type": "checkbox",
        "label": "Use custom css",
        "default":false,
        "info": "If you want custom style for this section."
      },
      { 
        "id": "code_cus_css",
        "type": "textarea",
        "label": "Code custom css",
        "info": "Use selector .SectionID to style css"
        
      }
    ],
    "blocks": [
      {
        "type": "counter_item",
        "name": "Counter item",
        "settings": [
                {
                    "type": "header",
                    "content": "+ Icon options"
                },
                {
                    "type": "select",
                    "id": "icon_themes",
                    "label": "Select icon", 
                    "info": "Only used for source  theme icon",
                    "default": "car",
                    "options": [
                        {
                            "value": "none",
                            "label": "None"
                        },
                        {
                            "value": "car",
                            "label": "Car"
                        },
                        {
                            "value": "diamond",
                            "label": "Diamond"
                        },
                        {
                            "value": "door-lock",
                            "label": "Door lock"
                        },
                        {
                            "value": "gym",
                            "label": "Gym"
                        },
                        {
                            "value": "hammer",
                            "label": "Hammer"
                        },
                        {
                            "value": "headphones",
                            "label": "Headphones"
                        },
                        {
                            "value": "helm",
                            "label": "Helm"
                        },
                        {
                            "value": "hourglass",
                            "label": "Hourglass"
                        },
                        {
                            "value": "map",
                            "label": "Map"
                        },
                        {
                            "value": "piggy",
                            "label": "Piggy"
                        },
                        {
                            "value": "refesh",
                            "label": "Refesh"
                        },
                        {
                            "value": "rocket",
                            "label": "Rocket"
                        },
                        {
                            "value": "shield",
                            "label": "Shield"
                        },
                        {
                            "value": "smile",
                            "label": "Smile"
                        },
                        {
                            "value": "cloud_upload",
                            "label": "Cloud upload"
                        },
                        {
                            "value": "cash",
                            "label": "Cash"
                        },
                        {
                            "value": "way",
                            "label": "Way"
                        },
                        {
                            "value": "wristwatch",
                            "label": "Wristwatch"
                        },
                        {
                            "value": "world",
                            "label": "World"
                        },
                        {
                            "value": "scissors",
                            "label": "Scissors"
                        },
                        {
                            "value": "wallet",
                            "label": "Wallet"
                        },
                        {
                            "value": "unlock",
                            "label": "Unlock"
                        },
                        {
                            "value": "umbrella",
                            "label": "Umbrella"
                        },
                        {
                            "value": "shuffle",
                            "label": "Shuffle"
                        },
                        {
                            "value": "repeat",
                            "label": "Repeat"
                        },
                        {
                            "value": "refesh-2",
                            "label": "Refesh 2"
                        },
                        {
                            "value": "medal",
                            "label": "Medal"
                        },
                        {
                            "value": "portfolio",
                            "label": "Portfolio"
                        },
                        {
                            "value": "like",
                            "label": "Like"
                        },
                        {
                            "value": "plance",
                            "label": "Plance"
                        },
                        {
                            "value": "map-maker",
                            "label": "Map maker"
                        },
                        {
                            "value": "help",
                            "label": "Help"
                        },
                        {
                            "value": "gift",
                            "label": "Gift"
                        },
                        {
                            "value": "cart",
                            "label": "Cart"
                        },
                        {
                            "value": "box",
                            "label": "Box"
                        },
                        {
                            "value": "back",
                            "label": "Back"
                        }
                    ]
                },
                {
                    "type": "image_picker",
                    "id": "image_icon",
                    "label": "Choose image",
                    "info": "Only used for source image"
                },
                {
                    "type": "text",
                    "id": "icon",
                    "label": "Enter icon",
                    "info": "Only used for source line awesome icon [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
                    "default": "las la-user"
                },
                {
                    "type": "header",
                    "content": "+ Content"
                },
          {
             "type": "text",
             "id": "heading",
             "label": "Heading"
          },
          {
             "type": "number",
             "id": "number",
             "label": "Number",
             "default": 999
          },
                          {
                    "type": "text",
                    "id": "unit",
                    "label": "Unit"
                },
          {
             "type": "textarea",
             "id": "des",
             "label": "Description",
             "default": "COUNTER LABEL"
          },
          {
            "type": "select",
            "id": "content_align",
            "label": "Content align",
            "default": "center",
            "options": [
              {
                "label": "Left",
                "value": "start"
              },
              {
                "label": "Center",
                "value": "center"
              },
              {
                "label": "Right",
                "value": "end"
              }
            ]
          },
          {
            "type": "image_picker",
            "id": "item_bg",
            "label": "Background image",
            "info": "Chose image for head item background"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Counter",
        "category": "Homepage",
        "blocks": [
          {
            "type": "counter_item", 
            "settings": {
              "number": 666
            }
          },
          {
            "type": "counter_item", 
            "settings": {
              "number": 888
            }
          },
          {
            "type": "counter_item", 
            "settings": {
              "number": 999
            }
          }
        ]
      }
    ]
  }
{% endschema %}