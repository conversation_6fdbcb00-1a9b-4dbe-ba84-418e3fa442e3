{{ 'button-style.css' | asset_url | stylesheet_tag }}
<link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid
  assign section_blocks   = section.blocks
  assign se_stts          = section.settings
  assign ccount           = cart.item_count
  assign ck_lumise        = 'ck_lumise'  
  assign cart_url         = routes.cart_url
  assign cart_change_url  = routes.cart_change_url
  assign min_qty          = settings.min_qty | plus: 0
  assign compare_tt_price = 0
  assign shipping_amount  = settings.free_ship_pr.metafields.theme.shipping_money.value | default: settings.free_ship_pr.price | default: -1
  assign total_price    = cart.total_price

  assign se_style = se_stts.style
  assign se_pos = se_stts.pos

  assign gift_pr = all_products[settings.gift_wrap_pr]
  assign gift_pr_id = gift_pr.id
  assign arr_gift_id = cart.items | where: 'product_id', gift_pr_id
  if cart.note != blank
    assign style_add  = 't4s-d-none'
    assign style_edit = ''
  else
    assign style_add  = ''
    assign style_edit = 't4s-d-none'
  endif
  assign edit_item = settings.edit_item
  assign cur_code_enabled = settings.currency_code_enabled
  assign atc_ajax_enable  = settings.atc_ajax_enable
  assign cart_ajax_enable = settings.cart_ajax_enable
-%}

<svg class="t4s-d-none">
<symbol id="icon-cart-remove" viewBox="0 0 24 24" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line>
</symbol>
<symbol id="icon-cart-edit" viewBox="0 0 24 24" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
</symbol>
<symbol id="icon-cart-tag" viewBox="0 0 448 512">
  <path d="M48 32H197.5C214.5 32 230.7 38.74 242.7 50.75L418.7 226.7C443.7 251.7 443.7 292.3 418.7 317.3L285.3 450.7C260.3 475.7 219.7 475.7 194.7 450.7L18.75 274.7C6.743 262.7 0 246.5 0 229.5V80C0 53.49 21.49 32 48 32L48 32zM112 176C129.7 176 144 161.7 144 144C144 126.3 129.7 112 112 112C94.33 112 80 126.3 80 144C80 161.7 94.33 176 112 176z"/>
</symbol>
<symbol id="icon-cart-spinner" viewBox="0 0 66 66">
  <circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
</symbol>
<symbol id="icon-cart-check" viewBox="0 0 448 512">
  <path d="M443.3 100.7C449.6 106.9 449.6 117.1 443.3 123.3L171.3 395.3C165.1 401.6 154.9 401.6 148.7 395.3L4.686 251.3C-1.562 245.1-1.562 234.9 4.686 228.7C10.93 222.4 21.06 222.4 27.31 228.7L160 361.4L420.7 100.7C426.9 94.44 437.1 94.44 443.3 100.7H443.3z"/>
</symbol>
<symbol id="icon-cart-selected" viewBox="0 0 24 24">
<path d="M9 20l-7-7 3-3 4 4L19 4l3 3z"></path>
</symbol>
</svg>

{%- capture html_btn -%}
    {%- if se_style == '1' -%}
      <div data-cart-tools class="t4s-when-cart-emty t4s-mini_cart__tool t4s-mini_cart__tool_icon t4s-text-center">
        {%- if se_stts.enable_note -%}
         <div data-tooltip="top" title="{{ 'cart.tool.note' | t }}" data-cart-tool_action data-id="note" class="mini_cart_tool_btn is--note is--addNote {{ style_add }}">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M 16 3 C 14.742188 3 13.847656 3.890625 13.40625 5 L 6 5 L 6 28 L 26 28 L 26 5 L 18.59375 5 C 18.152344 3.890625 17.257813 3 16 3 Z M 16 5 C 16.554688 5 17 5.445313 17 6 L 17 7 L 20 7 L 20 9 L 12 9 L 12 7 L 15 7 L 15 6 C 15 5.445313 15.445313 5 16 5 Z M 8 7 L 10 7 L 10 11 L 22 11 L 22 7 L 24 7 L 24 26 L 8 26 Z"/></svg>
         </div>
         <div data-tooltip="top" title="{{ 'cart.tool.edit_note' | t }}" data-cart-tool_action data-id="note" class="mini_cart_tool_btn is--note is--editNote {{ style_edit }}">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M 16 2 C 14.74 2 13.850156 2.89 13.410156 4 L 5 4 L 5 29 L 27 29 L 27 4 L 18.589844 4 C 18.149844 2.89 17.26 2 16 2 z M 16 4 C 16.55 4 17 4.45 17 5 L 17 6 L 20 6 L 20 8 L 12 8 L 12 6 L 15 6 L 15 5 C 15 4.45 15.45 4 16 4 z M 7 6 L 10 6 L 10 10 L 22 10 L 22 6 L 25 6 L 25 27 L 7 27 L 7 6 z M 9 13 L 9 15 L 11 15 L 11 13 L 9 13 z M 13 13 L 13 15 L 23 15 L 23 13 L 13 13 z M 9 17 L 9 19 L 11 19 L 11 17 L 9 17 z M 13 17 L 13 19 L 23 19 L 23 17 L 13 17 z M 9 21 L 9 23 L 11 23 L 11 21 L 9 21 z M 13 21 L 13 23 L 23 23 L 23 21 L 13 21 z"/></svg>
        </div>
        {%- endif -%}
        {%- if se_stts.enable_gift_wrap and gift_pr.available == true -%}<div data-tooltip="top" title="{{ 'cart.tool.add_gift_wrap' | t }}" data-toogle-gift data-cart-tool_action data-id="gift" class="mini_cart_tool_btn is--gift"{% if arr_gift_id.size > 0 %} style=" display:none"{% endif %}><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M 12 5 C 10.355469 5 9 6.355469 9 8 C 9 8.351563 9.074219 8.683594 9.1875 9 L 4 9 L 4 15 L 5 15 L 5 28 L 27 28 L 27 15 L 28 15 L 28 9 L 22.8125 9 C 22.925781 8.683594 23 8.351563 23 8 C 23 6.355469 21.644531 5 20 5 C 18.25 5 17.0625 6.328125 16.28125 7.4375 C 16.175781 7.585938 16.09375 7.730469 16 7.875 C 15.90625 7.730469 15.824219 7.585938 15.71875 7.4375 C 14.9375 6.328125 13.75 5 12 5 Z M 12 7 C 12.625 7 13.4375 7.671875 14.0625 8.5625 C 14.214844 8.78125 14.191406 8.792969 14.3125 9 L 12 9 C 11.433594 9 11 8.566406 11 8 C 11 7.433594 11.433594 7 12 7 Z M 20 7 C 20.566406 7 21 7.433594 21 8 C 21 8.566406 20.566406 9 20 9 L 17.6875 9 C 17.808594 8.792969 17.785156 8.78125 17.9375 8.5625 C 18.5625 7.671875 19.375 7 20 7 Z M 6 11 L 26 11 L 26 13 L 17 13 L 17 12 L 15 12 L 15 13 L 6 13 Z M 7 15 L 25 15 L 25 26 L 17 26 L 17 16 L 15 16 L 15 26 L 7 26 Z"/></svg></div>{%- endif -%}
        {%- if se_stts.enable_rates -%}<div data-tooltip="top" title="{{ 'cart.shipping_estimator.estimate' | t | escape }}" data-cart-tool_action data-id="rates" class="mini_cart_tool_btn is--rates"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M 1 4 L 1 25 L 4.15625 25 C 4.601563 26.71875 6.148438 28 8 28 C 9.851563 28 11.398438 26.71875 11.84375 25 L 20.15625 25 C 20.601563 26.71875 22.148438 28 24 28 C 25.851563 28 27.398438 26.71875 27.84375 25 L 31 25 L 31 14.59375 L 30.71875 14.28125 L 24.71875 8.28125 L 24.40625 8 L 19 8 L 19 4 Z M 3 6 L 17 6 L 17 23 L 11.84375 23 C 11.398438 21.28125 9.851563 20 8 20 C 6.148438 20 4.601563 21.28125 4.15625 23 L 3 23 Z M 19 10 L 23.5625 10 L 29 15.4375 L 29 23 L 27.84375 23 C 27.398438 21.28125 25.851563 20 24 20 C 22.148438 20 20.601563 21.28125 20.15625 23 L 19 23 Z M 8 22 C 9.117188 22 10 22.882813 10 24 C 10 25.117188 9.117188 26 8 26 C 6.882813 26 6 25.117188 6 24 C 6 22.882813 6.882813 22 8 22 Z M 24 22 C 25.117188 22 26 22.882813 26 24 C 26 25.117188 25.117188 26 24 26 C 22.882813 26 22 25.117188 22 24 C 22 22.882813 22.882813 22 24 22 Z"/></svg></div>{%- endif -%}
        {%- if se_stts.enable_discount -%}<div data-tooltip="top" title="{{ 'cart.tool.add_coupon' | t }}" data-cart-tool_action data-id="discount" class="mini_cart_tool_btn is--discount"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M 16 5 L 15.6875 5.28125 L 4.28125 16.8125 L 3.59375 17.5 L 4.28125 18.21875 L 13.78125 27.71875 L 14.5 28.40625 L 15.1875 27.71875 L 26.71875 16.3125 L 27 16 L 27 5 Z M 16.84375 7 L 25 7 L 25 15.15625 L 14.5 25.59375 L 6.40625 17.5 Z M 22 9 C 21.449219 9 21 9.449219 21 10 C 21 10.550781 21.449219 11 22 11 C 22.550781 11 23 10.550781 23 10 C 23 9.449219 22.550781 9 22 9 Z"/></svg></div>{%- endif -%}
      </div>
    {%- else -%}
      <div data-cart-tools class="t4s-when-cart-emty t4s-mini_cart__tool t4s-mini_cart__tool_button">
        {%- if se_stts.enable_note -%}
        <div data-cart-tool_action data-id="note" class="mini_cart_tool_btn is--note is--addNote t4s-pr t4s-truncate {{ style_add }}">{{ 'cart.tool.note' | t }}</div>
        <div data-cart-tool_action data-id="note" class="mini_cart_tool_btn is--note is--editNote t4s-pr t4s-truncate {{ style_edit }}">{{ 'cart.tool.edit_note' | t }}</div>
        {%- endif -%}
        {%- if se_stts.enable_gift_wrap and gift_pr.available == true -%}<div data-toogle-gift data-cart-tool_action data-id="gift" class="mini_cart_tool_btn is--gift t4s-pr t4s-truncate"{% if arr_gift_id.size > 0 %} style=" display:none"{% endif %}>{{ 'cart.tool.add_gift_wrap' | t }}</div>{%- endif -%}
        {%- if se_stts.enable_rates -%}<div data-cart-tool_action data-id="rates" class="mini_cart_tool_btn is--rates t4s-pr t4s-truncate">{{ 'cart.shipping_estimator.estimate' | t }}</div>{%- endif -%}
        {%- if se_stts.enable_discount -%}<div data-cart-tool_action data-id="discount" class="mini_cart_tool_btn is--discount t4s-pr t4s-truncate">{{ 'cart.tool.add_coupon' | t }}</div>{%- endif -%}
      </div>
   {%- endif -%}
{%- endcapture -%}

{%- if request.design_mode -%}
<link rel="stylesheet" href="{{ 'mini-cart.css' | asset_url }}" media="print" onload="this.media='all'">
<div data-cart-wrapper id="t4s-mini_cart" data-ccount="{{ ccount }}" class="t4s-drawer t4s-drawer__right t4s-dn" aria-hidden="true">
{%- endif -%}

   <div class="t4s-drawer__header"><span>{{ 'cart.mini_cart.title' | t }}</span><button class="t4s-drawer__close" data-drawer-close aria-label="{{ 'cart.mini_cart.close_cart' | t }}"><svg class="t4s-iconsvg-close" role="presentation" viewBox="0 0 16 14"><path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path></svg></button></div>
   {%- if se_stts.enable_calc_ship and shipping_amount > -1 -%}
      {%- liquid
      assign shipping_money = shipping_amount | money 
      assign enable_shipbar = settings.enable_shipbar
      assign des_shipbar_per_1 = settings.des_shipbar_per_1
      assign des_shipbar_per_2 = settings.des_shipbar_per_2
      assign des_shipbar_per_3 = settings.des_shipbar_per_3
      assign less_100 = 99.99 -%}
      
      {%- if enable_shipbar -%}
         {%- capture text_w_ship -%}
            {%- if total_price == 0 -%}
               {%- liquid
               assign array_shipbar_per = des_shipbar_per_1 | append: ';' | append: des_shipbar_per_2 | append: ';' | append: des_shipbar_per_3 | split: ';' | sort_natural
               assign percent_class =  array_shipbar_per[0] | plus: 0
               if percent_class == 100
                  assign percent_class = less_100
               endif
              -%}
               {%- capture style_w %} style="width:0"{% endcapture -%}
               <div data-cart-ship-text class="t4s-cart__thres1">{{ 'cart.shipping_threshold.text_1_html' | t: money: shipping_money }}</div>
            {%- elsif shipping_amount > total_price %}{% assign space_money = shipping_amount | minus: total_price | money -%}
               <div data-cart-ship-text class="t4s-cart__thres2">{{ 'cart.shipping_threshold.text_2_html' | t: money: space_money }}</div>
            
               {%- liquid
               assign array_shipbar_per = des_shipbar_per_1 | append: ';' | append: des_shipbar_per_2 | append: ';' | append: des_shipbar_per_3 | split: ';' | sort_natural
               assign percent_w2 = shipping_amount | minus: total_price | times: 100.0 | divided_by: shipping_amount
               assign percent_w2 = 100 | minus: percent_w2
               assign shipbar_num_0 = array_shipbar_per[0] | plus: 0
               assign shipbar_num_1 = array_shipbar_per[1] | plus: 0
               assign shipbar_num_2 = array_shipbar_per[2] | plus: 0
               if percent_w2 < shipbar_num_0
                  assign percent_class = shipbar_num_0
               elsif percent_w2 < shipbar_num_1
                  assign percent_class = shipbar_num_1
               else
                  assign percent_class = shipbar_num_2
               else
                  assign percent_class = 99.994
               endif
               if percent_class == 100
                  assign percent_class = less_100
               endif
              -%}
               {%- capture style_w %} style="width:{{ percent_w2 }}%"{% endcapture -%}
            {%- else -%}
               <div data-cart-ship-text data-cart-ship-done class="t4s-cart__thres3">{{ 'cart.shipping_threshold.text_3' | t | replace: '[', '<span class="t4s-cr is--congratulations">' | replace: ']', '</span>' }}</div>
               {%- assign percent_class = 100 -%}
               {%- capture style_w %} style="width:100%"{% endcapture -%}
            {%- endif -%}

         {%- endcapture -%}
          
         {%- style -%}
         [data-t4s-percent="{% if des_shipbar_per_1 == 100 %}{{ less_100 }}{% else %}{{ des_shipbar_per_1 }}{% endif %}"] { --main-threshold-color: {{ settings.des_shipbar_cl_1 }}; }
         [data-t4s-percent="{% if des_shipbar_per_2 == 100 %}{{ less_100 }}{% else %}{{ des_shipbar_per_2 }}{% endif %}"] { --main-threshold-color: {{ settings.des_shipbar_cl_2 }}; }
         [data-t4s-percent="{% if des_shipbar_per_3 == 100 %}{{ less_100 }}{% else %}{{ des_shipbar_per_3 }}{% endif %}"] { --main-threshold-color: {{ settings.des_shipbar_cl_3 }}; }
         [data-t4s-percent="99.994"],[data-t4s-percent="100"] { --main-threshold-color: {{ settings.des_shipbar_cl_4 }}; }
         {%- endstyle -%}
      {%- endif -%}

      <link rel="stylesheet" href="{{ 'shipping_bar.css' | asset_url }}" media="all">
      <div data-cart-calc-shipping data-t4s-percent="{{ percent_class }}" class="t4s-cart__threshold">
         {{- text_w_ship -}}
         {%- if enable_shipbar %}<div class="t4s-cart-thes__bar bgt4_svg{{ settings.des_shipbar }} t4s-pr"><span data-cart-ship-bar class="t4s-pr t4s-d-block t4s-h-100"{{ style_w }}><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" width="22"><path class="fa-primary" d="M64 48C64 21.49 85.49 0 112 0H368C394.5 0 416 21.49 416 48V96H466.7C483.7 96 499.1 102.7 512 114.7L589.3 192C601.3 204 608 220.3 608 237.3V352C625.7 352 640 366.3 640 384C640 401.7 625.7 416 608 416H574.9C567.1 361.7 520.4 320 464 320C407.6 320 360.9 361.7 353.1 416H286.9C279.1 361.7 232.4 320 176 320C127.9 320 86.84 350.4 70.99 392.1C66.56 385.7 64 377.1 64 368V256H208C216.8 256 224 248.8 224 240C224 231.2 216.8 224 208 224H64V192H240C248.8 192 256 184.8 256 176C256 167.2 248.8 160 240 160H64V128H272C280.8 128 288 120.8 288 112C288 103.2 280.8 96 272 96H64L64 48zM544 256V237.3L466.7 160H416V256H544z"/><path class="t4s-ship-secondary" d="M272 128H16C7.164 128 0 120.8 0 112C0 103.2 7.164 96 16 96H272C280.8 96 288 103.2 288 112C288 120.8 280.8 128 272 128zM240 160C248.8 160 256 167.2 256 176C256 184.8 248.8 192 240 192H48C39.16 192 32 184.8 32 176C32 167.2 39.16 160 48 160H240zM208 224C216.8 224 224 231.2 224 240C224 248.8 216.8 256 208 256H16C7.164 256 0 248.8 0 240C0 231.2 7.164 224 16 224H208zM256 432C256 476.2 220.2 512 176 512C131.8 512 96 476.2 96 432C96 387.8 131.8 352 176 352C220.2 352 256 387.8 256 432zM544 432C544 476.2 508.2 512 464 512C419.8 512 384 476.2 384 432C384 387.8 419.8 352 464 352C508.2 352 544 387.8 544 432z"/></svg></span></div>{% endif -%}
      </div>
   {%- endif -%}

   <form action="{{ cart_url }}" method="post" novalidate class="t4s-drawer__wrap">
      <input type="hidden" data-cart-attr-rm name="attributes[collection_items_per_row]" value="">
      {%- if se_stts.enable_discount %}<input type="hidden" data-cart-discount name="discount" value="">{% endif -%}
      <div class="t4s-drawer__main">
      <div data-t4s-scroll-me class="t4s-drawer__scroll t4s-current-scrollbar">
         <div class="t4s-cookie-message t4s-dn">{{ 'cart.general.cookies_required' | t }}</div>
         {%- if se_style == '1' and se_pos == '1' %}{{- html_btn -}}{% endif -%}
         <div data-cart-items class="t4s-mini_cart__items t4s_ratioadapt t4s-product">
            {%- if ccount > 0 -%}

               {%- for item in cart.items -%}
               {%- render 'cart-item', item: item, gift_pr_id: gift_pr_id, min_qty: min_qty, compare_tt_price: compare_tt_price, cart_change_url: cart_change_url, edit_item: edit_item, cur_code_enabled: cur_code_enabled, atc_ajax_enable: atc_ajax_enable, cart_ajax_enable: cart_ajax_enable -%}
               {%- endfor -%}

            {%- else -%}

               <style>
                  #t4s-mini_cart .t4s-drawer__bottom {
                      opacity: 0;
                      -webkit-transform: translateY(45px);
                      transform: translateY(45px);
                      -webkit-transition: opacity .25s cubic-bezier(.25,.46,.45,.94),-webkit-transform .25s cubic-bezier(.25,.46,.45,.94);
                      transition: opacity .25s cubic-bezier(.25,.46,.45,.94),transform .25s cubic-bezier(.25,.46,.45,.94),-webkit-transform .25s cubic-bezier(.25,.46,.45,.94);
                   }
               </style> 
               <div class="t4s-mini_cart__emty">
                  <svg id="icon-cart-emty" widht="50" height="50" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path d="M263.4 103.4C269.7 97.18 279.8 97.18 286.1 103.4L320 137.4L353.9 103.4C360.2 97.18 370.3 97.18 376.6 103.4C382.8 109.7 382.8 119.8 376.6 126.1L342.6 160L376.6 193.9C382.8 200.2 382.8 210.3 376.6 216.6C370.3 222.8 360.2 222.8 353.9 216.6L320 182.6L286.1 216.6C279.8 222.8 269.7 222.8 263.4 216.6C257.2 210.3 257.2 200.2 263.4 193.9L297.4 160L263.4 126.1C257.2 119.8 257.2 109.7 263.4 103.4zM80 0C87.47 0 93.95 5.17 95.6 12.45L100 32H541.8C562.1 32 578.3 52.25 572.6 72.66L518.6 264.7C514.7 278.5 502.1 288 487.8 288H158.2L172.8 352H496C504.8 352 512 359.2 512 368C512 376.8 504.8 384 496 384H160C152.5 384 146.1 378.8 144.4 371.5L67.23 32H16C7.164 32 0 24.84 0 16C0 7.164 7.164 0 16 0H80zM107.3 64L150.1 256H487.8L541.8 64H107.3zM128 456C128 425.1 153.1 400 184 400C214.9 400 240 425.1 240 456C240 486.9 214.9 512 184 512C153.1 512 128 486.9 128 456zM184 480C197.3 480 208 469.3 208 456C208 442.7 197.3 432 184 432C170.7 432 160 442.7 160 456C160 469.3 170.7 480 184 480zM512 456C512 486.9 486.9 512 456 512C425.1 512 400 486.9 400 456C400 425.1 425.1 400 456 400C486.9 400 512 425.1 512 456zM456 432C442.7 432 432 442.7 432 456C432 469.3 442.7 480 456 480C469.3 480 480 469.3 480 456C480 442.7 469.3 432 456 432z"/></svg>
                  <p>{{ 'cart.mini_cart.empty' | t }}</p>
                  {%- assign btn_blocks = section_blocks | where: "type", 'btn' -%}
                  {%- if btn_blocks.size > 0 -%}
                  {%- for block in btn_blocks -%}
                  {%- assign bk_stts = block.settings -%}
                  {%- assign button_style = bk_stts.button_style -%}
                     {%- if bk_stts.title != blank -%}
                        <p class="t4s-return-to-shop"><a data-loading-bar class="t4s-btn-cart__emty t4s-btn t4s-btn-base t4s-btn-style-{{ button_style }} t4s-btn-color-{{ bk_stts.btn_cl }} {% if button_style == 'default' or button_style == 'outline' %}t4s-btn-effect-{{ bk_stts.button_effect }}{% endif %} t4s-justify-content-center t4s-truncate" href="{{ bk_stts.url | default: routes.all_products_collection_url }}">{{ bk_stts.title }}{%- if bk_stts.btn_icon -%}<svg class="t4s-btn-icon" width="14"><use xlink:href="#t4s-icon-btn"></use></svg>{%- endif -%}</a></p>
                     {%- endif -%}
                  {%- endfor -%}
                  {%- endif -%}
               </div>

            {%- endif -%}
         </div>

         {%- unless se_style == '1' and se_pos == '1' %}{{- html_btn -}}{% endunless -%}

        {%- if se_stts.enable_pr %}<div class="t4s-when-cart-emty" data-cart-upsell-options='{ "baseurl":"{{ routes.product_recommendations_url }}", "limit":5, "product_id":{{ cart.items.first.product_id | default: 19041994 }}, "section_id": "mini_cart_upsell" }'></div>{% endif -%}

      </div>
      </div>
      <div class="t4s-drawer__bottom">
         <div data-cart-discounts>
         {%- if cart.cart_level_discount_applications != blank -%}
            <ul class="t4s-cart_discounts">
            {%- for discount_application in cart.cart_level_discount_applications -%}<li class="t4s-order_cart_discounts"><svg viewBox="0 0 24 24" width="20"><use href="#icon-cart-tag"/></svg> {{- discount_application.title -}} (-{{ discount_application.total_allocated_amount | money }})</li>{%- endfor -%}
            </ul>
         {%- endif -%}
         </div>
         {%- for block in section_blocks -%}
             {%- assign bk_stts = block.settings -%}
             {%- assign button_style = bk_stts.button_style -%}
            {%- case block.type -%}
               {%- when 'price' -%}
                  <div class="t4s-cart-total t4s-row t4s-gx-5 t4s-gy-0 t4s-align-items-center t4s-justify-content-between" {{ block.shopify_attributes }}>
                     <div class="t4s-col-auto"><strong>{{ 'cart.mini_cart.subtotal' | t }}</strong></div>
                     <div data-cart-prices class="t4s-col-auto t4s-text-right">
                        {%- if cart.total_discount > 0 -%}
                        <div class="t4s-cart__originalPrice t4s-d-inline-block">{{ cart.original_total_price | money }}</div>
                        <div class="t4s-cart__discountPrice t4s-d-inline-block"> - {{ cart.total_discount | money }}</div>
                        {%- elsif compare_tt_price > total_price and false -%}
                        <div class="t4s-cart__originalPrice t4s-d-inline-block">{{ compare_tt_price | money }}</div>
                        <div class="t4s-cart__discountPrice t4s-d-inline-block"> - {{ compare_tt_price | minus: total_price | money }}</div>
                        {%- endif -%}
                        <div class="t4s-cart__totalPrice">{{ total_price | money_with_currency }}</div>
                     </div>
                  </div>

               {%- when 'tax' -%}
                  {%- capture taxes_shipping_checkout -%}{%- assign page_url = settings.link_ship -%}
                     {%- if se_stts.show_discount -%}
                         {%- if cart.taxes_included and page_url != blank -%}
                           {{ 'cart.general.taxes_included_discounts_and_shipping_policy_html' | t: link: page_url }}
                         {%- elsif cart.taxes_included -%}
                           {{ 'cart.general.taxes_included_discounts_but_shipping_at_checkout' | t }}
                         {%- elsif page_url != blank -%}
                           {{ 'cart.general.taxes_discounts_and_shipping_policy_at_checkout_html' | t: link: page_url }}
                         {%- else -%}
                           {{ 'cart.general.taxes_discounts_and_shipping_at_checkout' | t }}
                         {%- endif -%}
                     {%- else -%}
                         {%- if cart.taxes_included and page_url != blank -%}
                           {{ 'cart.general.taxes_included_and_shipping_policy_html' | t: link: page_url }}
                         {%- elsif cart.taxes_included -%}
                           {{ 'cart.general.taxes_included_but_shipping_at_checkout' | t }}
                         {%- elsif page_url != blank -%}
                           {{ 'cart.general.taxes_and_shipping_policy_at_checkout_html' | t: link: page_url }}
                         {%- else -%}
                           {{ 'cart.general.taxes_and_shipping_at_checkout' | t }}
                         {%- endif -%}
                     {%- endif -%}
                  {%- endcapture -%}
                  <p class="t4s-cart__tax" {{ block.shopify_attributes }}>{{ taxes_shipping_checkout }}</p>

               {%- when 'agree' -%}
                  {%- capture terms_and_conditions -%}{%- assign page_url = settings.link_conditions -%}
                   {%- if page_url != blank -%}
                     {{ 'cart.general.terms_and_conditions_html' | t: link: page_url }}
                   {%- else -%}
                     {{ 'cart.general.terms_and_conditions' | t }}
                   {%- endif -%}
                  {%- endcapture -%}
                  <p class="t4s-pr t4s-d-block t4s-cart__agree" {{ block.shopify_attributes }}><input type="checkbox" id="cart_agree" data-agree-checkbox name="{{ ck_lumise }}"><label for="cart_agree">{{ terms_and_conditions }}</label><svg class="t4s-dn t4s-icon_checked"><use href="#icon-cart-selected"/></svg></p>

               {%- when 'btnc' -%}    
                  <a {{ block.shopify_attributes }} href="{{ cart_url }}" data-loading-bar class="t4s-btn__cart t4s-btn t4s-btn-base t4s-btn-style-{{ button_style }} t4s-btn-color-{{ bk_stts.btn_cl }} {% if button_style == 'default' or button_style == 'outline' %}t4s-btn-effect-{{ bk_stts.button_effect }}{% endif %} t4s-w-100 t4s-justify-content-center t4s-truncate">{{ 'cart.mini_cart.view' | t }} {%- if bk_stts.btn_icon -%}<svg class="t4s-btn-icon" width="14"><use xlink:href="#t4s-icon-btn"></use></svg>{%- endif -%}</a>

               {%- when 'btnck' -%}
                  {%- if settings.currency_type == '2' and settings.notify_currency and settings.mess_currency != blank %}{% assign cart_iso_code = cart.currency.iso_code %}{% assign text1 = '{{ currency }}' -%}
                  <p class="t4s-db" data-currency-jsnotify>{{ settings.mess_currency | replace: text1, cart_iso_code | replace: '[currency]', cart_iso_code | replace: '[original_currency]', cart_iso_code | replace: '[current_currency]', '<span class="selected-currency"></span>' }}</p>
                  {%- endif -%}
                  <button {{ block.shopify_attributes }} type="submit" data-loading-bar data-confirm="{{ ck_lumise }}" name="checkout" class="t4s-btn__checkout t4s-btn t4s-btn-base t4s-btn-style-{{ button_style }} t4s-btn-color-{{ bk_stts.btn_cl }} {% if button_style == 'default' or button_style == 'outline' %}t4s-btn-effect-{{ bk_stts.button_effect }}{% endif %} t4s-w-100 t4s-justify-content-center t4s-truncate">{{ 'cart.mini_cart.checkout' | t }} {%- if bk_stts.btn_icon -%}<svg class="t4s-btn-icon" width="14"><use xlink:href="#t4s-icon-btn"></use></svg>{%- endif -%}</button>

               {%- when 'btnmr' -%}
                  {%- if additional_checkout_buttons and unadmin_sp  -%}{{ arr_agree[0] }}<div {{ block.shopify_attributes }} data-add-ckt4 class="additional_checkout_buttons additional-checkout-buttons--vertical mt__10 mb__10{{ cl_agree }}">{{ content_for_additional_checkout_buttons }}</div>{{ arr_agree[1] }}{%- endif -%}
               
               {%- when 'img' -%}
                  {% if bk_stts.image != blank %}
                  {%- assign image = bk_stts.image -%}
                  <div class="t4s-cat__imgtrust t4s_ratioadapt" {{ block.shopify_attributes }}>
                     <div class="t4s_ratio t4s-pr t4s-oh" style="--aspect-ratioapt:{{ image.aspect_ratio | default: 1 }};">
                       <img class="t4s-w-100 lazyloadt4s" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                       <span class="lazyloadt4s-loader is-bg-img" style="background: url({{ image | image_url: width: 1 }});"></span>
                     </div>
                   </div>
                  {% elsif bk_stts.svg != blank %}
                    {%- assign arr = bk_stts.svg | remove: ' ' | split: "," -%}
                    <div class="t4s-cat__imgtrust" {{ block.shopify_attributes }} style="display: flex;flex-wrap: wrap;gap: 10px;justify-content: center;">
                    {%- for img in arr -%}
                      {%- if img contains 'cust4__' -%}
                        {%- assign get_img = img | remove_first: "cust4__" -%}
                        {%- assign image = images[get_img] -%}
                        {%- if image == blank %}{% continue %}{% endif -%}
                        <img src="{{ image | image_url }}" alt="{{ image.alt | escape }}" height="{{ bk_stts.height }}" width="" loading="lazy" style="height:{{bk_stts.height}}px">
                      {%- else -%}
                        <img src="{{ img | payment_type_img_url }}" alt="{{ img | replace: '_', ' ' }}" height="{{ bk_stts.height }}"  width="" loading="lazy" style="height:{{bk_stts.height}}px">
                      {%- endif -%}
                    {%- endfor -%}
                    </div>
                  {% endif -%}
               
               {%- else -%}{% continue -%}
            {%- endcase -%}
         {%- endfor -%}
      </div>
   </form>

   {%- if se_stts.enable_note -%}
   <div class="t4s-mini_cart-tool__content is--note t4s-pe-none">             
      <label for="CartSpecialInstructions" class="t4s-d-block"><span class="t4s-txt_add_note {{ style_add }}">{{ 'cart.tool.note' | t }}</span><span class="t4s-txt_edit_note {{ style_edit }}">{{ 'cart.tool.edit_note' | t }}</span></label>
      <textarea name="note" data-opend-focus id="CartSpecialInstructions" placeholder="{{ 'cart.tool.placeholder_note' | t }}">{{ cart.note }}</textarea>
      <p><button type="button" data-cart-tool_close class="t4s-mini_cart-tool__primary">{{ 'cart.tool.save' | t }}</button></p>
      <p><button type="button" data-cart-tool_close class="t4s-mini_cart-tool__back">{{ 'cart.tool.cancel' | t }}</button></p>
   </div>
   {%- endif -%}

   {%- if se_stts.enable_rates %}{% assign idShip = 'mini_cart' -%}
   <div class="t4s-mini_cart-tool__content is--rates t4s-pe-none">
      <div class="t4s-mini_cart-tool__wrap" data-estimate-shipping-wrap data-id="{{ idShip }}">
        <span class="t4s-mini_cart-tool__text">{{ 'cart.shipping_estimator.title' | t }}</span>
          <div class="t4s-field">
            <label for="ShippingCountry_{{ idShip }}">{{ 'cart.shipping_estimator.country' | t }}</label>
            <select id="ShippingCountry_{{ idShip }}" name="country" data-default="{% if customer %}{{ customer.default_address.country }}{% elsif se_stts.ship_df_country != '' %}{{ se_stts.ship_df_country | escape }}{% endif %}">{{- country_option_tags -}}</select>
          </div>
          <div class="t4s-field" id="ShippingProvinceContainer_{{ idShip }}" style="display:none">
            <label for="ShippingProvince_{{ idShip }}" id="address_province_label">{{ 'cart.shipping_estimator.province' | t }}</label>
            <select id="ShippingProvince_{{ idShip }}" name="province" data-default="{% if customer %}{{ customer.default_address.province }}{% endif %}"></select>
          </div>  
          <div class="t4s-field">
            <label for="ShippingZip_{{ idShip }}">{{ 'cart.shipping_estimator.zip_code' | t }}</label>
            <input type="text" data-opend-focus id="ShippingZip_{{ idShip }}" name="zip" value="{% if customer %}{{ customer.default_address.zip }}{% endif %}"/>
          </div>
          <div class="t4s-field">
            <button data-action="estimate-shipping" type="button" class="t4s-get__rates t4s-mini_cart-tool__primary t4s-btn-loading__svg"> 
                 <span class="t4s-btn-atc_text">{{ 'cart.shipping_estimator.estimate' | t }}</span>
                 <div class="t4s-loading__spinner t4s-dn">
                     <svg  width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
                 </div>  
            </button>
          </div>
          <div class="t4s-field">
            <button type="button" data-cart-tool_close class="t4s-mini_cart-tool__back">{{ 'cart.tool.cancel' | t }}</button>
          </div>
          <div data-response-rates class="t4s-response__rates"></div>
          <template data-lang-rates class="t4s-d-none">
           {
             "multiple_rates": {{ 'cart.shipping_estimator.multiple_rates' | t | json }},
             "one_rate": {{ 'cart.shipping_estimator.one_rate' | t | json }},
             "no_rates": {{ 'cart.shipping_estimator.no_rates' | t | json }},
             "rate_value": {{ 'cart.shipping_estimator.rate_value' | t | json }},
             "errors": {{ 'cart.shipping_estimator.errors' | t | json }}
           }
          </template> 
      </div>
   </div>
   {%- endif -%}

   {%- if se_stts.enable_gift_wrap and gift_pr.available == true -%}
     <div class="t4s-mini_cart-tool__content is--gift t4s-pe-none">
         <div class="t4s-mini_cart-tool__wrap t4s-text-center">
             <div class="t4s-field">
                 <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round" class="t4s-gift-svg t4s-d-block t4s-pr"><polyline points="20 12 20 22 4 22 4 12"></polyline><rect x="2" y="7" width="20" height="5"></rect><line x1="12" y1="22" x2="12" y2="7"></line><path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"></path><path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"></path></svg>
                 {%- assign gift_pr_money = gift_pr.variants.first.price | money -%}
                 <span class="t4s-gift_wrap_text t4s-d-block">{{ 'cart.tool.gift_wrap_html' | t: money: gift_pr_money }}</span>
             </div>
             <div class="t4s-field">
                 <a href="{{ gift_pr.url }}" data-variant-id="{{ gift_pr.variants.first.id }}" data-action-atc data-addd-gift class="t4s-mini_cart-tool__primary t4s-truncate t4s-btn-loading__svg">
                     <span class="t4s-btn-atc_text">{{ 'cart.tool.add_gift_wrap' | t }}</span> 
                     <div class="t4s-loading__spinner t4s-dn">
                         <svg  width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
                     </div>  
                 </a>
             </div>
             <div class="t4s-field">
                 <button type="button" data-cart-tool_close class="t4s-mini_cart-tool__back">{{ 'cart.tool.cancel' | t }}</button>
             </div>
         </div>
     </div>
   {%- endif -%}

   {%- if se_stts.enable_discount -%}
   <div class="t4s-mini_cart-tool__content is--discount t4s-pe-none">
     <div class="t4s-mini_cart-tool__wrap">
        <span class="t4s-mini_cart-tool__text">{{ 'cart.tool.add_coupon' | t }}</span>
        <p>{{ 'cart.tool.info_coupon' | t }}</p>
        <div class="t4s-field">
          <input type="text" data-opend-focus data-name="discount" id="CartDiscountcode" value placeholder="{{ 'cart.tool.placeholder_coupon' | t }}">
        </div>
        <div class="t4s-field">
          <button type="button" data-action="save-discountcode" data-cart-tool_close class="t4s-mini_cart-tool__primary">{{ 'cart.tool.save' | t }}</button>
        </div>
        <div class="t4s-field">
          <button type="button" data-cart-tool_close class="t4s-mini_cart-tool__back">{{ 'cart.tool.cancel' | t }}</button>
        </div>
     </div>
   </div>
   {%- endif -%}

{%- if request.design_mode %}</div>{% endif -%}

{%- schema -%}
{
  "name": "Shopping Cart Widget",
  "max_blocks": 20,
  "settings": [
    {
      "type": "paragraph",
      "content": "Those settings are only applicable when the cart contains at least one product."
    },
    {
      "type": "select",
      "id": "style",
      "options": [
        {
          "value": "1",
          "label": "Icon"
        },
        {
          "value": "2",
          "label": "Button text"
        }
      ],
      "label": "Buttons style"
    },
    {
      "type": "select",
      "id": "pos",
      "default": "2",
      "options": [
        {
          "value": "1",
          "label": "Top"
        },
        {
          "value": "2",
          "label": "Bottom"
        }
      ],
      "info": "Only working with button style icon",
      "label": "Position button icon"
    },
    {
      "type": "checkbox",
      "id": "enable_pr",
      "label": "Enable product recommendations",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_calc_ship",
      "label": "Enable calc shipping",
      "info": "Free shipping minimum amount.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_note",
      "label": "Enable order notes",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_rates",
      "label": "Enable shipping rates calculator",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_discount",
      "label": "Enable input box discounts codes",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_gift_wrap",
      "label": "Enable gift wrap",
      "default": true
    },
    {
      "type": "header",
      "content": "+ Shipping Rates Calculator"
    },
    {
      "type": "text",
      "id": "ship_df_country",
      "label": "Default country selection",
      "default": "United States"
    },
    {
      "type": "paragraph",
      "content": "If your customer is logged-in, the country in his default shipping address will be selected. If you are not sure about the  spelling to use here, refer to the first checkout page."
    }
  ],
  "blocks": [
    {
      "type": "price",
      "name": "Total price",
      "limit": 1
    },
    {
      "type": "tax",
      "name": "Taxes and shipping info",
      "limit": 1
    },
    {
      "type": "agree",
      "name": "Terms,conditions checkbox",
      "limit": 1
    },
    {
      "type": "btnc",
      "name": "Button Cart",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "btn_icon",
          "label": "Enable button icon",
          "default": false
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "label": "Default",
              "value": "default"
            },
            {
              "label": "Outline",
              "value": "outline"
            },
            {
              "label": "Bordered bottom",
              "value": "bordered"
            },
            {
              "label": "Link",
              "value": "link"
            }
          ]
        },
        {
          "type": "select",
          "id": "btn_cl",
          "label": "Button color",
          "default": "dark",
          "options": [
            {
              "value": "light",
              "label": "Light"
            },
            {
              "value": "dark",
              "label": "Dark"
            },
            {
              "value": "primary",
              "label": "Primary"
            },
            {
              "value": "custom1",
              "label": "Custom color 1"
            },
            {
              "value": "custom2",
              "label": "Custom color 2"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_effect",
          "label": "Button hover effect",
          "default": "default",
          "info": "Only working button style default, outline",
          "options": [
            {
              "label": "Default",
              "value": "default"
            },
            {
              "label": "Fade",
              "value": "fade"
            },
            {
              "label": "Rectangle out",
              "value": "rectangle-out"
            },
            {
              "label": "Sweep to right",
              "value": "sweep-to-right"
            },
            {
              "label": "Sweep to left",
              "value": "sweep-to-left"
            },
            {
              "label": "Sweep to bottom",
              "value": "sweep-to-bottom"
            },
            {
              "label": "Sweep to top",
              "value": "sweep-to-top"
            },
            {
              "label": "Shutter out horizontal",
              "value": "shutter-out-horizontal"
            },
            {
              "label": "Outline",
              "value": "outline"
            },
            {
              "label": "Shadow",
              "value": "shadow"
            }
          ]
        }
      ]
    },
    {
      "type": "btnck",
      "name": "Button Checkout",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "btn_icon",
          "label": "Enable button icon",
          "default": false
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "label": "Default",
              "value": "default"
            },
            {
              "label": "Outline",
              "value": "outline"
            },
            {
              "label": "Bordered bottom",
              "value": "bordered"
            },
            {
              "label": "Link",
              "value": "link"
            }
          ]
        },
        {
          "type": "select",
          "id": "btn_cl",
          "label": "Button color",
          "default": "dark",
          "options": [
            {
              "value": "light",
              "label": "Light"
            },
            {
              "value": "dark",
              "label": "Dark"
            },
            {
              "value": "primary",
              "label": "Primary"
            },
            {
              "value": "custom1",
              "label": "Custom color 1"
            },
            {
              "value": "custom2",
              "label": "Custom color 2"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_effect",
          "label": "Button hover effect",
          "default": "default",
          "info": "Only working button style default, outline",
          "options": [
            {
              "label": "Default",
              "value": "default"
            },
            {
              "label": "Fade",
              "value": "fade"
            },
            {
              "label": "Rectangle out",
              "value": "rectangle-out"
            },
            {
              "label": "Sweep to right",
              "value": "sweep-to-right"
            },
            {
              "label": "Sweep to left",
              "value": "sweep-to-left"
            },
            {
              "label": "Sweep to bottom",
              "value": "sweep-to-bottom"
            },
            {
              "label": "Sweep to top",
              "value": "sweep-to-top"
            },
            {
              "label": "Shutter out horizontal",
              "value": "shutter-out-horizontal"
            },
            {
              "label": "Outline",
              "value": "outline"
            },
            {
              "label": "Shadow",
              "value": "shadow"
            }
          ]
        }
      ]
    },
    {
      "type": "img",
      "name": "Image trust",
      "limit": 1,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "textarea",
          "id": "svg",
          "label": "SVG list",
          "default": "amazon_payments,american_express,apple_pay,bitcoin,dankort,diners_club,discover,dogecoin,dwolla,forbrugsforeningen,interac,google_pay,jcb,klarna,klarna-pay-later,litecoin,maestro,master,paypal,shopify_pay,sofort,visa",
          "info": "Review the [list of available values](https:\/\/github.com\/activemerchant\/payment_icons\/tree\/master\/app\/assets\/images\/payment_icons) and copy the name of each icon that you need from that list, without the .svg extension"
        },
        {
          "type": "number",
          "id": "height",
          "label": "SVG height",
          "default": 30
        }
      ]
    },
    {
      "type": "btn",
      "name": "Button emty",
      "settings": [
        {
          "type": "paragraph",
          "content": "Note: Only show when cart empty."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Button title",
          "default": "Return To Shop",
          "info": "If set blank will not show"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Button link"
        },
        {
          "type": "checkbox",
          "id": "btn_icon",
          "label": "Enable button icon",
          "default": false
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "label": "Default",
              "value": "default"
            },
            {
              "label": "Outline",
              "value": "outline"
            },
            {
              "label": "Bordered bottom",
              "value": "bordered"
            },
            {
              "label": "Link",
              "value": "link"
            }
          ]
        },
        {
          "type": "select",
          "id": "btn_cl",
          "label": "Button color",
          "default": "dark",
          "options": [
            {
              "value": "light",
              "label": "Light"
            },
            {
              "value": "dark",
              "label": "Dark"
            },
            {
              "value": "primary",
              "label": "Primary"
            },
            {
              "value": "custom1",
              "label": "Custom color 1"
            },
            {
              "value": "custom2",
              "label": "Custom color 2"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_effect",
          "label": "Button hover effect",
          "default": "fade",
          "info": "Only working button style default, outline",
          "options": [
            {
              "label": "Default",
              "value": "default"
            },
            {
              "label": "Fade",
              "value": "fade"
            },
            {
              "label": "Rectangle out",
              "value": "rectangle-out"
            },
            {
              "label": "Sweep to right",
              "value": "sweep-to-right"
            },
            {
              "label": "Sweep to left",
              "value": "sweep-to-left"
            },
            {
              "label": "Sweep to bottom",
              "value": "sweep-to-bottom"
            },
            {
              "label": "Sweep to top",
              "value": "sweep-to-top"
            },
            {
              "label": "Shutter out horizontal",
              "value": "shutter-out-horizontal"
            },
            {
              "label": "Outline",
              "value": "outline"
            },
            {
              "label": "Shadow",
              "value": "shadow"
            }
          ]
        }
      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "price"
      },
      {
        "type": "tax"
      },
      {
        "type": "agree"
      },
      {
        "type": "btnc"
      },
      {
        "type": "btnck"
      },
      {
        "type": "img"
      },
      {
        "type": "btn"
      }
    ]
  }
}
{% endschema %}