{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign form_id = 'password-main'
  assign checkbox_mail = settings.checkbox_mail
  assign buttonIcon = true
  assign platform_email = '94' | default: settings.platform_email
  assign html = 'div'
-%}


<div class="password-main">
  <div class="password-main__inner">
    <div class="t4s-container t4s-text-center">
      <div class="password-content">
        <h2 class="password-content__title">{{ se_stts.heading }}</h2>
        <p>{{ se_stts.content }}</p>
        {% if se_stts.date != blank %}
          {{ 'countdown.css' | asset_url | stylesheet_tag }}
          <div class="t4s-countdown t4s-countdown-des-1 t4s-countdown-size-large t4s-text-center" {{ block.shopify_attributes }} style="--number-cl:#ffffff;--text-cl:#000000;--border-cl:#000000;--bg-cl:#000000; --bd-width:0px; --bdr:0%; --space-item:20px;--space-item-mb: 10px;">
            <div class="time" data-countdown-t4s data-date='{{ se_stts.date }}'>
              <span class="countdown-days">
                <span class="cd_timet4 cd-number">%-D</span>
                <span class="cd_txtt4 cd-text">%!D:{{ "sections.countdown_text.day" | t }},{{ "sections.countdown_text.day_plural" | t }};</span>
              </span>
              <span class="countdown-hours">
                <span class="cd_timet4 cd-number">%H</span> 
                <span class="cd_txtt4 cd-text">%!H:{{ "sections.countdown_text.hr" | t }},{{ "sections.countdown_text.hr_plural" | t }};</span>
              </span>
              <span class="countdown-min">
                <span class="cd_timet4 cd-number">%M</span> 
                <span class="cd_txtt4 cd-text">%!M:{{ "sections.countdown_text.min" | t }},{{ "sections.countdown_text.min_plural" | t }};</span>
              </span>
              <span class="countdown-sec">
                <span class="cd_timet4 cd-number">%S</span> 
                <span class="cd_txtt4 cd-text">%!S:{{ "sections.countdown_text.sec" | t }},{{ "sections.countdown_text.sec_plural" | t }};</span>
              </span>
            </div>
          </div>
        {% endif %}

        {%- capture conditions_mail -%}
          {%- if checkbox_mail -%}
            <div class="t4s-clearfix"></div>
            {%- assign page_url = pages[settings.link_mail].url -%}
            <{{ html }} class="t4s-agree__checkbox is--email t4s-pr t4s-d-inline-block">
              <input type="checkbox" data-agreeMail-checkbox id="t4s-agree_{{ form_id }}" name="t4s-agree_{{ form_id }}" required="required">
              <label for="t4s-agree_{{ form_id }}">{% if page_url != blank %}{{ 'sections.agree_mail.link_conditions_html' | t: link: page_url }}{% else %}{{ 'sections.agree_mail.link_conditions_emty' | t }}{% endif %}</label>
              <svg class="t4s-dn t4s-icon_checked" viewBox="0 0 24 24"><path d="M9 20l-7-7 3-3 4 4L19 4l3 3z"></path></svg>
            </{{ html }}>
          {%- endif -%}
        {%- endcapture -%}
        {%- case platform_email -%}
          {%- when '3' -%}
            <form data-t4s-klaviyo-form data-form-mail-agree id="t4s-form-{{ form_id }}" class="t4s-pr t4s-z-100 t4s-newsletter__form is--klaviyo" action="//manage.kmail-lists.com/subscriptions/subscribe" data-ajax-submit="//manage.kmail-lists.com/ajax/subscriptions/subscribe" method="GET"{% if settings.ajax_klaviyo and settings.klaviyo_list_id != blank %} data-t4s-klaviyo-ajax{% endif %}>
              <input type="hidden" name="g" value="{{ settings.klaviyo_list_id }}">
              <div class="t4s-newsletter__fields">
                <div class="t4s-newsletter__inner t4s-row t4s-g-0 t4s-pr">
                  <div class="t4s-col-item is--col-email"><input type="email" name="email" placeholder="{{ section.settings.newsletter_placeholder | escape }}"  value="{% if customer %}{{ customer.email }}{% endif %}" class="t4s-newsletter__email" required="required"></div>
                  <div class="t4s-col-item is--col-btn">
                    <button data-t4s-klaviyo-submit data-agreeMail-btn type="submit" class="t4s-w-100 t4s-newsletter__submit t4s-truncate t4s-btn-loading__svg">
                      <span class="t4s-newsletter__text">{{ section.settings.newsletter_button_text | escape }}</span>
                      <span class="t4s-loading__spinner t4s-dn">
                        <svg width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
                      </span>
                    </button>
                  </div>
                </div>
                {{- conditions_mail -}}
              </div>
              <div class="t4s-newsletter__response klaviyo_messages">
                <div class="t4s-newsletter__success success_message t4s-dn"><svg width="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M443.3 100.7C449.6 106.9 449.6 117.1 443.3 123.3L171.3 395.3C165.1 401.6 154.9 401.6 148.7 395.3L4.686 251.3C-1.562 245.1-1.562 234.9 4.686 228.7C10.93 222.4 21.06 222.4 27.31 228.7L160 361.4L420.7 100.7C426.9 94.44 437.1 94.44 443.3 100.7H443.3z"/></svg>{{ section.settings.signup_form_success | escape }}</div>
                <div class="t4s-newsletter__error error_message t4s-dn"></div>
              </div>
            </form>

          {%- when '4' -%}
            <form id="t4s-form-{{ form_id }}" data-form-mail-agree role="form" action="{%- if settings.ajax_mailChimp -%}{{ settings.action_mailchimp | replace: '/post?u', '/post-json?u' }}{%- else -%}{{ settings.action_mailchimp }}{%- endif -%}" class="t4s-pr t4s-z-100 t4s-newsletter__form is--mailChimp" method="post"{%- if settings.ajax_mailChimp and settings.action_mailchimp != blank %} data-t4s-mailChimp-ajax{%- endif -%}>
              <div class="t4s-newsletter__fields">
                <div class="t4s-newsletter__inner t4s-row t4s-g-0 t4s-pr t4s-oh">
                  <div class="t4s-col-item is--col-email"><input type="email" name="EMAIL" placeholder="{{ section.settings.newsletter_placeholder | escape }}"  value="{% if customer %}{{ customer.email }}{% endif %}" class="t4s-newsletter__email" required="required"></div>
                  <div class="{{ class_btn }} t4s-col-item is--col-btn">
                    <button data-t4s-mailChimp-submit data-agreeMail-btn type="submit" class="t4s-w-100 t4s-newsletter__submit t4s-truncate t4s-btn-loading__svg">
                    <span class="t4s-newsletter__text">{{ section.settings.newsletter_button_text | escape }}</span>
                    <span class="t4s-loading__spinner t4s-dn">
                      <svg width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
                    </span>
                  </button>
                </div>
                </div>
                {{- conditions_mail -}}
              </div>
              <div data-new-response-form class="t4s-newsletter__response">
                <div data-new-response-success class="t4s-newsletter__success t4s-dn"><svg width="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M443.3 100.7C449.6 106.9 449.6 117.1 443.3 123.3L171.3 395.3C165.1 401.6 154.9 401.6 148.7 395.3L4.686 251.3C-1.562 245.1-1.562 234.9 4.686 228.7C10.93 222.4 21.06 222.4 27.31 228.7L160 361.4L420.7 100.7C426.9 94.44 437.1 94.44 443.3 100.7H443.3z"/></svg>{{ section.settings.signup_form_success | escape }}</div>
                <div data-new-response-error class="t4s-newsletter__error t4s-dn"></div>
              </div>
            </form>

          {%- else -%}

            {%- assign form_id = 't4s-form-' | append: form_id -%}
            {%- form 'customer', class: 't4s-pr t4s-z-100 t4s-newsletter__form', id: form_id, data-form-mail-agree: '' -%}
              <input type="hidden" name="contact[tags]" value="newsletter">
              <div class="t4s-newsletter__fields">
                <div class="t4s-newsletter__inner t4s-row t4s-g-0 t4s-pr t4s-oh">
                  <div class="{{ class_email }} t4s-col-item is--col-email">
                    <input type="email" name="contact[email]" placeholder="{{ section.settings.newsletter_placeholder | escape }}"  value="{% if customer %}{{ customer.email }}{% endif %}" class="t4s-newsletter__email" required="required">
                  </div>
                  <div class="t4s-col-item is--col-btn">
                    <button data-agreeMail-btn type="submit" class="t4s-w-100 t4s-newsletter__submit t4s-truncate t4s-btn-loading__svg">
                      <span class="t4s-newsletter__text">{{ section.settings.newsletter_button_text | escape }}</span>
                      <span class="t4s-loading__spinner t4s-dn">
                        <svg width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
                      </span>
                    </button>
                  </div>
                </div>
                {{- conditions_mail -}}
              </div>
              <div data-new-response-form class="t4s-newsletter__response">
                {%- if form.posted_successfully? -%}
                <div class="t4s-newsletter__success"><svg width="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M443.3 100.7C449.6 106.9 449.6 117.1 443.3 123.3L171.3 395.3C165.1 401.6 154.9 401.6 148.7 395.3L4.686 251.3C-1.562 245.1-1.562 234.9 4.686 228.7C10.93 222.4 21.06 222.4 27.31 228.7L160 361.4L420.7 100.7C426.9 94.44 437.1 94.44 443.3 100.7H443.3z"/></svg>{{ section.settings.signup_form_success | escape }}</div>
                {%- elsif form.errors -%}
                <div class="t4s-newsletter__error">{{ form.errors | default_errors }}</div>{%- endif -%}
              </div>
            {%- endform -%}
        {%- endcase -%}

        {%- if se_stts.social != '0' -%}
          {{ 'icon-social.css' | asset_url | stylesheet_tag }}
          <div class="t4s-socials-block t4s-setts-color-true" style="--cl:#000;">
            {%- if se_stts.social == '2' -%} 
              {%- assign follow_social = true -%} 
            {%- else -%} 
              {%- assign share_image = settings.share_image | default: page_image | default: settings.logo -%} 
            {%- endif -%} 
            {%- render 'social_sharing', style: 1, use_color_set: true, size: 'small', space_h_item: 20, space_h_item_mb: 20, space_v_item: 20, space_v_item_mb: 20, share_permalink: shop.url, share_title: shop.name, share_image: share_image, follow_social: follow_social -%} 
          </div>
        {%- endif -%}
      </div>
    </div>
  </div>
</div>
<style>
  @media (min-width: 1025px){
    #MainContent {
      height: 100vh;
      flex: 1 0 0%;
      justify-content: center;
      overflow-y: auto;
      display: flex;
      align-items: center;
    }
  }
  #MainContent {
    padding-top: 50px;
  }
  .password-main__inner {
    width: 600px;
    margin: auto;
    max-width: 90%;
  }
  h2.password-content__title {
    margin-bottom: 20px;
  }
  input.t4s-newsletter__email {
    width: 100%;
    font-size: 13px;
    text-align: center;
    color: var(--text-color);
  } 
  .t4s-newsletter__submit {
    background: var(--accent-color);
    margin-top: 20px;
    box-shadow: 0 5px 15px 0 rgb(0 0 0 / 15%);
    position: relative;
  }
  
  .t4s-newsletter__submit:hover {
    opacity: 0.8;
  }
  .t4s-text-center .t4s-socials {
    justify-content: center;
    margin: 0;
  }
  .t4s-newsletter__success {
    font-size: 13px;
    display: flex;
    justify-content: center;
    column-gap: 5px;
  }
  .t4s-newsletter__response{
    margin-top: 15px;
  }
  .t4s-countdown {
    margin-bottom: 30px;
  }
  .t4s-btn-loading__svg.is--loading>.t4s-loading__spinner {
    top: 50%;
    left: 50%;
    width: 1.8rem;
    transform: translate(-50%,-50%);
    position: absolute;
    display: flex;
  }
  .t4s-btn-loading__svg.is--loading .t4s-svg__spinner, .t4s-svg-spinner:not([hidden]) {
    animation: 1.4s linear infinite t4s_rotator;
  }
  .t4s-newsletter__inner .t4s-newsletter__submit svg {
      margin-left: 5px;
  }
  .t4s-btn-loading__svg.is--loading span.t4s-newsletter__text {
    opacity: 0;
    visibility: hidden;
  }
  @keyframes t4s_rotator{0%{transform:rotate(0)}100%{transform:rotate(270deg) }} 
  input[type=checkbox][data-agreeMail-checkbox] {
    opacity: 0;
    position: absolute;
    /* display: none;
    pointer-events: none;
    visibility: hidden; */
  }
  input[type=checkbox][data-agreeMail-checkbox]+label:before {
      content: '';
      display: inline-block;
      margin-right: 10px;
      width: 16px;
      height: 16px;
      min-width: 16px;
      border: 1px solid var(--border-color);
      background: var(--t4s-color-light);
      box-shadow: 0 1px rgb(212 214 216 / 40%);
      border-radius: 2px;
      -webkit-appearance: none;
      box-shadow: none;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: 50%;
      transition: .2s ease-in-out;
      position: relative;
      top: 3px;
  }
  input[type=checkbox][data-agreeMail-checkbox]~svg {
      display: block;
      width: 12px;
      height: 12px;
      fill: var(--t4s-light-color);
      position: absolute;
      top: 5px;
      left: 2px;
      pointer-events: none;
      transform: scale(0);
      -webkit-transform: scale(0);
      -webkit-transition: .25s ease-in-out;
      transition: .25s ease-in-out;
  }
  input[type=checkbox][data-agreeMail-checkbox]:checked+label:before {
      background-color: var(--accent-color);
      border-color: var(--accent-color);
  }
  input[type=checkbox][data-agreeMail-checkbox]:checked~svg {
      transform: scale(1);
      -webkit-transform: scale(1);
  }
  .t4s-agree__checkbox.is--email {
    margin-top: 12px;
  }
</style>
{%- schema -%}
  {
    "name": "Password",
    "class": "t4s-section t4s-section-all t4s_tp_cdt t4s_tp_cd t4s-se-countdown",
     "settings": [
      {
        "type": "text",
        "id": "heading",
        "label": "Heading",
        "default": "Coming Soon!"
      },
      {
        "type": "textarea",
        "id": "content",
        "label": "Content",
        "default": "We are about to go public so be sure to root on us and check back in anytime!<br> Can't wait to have you!"
      },
     /*{
        "type": "text",
        "id": "date",
        "label": "Date countdown",
        "default": "2022\/12\/26",
        "info": "Countdown to bring the site out"
      },*/
       {
        "type": "text",
        "id": "newsletter_placeholder",
        "label": "Newsletter placeholder text",
        "default": "Your email address"
      },
      {
        "type": "text",
        "id": "newsletter_button_text",
        "label": "Newsletter button text",
        "default": "Notify me"
      },
      {
        "type": "text",
        "id": "signup_form_success",
        "label": "Signup form success",
        "default": "We will send you an email right before we open!"
      },
      {
        "type": "select",
        "id": "social",
        "options": [
          {
            "value": "0",
            "label": "None"
          },
          {
            "value": "1",
            "label": "Share"
          },
          {
            "value": "2",
            "label": "Follow"
          }
        ],
        "label": "Social:",
        "default": "2"
      }
      
     ]
  }
{% endschema %}