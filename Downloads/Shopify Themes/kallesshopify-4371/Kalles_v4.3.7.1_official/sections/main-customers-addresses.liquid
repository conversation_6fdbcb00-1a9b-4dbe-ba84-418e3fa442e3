{{ 'customer.min.css' | asset_url | stylesheet_tag }}
<script src="{{ 'customer.min.js' | asset_url }}" defer></script>

{%- paginate customer.addresses by 5 -%}
<div class="t4s-customer t4s-customer-account is--addresses t4s-row" data-customer-addresses>
  <div class="t4s-col-12 t4s-col-md-3 t4s-col-item t4s-account-sidebar">
      <nav class="t4s-account-nav">
         <ul>
            <li class="t4s-account-nav-link t4s-account-nav-link--dashboard">
               <a href="{{ routes.account_url }}"><svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round" class="css-i6dzq1"><rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect></svg> {{ 'customer.account.dashboard' | t }}</a>
            </li>
            <li class="t4s-account-nav-link t4s-account-nav-link--edit-address is--active t4s-pe-none">
               <a href="{{ routes.account_addresses_url }}"><svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round" class="css-i6dzq1"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg> {{ 'customer.addresses.title' | t }} ({{ customer.addresses_count }})</a>
            </li>
            {%- if settings.wishlist_mode != '0' -%}
            <li class="t4s-account-nav-link t4s-account-nav-link--wishlist">
               {%- if settings.wishlist_mode != '3' -%}
               <a data-link-wishlist href="{{ routes.search_url }}/?view=wishlist"><svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round" class="css-i6dzq1"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg> {{ 'customer.account.wishlist' | t }} (<span data-count-wishlist class="t4s-wcount">0</span>)</a>
               {%- else -%}
               <a href="/pages/wishlist"><svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round" class="css-i6dzq1"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg> {{ 'customer.account.wishlist' | t }} (<span data-count-wis class="t4s-wcount ssw-counter-fave-menu">0</span>)</a>
               {%- endif -%}
            </li>
            {%- endif -%}
            <li class="t4s-account-nav-link t4s-account-nav-link--customer-logout">
              <a  href="{{ routes.account_logout_url }}" data-no-instant><svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round" class="css-i6dzq1"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path><polyline points="16 17 21 12 16 7"></polyline><line x1="21" y1="12" x2="9" y2="12"></line></svg> {{ 'customer.account.log_out' | t }}</a>
            </li>
         </ul>
      </nav>
  </div>
  <div class="t4s-col-12 t4s-col-md-9 t4s-col-item t4s-account-content t4s-text-center">
    <svg style="display: none">
      <symbol id="icon-caret" viewBox="0 0 10 6">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.354.646a.5.5 0 00-.708 0L5 4.293 1.354.646a.5.5 0 00-.708.708l4 4a.5.5 0 00.708 0l4-4a.5.5 0 000-.708z" fill="currentColor">
      </symbol>
    </svg>
    <div data-address>
      <button class="t4s_btn_black" 
        type="button"
        aria-expanded="false"
        aria-controls="AddAddress">
        {{ 'customer.addresses.add_new' | t }}
      </button>
      <div id="AddAddress">
        <h2 id="AddressNewHeading" class="t4s_title_addresses">{{ 'customer.addresses.add_new' | t }}</h2>
        {%- form 'customer_address', customer.new_address, aria-labelledBy: 'AddressNewHeading' -%}
          <div class="t4s_field t4s-pr">
            <input class="t4s_frm_input" type="text" id="AddressFirstNameNew" name="address[first_name]" value="{{ form.first_name }}" autocomplete="given-name" placeholder="{{ 'customer.addresses.first_name' | t }}">
            <label for="AddressFirstNameNew">{{ 'customer.addresses.first_name' | t }}</label>
          </div>
          <div class="t4s_field t4s-pr">
            <input class="t4s_frm_input" type="text" id="AddressLastNameNew" name="address[last_name]" value="{{ form.last_name }}" autocomplete="family-name" placeholder="{{ 'customer.addresses.last_name' | t }}">
            <label for="AddressLastNameNew">{{ 'customer.addresses.last_name' | t }}</label>
          </div>
          <div class="t4s_field t4s-pr">
            <input class="t4s_frm_input" type="text" id="AddressCompanyNew" name="address[company]" value="{{ form.company }}" autocomplete="organization" placeholder="{{ 'customer.addresses.company' | t }}">
            <label for="AddressCompanyNew">{{ 'customer.addresses.company' | t }}</label>
          </div>
          <div class="t4s_field t4s-pr">
            <input class="t4s_frm_input" type="text" id="AddressAddress1New" name="address[address1]" value="{{ form.address1 }}" autocomplete="address-line1" placeholder="{{ 'customer.addresses.address1' | t }}">
            <label for="AddressAddress1New">{{ 'customer.addresses.address1' | t }}</label>
          </div>
          <div class="t4s_field t4s-pr">
            <input class="t4s_frm_input" type="text" id="AddressAddress2New" name="address[address2]" value="{{ form.address2 }}" autocomplete="address-line2" placeholder="{{ 'customer.addresses.address2' | t }}">
            <label for="AddressAddress2New">{{ 'customer.addresses.address2' | t }}</label>
          </div>
          <div class="t4s_field t4s-pr">
            <input class="t4s_frm_input" type="text" id="AddressCityNew" name="address[city]" value="{{ form.city }}" autocomplete="address-level2" placeholder="{{ 'customer.addresses.city' | t }}">
            <label for="AddressCityNew">{{ 'customer.addresses.city' | t }}</label>
          </div>
          <div>
            <label for="AddressCountryNew">{{ 'customer.addresses.country' | t }}</label>
            <div class="select">
              <select
                id="AddressCountryNew"
                name="address[country]"
                data-default="{{ form.country }}"
                autocomplete="country">
                {{ all_country_option_tags }}
              </select>
<!--               <svg aria-hidden="true" focusable="false" viewBox="0 0 10 6">
                <use href="#icon-caret" />
              </svg> -->
            </div>
          </div>
          <div id="AddressProvinceContainerNew" style="display: none">
            <label for="AddressProvinceNew">{{ 'customer.addresses.province' | t }}</label>
            <div class="select">
              <select
                id="AddressProvinceNew"
                name="address[province]"
                data-default="{{ form.province }}"
                autocomplete="address-level1"
              >
              </select>
<!--               <svg aria-hidden="true" focusable="false" viewBox="0 0 10 6">
                <use href="#icon-caret" />
              </svg> -->
            </div>
          </div>
          <div class="t4s_field t4s-pr">
            <input class="t4s_frm_input" type="text" id="AddressZipNew" name="address[zip]" value="{{ form.zip }}" autocapitalize="characters" autocomplete="postal-code" placeholder="{{ 'customer.addresses.zip' | t }}">
            <label for="AddressZipNew">{{ 'customer.addresses.zip' | t }}</label>
          </div>
          <div class="t4s_field t4s-pr">
            <input class="t4s_frm_input" type="tel" id="AddressPhoneNew" name="address[phone]" value="{{ form.phone }}" autocomplete="tel" placeholder="{{ 'customer.addresses.phone' | t }}">
            <label for="AddressPhoneNew">{{ 'customer.addresses.phone' | t }}</label>
          </div>
          <div>
            {{ form.set_as_default_checkbox }}
            <label for="address_default_address_new">{{ 'customer.addresses.set_default' | t }}</label>
          </div>
          <div>
            <button class="t4s_btn_black">{{ 'customer.addresses.add' | t }}</button>
            <button class="t4s_btn_white" type="reset">{{ 'customer.addresses.cancel' | t }}</button>
          </div>
        {%- endform -%}
      </div>
    </div>

    <ul role="list">
      {%- for address in customer.addresses -%}
        <li data-address>
          {%- if address == customer.default_address -%}
            <h2>{{ 'customer.addresses.default' | t }}</h2>
          {%- endif -%}
          {{ address | format_address }}
          <button class="t4s_btn_black" 
            type="button"
            id="EditFormButton_{{ address.id }}"
            aria-label="{{ 'customer.addresses.edit_address' | t }} {{ forloop.index }}"
            aria-controls="EditAddress_{{ address.id }}"
            aria-expanded="false"
            data-address-id="{{ address.id }}"
          >
            {{ 'customer.addresses.edit' | t }}
          </button>
          <button class="t4s_btn_white"
            type="button"
            aria-label="{{ 'customer.addresses.delete' | t }} {{ forloop.index }}"
            data-target="{{ address.url }}"
            data-confirm-message="{{ 'customer.addresses.delete_confirm' | t }}"
          >
            {{ 'customer.addresses.delete' | t }}
          </button>
          <div id="EditAddress_{{ address.id }}">
            <h2 class="t4s_title_addresses">{{ 'customer.addresses.edit_address' | t }}</h2>
            {%- form 'customer_address', address -%}
              <div class="t4s_field t4s-pr">
                <input class="t4s_frm_input" type="text" id="AddressFirstName_{{ form.id }}" name="address[first_name]" value="{{ form.first_name }}" autocomplete="given-name" placeholder="{{ 'customer.addresses.first_name' | t }}">
                <label for="AddressFirstName_{{ form.id }}">{{ 'customer.addresses.first_name' | t }}</label>
              </div>
              <div class="t4s_field t4s-pr">
                <input class="t4s_frm_input" type="text" id="AddressLastName_{{ form.id }}" name="address[last_name]" value="{{ form.last_name }}" autocomplete="family-name" placeholder="{{ 'customer.addresses.last_name' | t }}">
                <label for="AddressLastName_{{ form.id }}">{{ 'customer.addresses.last_name' | t }}</label>
              </div>
              <div class="t4s_field t4s-pr">
                <input class="t4s_frm_input" type="text" id="AddressCompany_{{ form.id }}" name="address[company]" value="{{ form.company }}" autocomplete="organization" placeholder="{{ 'customer.addresses.company' | t }}">
                <label for="AddressCompany_{{ form.id }}">{{ 'customer.addresses.company' | t }}</label>
              </div>
              <div class="t4s_field t4s-pr">
                <input class="t4s_frm_input" type="text" id="AddressAddress1_{{ form.id }}" name="address[address1]" value="{{ form.address1 }}" autocomplete="address-line1" placeholder="{{ 'customer.addresses.address1' | t }}">
                <label for="AddressAddress1_{{ form.id }}">{{ 'customer.addresses.address1' | t }}</label>
              </div>
              <div class="t4s_field t4s-pr">
                <input class="t4s_frm_input" type="text" id="AddressAddress2_{{ form.id }}" name="address[address2]" value="{{ form.address2 }}" autocomplete="address-line2" placeholder="{{ 'customer.addresses.address2' | t }}">
                <label for="AddressAddress2_{{ form.id }}">{{ 'customer.addresses.address2' | t }}</label>
              </div>
              <div class="t4s_field t4s-pr">
                <input class="t4s_frm_input" type="text" id="AddressCity_{{ form.id }}" name="address[city]" value="{{ form.city }}" autocomplete="address-level2" placeholder="{{ 'customer.addresses.city' | t }}">
                <label for="AddressCity_{{ form.id }}">{{ 'customer.addresses.city' | t }}</label>
              </div>
              <div>
                <label for="AddressCountry_{{ form.id }}">
                  {{ 'customer.addresses.country' | t }}
                </label>
                <div class="select">
                  <select
                    id="AddressCountry_{{ form.id }}"
                    name="address[country]"
                    data-address-country-select
                    data-default="{{ form.country }}"
                    data-form-id="{{ form.id }}"
                    autocomplete="country"
                  >
                    {{ all_country_option_tags }}
                  </select>
<!--                   <svg aria-hidden="true" focusable="false" viewBox="0 0 10 6">
                    <use href="#icon-caret" />
                  </svg> -->
                </div>
              </div>
              <div id="AddressProvinceContainer_{{ form.id }}" style="display:none;">
                <label for="AddressProvince_{{ form.id }}">
                  {{ 'customer.addresses.province' | t }}
                </label>
                <div class="select">
                  <select
                    id="AddressProvince_{{ form.id }}"
                    name="address[province]"
                    data-default="{{ form.province }}"
                    autocomplete="address-level1"
                  >
                  </select>
<!--                   <svg aria-hidden="true" focusable="false" viewBox="0 0 10 6"><use href="#icon-caret" /> </svg> -->
                </div>
              </div>
              <div class="t4s_field t4s-pr">
                <input class="t4s_frm_input" type="text" id="AddressZip_{{ form.id }}" name="address[zip]" value="{{ form.zip }}" autocapitalize="characters" autocomplete="postal-code" placeholder="{{ 'customer.addresses.zip' | t }}">
                <label for="AddressZip_{{ form.id }}">{{ 'customer.addresses.zip' | t }}</label>
              </div>
              <div class="t4s_field t4s-pr">
                <input class="t4s_frm_input" type="tel" id="AddressPhone_{{ form.id }}"  name="address[phone]" value="{{ form.phone }}" autocomplete="tel" placeholder="{{ 'customer.addresses.phone' | t }}">
                <label for="AddressPhone_{{ form.id }}">{{ 'customer.addresses.phone' | t }}</label>
              </div>
              <div>
                {{ form.set_as_default_checkbox }}
                <label for="address_default_address_{{ form.id }}">
                  {{ 'customer.addresses.set_default' | t }}
                </label>
              </div>
              <div>
                <button class="t4s_btn_black">{{ 'customer.addresses.update' | t }}</button>
                <button class="t4s_btn_white" type="reset">{{ 'customer.addresses.cancel' | t }}</button>
              </div>
            {%- endform -%}
          </div>
        </li>
      {%- endfor -%}
    </ul>

    {%- if paginate.pages > 1 -%}{% render 'pagination', paginate: paginate, ajax:false %}{%- endif -%}

  </div>
</div>
{%- endpaginate -%}
<script>
  document.addEventListener('DOMContentLoaded', function() {
     typeof CustomerAddresses !== 'undefined' && new CustomerAddresses();
  });
  {%- comment -%}
  window.onload = () => {
    typeof CustomerAddresses !== 'undefined' && new CustomerAddresses();
  }
  {%- endcomment -%}
</script>


{%- schema -%}
{
  "name": "Addresses",
  "tag": "section",
  "class": "t4s-section t4s-section-customers t4s-container"
}
{% endschema %}