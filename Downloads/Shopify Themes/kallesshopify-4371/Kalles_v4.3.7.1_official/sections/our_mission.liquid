<!-- sections/our_mission.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{%- liquid
    assign sid = section.id
    assign se_stts = section.settings
    assign se_blocks = section.blocks
    assign stt_layout = se_stts.layout
    assign stt_image_bg = se_stts.image_bg
    if stt_layout == 't4s-se-container' 
        assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
    elsif stt_layout == 't4s-container-wrap'
        assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
    else
        assign html_layout = '__' | split: '__'
    endif
    assign t4s_se_class = 't4s_nt_se_' | append: sid
    if se_stts.use_cus_css and se_stts.code_cus_css != blank
        render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
    endif 
 -%}
<div class="t4s-section-inner {{ t4s_se_class }} t4s_nt_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
    {{- html_layout[0] -}}
        {%- if stt_layout == 't4s-se-container' -%}
        <div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
            {%- render 'section_tophead', se_stts: se_stts -%}
            <div class="t4s-our-mission__list t4s-row t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }}">
                {%- for block in se_blocks -%}
                    {%- assign bk_stts = block.settings -%}
                    <div class="t4s-col-item t4s-col-md-{{ bk_stts.col_tb }} t4s-col-{{ bk_stts.col_mb }} t4s-text-{{ bk_stts.text_align }}" timeline hdt-reveal="slide-in"> 
                        <h3>{{ bk_stts.heading }}</h3>
                        <p>{{ bk_stts.content }}</p>
                    </div>
                {%- endfor -%}
            </div>
    {{- html_layout[1] -}}
</div>
<style>
    .t4s-our-mission h3{margin-bottom:10px;font-size: 20px;}
    .t4s-our-mission p{margin:0px;}
</style>
{%- schema -%}
{
    "name":"Our mission",
    "tag":"section",
    "class":"t4s-section t4s-section-all t4s-our-mission",
    "settings":[
        {
            "type": "header",
            "content": "1. Heading options"
        },
        {
            "type": "select",
            "id": "design_heading",
            "label": "+ Design heading",
            "default": "1",
            "options": [
                {
                    "value": "1",
                    "label": "Design 01"
                },
                {
                    "value": "2",
                    "label": "Design 02"
                },
                {
                    "value": "3",
                    "label": "Design 03"
                },
                {
                    "value": "4",
                    "label": "Design 04"
                },
                {
                    "value": "5",
                    "label": "Design 05"
                },
                {
                    "value": "6",
                    "label": "Design 06 (width line-awesome)"
                },
                {
                    "value": "7",
                    "label": "Design 07"
                },
                {
                    "value": "8",
                    "label": "Design 08"
                },
                {
                    "value": "9",
                    "label": "Design 09"
                },
                {
                    "value": "10",
                    "label": "Design 10"
                },
                {
                    "value": "11",
                    "label": "Design 11"
                },
                {
                    "value": "12",
                    "label": "Design 12"
                },
                {
                    "value": "13",
                    "label": "Design 13"
                },
                {
                    "value": "14",
                    "label": "Design 14"
                },
                {
                    "value": "15",
                    "label": "Design 15"
                },
                {
                  "value": "16",
                  "label": "Design 16"
                }
            ]
        },
        {
            "type": "select",
            "id": "heading_align",
            "label": "+ Heading align",
            "default": "t4s-text-center",
            "options": [
                {
                    "value": "t4s-text-start",
                    "label": "Left"
                },
                {
                    "value": "t4s-text-center",
                    "label": "Center"
                },
                {
                    "value": "t4s-text-end",
                    "label": "Right"
                }
            ]
        },
        {
            "type": "text",
            "id": "top_heading",
            "label": "+ Heading"
        },
        {
            "type": "text",
            "id": "icon_heading",
            "label": "Enter a name icon [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
            "info": "Only used for design 6",
            "default": "las la-gem"
        },
        {
            "type": "textarea",
            "id": "top_subheading",
            "label": "+ Subheading"
        },
        {
            "type": "number",
            "id": "tophead_mb",
            "label": "+ Space bottom (px)",
            "info": "The vertical spacing between heading and content.",
            "default": 30
        },
        {
            "type": "header",
            "content": "2. General options"
        },
        {
            "type": "select",
            "id": "space_h_item",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                }
            ],
            "label": "Space horizontal between items",
            "default": "30"
        },
        {
            "type": "select",
            "id": "space_v_item",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                }
            ],
            "label": "Space vertical vertical items",
            "default": "30"
        },
        {
            "type": "select",
            "id": "space_h_item_mb",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                }
            ],
            "label": "Space horizontal between items (Mobile)",
            "default": "10"
        },
        {
            "type": "select",
            "id": "space_v_item_mb",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                }
            ],
            "label": "Space vertical vertical items (Mobile)",
            "default": "10"
        },
        {
            "type": "header",
            "content": "3.Design options"
        },
        {
            "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
            "options": [
                { "value": "t4s-se-container", "label": "Container"},
                { "value": "t4s-container-wrap", "label": "Wrapped container"},
                { "value": "t4s-container-fluid", "label": "Full width"}
            ]
        },
        {
            "type": "color",
            "id": "cl_bg",
            "label": "Background"
        },
        {
            "type": "color_background",
            "id": "cl_bg_gradient",
            "label": "Background gradient"
        },
        {
            "type": "image_picker",
            "id": "image_bg",
            "label": "Background image"
        },
        {
            "type": "text",
            "id": "mg",
            "label": "Margin",
            "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
            "default": ",,50px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd",
            "label": "Padding",
            "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
            "placeholder": "50px,,50px,"
        },
        {
          "type": "header",
          "content": "+ Design Tablet Options"
        },
        {
          "type": "text",
          "id": "mg_tb",
          "label": "Margin",
          "placeholder": ",,50px,"
        },
        {
          "type": "text",
          "id": "pd_tb",
          "label": "Padding",
          "placeholder": ",,50px,"
        },
        {
            "type": "header",
            "content": "+ Design mobile options"
        },
        {
            "type": "text",
            "id": "mg_mb",
            "label": "Margin",
            "default": ",,30px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_mb",
            "label": "Padding",
            "placeholder": ",,50px,"
        },
        {
            "type": "header",
            "content": "4. Custom css"
        },
        {
            "id": "use_cus_css",
            "type": "checkbox",
            "label": "Use custom css",
            "default":false,
            "info": "If you want custom style for this section."
        },
        { 
            "id": "code_cus_css",
            "type": "textarea",
            "label": "Code custom css",
            "info": "Use selector .SectionID to style css"
            
        }
    ],
    "blocks":[
        {
            "type":"om_item",
            "name":"Our mission",
            "settings":[
                {
                    "type":"text",
                    "id":"heading",
                    "label":"Heading",
                    "default":"OUR MISSION"
                },
                {
                    "type":"textarea",
                    "id":"content",
                    "label":"Content",
                    "default":"Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, <em>totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae</em> vitae dicta sunt explicabo nemo enim ipsam."
                },
                {
                    "type": "select",
                    "id": "text_align",
                    "label": "Text alignment",
                    "default": "start",
                    "options":[
                        {
                            "label":"Left",
                            "value":"start"
                        },
                        {
                            "label":"Center",
                            "value":"center"
                        },
                        {
                            "label":"Right",
                            "value":"end"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "col_tb",
                    "label": "Width",
                    "default": "6",
                    "options": [
                        {
                            "value": "12",
                            "label": "100%"
                        },
                        {
                            "value": "6",
                            "label": "50%"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "col_mb",
                    "label": "Width (Tablet)",
                    "default": "6",
                    "options": [
                        {
                            "value": "12",
                            "label": "100%"
                        },
                        {
                            "value": "6",
                            "label": "50%"
                        }
                    ]
                }
            ]
        }
    ],
    "presets": [
        {
            "name": "Our mission",
            "category": "homepage",
            "blocks": [
                {"type": "om_item"},
                {"type": "om_item"},
                {"type": "om_item"},
                {"type": "om_item"}
            ]
        }
    ]
}
{% endschema %}