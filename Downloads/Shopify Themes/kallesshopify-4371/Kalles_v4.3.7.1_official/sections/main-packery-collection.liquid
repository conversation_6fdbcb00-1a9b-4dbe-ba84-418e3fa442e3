<!-- sections/main-collection.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'collection-pages.css' | asset_url | stylesheet_tag }}
{{ 'collection-products.css' | asset_url | stylesheet_tag }}
{{ 'collection-products-list.css' | asset_url | stylesheet_tag }}
{{ 'button-style.css' | asset_url | stylesheet_tag }}
<link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
<link href="{{ 'loading.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid 
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign image_ratio = se_stts.image_ratio
  if image_ratio == "ratioadapt"
    assign imgatt = ''
   else 
    assign imgatt = 'data-'
  endif
  assign use_pagination = se_stts.use_pagination 
  assign sett_equal = se_stts.use_eq_height
  assign show_vendor = se_stts.show_vendor
  assign use_link_vendor = settings.use_link_vendor
  assign enable_rating = settings.enable_rating
  assign inc_pr = se_stts.pr_des
  assign limit = se_stts.limit
  assign placeholder_img = settings.placeholder_img
  assign nt_filter = 'nt_filter'

  assign show_img = settings.show_img
  assign isGrowaveWishlist = false
  if settings.wishlist_mode == "3" and shop.customer_accounts_enabled
    assign isGrowaveWishlist = true
  endif
  assign enable_pr_size = settings.enable_pr_size
  assign pr_size_pos = settings.pr_size_pos
  assign show_size_type = settings.show_size_type
  assign size_ck = settings.size_ck | append: ',size,sizes,Größe' 
  assign get_size = size_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

  assign enable_pr_color = settings.enable_pr_color
  assign show_cl_type = settings.show_color_type
  assign color_ck = settings.color_ck | append: ',color,colors,couleur,colour'
  assign get_color = color_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

  assign price_varies_style = settings.price_varies_style
  assign app_review = settings.app_review
  assign use_countdown = se_stts.use_cdt
  
  assign txt_cd = 'products.grid_items.offer_end_in' | t
  
  assign enable_bar_lm = se_stts.enable_bar_lm
  assign results_count = collection.products_count

  assign isLoadmore = false
  if use_pagination == "load-more" or use_pagination == "infinite" 
    assign isLoadmore = true
    assign typeAjax = 'LmIsotope'
  else
    assign typeAjax = 'AjaxIsotope'
  endif
  assign type_filters = section.settings.type_filters
  assign se_blocks_type = section.blocks | map: "type"

 -%}
  {%- if se_blocks_type contains "filter" -%}
  <link rel="stylesheet" href="{{ 'drawer.min.css' | asset_url }}" media="all">
  <link rel="stylesheet" href="{{ 'facets.css' | asset_url }}" media="all">
  {%- endif -%}

{%- paginate collection.products by limit -%}
<div class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }} {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s {% endif %}"  {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} {% render 'section_style', se_stts: se_stts %} >
  {{- html_layout[0] -}}
  {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner {% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s {% endif %} "  {% if stt_image_bg != blank %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} > {%- endif -%}
  {%- if se_stts.position_desc == '1' and se_stts.show_desc -%}<div id="t4s-desc-collection" class="t4s-desc-collection t4s-desc-before">{{ collection.description }}</div>{%- endif -%} 
  {% if se_blocks.size > 0 %}
    <div class="t4s-collection-header t4s-d-flex 4s-align-items-center">
      {%- for block in se_blocks -%}
        {%- assign bk_stts = block.settings -%}
        {%- case block.type -%}
          {%- when 'sortby' -%} 
            {%- assign sort_by_true = true -%}
            {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
              {%- assign option_selected = collection.sort_options | where: "value",sort_by | first -%}
              <div class="t4s-dropdown t4s-dropdown__sortby">
                  <button data-dropdown-open data-position="bottom-end" data-id="t4s__sortby"><span class="t4s-d-none t4s-d-md-block">{{ option_selected.name | escape }}</span><span class="t4s-d-md-none">{{ 'collections.general.sort_button' | t }}</span><svg class="t4s-icon-select-arrow" role="presentation" viewBox="0 0 19 12"><use xlink:href="#t4s-select-arrow"></use></svg></button>
                  <div data-dropdown-wrapper class="t4s-dropdown__wrapper" id="t4s__sortby">
                     <div class="t4s-drop-arrow"></div>
                     <div class="t4s-dropdown__header">
                        <span class="t4s-dropdown__title">{{ 'collections.general.sort_by_label' | t }}</span>
                        <button data-dropdown-close aria-label="{{ 'general.aria.close' | t }}"><svg role="presentation" class="t4s-iconsvg-close" viewBox="0 0 16 14"><path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path></svg></button>
                     </div>
                     <div class="t4s-dropdown__content">
                        {%- for option in collection.sort_options -%}
                        <button data-dropdown-item data-sortby-item data-value="{{ option.value | escape }}"{% if option.value == sort_by %} class="is--selected"{% endif %}>{{ option.name | escape }}</button>
                        {%- endfor -%}
                     </div>
                  </div>
              </div> 
          {%- when 'filter' -%}
            <div class="t4s-btn-filter-wrapper">
              {%- liquid
                assign style_filters = block.settings.style_filters
                if collection.filters.size > 0 and type_filters == 'facets'
                  assign show_filter = true
                elsif type_filters == 'facets_tags'
                  if collection.all_tags.size > 0
                  assign show_filter = true
                  if request.design_mode
                    assign id_se = '#shopify-section-facets_tags '
                  endif
                  echo '<style>button.t4s-btn-filter {opacity: 0 !important; pointer-events: none !important; }.t4s-toolbart-filter.t4s-toolbar-item{ display: none !important;}</style>'
                  endif
                else
                  assign show_filter = false
                endif
              -%}
              {%- if show_filter %}
                <button data-btn-as-a class="t4s-btn-filter" data-drawer-delay data-drawer-options='{ "id":"{{ id_se }}#t4s-filter-hidden" }' aria-label="Show filters"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="16" height="16"><path d="M324.4 64C339.6 64 352 76.37 352 91.63C352 98.32 349.6 104.8 345.2 109.8L240 230V423.6C240 437.1 229.1 448 215.6 448C210.3 448 205.2 446.3 200.9 443.1L124.7 385.6C116.7 379.5 112 370.1 112 360V230L6.836 109.8C2.429 104.8 0 98.32 0 91.63C0 76.37 12.37 64 27.63 64H324.4zM144 224V360L208 408.3V223.1C208 220.1 209.4 216.4 211.1 213.5L314.7 95.1H37.26L140 213.5C142.6 216.4 143.1 220.1 143.1 223.1L144 224zM496 400C504.8 400 512 407.2 512 416C512 424.8 504.8 432 496 432H336C327.2 432 320 424.8 320 416C320 407.2 327.2 400 336 400H496zM320 256C320 247.2 327.2 240 336 240H496C504.8 240 512 247.2 512 256C512 264.8 504.8 272 496 272H336C327.2 272 320 264.8 320 256zM496 80C504.8 80 512 87.16 512 96C512 104.8 504.8 112 496 112H400C391.2 112 384 104.8 384 96C384 87.16 391.2 80 400 80H496z"></path></svg>{{ 'collections.general.filter_button' | t }}</button>
              {% endif -%}
            </div>
        {%- endcase -%}
      {%- endfor -%}
    </div>
    {%- if show_filter and style_filters == 'area' %}<div class="t4s-filter-area" data-filter-area></div>{% endif -%}
    {% if sort_by_true %}
      <link rel="stylesheet" href="{{ 'base_drop.min.css' | asset_url }}" media="all">
    {% endif %}
  {% endif %}
  <div class="t4s-row">
    <div  data-ntajax-container{% if show_filter %} data-has-filters{% endif %} data-ntajax-options='{"id":"{{ section.id }}","type":"{{ typeAjax }}","typeFilters":"{{ type_filters }}","isProduct":true,"updateURL":true,"updateURLPrev":true,"sort_by":"{{ collection.sort_by | default: collection.default_sort_by }}"}'{% unless collection.current_type or collection.current_vendor %} data-collection-url="{{ collection.url }}"{% endunless %} class="t4s-col-item t4s-col-12 t4s-main-area t4s-main-collection-page">
      {%- assign total_active_values = 0 -%}
       {%- capture filters__active -%}
            <div class="t4s-active-filters__count t4s-d-inline-block">{{ 'products.facets.results_with_count_html' | t:count:collection.products_count }}</div>
            {%- for filter in collection.filters -%}
              {%- if filter.type == "price_range" -%}
                {%- if filter.min_value.value != nil or filter.max_value.value != nil -%}
                {%- assign total_active_values = total_active_values | plus: 1 -%}
                  <a class="t4s-active-filters__remove-filter" href="{{ filter.url_to_remove }}">
                    {%- assign min_value = filter.min_value.value | default: 0 -%}
                    {%- assign max_value = filter.max_value.value | default: filter.range_max -%}
                    {{ min_value | money }} - {{ max_value | money }}
                  </a>
                {%- endif -%}
              {%- else -%}
                {%- for filter_value in filter.active_values -%}
                  {%- assign total_active_values = total_active_values | plus: filter.active_values.size -%}
                  <a class="t4s-active-filters__remove-filter" href="{{ filter_value.url_to_remove }}">{{ filter_value.label }}</a>
                {%- endfor -%}
              {%- endif- %}
            {%- endfor -%}
            {%- if total_active_values > 1 %}<a href="{{ collection.url }}{% if collection.sort_by != blank %}?sort_by={{ collection.sort_by }}{% endif %}" class="t4s-active-filters__clear">{{ 'products.facets.clear_all' | t }}</a>{% endif -%}
        {%- endcapture -%}
        {%- if total_active_values > 0 %}<div class="t4s-active-filters">{{- filters__active -}}</div>{% endif -%}

         {%- if current_tags.size > 0 and type_filters == 'facets_tags' -%}
            <div class="t4s-active-filters">
               <div class="t4s-active-filters__count t4s-d-inline-block">{{ 'products.facets.results_with_count_html' | t:count:collection.products_count }}</div>
               {%- if current_tags.size > 0 -%}{%- for tag in current_tags -%}{%- assign txt_tag = tag | replace: '-', ' ' | replace: '_', ' ' -%}{{ txt_tag | link_to_remove_tag: tag | replace: 'title=', 'class="t4s-active-filters__remove-filter" aria-label=' }}{%- endfor -%}{%- endif -%}
               {%- if current_tags.size > 1 -%}<a class="t4s-active-filters__clear" href="{{ collection.url }}{% if collection.sort_by != blank %}?sort_by={{ collection.sort_by }}{% endif %}">{{ 'products.facets.clear_all' | t }}</a>{%- endif -%}
            </div>
         {%- endif -%}
      {%- if paginate.previous.is_link and isLoadmore -%}
        <div data-wrap-lm-prev class="t4s-pagination-wrapper t4s-prs-head t4s-has-btn-{{ use_pagination }} {{ se_stts.btn_pos }} t4s-w-100" timeline hdt-reveal="slide-in">
          <a data-load-more data-is-prev href="{{ paginate.previous.url }}" class="t4s-pr t4s-loadmore-btn t4s-btn t4s-btn-loading__svg t4s-btn-base t4s-btn-style-{{ se_stts.button_style }} t4s-btn-size-{{ se_stts.btns_size }} t4s-btn-icon-{{ se_stts.btn_icon }} t4s-btn-color-{{ se_stts.btns_cl }} {% if se_stts.button_style == 'default' or se_stts.button_style == 'outline' %}t4s-btn-effect-{{ se_stts.button_effect }}{% endif %}">
            <span class="t4s-btn-atc_text">{{ 'search.pagination.load_prev' | t }}</span> 
            {% if se_stts.btn_icon %}
              <svg class="t4s-btn-icon" viewBox="0 0 32 32"><path d="M 15 4 L 15 24.0625 L 8.21875 17.28125 L 6.78125 18.71875 L 15.28125 27.21875 L 16 27.90625 L 16.71875 27.21875 L 25.21875 18.71875 L 23.78125 17.28125 L 17 24.0625 L 17 4 Z"/></svg>
            {% endif %}
            <div class="t4s-loading__spinner t4s-dn">
              <svg  width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
            </div> 
          </a>
        </div>
      {%- endif -%}  
      <div data-contentlm-replace class="isotopet4s t4s_box_pr_masonry t4s-products {{ class_listview }} t4s-text-{{ se_stts.content_align }} t4s_{{ image_ratio }} t4s_position_{{ se_stts.image_position }} t4s_{{ se_stts.image_size }} t4s-row t4s-row-cols-{{ col_mobile }} t4s-row-cols-md-{{ col_tablet }} t4s-row-cols-lg-{{ col_desktop }} t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }}" data-isotopet4s-js='{ "itemSelector": ".t4s-product", "layoutMode": "packery" }'>
        {%- for product in collection.products %}
          {%- if isHasCollection -%}{% assign pr_url = product.url | within: collection %}{% else %}{%- assign pr_url = product.url %}{% endif -%}
          {%- capture col_dk %}{% cycle '3','6','3','3','3','3','3','3','3','6','3','3','3' %}{%- endcapture -%}
          {%- render 'product-packery-item', product: product, pr_url: pr_url, col_dk: col_dk, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
        {%- endfor -%}
      </div>
      {%- if paginate.pages > 1 -%}
        <div class="t4s-row t4s-prs-footer t4s-has-btn-{{ use_pagination }} {{ se_stts.btn_pos }}">
          {%- if use_pagination == 'default' -%}
            {%- render 'pagination', paginate: paginate, anchor: '' -%}
          {%- elsif paginate.next.is_link -%}
             <div data-wrap-lm class="t4s-pagination-wrapper t4s-w-100" timeline hdt-reveal="slide-in">
               {%- if enable_bar_lm -%}
               <div data-wrap-lm-bar class="t4s-lm-bar t4s-btn-color-{{ se_stts.btns_cl }}">
                 {%- assign current_pr_size = collection.products.size | plus: paginate.current_offset -%}
                  <span class="t4s-lm-bar--txt">{{ 'collections.pagination.bar_with_count_html' | t: current_count: current_pr_size, total_count: results_count }}</span>
                  <div class="t4s-lm-bar--progress t4s-pr t4s-oh"><span class="t4s-lm-bar--current t4s-pa t4s-l-0 t4s-r-0 t4s-t-0 t4s-b-0" style="width: {{ current_pr_size | times: 100.0 | divided_by: results_count }}%"></span></div>
               </div>
               {%- endif -%}
                <a data-load-more {% if use_pagination == 'infinite' %} data-load-onscroll {% endif %}  href="{{ paginate.next.url }}" class="t4s-pr t4s-loadmore-btn t4s-btn-loading__svg t4s-btn t4s-btn-base t4s-btn-style-{{ se_stts.button_style }} t4s-btn-size-{{ se_stts.btns_size }} t4s-btn-icon-{{ se_stts.btn_icon }} t4s-btn-color-{{ se_stts.btns_cl }} {% if se_stts.button_style == 'default' or se_stts.button_style == 'outline' %}t4s-btn-effect-{{ se_stts.button_effect }}{% endif %}">
                  <span class="t4s-btn-atc_text">{{ 'collections.pagination.load_more' | t }}</span> 
                  {% if se_stts.btn_icon %}
                    <svg class="t4s-btn-icon" viewBox="0 0 32 32"><path d="M 15 4 L 15 24.0625 L 8.21875 17.28125 L 6.78125 18.71875 L 15.28125 27.21875 L 16 27.90625 L 16.71875 27.21875 L 25.21875 18.71875 L 23.78125 17.28125 L 17 24.0625 L 17 4 Z"/></svg>
                  {% endif %}
                  <div class="t4s-loading__spinner t4s-dn">
                    <svg  width="16" height="16" aria-hidden="true" focusable="false" role="presentation" class="t4s-svg__spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
                  </div> 
                </a>
             </div>
          {%- endif -%}
        </div>
      {%- endif -%}
      {%- if collection.products.size == 0 -%}
        <div class="t4s-coll-empty t4s-align-items-center">
          <div class="t4s-no-result-product t4s-d-flex">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="18" height="18"><path d="M506.3 417l-213.3-364c-16.33-28-57.54-28-73.98 0l-213.2 364C-10.59 444.9 9.849 480 42.74 480h426.6C502.1 480 522.6 445 506.3 417zM232 168c0-13.25 10.75-24 24-24S280 154.8 280 168v128c0 13.25-10.75 24-23.1 24S232 309.3 232 296V168zM256 416c-17.36 0-31.44-14.08-31.44-31.44c0-17.36 14.07-31.44 31.44-31.44s31.44 14.08 31.44 31.44C287.4 401.9 273.4 416 256 416z"></path></svg>{{ 'collections.general.no_matches' | t }}
          </div>
        </div>
      {%- endif -%}
    </div>
    <aside data-sidebar-content class="t4s-col-item t4s-col-12 t4s-col-lg-3 t4s-sidebar t4s-dn"><div class="t4s-loading--bg"></div></aside>
  </div>
  {%- if se_stts.position_desc == '2' and se_stts.show_desc -%}<div id="t4s-desc-collection" class="t4s-desc-collection t4s-desc-before">{{ collection.description }}</div>{%- endif -%} 
  {{- html_layout[1] -}}
</div>
{%- endpaginate -%}
{%- schema -%}
  {
    "name": "Main packery collection",
    "tag": "section",
    "class": "t4s-section t4s-section-main t4s-collection-page t4s_tp_istope t4s_tp_countdown",
    "settings": [
      {
        "type": "header",
        "content": "1. General options"
      },
  	  {
        "type": "checkbox",
        "id": "show_desc",
        "label": "Show description collection",
        "default": false
      },
      {
        "type": "select",
        "id": "position_desc",
        "options": [
          {
            "value": "1",
            "label": "Before main collection"
          },
          {
            "value": "2",
            "label": "After main collection"
          }
        ],
        "label": "Position description collection",
        "default": "2"
      },
      {
        "type": "checkbox",
        "id": "show_vendor",
        "label": "Show product vendors",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "use_cdt",
        "label": "Show product countdown",
        "default": false
      },
      {
        "type": "header",
        "content": "+ Options image products"
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Image ratio",
        "default": "rationt",
        "info": "Aspect ratio custom will settings in general panel",
        "options": [
          {
            "group": "Natural",
            "value": "ratioadapt",
            "label": "Adapt to image"
          },
          {
            "group": "Landscape",
            "value": "ratio2_1",
            "label": "2:1"
          },
          {
            "group": "Landscape",
            "value": "ratio16_9",
            "label": "16:9"
          },
          {
            "group": "Landscape",
            "value": "ratio8_5",
            "label": "8:5"
          },
          {
            "group": "Landscape",
            "value": "ratio3_2",
            "label": "3:2"
          },
          {
            "group": "Landscape",
            "value": "ratio4_3",
            "label": "4:3"
          },
          {
            "group": "Landscape",
            "value": "rationt",
            "label": "Ratio ASOS"
          },
          {
            "group": "Squared",
            "value": "ratio1_1",
            "label": "1:1"
          },
          {
            "group": "Portrait",
            "value": "ratio2_3",
            "label": "2:3"
          },
          {
            "group": "Portrait",
            "value": "ratio1_2",
            "label": "1:2"
          },
          {
            "group": "Custom",
            "value": "ratiocus1",
            "label": "Ratio custom 1"
          },
          {
            "group": "Custom",
            "value": "ratiocus2",
            "label": "Ratio custom 2"
          },
          {
            "group": "Custom",
            "value": "ratio_us3",
            "label": "Ratio custom 3"
          },
          {
            "group": "Custom",
            "value": "ratiocus4",
            "label": "Ratio custom 4"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_size",
        "label": "Image size",
        "default": "cover",
        "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "cover",
            "label": "Full"
          },
          {
            "value": "contain",
            "label": "Auto"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "1",
            "label": "Left top"
          },
          {
            "value": "2",
            "label": "Left center"
          },
          {
            "value": "3",
            "label": "Left bottom"
          },
          {
            "value": "4",
            "label": "Right top"
          },
          {
            "value": "5",
            "label": "Right center"
          },
          {
            "value": "6",
            "label": "Right bottom"
          },
          {
            "value": "7",
            "label": "Center top"
          },
          {
            "value": "8",
            "label": "Center center"
          },
          {
            "value": "9",
            "label": "Center bottom"
          }
        ],
        "label": "Image position",
        "default": "8"
      },
      {
        "type": "select",
        "id": "content_align",
        "label": "Product content align",
        "default": "default",
        "options": [
          {
            "label": "Default",
            "value": "default"
          },
          {
            "label": "Center",
            "value": "center"
          }
        ]
      },
      {
        "type": "range",
        "id": "limit",
        "min": 1,
        "max": 50,
        "step": 1,
        "label": "Maximum products to show",
        "default": 14
      },
      {
        "type": "select",
        "id": "col_tb",
        "label": "Items per row (Tablet)",
        "default": "2",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_mb",
        "label": "Items per row (Mobile)",
        "default": "2",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          }
        ]
      },
      {
        "type": "select",
        "id": "space_h_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_v_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_h_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items (Mobile)",
        "default": "10"
      },
      {
        "type": "select",
        "id": "space_v_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "15", 
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items (Mobile)",
        "default": "10"
      },
      {
        "type": "header",
        "content": "Pagination options"
      },
      {
        "type": "select",
        "id": "use_pagination",
        "label": "Pagination",
        "default": "default",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "load-more",
            "label": "'Load more' button"
          },
          {
            "value": "infinite",
            "label": "Infinite scrolling"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "enable_bar_lm",
        "label": "Enable progress bar?",
        "info": "Only active when you use 'Load more' or 'Infinite scrolling'",
        "default": true
      },
      {
        "type": "paragraph",
        "content": "Page-loading speed is everything for good user experience. Multiple researches have shown that slow load times result in people leaving your site or delete your app which result in low conversion rates. And that’s bad news for those who use an infinite-scrolling. The more users scroll down a page, more content has to load on the same page. As a result, the page performance will increasingly slow down."
      },
      {
        "type": "paragraph",
        "content": "Another problem is limited resources of the user’s device. On many infinite scrolling sites, especially those with many images, devices with limited resources (such as mobile devices or tablets with dated hardware) can start slowing down because of the sheer number of assets it has loaded."
      },
      {
        "type": "paragraph",
        "content": "Therefore, we recommend that you only use 'Load more', 'Infinite scrolling' for when your collection is less than or equal to 400 products"
      },
      {
        "type":"checkbox",
        "id":"btn_icon",
        "label":"Enable button icon",
        "default":false
      },
      {
        "type": "select",
        "id": "button_style",
        "label": "Button style",
        "options": [
            {
                "label": "Default",
                "value": "default"
            },
            {
                "label": "Outline",
                "value": "outline"
            },
            {
                "label": "Bordered bottom",
                "value": "bordered"
            },
            {
                "label": "Link",
                "value": "link"
            }
        ]
      },
      {
        "type": "select",
        "id": "btns_size",
        "label": "Button size",
        "default":"large",
        "options": [
            {
                "label": "Small",
                "value": "small"
            },
            {
                "label": "Extra-mall",
                "value": "extra-small"
            },
            {
                "label": "Medium",
                "value": "medium"
            },
            {
                "label": "Extra-medium",
                "value": "extra-medium"
            },
            {
                "label": "Large",
                "value": "large"
            },
            {
                "label": "Extra large",
                "value": "extra-large"
            }
        ]
      },
      {
        "type": "select",
        "id": "btns_cl",
        "label": "Button color",
        "default": "dark",
        "options": [
          {
              "value": "light",
              "label": "Light"
          },
          {
              "value": "dark",
              "label": "Dark"
          },
          {
              "value": "primary",
              "label": "Primary"
          },
          {
              "value": "custom1",
              "label": "Custom color 1"
          },
          {
              "value": "custom2",
              "label": "Custom color 2"
          }
        ]
      },
      {
        "type":"select",
        "id":"button_effect",
        "label":"Button hover effect",
        "default":"default",
        "info":"Only working button style default, outline",
        "options":[
            {
                "label":"Default",
                "value":"default"
            },
            {
                "label":"Fade",
                "value":"fade"
            },
            {
                "label":"Rectangle out",
                "value":"rectangle-out"
            },
            {
                "label":"Sweep to right",
                "value":"sweep-to-right"
            },
            {
                "label":"Sweep to left",
                "value":"sweep-to-left"
            },
            {
                "label":"Sweep to bottom",
                "value":"sweep-to-bottom"
            },
            {
                "label":"Sweep to top",
                "value":"sweep-to-top"
            },
            {
                "label":"Shutter out horizontal",
                "value":"shutter-out-horizontal"
            },
            {
                "label":"Outline",
                "value":"outline"
            },
            {
                "label":"Shadow",
                "value":"shadow"
            }
        ]
      },
      {
        "type": "select",
        "id": "btn_pos",
        "label": "Button position",
        "default": "t4s-text-center",
        "options": [
          {
            "value": "t4s-text-start",
            "label": "Left"
          },
          {
            "value": "t4s-text-center",
            "label": "Center"
          },
          {
            "value": "t4s-text-end",
            "label": "Right"
          }
        ]
      },
      {
        "type": "header",
        "content": "+ Collection filter"
      },
      {
        "type": "select",
        "id": "type_filters",
        "label": "Filters type",
        "default": "facets",
        "options": [
          {
            "value": "facets_tags",
            "label": "Filter by tags"
          },
          {
            "value": "facets",
            "label": "Filter by product options"
          }
        ]
      },
      {
        "type": "header",
        "content": "2. Design options"
      },
      {
        "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped container"},
            { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      }
    ],
    "blocks": [
      {
         "type": "sortby",
         "name": "Sortby",
         "limit": 1
       },
       {
         "type": "filter",
         "name": "Filter",
         "limit": 1,
         "settings": [
            {
              "type": "select",
              "id": "style_filters",
              "label": "Filter style will show after click button filter ( desktop )",
              "default": "sidebar",
              "options": [
                {
                  "value": "sidebar",
                  "label": "Hidden sidebar"
                },
                {
                  "value": "area",
                  "label": "Filters area"
                }
              ]
            },
            {
              "type": "paragraph",
              "content": "On mobile always use hidden sidebar"
            }
          ]
       }
     
    ],
    "default": {
      "blocks": [
          { "type": "filter"},
          { "type": "sortby"}
        ]
    }
  }
{%- endschema -%} 

{%- javascript -%}
{%- endjavascript -%}