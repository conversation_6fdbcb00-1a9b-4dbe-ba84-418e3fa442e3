<!-- sections/promo_text.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'promo-text.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
{%- liquid
    assign sid = section.id
    assign se_stts = section.settings
    assign se_blocks = section.blocks
    assign stt_layout = se_stts.layout
    assign stt_image_bg = se_stts.image_bg

    if stt_layout == 't4s-se-container' 
        assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
    elsif stt_layout == 't4s-container-wrap'
        assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
    else
        assign html_layout = '__' | split: '__'
    endif
    assign t4s_se_class = 't4s_nt_se_' | append: sid
    if se_stts.use_cus_css and se_stts.code_cus_css != blank
        render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
    endif 
 -%}
<div class="t4s-section-inner {{ t4s_se_class }} t4s_nt_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
        <div class="t4s-row t4s-text-center t4s-slide-eff-{{ se_stts.effect }} {% if se_blocks.size > 1 %}flickityt4s{% endif %}" {% if se_blocks.size > 1 %} data-flickityt4s-js='{"cellAlign": "center","imagesLoaded": 0,"lazyLoad": 0,"freeScroll": 0,"wrapAround": true,"autoPlay" : {{ se_stts.au_time | times: 1000 }},"pauseAutoPlayOnHover" : true, "prevNextButtons": false,"pageDots": false, "contain" : 1,"adaptiveHeight" : 1,"dragThreshold" : 5,"percentPosition": 1 }' {% endif %}>
            {%- if se_blocks.size > 0 -%}
                {%- for block in se_blocks %}
                    {%- assign bk_stts = block.settings -%}
                    {%- capture cap_date -%}<span data-countdown-t4s data-loop="{%- if bk_stts.dayx > 0 -%}true{%- else -%}false{%- endif -%}" data-date="{{ bk_stts.countdown }}" data-dayl="{{ bk_stts.dayx }}"><span>{{ bk_stts.cus_cd }}</span></span>{%- endcapture -%}
                    <div class="t4s-col-item t4s-col-12 t4s-gx-10 t4s-prt-text t4s-rte" data-lh="{{ bk_stts.lh_text_mb }}" data-lh-md="{{ bk_stts.lh_text }}" data-lh-lg="{{ bk_stts.lh_text }}" id="b_{{ block.id }}" data-select-flickity {{ block.shopify_attributes }} style="--text-cl:{{ bk_stts.cl_text }};--color-link:{{ bk_stts.cl_link }};--text-fs:{{ bk_stts.fs_text }}px;--text-fw:{{ bk_stts.fw_text }};--text-ls:{{ bk_stts.ls_text }}px;--text-lh:{{ bk_stts.lh_text }}px;--text-fs-mb:{{ bk_stts.fs_mb_text }}px;--text-lh-mb:{{ bk_stts.lh_text_mb }}px;--text-ls-mb:{{ bk_stts.ls_mb_text }}px;" timeline hdt-reveal="slide-in">
                        {{- bk_stts.content | replace: '[countdown]',cap_date -}}
                    </div>
                {%- endfor -%}
            {%- endif -%}
        </div>
    {{- html_layout[1] -}}
</div>
{%- schema -%}
{
  "name": "Promo text",
  "tag": "section",
  "class": "t4s-section t4s-section-all t4s_bk_flickity t4s_tp_cd t4s-promo-text",
  "settings": [
    {
      "type": "header",
      "content": "1. General options"
    },
    {
      "type": "select",
      "id": "effect",
      "label": "Effect carousel",
      "default": "slider",
      "options": [
        {
          "value": "fade",
          "label": "Fade"
        },
        {
          "value": "slider",
          "label": "Carousel"
        },
        {
          "value": "fade t4s-slide-eff-translate",
          "label": "Translate"
        }
      ]
    },
    {
      "type": "range",
      "id": "au_time",
      "min": 0,
      "max": 30,
      "step": 0.5,
      "label": "Autoplay speed in second",
      "info": "Set is '0' to disable autoplay",
      "unit": "s",
      "default": 3
    },
    {
      "type": "header",
      "content": "2. Design options"
    },
    {
      "type": "select",
      "id": "layout",
      "default": "t4s-container-wrap",
      "label": "Layout",
      "options": [
        {
          "value": "t4s-se-container",
          "label": "Container"
        },
        {
          "value": "t4s-container-wrap",
          "label": "Wrapped container"
        },
        {
          "value": "t4s-container-fluid",
          "label": "Full width"
        }
      ]
    },
    {
      "type": "color",
      "id": "cl_bg",
      "label": "Background",
      "default": "#000"
    },
    {
      "type": "color_background",
      "id": "cl_bg_gradient",
      "label": "Background gradient"
    },
    {
      "type": "image_picker",
      "id": "image_bg",
      "label": "Background image"
    },
    {
      "type": "text",
      "id": "mg",
      "label": "Margin",
      "info": "Margin top, margin right, margin bottom, margin left. If you not use to blank",
      "default": ",,50px,",
      "placeholder": ",,50px,"
    },
    {
      "type": "text",
      "id": "pd",
      "label": "Padding",
      "info": "Padding top, padding right, padding bottom, padding left. If you not use to blank",
      "placeholder": "50px,,50px,",
      "default": "12px,,12px,"
    },
    {
      "type": "header",
      "content": "+ Design Tablet Options"
    },
    {
      "type": "text",
      "id": "mg_tb",
      "label": "Margin",
      "placeholder": ",,50px,"
    },
    {
      "type": "text",
      "id": "pd_tb",
      "label": "Padding",
      "placeholder": ",,50px,"
    },
    {
      "type": "header",
      "content": "+ Design mobile options"
    },
    {
      "type": "text",
      "id": "mg_mb",
      "label": "Margin",
      "default": ",,30px,",
      "placeholder": ",,50px,"
    },
    {
      "type": "text",
      "id": "pd_mb",
      "label": "Padding",
      "placeholder": ",,50px,",
      "default": "10px,,10px,"
    },
    {
      "type": "header",
      "content": "3. Custom css"
    },
    {
      "id": "use_cus_css",
      "type": "checkbox",
      "label": "Use custom css",
      "default": false,
      "info": "If you want custom style for this section."
    },
    {
      "id": "code_cus_css",
      "type": "textarea",
      "label": "Code custom css",
      "info": "Use selector .SectionID to style css"
    }
  ],
  "blocks": [
    {
      "type": "1",
      "name": "Content",
      "settings": [
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>TODAY DEAL SALE OFF 70%. END IN [countdown]. HURRY UP!!!</p>"
        },
        {
          "type": "text",
          "id": "countdown",
          "label": "Date countdown",
          "default": "2023/12/20",
          "info": "Use shortocdes: [countdown]. Countdown to the end sale date will be shown"
        },
        {
          "type": "textarea",
          "id": "cus_cd",
          "label": "Custom countdown",
          "default": "%-D day%!D %H:%M:%S",
          "placeholder": "%-D day%!D %H:%M:%S"
        },
        {
          "type": "range",
          "id": "dayx",
          "min": 0,
          "max": 100,
          "step": 1,
          "label": "Reset countdown every x days from an initial date",
          "info": "Set is '0' to disable reset countdown",
          "unit": "day",
          "default": 0
        },
        {
          "type": "color",
          "id": "cl_text",
          "label": "Color text",
          "default": "#fff"
        },
        {
          "type": "color",
          "id": "cl_link",
          "label": "Color link",
          "default": "#56cfe1"
        },
        {
          "type": "range",
          "id": "fw_text",
          "min": 300,
          "max": 900,
          "step": 100,
          "label": "Font weight",
          "default": 600
        },
        {
          "type": "range",
          "id": "fs_text",
          "min": 8,
          "max": 50,
          "step": 0.5,
          "label": "Font size",
          "unit": "px",
          "default": 12
        },
        {
          "type": "range",
          "id": "lh_text",
          "min": 0,
          "max": 100,
          "step": 1,
          "label": "Line height",
          "info": "set is '0' use to default",
          "unit": "px",
          "default": 0
        },
        {
          "type": "number",
          "id": "ls_text",
          "label": "Letter spacing (in pixel)",
          "info": "set is '0' use to default",
          "default": 0
        },
        {
          "type": "range",
          "id": "fs_mb_text",
          "min": 8,
          "max": 50,
          "step": 0.5,
          "label": "Font size (Mobile)",
          "unit": "px",
          "default": 10
        },
        {
          "type": "range",
          "id": "lh_text_mb",
          "min": 0,
          "max": 50,
          "step": 0.5,
          "label": "Line height (Mobile)",
          "info": "set is '0' use to default",
          "unit": "px",
          "default": 0
        },
        {
          "type": "number",
          "id": "ls_mb_text",
          "label": "Letter spacing (in pixel)",
          "info": "set is '0' use to default",
          "default": 0
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Promo text",
      "category": "homepage",
      "blocks": [
        {
          "type": "1"
        }
      ]
    }
  ]
}
{% endschema %}