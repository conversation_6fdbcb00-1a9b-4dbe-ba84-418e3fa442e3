<!-- sections/collections-list-packery.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'collection.css' | asset_url | stylesheet_tag }}
{%- liquid
  assign image_fix = image_nt | image_tag
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign image_ratio = se_stts.image_ratio
  if image_ratio == "ratioadapt"
    assign imgatt = ''
   else 
    assign imgatt = 'data-'
  endif
  assign open_link = se_stts.open_link
  assign subtitle = se_stts.collection_subtitle
  assign b_effect = se_stts.b_effect
  assign img_effect = se_stts.img_effect

  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif 

  assign collection_des = se_stts.collection_des
  assign title_cl_pri       = se_stts.title_cl | color_extract: 'lightness'
  assign title_cl_hover_pri       = se_stts.title_cl_hover | color_extract: 'lightness'
  assign subtitle_cl_pri       = se_stts.subtitle_cl | color_extract: 'lightness'
  assign count_cl_pri       = se_stts.count_cl | color_extract: 'lightness'

  if title_cl_pri < 85  
    assign title_cl_sec = "#fff"
  else 
    assign title_cl_sec = "#222"
  endif
  if title_cl_hover_pri < 85 
    assign title_cl_hover_sec = "#fff"
  else 
    assign title_cl_hover_sec = "#222"
  endif
  if subtitle_cl_pri < 85 
    assign subtitle_cl_sec = "#fff"
  else 
    assign subtitle_cl_sec = "#222"
  endif
  if count_cl_pri < 85 
    assign count_cl_sec = "#fff"
  else 
    assign count_cl_sec = "#222"
  endif
 -%} 
<div class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }} {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
    {%- render 'section_tophead', se_stts: se_stts -%}
      <div class="isotopet4s t4s-collection-border-{{ se_stts.border }} t4s_{{ image_ratio }} t4s_position_{{ se_stts.image_position }} t4s_{{ se_stts.image_size }} t4s-row t4s-gx-md-{{ se_stts.space_h_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gy-{{ se_stts.space_v_item_mb }}" data-isotopet4s-js='{ "itemSelector": ".grid-item", "layoutMode": "packery" }' style="--title-cl-pri: {{ se_stts.title_cl }};--title-cl-pri-hover: {{ se_stts.title_cl_hover }};--title-cl-second: {{ title_cl_sec }};--title-cl-second-hover: {{ title_cl_hover_sec }};--subtitle-cl: {{ se_stts.subtitle_cl }};--subtitle-cl2: {{ subtitle_cl_sec }};--count-cl-pri: {{ se_stts.count_cl }};--count-cl-second: {{ count_cl_sec }};--border-cl: {{ se_stts.border_cl }};--item-rd: {{ se_stts.item_rd }}%;--item-pd: {{ se_stts.item_pd }}px;--space-bottom: {{ se_stts.space_bottom }}px;--space-bottom-mb: {{ se_stts.space_bottom_mb }}px;">
        {%- for block in se_blocks -%}
          {%- assign bk_stts = block.settings -%}
          {% capture current %}{% cycle 1, 2, 3, 4, 5, 6 %}{% endcapture %}
          <div class="t4s-col-item grid-item t4s-collection-item t4s-coll-style-{{ collection_des }} t4s-col-lg-{{ bk_stts.col_dk }} t4s-col-md-{{ bk_stts.col_tb }} t4s-col-{{ bk_stts.col_mb }}" id="b_{{ block.id }}">
            {%- render 'collection_item',collection_des:collection_des,source:"image",b_effect:b_effect,img_effect:img_effect, bk_stts: bk_stts,imgatt:imgatt,open_link:open_link,subtitle:subtitle,current:current -%}
          </div>
        {%- endfor -%}
    </div>
    {{- html_layout[1] -}}
</div>

{%- schema -%}
  {
    "name": "Collections list packery",
    "tag": "section",
    "class": "t4s-section t4s-section-all t4s_tp_cdt t4s_tp_istope t4s-collection-list-packery",
    "settings": [
      {
          "type": "header",
          "content": "1. Heading options"
      },
      {
          "type": "select",
          "id": "design_heading",
          "label": "+ Design heading",
          "default": "1",
          "options": [
              {
                  "value": "1",
                  "label": "Design 01"
              },
              {
                  "value": "2",
                  "label": "Design 02"
              },
              {
                  "value": "3",
                  "label": "Design 03"
              },
              {
                  "value": "4",
                  "label": "Design 04"
              },
              {
                  "value": "5",
                  "label": "Design 05"
              },
              {
                  "value": "6",
                  "label": "Design 06 (width line-awesome)"
              },
              {
                  "value": "7",
                  "label": "Design 07"
              },
              {
                  "value": "8",
                  "label": "Design 08"
              },
              {
                  "value": "9",
                  "label": "Design 09"
              },
              {
                  "value": "10",
                  "label": "Design 10"
              },
              {
                  "value": "11",
                  "label": "Design 11"
              },
              {
                  "value": "12",
                  "label": "Design 12"
              },
              {
                  "value": "13",
                  "label": "Design 13"
              },
              {
                  "value": "14",
                  "label": "Design 14"
              },
              {
                "value": "15",
                "label": "Design 15"
              },
              {
                "value": "16",
                "label": "Design 16"
              }
          ]
      },
      {
          "type": "select",
          "id": "heading_align",
          "label": "+ Heading align",
          "default": "t4s-text-center",
          "options": [
              {
                  "value": "t4s-text-start",
                  "label": "Left"
              },
              {
                  "value": "t4s-text-center",
                  "label": "Center"
              },
              {
                  "value": "t4s-text-end",
                  "label": "Right"
              }
          ]
      },
      {
          "type": "text",
          "id": "top_heading",
          "label": "+ Heading",
          "default": "Collections list packery"
      },
      {
        "type": "text",
        "id": "icon_heading",
        "label": "Enter a icon name on heading",
        "info": "Only used for design 6 [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
        "default": "las la-gem"
      },
      {
          "type": "textarea",
          "id": "top_subheading",
          "label": "+ Subheading"
      }, 
      {
        "type": "number",
        "id": "tophead_mb",
        "label": "+ Space bottom (px)",
        "info": "The vertical spacing between heading and content",
        "default": 30
      },
      {
        "type": "header",
        "content": "2. General options"
      },
      {
        "type": "select",
        "id": "collection_des",
        "label": "Collection item design",
        "default": "1",
        "options": [
          {
            "value": "1",
            "label": "Design 1"
          },
          {
            "value": "2",
            "label": "Design 2"
          },
          {
            "value": "3",
            "label": "Design 3"
          },
          {
            "value": "4",
            "label": "Design 4"
          },
          {
            "value": "5",
            "label": "Design 5"
          },
          {
            "value": "6",
            "label": "Design 6"
          },
          {
            "value": "7",
            "label": "Design 7"
          },
          {
            "value": "8",
            "label": "Design 8 (Only image)"
          },
          {
            "value": "9",
            "label": "Design 9"
          },
          {
            "value": "10",
            "label": "Design 10"
          },
          {
            "value": "11",
            "label": "Design 11"
          },
          {
            "value": "12",
            "label": "Design 12"
          },
          {
            "value": "13",
            "label": "Design 13"
          },
          {
            "value": "14",
            "label": "Design 14"
          },
          {
            "value": "15",
            "label": "Design 15"
          }
        ]
      },
      {
        "type": "color",
        "id": "title_cl",
        "label": "Title color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "id": "title_cl_hover",
        "label": "Title hover color",
        "default": "#222222"
      },
      {
        "type": "color",
        "id": "subtitle_cl",
        "label": "Subtitle color",
        "default": "#878787"
      },
      {
        "type": "color",
        "id": "count_cl",
        "label": "Count color",
        "default": "#222222"
      },
      {
        "type": "color",
        "id": "border_cl",
        "label": "Border color",
        "default": "#e5e5e5"
      },
      {
        "type": "text",
        "id": "collection_subtitle",
        "label": "Collection subtitle",
        "default": "Products"
      },
      {
        "type": "select",
        "id": "open_link",
        "info": "Works when the item has a link",
        "options": [
          {
            "value": "_self",
            "label": "Current window"
          },
         {
            "value": "_blank",
            "label": "New window"
          }
        ],
        "label": "Open link in",
        "default": "_self"
      },
      {
        "type": "header",
        "content": "+ Options image collection"
      },
      {
        "type": "range",
        "id": "space_bottom",
        "min": 0,
        "max": 60,
        "step": 1,
        "label": "Space bottom",
        "unit": "px",
        "default": 20,
        "info": "Space between image and info of collection"
      },
      {
        "type": "range",
        "id": "space_bottom_mb",
        "min": 0,
        "max": 60,
        "step": 1,
        "label": "Space bottom (Mobile)",
        "unit": "px",
        "default": 10
      },
      {
        "type": "checkbox",
        "id": "border",
        "label": "Enable border",
        "default": false
      },
      {
        "type": "range",
        "id": "item_pd",
        "min": 0,
        "max": 50,
        "step": 1,
        "label": "Image padding",
        "unit": "px",
        "default": 0,
        "info": "Only working when collection has border"
      },
      {
        "type": "range",
        "id": "item_rd",
        "min": 0,
        "max": 50,
        "step": 1,
        "label": "Image rounded",
        "unit": "%",
        "default": 0
      },
      {
        "type": "select",
        "id": "img_effect",
        "label": "Image hover effect",
            "info": "Waring: Hovering effect will resize your images",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "zoom",
            "label": "Zoom in"
          },
          {
            "value": "rotate",
            "label": "Rotate"
          },
          {
            "value": "translateToTop",
            "label": "Move to top "
          },
          {
            "value": "translateToRight",
            "label": "Move to right"
          },
          {
            "value": "translateToBottom",
            "label": "Move to bottom"
          },
          {
            "value": "translateToLeft",
            "label": "Move to left"
          }
        ]
      },
      {
        "type": "select",
        "id": "b_effect",
        "label": "Collection hover effect",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "border-run",
            "label": "Border run"
          },
          {
            "value": "pervasive-circle",
            "label": "Pervasive circle"
          },
          {
            "value": "plus-zoom-overlay",
            "label": "Plus zoom overlay"
          },
          {
            "value": "dark-overlay",
            "label": "Dark overlay"
          },
          {
            "value": "light-overlay",
            "label": "Light overlay"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Image ratio",
        "default": "ratio1_1",
        "info": "Aspect ratio custom will settings in general panel",
        "options": [
          {
            "group": "Natural",
            "value": "ratioadapt",
            "label": "Adapt to image"
          },
          {
            "group": "Landscape",
            "value": "ratio2_1",
            "label": "2:1"
          },
          {
            "group": "Landscape",
            "value": "ratio16_9",
            "label": "16:9"
          },
          {
            "group": "Landscape",
            "value": "ratio8_5",
            "label": "8:5"
          },
          {
            "group": "Landscape",
            "value": "ratio3_2",
            "label": "3:2"
          },
          {
            "group": "Landscape",
            "value": "ratio4_3",
            "label": "4:3"
          },
          {
            "group": "Landscape",
            "value": "rationt",
            "label": "Ratio ASOS"
          },
          {
            "group": "Squared",
            "value": "ratio1_1",
            "label": "1:1"
          },
          {
            "group": "Portrait",
            "value": "ratio2_3",
            "label": "2:3"
          },
          {
            "group": "Portrait",
            "value": "ratio1_2",
            "label": "1:2"
          },
          {
            "group": "Custom",
            "value": "ratiocus1",
            "label": "Ratio custom 1"
          },
          {
            "group": "Custom",
            "value": "ratiocus2",
            "label": "Ratio custom 2"
          },
          {
            "group": "Custom",
            "value": "ratio_us3",
            "label": "Ratio custom 3"
          },
          {
            "group": "Custom",
            "value": "ratiocus4",
            "label": "Ratio custom 4"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_size",
        "label": "Image size",
        "default": "cover",
        "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "cover",
            "label": "Full"
          },
          {
            "value": "contain",
            "label": "Auto"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "info": "The first value is the horizontal position and the second value is the vertical. These settings apply only if the image ratio is not set to 'Adapt to image', it also does not work when using 'Focal point' on the image.",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "1",
            "label": "Left top"
          },
          {
            "value": "2",
            "label": "Left center"
          },
          {
            "value": "3",
            "label": "Left bottom"
          },
          {
            "value": "4",
            "label": "Right top"
          },
          {
            "value": "5",
            "label": "Right center"
          },
          {
            "value": "6",
            "label": "Right bottom"
          },
          {
            "value": "7",
            "label": "Center top"
          },
          {
            "value": "8",
            "label": "Center center"
          },
          {
            "value": "9",
            "label": "Center bottom"
          }
        ],
        "label": "Image position",
        "default": "8"
      },
      {
        "type": "select",
        "id": "space_h_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_v_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_h_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items (Mobile)",
        "default": "10"
      },
      {
        "type": "select",
        "id": "space_v_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items (Mobile)",
        "default": "10"
      },
      {
        "type": "header",
        "content": "3. Design options"
      },
      {
        "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped container"},
            { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      }, 
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "4. Custom css"
      },
      {
        "id": "use_cus_css",
        "type": "checkbox",
        "label": "Use custom css",
        "default":false,
        "info": "If you want custom style for this section."
      },
      { 
        "id": "code_cus_css",
        "type": "textarea",
        "label": "Code custom css",
        "info": "Use selector .SectionID to style css"
        
      }
    ],
    "blocks": [
      {
        "type": "collection_item",
        "name": "Collection item",
        "settings": [
          {
              "id": "collection",
              "type": "collection",
              "label": "Collection"
          },
          {
            "type": "image_picker",
            "id": "image",
            "label": "Collection image"
          },
          {
            "type": "select",
            "id": "col_dk",
            "label": "Item width",
            "default": "6",
            "options": [
               {
                "value": "12",
                "label": "100%"
              },
              {
                "value": "9",
                "label": "75%"
              },
              {
                "value": "8",
                "label": "66.66%"
              },
              {
                "value": "6",
                "label": "50%"
              },
              {
                "value": "4",
                "label": "33.33%"
              },
              {
                "value": "3",
                "label": "25%"
              }
            ]
          },
          {
            "type": "select",
            "id": "col_tb",
            "label": "Item width on Tablets",
            "default": "6",
            "options": [
               {
                "value": "12",
                "label": "100%"
              },
              {
                "value": "9",
                "label": "75%"
              },
              {
                "value": "8",
                "label": "66.66%"
              },
              {
                "value": "6",
                "label": "50%"
              },
              {
                "value": "4",
                "label": "33.33%"
              },
              {
                "value": "3",
                "label": "25%"
              }
            ]
          },
          {
            "type": "select",
            "id": "col_mb",
            "label": "Item width on Mobiles",
            "default": "12",
            "options": [
              {
                "value": "12",
                "label": "100%"
              },
              {
                "value": "9",
                "label": "75%"
              },
              {
                "value": "8",
                "label": "66.66%"
              },
              {
                "value": "6",
                "label": "50%"
              },
              {
                "value": "4",
                "label": "33.33%"
              },
              {
                "value": "3",
                "label": "25%"
              }
            ]
          },
          {
            "type": "text",
            "id": "collection_title",
            "label": "Collection label",
            "info" : "Leave empty to use 'collection title'.",
            "default": "Collection title"
          },
          {
            "type": "url",
            "id": "collection_link",
            "label": "Link (optional)",
            "info" : "Leave empty to use 'collection url'."
          }
        ]
      }
    ],
  "presets": [
      {
        "name": "Collections list packery",
        "category": "Homepage",
        "blocks": [
          { "type": "collection_item",
            "settings": {
              "col_dk": "6",
              "col_tb": "6",
              "col_mb": "12"
            }
          },
          { "type": "collection_item",
            "settings": {
              "col_dk": "3",
              "col_tb": "3",
              "col_mb": "6"
            } 
          },
          { "type": "collection_item",
            "settings": {
              "col_dk": "3",
              "col_tb": "3",
              "col_mb": "6"
            } 
          },
          { "type": "collection_item",
            "settings": {
              "col_dk": "6",
              "col_tb": "6",
              "col_mb": "12"
            } 
          },
          { "type": "collection_item",
            "settings": {
              "col_dk": "3",
              "col_tb": "3",
              "col_mb": "6"
            } 
          },
          { "type": "collection_item",
            "settings": {
             "col_dk": "3",
              "col_tb": "3",
              "col_mb": "6"
            } 
          }
        ]
      }
    ]
  }
{%- endschema -%}

{%- javascript -%}
{%- endjavascript -%}