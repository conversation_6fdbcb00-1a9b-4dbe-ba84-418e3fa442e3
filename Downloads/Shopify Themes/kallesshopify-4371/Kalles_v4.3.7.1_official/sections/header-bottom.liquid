 {%- liquid
  assign se_stts = section.settings
  assign se_blocks = section.blocks 
  assign admin_sp = request.design_mode
  assign h__bgimg = se_stts.h__bgimg
  assign h_transparent = se_stts.transparent_header
  if request.page_type != 'index' 
    assign h_transparent = false 
  endif
  assign space_transparent = se_stts.space_transparent_header
  assign source = se_stts.source 
-%}

{%- style -%}
  {%- assign opnav  = se_stts.opnav | divided_by: 100.0 -%}
  .t4s-header__wrapper {
    --h-text-color      : {{ se_stts.clnav }};
    --h-text-color-rgb  : {{ se_stts.clnav | color_to_rgb | remove: 'rgba(' | remove: 'rgb(' | remove: ')' }};
    --h-text-color-hover: {{ se_stts.clnav_hover }};
    --h-bg-color        : {{ se_stts.bgnav | color_modify: 'alpha', opnav }};
    background-color: var(--h-bg-color);
    --primary-form-color : {{ se_stts.primary_form_color }};
    --secondary-form-color : {{ se_stts.secondary_form_color }};
  }
  .t4s-count-box {
    --h-count-bgcolor: {{ se_stts.bg_hc }};
    --h-count-color: {{ se_stts.cl_hc }};
  }

  {%- if h__bgimg != blank %}
  .t4s-header__bgimg {
      background-size: cover;
      background-repeat: no-repeat;
  }
  {%- endif -%}

  {%- if h_transparent -%}

    .t4s-section-header,#shopify-section-top-bar { --h-space-tr: {% if space_transparent %}30px{% else %}0px{% endif %} }

    {%- assign opnavtr  = se_stts.opnavtr | divided_by: 100.0 -%}
    .t4s-header__wrapper {
      --h-text-color      : {{ se_stts.clnavtr }};
      --h-text-color-rgb  : {{ se_stts.clnavtr | color_to_rgb | remove: 'rgba(' | remove: 'rgb(' | remove: ')' }};
      --h-text-color-hover: {{ se_stts.clnavtr_hover }};
      --h-bg-color        : {{ se_stts.bgnavtr | color_modify: 'alpha', opnavtr }};
    }
    .t4s-section-header {
        margin-top: var(--h-space-tr);
        margin-bottom: calc(-1 * (var(--header-height) + var(--h-space-tr)));
        position: relative;
        top: 0;
        z-index: 460;
    }

    {%- if space_transparent -%}
      .is--topbar-transparent.is--header-transparent #shopify-section-top-bar {
        margin-top: var(--h-space-tr);
      }
      #t4s-hsticky__sentinel {
        bottom:calc(-1 * var(--h-space-tr));
      }
    {%- endif -%}

  {%- endif -%}

  {%- if se_stts.sticky_header -%}
  
    {%- unless se_stts.scroll_header -%}
    .t4s-hsticky__ready .t4s-section-header {
        position: sticky;      
        top: 0;
        z-index: 460;
    }
    .is-header--stuck .t4s-section-header {
      -webkit-box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
      box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
    }
    {%- endunless -%}

    {%- assign opnavst  = se_stts.opnavst | divided_by: 100.0 -%}
    .is-header--stuck .t4s-header__wrapper {
      --h-text-color      : {{ se_stts.clnavst }};
      --h-text-color-rgb  : {{ se_stts.clnavst | color_to_rgb | remove: 'rgba(' | remove: 'rgb(' | remove: ')' }};
      --h-text-color-hover: {{ se_stts.clnavst_hover }};
      --h-bg-color        : {{ se_stts.bgnavst | color_modify: 'alpha', opnavst }};
    }
    .is-header--stuck .header__sticky-logo {
      display:block !important
    }
    .is-header--stuck .header__normal-logo,
    .is-header--stuck .header__mobile-logo {
      display:none !important
    }
  {%- endif -%}
 
  .t4s-section-header [data-header-height] {
      min-height: {{ se_stts.h_navmb }}px;    
  }
  .t4s-section-header [data-header-height2] {
      min-height: {{ se_stts.height2 }}px;    
  }
  .t4s-header__logo img {
    padding-top: 5px;
    padding-bottom: 5px;
    transform: translateZ(0);
    max-height: inherit;
    height: auto;
    width: 100%;
    max-width: 100%;
  }
  .t4s-header__logo img[src*=".svg"] {
    height: 100%;
    perspective: 800px;
    -webkit-perspective: 800px;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
  .t4s-site-nav__icons .t4s-site-nav__icon {
      padding: 0 6px;
      display: inline-block;
      line-height: 1;
  }
  .t4s-site-nav__icons svg.t4s-icon {
      color: var(--h-text-color);
      line-height: 1;
      vertical-align: middle;
      transition: color 0.2s ease-in-out;
      width: 22px;
      height: 22px;
  }
  .t4s-site-nav__icons.t4s-use__kalles svg.t4s-icon--account {
      width: 24px;
      height: 24px;
  }
  .t4s-site-nav__icons.t4s-use__line svg.t4s-icon {
    width: 25px;
    height: 25px;
  }
  .t4s-site-nav__icon>a:hover svg.t4s-icon {
      color: var(--h-text-color-hover);
  }
  .t4s-site-nav__icon a { 
    display: inline-block;
    line-height: 1;
  }
  .t4s-header__wrapper .t4s-socials a,
  .t4s-site-nav__cart >a,.t4s-push-menu-btn,.t4s-col__textSocial,.t4s-col__textSocial a {color: var(--h-text-color)}
  .t4s-site-nav__cart >a:hover,.t4s-col__textSocial a:hover {color: var(--h-text-color-hover)}
  @media (min-width: 768px) {
    .t4s-site-nav__icons .t4s-site-nav__icon {
        padding: 0 8px;
    }
  }
  @media (min-width: 1025px) {
    {%- if se_stts.border -%}
    .t4s-section-header__mid {
      border-bottom: 1px solid rgba(var(--h-text-color-rgb), 0.15);
     }
    {%- endif -%}
      .t4s-section-header [data-header-height] {
         min-height: {{ se_stts.height }}px;    
      }
      {%- if se_stts.full_header -%}
      .t4s-announcement-bar >.t4s-container, .t4s-top-bar >.t4s-container, .t4s-header__wrapper >.t4s-container {
          max-width: 100%;
      }
      {%- else -%}
         {%- if space_transparent and h_transparent -%}
          .is--topbar-transparent.is--header-transparent #shopify-section-top-bar,
          html:not(.is-header--stuck) .t4s-section-header {
            max-width: 1170px;
            margin-right: auto;
            margin-left: auto;
          }
        {%- endif -%}
        .t4s-header__wrapper>.t4s-container {
          padding-right: 20px;
          padding-left: 20px;
        }
      {%- endif -%}
      .t4s-nav__ul {
          margin: 0;padding:0
      }
    .t4s-nav__ul>li> a {
        color: var(--h-text-color);
        padding: 5px 17.5px;
        text-transform: none;
        font-family: var(--font-family-{{ se_stts.fm_nav }});
        font-weight: {{ se_stts.fw_nav }};
        font-size: {{ se_stts.fs_nav }}px;
        {%- if se_stts.ls_nav != 0 %}letter-spacing: {{ se_stts.ls_nav }}px;{% endif -%}
    }
    .t4s-nav__ul>li> a:hover { color: var(--h-text-color-hover) !important}
    .t4s-nav__ul .t4s-icon-select-arrow {
        position: static;
        width: 8px;
        margin-left: 4px;
        height: 8px;
        opacity: .8;
    }
    .t4s-col__textSocial p { margin-bottom: 0; }
    {%- if se_stts.enable_active %}
    .t4s-nav__ul>li.is--nav__active> a {
      color: var(--h-text-color-hover) !important;
      transition: none;
    }
    {%- endif -%}
    #shopify-section-header-bottom .t4s-search-header__form,
    #shopify-section-header-bottom .t4s-frm-search__results{min-width:400px}
    #shopify-section-header-bottom .t4s-search-header__input{height:50px}
    #shopify-section-header-bottom .t4s-search-header__form{border:none}
  }
{%- endstyle -%}

<div data-header-options='{ "isTransparent": {{ h_transparent }},"isSticky": {{ se_stts.sticky_header }},"hideScroldown": {{ se_stts.scroll_header }} }' class="t4s-header__wrapper t4s-pr t4s-layout-layout_bottom{% if h__bgimg != blank %} lazyloadt4s t4s-header__bgimg" data-bgset="{{ h__bgimg | image_url: width: 1 }}" data-ratio="{{ h__bgimg.aspect_ratio }}" data-sizes="auto"{% else %}"{% endif %}>
    <div class="t4s-container">
        <div class="t4s-section-header__mid">
        	{%- if source != '4' -%}
	        <div data-header-height class="t4s-row t4s-gx-15 t4s-gx-md-30 t4s-align-items-center">
		          <div class="t4s-col-md-4 t4s-col-3 t4s-d-lg-none t4s-col-item">{%- render 'push_menu' -%}</div>
		          <div class="t4s-col-3 t4s-text-lg-start t4s-d-none t4s-d-lg-block t4s-col-item t4s-col__textSocial">
			          {%- if source == '1' -%}<div class="t4s-rte">{{- se_stts.txt -}}</div>
			          {%- elsif source == '2' or source == '3' -%}
                  {%- if source == '3' %}{% assign follow_social = true %}{% endif -%}
                  {{ 'icon-social.css' | asset_url | stylesheet_tag }}
			            {%- render 'social_sharing', style: 1, use_color_set: false, size: 'small', space_h_item: 15, space_h_item_mb: 10, space_v_item: 0, space_v_item_mb: 0, share_permalink: shop.url, share_title: shop.name, share_image: share_image, follow_social: follow_social -%}
                {%- elsif source == '5' %}
		              {%- render 'search_form_header'-%}
                {%- endif -%}
		          </div>
		          <div class="t4s-col-lg-6 t4s-col-md-4 t4s-col-6 t4s-text-center t4s-col-item">{%- render 't4s_logo', tag: 'div', isTransparent: h_transparent -%}</div>
		          <div class="t4s-col-lg-3 t4s-col-md-4 t4s-col-3 t4s-text-end t4s-col-group_btns t4s-col-item t4s-lh-1">{%- render 't4s_group_btns', se_stts: se_stts -%}</div>
	       </div>
	       {%- else -%}
	        <div data-header-height class="t4s-row t4s-gx-15 t4s-gx-md-30 t4s-align-items-center">
		          <div class="t4s-col-md-4 t4s-col-3 t4s-d-lg-none t4s-col-item">{%- render 'push_menu' -%}</div>
		          <div class="t4s-col-lg-3 t4s-d-lg-block t4s-d-none t4s-col-group_btns t4s-col-item t4s-lh-1">{%- render 't4s_group_btns_split', se_stts: se_stts, isSplit1: true -%}</div>
		          <div class="t4s-col-lg-6 t4s-col-md-4 t4s-col-6 t4s-text-center t4s-col-item">{%- render 't4s_logo', tag: 'div', isTransparent: h_transparent -%}</div>
		          <div class="t4s-col-lg-3 t4s-col-md-4 t4s-col-3 t4s-text-end t4s-col-group_btns t4s-col-item t4s-lh-1">{%- render 't4s_group_btns_split', se_stts: se_stts, isSplit1: false -%}</div>
	       </div>
	       {%- endif -%}
        </div>
	    <div class="t4s-section-header__bot t4s-d-none t4s-d-lg-block">
	       <div data-header-height2 class="t4s-row t4s-g-0 t4s-align-items-center">
	          <div class="t4s-col t4s-col-item">{%- render 'menu_blocks', admin_sp: admin_sp, se_blocks: se_blocks, se_stts: se_stts -%}</div>
	       </div>
	    </div>
    </div>
</div>

{%- if h_transparent -%}
<script>
document.documentElement.classList.add('is--header-transparent');
document.documentElement.style.setProperty('--header-height', document.getElementById('shopify-section-{{ section.id }}').offsetHeight + 'px');
</script>
{%- endif -%}

{%- schema -%}
  {
   "name": "Header bottom",
   "tag": "header",
   "class": "t4s-section t4s-section-header",
   "settings": [
      {
        "type": "checkbox",
        "id": "full_header",
        "info": "Make header full width",
        "label": "Enable full Width",
        "default": false
      },
      {
        "type": "header",
        "content": "+ Options only working desktop"
      },
      {
        "type": "range",
        "id": "height",
        "label": "Custom header top height",
        "min": 60,
        "max": 160,
        "step": 1,
        "unit": "px",
        "default": 90
      },
      {
        "type": "range",
        "id": "height2",
        "label": "Custom header bottom height",
        "min": 40,
        "max": 140,
        "step": 1,
        "unit": "px",
        "default": 50
      },
      {
        "type": "checkbox",
        "id": "arrow",
        "label": "Show dropdown arrow",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "border",
        "label": "Show border bottom",
        "default": true
      },
      {
        "type": "select",
        "id": "align",
         "options": [
          {
            "value": "start",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "end",
            "label": "Right"
          }
         ],
        "label": "Main menu align:",
        "default": "center"
      },
      {
        "type": "select",
        "id": "hover",
        "options": [
          {
            "value": "hover_sideup",
            "label": "Hover Slide Up"
          },
          {
            "value": "hover_fadein",
            "label": "Hover Fade In"
          }
        ],
        "label": "Hover effect:",
        "default": "hover_sideup"
      },
      {
        "type": "checkbox",
        "id": "enable_active",
        "info": "Make hightlight if the link is active",
        "label": "Enable link active",
        "default": false
      },
      {
        "type": "header",
        "content": "+ Options only working Tablet, mobile"
      },
      {
        "type": "range",
        "id": "h_navmb",
        "label": "Custom header mobile height",
        "min": 60,
        "max": 160,
        "step": 1,
        "unit": "px",
        "default": 62
      },
      /*{
        "type": "checkbox",
        "id": "mb_cat",
        "label": "Show Categories?",
        "info": "Add, Edit content to this section using the Sections sidebar.",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "only_icon",
        "label": "Only click icon?",
        "info": "Only click icon to show submenu.",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "first_cat",
        "label": "Categories position the first?",
        "default": false
      },*/
      {
        "type": "header",
        "content": "+ Header Colors:"
      },
      {
        "type": "image_picker",
        "id": "h__bgimg",
        "label": "Header Background image"
      },
      {
        "type": "color",
        "id": "bgnav",
        "label": "Header background color",
        "default": "#ffffff"
      },
      {
        "type": "range",
        "id": "opnav",
        "label": "Background opacity",
        "default": 100,
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "%"
      },
      {
        "type": "color",
        "id": "clnav",
        "label": "Header text/icon color",
        "default": "#222222"
      },
      {
        "type": "color",
        "id": "clnav_hover",
        "label": "Header text/icon color when hover",
        "default": "#56cfe1"
      },
      {
        "type": "header",
        "content": "+ Header Group buttons:"
      },
      {
        "type": "select",
        "id": "h_icon",
        "options": [
          {
            "value": "kalles",
            "label": "Kalles icon"
          },
          {
            "value": "pe",
            "label": "Pe icon"
          },
          {
            "value": "drawn",
            "label": "Drawn icon"
          },
          {
            "value": "line",
            "label": "Line awesome"
          }
        ],
        "label": "Design icon:",
        "default": "kalles"
      },
      {
        "type": "select",
        "id": "hover_icon",
        "options": [
          {
            "value": "1",
            "label": "Simple"
          },
          {
            "value": "2",
            "label": "Zoom"
          },
          {
            "value": "3",
            "label": "Zoom and skew"
          }
        ],
        "label": "Hover effect icon:",
        "default": "2"
      },
      {
        "type": "checkbox",
        "id": "show_search",
        "label": "Show search icon?",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_acc",
        "label": "Show account icon?",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_wis",
        "label": "Show wishlist icon?",
        "default": true
      },
      {
        "type": "select",
        "id": "cart_des",
        "options": [
          {
            "value": "0",
            "label": "Disable"
          },
          {
            "value": "1",
            "label": "Cart count"
          },
          {
            "value": "2",
            "label": "Cart count, total price"
          },
          {
            "value": "3",
            "label": "Cart count 2"
          },
          {
            "value": "4",
            "label": "Cart total price"
          },
          {
            "value": "5",
            "label": "Cart divider, total price"
          }
        ],
        "label": "Shopping cart:",
        "default": "1",
        "info": "Set your shopping cart widget design in the header."
      },
      {
        "type": "color",
        "id": "bg_hc",
        "label": "Count background color",
        "default": "#000000"
      },
      {
        "type": "color",
        "id": "cl_hc",
        "label": "Count text color",
        "default": "#ffffff"
      },
      {
        "type": "header",
        "content": "+ Sticky header"
      }, 
      {
        "type": "checkbox",
        "id": "sticky_header",
        "label": "Enable sticky header",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "scroll_header",
        "label": "Hide sticky header on scroll down",
        "default": true
      },
      {
        "type": "color",
        "id": "bgnavst",
        "label": "Header background color",
        "default": "#ffffff"
      },
      {
        "type": "range",
        "id": "opnavst",
        "label": "Background opacity",
        "default": 100,
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "%"
      },
      {
        "type": "color",
        "id": "clnavst",
        "label": "Header text/icon color",
        "default": "#222222"
      },
      {
        "type": "color",
        "id": "clnavst_hover",
        "label": "Header text/icon color when hover",
        "default": "#56cfe1"
      },
      {
        "type": "header",
        "content": "+ Transparent header"
      },
      {
        "type": "checkbox",
        "id": "transparent_header",
        "label": "Enable transparent header",
        "info": "Only active on homepage",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "space_transparent_header",
        "label": "Enable transparent header space top",
        "info": "Only active on homepage and transparent header active",
        "default": false
      },
      {
        "type": "color",
        "id": "bgnavtr",
        "label": "Header background color",
        "default": "#000000"
      },
      {
        "type": "range",
        "id": "opnavtr",
        "label": "Background opacity",
        "default": 40,
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "%"
      },
      {
        "type": "color",
        "id": "clnavtr",
        "label": "Header text/icon color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "id": "clnavtr_hover",
        "label": "Header text/icon color when hover",
        "default": "#ffffff"
      },
      {
        "type": "header",
        "content": "+ Text/Social"
      },
      {
        "type": "select",
        "id": "source",
        "options": [
          {
            "value": "0",
            "label": "None"
          },
          {
            "value": "1",
            "label": "Text"
          },
          {
            "value": "2",
            "label": "Social share"
          },
          {
            "value": "3",
            "label": "Social follow"
          },
          {
            "value": "4",
            "label": "Button search, account"
          },
          {
            "value": "5",
            "label": "Search form"
          }
        ],
        "label": "Left show",
        "default": "2"
      },
      {
        "type": "richtext",
        "id": "txt",
        "label": "Text",
        "info": "You can place here some advertisement or phone numbers.",
        "default": "<p>Welcome to our store!</p>"
      },
      {
        "type": "header",
        "content": "+ Options with search form"
      },
      {
        "type":"color",
        "id":"primary_form_color",
        "label":"Primary form color",
        "default":"#fff"
      },
        {
        "type":"color",
        "id":"secondary_form_color",
        "label":"Secondary form color",
        "default":"#222"
      },
      {
        "type": "header",
        "content": "+ Navigation typography"
      },
      {
        "type": "select",
        "id": "fm_nav",
        "label": "Font Family",
        "default": "1",
        "options": [
          {
            "value": "1",
            "label": "Font Family #1"
          },
          {
            "value": "2",
            "label": "Font Family #2"
          },
          {
            "value": "3",
            "label": "Font Family #3"
          }
        ]
      },
      {
        "type": "range",
        "id": "fs_nav",
        "min": 10,
        "max": 20,
        "step": 0.5,
        "label": "Font size",
        "unit": "px",
        "default": 14
      },
      {
        "type": "range",
        "id": "fw_nav",
        "min": 300,
        "max": 800,
        "step": 100,
        "label": "Font weight",
        "default": 400
      },
      {
        "type": "number",
        "id": "ls_nav",
        "label": "Letter spacing (in pixel)",
        "info": "set is '0' use to default",
        "default": 0
      }
   ],
   "blocks": [
       {
         "type": "mega",
         "name": "Mega item",
         "settings": [
            {
              "type": "text",
              "id": "title",
              "label": "Heading",
              "default": "mega"
            },
            {
              "type": "url",
              "id": "url",
              "label": "Link"
            },
            {
              "type": "select",
              "id": "open_link",
              "options": [
                {
                  "value": "_self",
                  "label": "Current window"
                },
               {
                  "value": "_blank",
                  "label": "New window"
                }
              ],
              "label": "Open link in"
            },
            {
              "id": "icon",
              "type": "text",
              "label": "Icon",
              "info":"[Get icons Line awesome](https://kalles.the4.co/font-lineawesome/)"
            },
            {
              "type": "checkbox",
              "id": "cus_cl",
              "label": "Use custom heading color?",
              "default": false
            },
            {
              "type":"color",
              "id":"cl",
              "default": "#ec0101",
              "label":"Heading color"
            },
            {
              "type":"text",
              "id":"lb",
              "label":"Label text"
            },
            {
              "type":"color",
              "id":"lb_cl",
              "label":"Label color",
              "default":"#00BADB"
            },
            {
              "type": "header",
              "content": "+ Submenu"
            },
            /*{
              "type": "checkbox",
              "id": "lazy_mn",
              "label": "Enable Lazy menu?",
              "info": "improve page load speed",
              "default": true
            },*/
            {
              "type": "select",
              "id": "pos_sub",
              "default": "bottom",
              "options": [
                {
                  "value": "bottom-start",
                  "label": "Start"
                },
                {
                  "value": "bottom",
                  "label": "Center"
                },
                {
                  "value": "bottom-end",
                  "label": "End"
                }
              ],
              "label": "Position submenu"
            },
            {
              "type": "select",
              "id": "wid",
              "options": [
                {
                  "value": "cus",
                  "label": "Custom"
                },
                {
                  "value": "full",
                  "label": "Full width"
                },
                {
                  "value": "full nav_t4cnt",
                  "label": "Content full width"
                }
              ],
              "label": "Width submenu:"
            },
            {
              "type": "range",
              "id": "cus_wid",
              "label": "+ Custom Width",
              "min": 200,
              "max": 1200,
              "step": 50,
              "unit": "px",
              "default": 1200
            },
            {
              "type": "range",
              "id": "id",
              "min": 1,
              "max": 16,
              "step": 1,
              "label": "ID",
              "unit": "#",
              "info": "ID connect mega menu.",
              "default": 1
            },
            {
              "type": "checkbox",
              "id": "enable_packery",
              "label": "Enable layout packery",
              "default": true
            },
            {
            "type": "select",
            "id": "r_s_h_item", 
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space horizontal items",
            "default": "30"
          },
          {
            "type": "select",
            "id": "r_s_v_item",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space vertical items", 
            "default": "30"
           }
         ]
       },
       {
         "type": "drop",
         "name": "Dropdown item",
         "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Dropdown"
          },
         {
            "type": "url",
            "id": "url",
            "label": "Link"
         },
          {
            "type": "select",
            "id": "open_link",
            "options": [
              {
                "value": "_self",
                "label": "Current window"
              },
             {
                "value": "_blank",
                "label": "New window"
              }
            ],
            "label": "Open link in"
          },
          {
            "type": "link_list",
            "id": "menu",
            "label": "Add menu"
          },
          {
            "id": "icon",
            "type": "text",
            "label": "Icon",
            "info":"[Get icons Line awesome](https://kalles.the4.co/font-lineawesome/)"
          },
          {
            "type": "checkbox",
            "id": "cus_cl",
            "label": "Use custom heading color?",
            "default": false
          },
          {
            "type":"color",
            "id":"cl",
            "default": "#ec0101",
            "label":"Heading color"
          },
          {
            "type":"text",
            "id":"lb",
            "label":"Label text"
          },
          {
            "type":"color",
            "id":"lb_cl",
            "label":"Label color"
          },
          {
            "type": "header",
            "content": "+ Submenu"
          },
          {
            "type": "checkbox",
            "id": "lazy_mn",
            "label": "Enable Lazy menu?",
            "info": "improve page load speed",
            "default": true
          },
          {
            "type": "select",
            "id": "pos_sub",
            "default": "bottom",
            "options": [
              {
                "value": "bottom-start",
                "label": "Start"
              },
              {
                "value": "bottom",
                "label": "Center"
              },
              {
                "value": "bottom-end",
                "label": "End"
              }
            ],
            "label": "Position submenu"
          },
          {
            "type": "select",
            "id": "pos",
            "options": [
                {
                  "value": "left",
                  "label": "Left"
                },
                {
                  "value": "right",
                  "label": "Right"
                }
            ],
            "label": "Position child submenu"
          }
          ]
       },
       {
         "type": "base",
         "name": "Base item",
         "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "base"
          },
         {
            "type": "url",
            "id": "url",
            "label": "Link",
            "default": "/"
         },
          {
            "type": "select",
            "id": "open_link",
            "options": [
              {
                "value": "_self",
                "label": "Current window"
              },
             {
                "value": "_blank",
                "label": "New window"
              }
            ],
            "label": "Open link in"
          },
          {
            "id": "icon",
            "type": "text",
            "label": "Icon",
            "info":"[Get icons Line awesome](https://kalles.the4.co/font-lineawesome/)"
          },
          {
            "type": "checkbox",
            "id": "cus_cl",
            "label": "Use custom heading color?",
            "default": false
          },
          {
            "type":"color",
            "id":"cl",
            "default": "#ec0101",
            "label":"Heading color"
          },
          {
            "type":"text",
            "id":"lb",
            "label":"Label text"
          },
          {
            "type":"color",
            "id":"lb_cl",
            "label":"Label color"
          }
          ]
       }
   ],
    "default": {
      "blocks": [
        {
          "type": "mega"
        },
        {
          "type": "mega"
        },
        {
          "type": "base"
        }
      ]
    }
}
{% endschema %}