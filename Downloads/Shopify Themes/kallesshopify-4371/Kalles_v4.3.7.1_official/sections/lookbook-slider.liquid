{{ 'section.css' | asset_url | stylesheet_tag }} 
{{ 'lookbook.css' | asset_url | stylesheet_tag }} 
{{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
{{ 'slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'button-style.css' | asset_url | stylesheet_tag }}
{{ 'collection-products.css' | asset_url | stylesheet_tag }}
<link href="{{ 'base_drop.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign section_blocks = section.blocks
  assign stt_layout = se_stts.layout
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign arr_img = section_blocks | where: "type", "img"
  assign routes_local = routes.cart_url | split: 'cart' | first 
  if se_stts.btn_owl == "outline"
      assign arrow_icon = 1 
  else
      assign arrow_icon = 2
  endif
  assign slide_eff = se_stts.eff
  assign index = 1
  assign offset = 0 
  assign root_url = routes.root_url
  if root_url != '/'
    assign root_url = root_url | append: '/'
  endif
 -%}

  <div class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }}" {% render 'section_style', se_stts: se_stts %}> 
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner">{%- endif -%}
     <div class="pin__slider t4s-row flickityt4s nt_slider t4s-flicky-slider t4s-gx-0 t4s-slide-eff-{{ slide_eff }} {% if se_stts.nav_btn == true %}  t4s-slider-btn-style-{{ se_stts.btn_owl }} t4s-slider-btn-{{ se_stts.btn_shape }} t4s-slider-btn-{{ se_stts.btn_size }} t4s-slider-btn-cl-{{ se_stts.btn_cl }} t4s-slider-btn-vi-{{ se_stts.btn_vi }} t4s-slider-btn-hidden-mobile-{{ se_stts.btn_hidden_mobile }} {% endif %} {%- if se_stts.nav_dot == true -%} t4s-dots-style-{{ se_stts.dot_owl }} t4s-dots-cl-{{ se_stts.dots_cl }} t4s-dots-round-{{ se_stts.dots_round }} t4s-dots-hidden-mobile-{{ se_stts.dots_hidden_mobile }} {%- endif -%}" data-flickityt4s-js='{"setPrevNextButtons":true,"arrowIcon":"{{ arrow_icon }}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": {{ se_stts.loop }},"prevNextButtons": {{ se_stts.nav_btn }},"percentPosition": 1,"pageDots": {{ se_stts.nav_dot }}, "autoPlay" : {{ se_stts.au_time | times: 1000 }}, "pauseAutoPlayOnHover" : {{ se_stts.au_hover }} }' style="--space-dots: {{ se_stts.dots_space }}px;--flickity-btn-pos: 30px; --flickity-btn-pos-mb: 30px;">
        {%- if arr_img.size > 0 -%}
          {%- for bl in arr_img -%}{% assign offset = offset | plus: 1 %}{% assign image = bl.settings.image -%}
            <div class="t4s-pr t4s-lookbook-wrapper t4s-col-item t4s-col-12"> 
              <div class="t4s-lookbook-img t4s-pr t4s-oh t4s_position_8 t4s_cover t4s_ratioadapt" timeline hdt-reveal="slide-in">
                {%- if image -%}{%- assign ratio = image.aspect_ratio -%}
                <div class="t4s-lookbook-img-wrap t4s_ratio" style="--aspect-ratioapt:{{ ratio | default: 1.7777 }};">
                    <img class="lazyloadt4s t4s-lz--fadeIn t4s-img-as-bg 
                    pin__image" data-src="{{ image | image_url: width: 1 }}" data-widths="[800, 1000, 1200, 1400, 1600, 1800, 2000, 2200, 2500, 3000, 3400, 3800, 4100]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                   <span class="lazyloadt4s-loader is-bg-img" style="background: url({{ image | image_url: width: 1 }})"></span>
                </div>
                {%- else -%}
                  {%- capture current -%}{% cycle 1, 2 %}{%- endcapture -%}
                  {{ 'lifestyle-' | append: current | placeholder_svg_tag: 't4s-placeholder-svg t4s-svg-bg1' }}  
                {%- endif -%}
              </div>  

              {%- if section_blocks.size > 0 -%}
                {%- for block in section_blocks offset: index -%}{%- assign index = index | plus: 1 -%}{%- assign bk_stts = block.settings -%}
                  {%- case block.type -%}
                    {%- when 'img' -%} {% break %}
                    {%- when 'pr' -%}{%- if bk_stts.product == blank -%}{%- continue -%}{%- endif -%}
                      <span data-bid="t4s_{{ se_id }}{{ block.id }}" data-pin-popup data-position="{{ bk_stts.pos_popup }}" data-is-pr data-href="{{ bk_stts.product.url }}" data-sid="render-pr_lb{{ se_stts.pr_pin_des }}" class="t4s-lookbook-pin is-type__pr pin__size--{{ bk_stts.pos_size }} pin_ic_{{ bk_stts.type }} pin__type_{{ block.id }}" {{ block.shopify_attributes }} {% render 'pin_position', bk_stts: bk_stts %}>
                        <span class="t4s-zoompin"></span>
                        <span class="t4s-pin-tt">
                          {%- if bk_stts.type != '3' -%}<i class="t4s-nav-link-icon"></i>
                          {%- else -%}<span class="t4s-truncate">{{ bk_stts.shorttxt }}</span>
                          {%- endif -%}
                        </span>
                      </span>
                    {%- when 'txt' -%}
                      <span data-bid="t4s_{{ se_id }}{{ block.id }}" data-pin-popup data-position="{{ bk_stts.pos_popup }}" class="t4s-lookbook-pin is-type__text pin__size--{{ bk_stts.pos_size }} pin_ic_{{ bk_stts.type }} pin__type_{{ block.id }}" {{ block.shopify_attributes }} {% render 'pin_position', bk_stts: bk_stts %}>
                        <span class="t4s-zoompin"></span>
                        <span class="t4s-pin-tt">
                          {%- if bk_stts.type != '3' -%}<i class="t4s-nav-link-icon"></i>
                          {%- else -%}<span class="t4s-truncate">{{ bk_stts.shorttxt }}</span>
                          {%- endif -%}
                        </span>
                      </span>
                        </span>
                     <template id="temt4s_{{ se_id }}{{ block.id }}">
                      <div data-pin-wrapper id="" class="t4s-lb__wrapper t4s-lb-txt-wrapper">
                         <div class="t4s-lb-arrow"></div>
                         <div class="t4s-lb__header">
                           <span class="t4s-lb__title">{{ 'sections.lookbook.title.text' | t }}</span>
                           <button data-pin-close aria-label="{{ 'general.aria.close' | t }}"><svg role="presentation" class="t4s-iconsvg-close" viewBox="0 0 16 14" width="16"><path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path></svg></button>
                         </div>
                         <div class="t4s-lb__content">
                            <div class="t4s-lb-title">{{ bk_stts.heading }}</div>
                            <div class="t4s-lb-content t4s-rte">{{ bk_stts.text }}</div>
                         </div>
                      </div>
                    </template>
                    {%- else -%}
                      <a href="{{ bk_stts.url }}" target="{{ bk_stts.open_link }}" class="t4s-lookbook-pin is-type__ink pin__size--{{ bk_stts.pos_size }} pin_ic_{{ bk_stts.type }} pin__type_{{ block.id }}" {% render 'pin_position', bk_stts: bk_stts %} >
                        <span class="t4s-zoompin"></span>
                         <span class="t4s-pin-tt">
                          {%- if bk_stts.type != '3' -%}<i class="t4s-nav-link-icon"></i>
                          {%- else -%}<span class="t4s-truncate">{{ bk_stts.shorttxt }}</span>
                          {%- endif -%}
                        </span>
                      </a>
                  {%- endcase -%}
                {%- endfor -%}
              {%- endif -%}  
            </div> 
          {%- endfor -%}
        {%- else -%}
        <div class="t4s-col-12 t4s-text-center">{%- render 'no-blocks' -%}</div>
        {%- endif -%}
     </div>
     {{- html_layout[1] -}}
  </div>

{%- schema -%}
  {
    "name": "Lookbook Slider",
    "class": "t4s-section t4s-section-all t4s_tp_lb t4s_tp_flickity t4s_bk_flickity",
    "max_blocks": 16,
    "settings": [
      {
        "type": "header",
        "content": "1. Slider Settings"
      },
      {
        "type": "select","id": "eff", "default": "slide",
        "label": "Slider effect","info":"Effect between transitioning slides",
        "options": [
            {
                "value": "slide","label": "Slide"
            },
            {
                "value": "fade","label": "Fade"
            }
        ]
      },
      {
        "type": "header",
        "content": "+Options for carousel layout"
      },
      {
        "type": "checkbox",
        "id": "loop",
        "label": "Enable loop",
        "info": "At the end of cells, wrap-around to the other end for infinite scrolling",
        "default": true
      }, 
      {
        "type": "range",
        "id": "au_time",
        "min": 0,
        "max": 30,
        "step": 0.5,
        "label": "Autoplay speed in second.",
        "info": "Set is '0' to disable autoplay",
        "unit": "s",
        "default": 0
      },
      {
        "type": "checkbox",
        "id": "au_hover",
        "label": "Pause autoplay on hover",
        "info": "Auto-playing will pause when the user hovers over the carousel",
        "default": true
      },
      {
        "type": "paragraph",
        "content": "—————————————————"
      },
      {
        "type": "paragraph",
        "content": "Prev next button"
      },
      {
        "type": "checkbox",
        "id": "nav_btn",
        "label": "Use prev next button",
        "info": "Creates and show previous & next buttons",
        "default": false
      },
      {
        "type": "select",
        "id": "btn_vi",
        "label": "Visible",
        "default": "hover",
        "options": [
          {
            "value": "always",
            "label": "Always"
          },
          {
            "value": "hover",
            "label": "Only hover"
          }
        ]
      },
      {
        "type": "select",
        "id": "btn_owl",
        "label": "Button style",
        "default": "default",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "outline",
            "label": "Outline"
          },
          {
            "value": "simple",
            "label": "Simple"
          }
        ]
      },
      {
        "type": "select",
        "id": "btn_shape",
        "label": "Button shape",
        "info": "Not working with button style 'Simple'",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "Default"
          },
          {
            "value": "round",
            "label": "Round"
          },
          {
            "value": "rotate",
            "label": "Rotate"
          }
        ]
      },
      {
          "type": "select",
          "id": "btn_cl",
          "label": "Button color",
          "default": "dark",
          "options": [
              {
                  "value": "default",
                  "label": "Default"
              },
              {
                  "value": "light",
                  "label": "Light"
              },
              {
                  "value": "dark",
                  "label": "Dark"
              },
              {
                  "value": "primary",
                  "label": "Primary"
              },
              {
                  "value": "custom1",
                  "label": "Custom color 1"
              },
              {
                  "value": "custom2",
                  "label": "Custom color 2"
              }
          ]
      },
      {
        "type": "select",
        "id": "btn_size",
        "label": "Buttons size",
        "default": "small",
        "options": [
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ]
      },
      {
        "type":"checkbox",
        "id":"btn_hidden_mobile",
        "label":"Hidden buttons on mobile ",
        "default": true
      },
      {
        "type": "paragraph",
        "content": "—————————————————"
      },
      {
        "type": "paragraph",
        "content": "Page dots"
      },
      {
        "type": "checkbox",
        "id": "nav_dot",
        "label": "Use page dots",
        "info": "Creates and show page dots",
        "default": false
      },
      {
        "type": "select",
        "id": "dot_owl",
        "label": "Dots style",
        "default": "default",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "outline",
            "label": "Outline"
          },
          {
            "value": "elessi",
            "label": "Elessi"
          }
        ]
      },
      {
        "type": "select",
        "id": "dots_cl",
        "label": "Dots color",
        "default": "dark",
        "options": [
          {
              "value": "default",
              "label": "Default"
          },
          {
              "value": "light",
              "label": "Light (Best on dark background)"
          },
          {
              "value": "dark",
              "label": "Dark"
          },
          {
              "value": "primary",
              "label": "Primary"
          },
          {
              "value": "custom1",
              "label": "Custom color 1"
          },
          {
              "value": "custom2",
              "label": "Custom color 2"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "dots_round",
        "label": "Enable dots round",
        "default": true
      },
      {
        "type": "range",
        "id": "dots_space",
        "min": 2,
        "max": 20,
        "step": 1,
        "label": "Dot between horizontal",
        "unit": "px",
        "default": 10
      },
      {
        "type":"checkbox",
        "id":"dots_hidden_mobile",
        "label":"Hidden dots on mobile ",
        "default": false
      },
      {
        "type": "header","content": "2. Pin product design"
      },
      {
        "type": "select",
        "id": "pr_pin_des",
        "options": [
            {
                "value": "1",
                "label": "Pin product design 1"
            },
            {
                "value": "2",
                "label": "Pin product design 2"
            },
            {
                "value": "3",
                "label": "Pin product design 3"
            },
            {
                "value": "4",
                "label": "Pin product design 4"
            },
            {
                "value": "5",
                "label": "Pin product design 5"
            },
            {
                "value": "6",
                "label": "Pin product design 6"
            }
        ],
        "label": "Select design",
          "default": "1"
      },
      {
        "type": "header","content": "3. Design Settings"
      },
      {
        "type": "select",
        "id": "layout",
        "default": "t4s-se-container",
        "label": "Section Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped Container"},
            { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
          "type": "color",
          "id": "cl_bg",
          "label": "Background"
      },
      {
          "type": "color_background",
          "id": "cl_bg_gradient",
          "label": "Background gradient"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      },
      {
          "type": "header",
          "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      }
    ],
    "blocks": [
      {
        "type": "img",
        "name": "Image slide (parent)",
        "limit": 10,
        "settings": [
          {
              "type": "image_picker","id": "image","label": "Choose image"
          }
        ]
      },
      {
        "type": "pr",
        "name": "Product",
        "settings": [
          {
            "type": "header","content": "+ Pin Settings"
          },
          {
            "type":"range","id":"pos_t","min":0,"max":100,"step":1,"unit":"%","label":"Position Top","default":50
          },
          {
            "type":"range","id":"pos_l","min":0,"max":100,"step":1,"unit":"%","label":"Position Left","default":50
          },
          {
             "type": "select","id": "type","label": "Title type",
             "options": [
                { "value": "1", "label": "icon 1"},
                { "value": "2", "label": "icon 2"},
                { "value": "3", "label": "Short Text"}
             ]
          },
         {
          "type":"text","id":"shorttxt","label":"Short Text","default":"$59"
          },
          /*{
           "type": "select","id": "pos_pin","label": "Position pin wrapper","default": "top",
           "options": [
              { "value": "top", "label": "Top"},
              { "value": "bottom", "label": "Bottom"},
              { "value": "left", "label": "Left"},
              { "value": "right", "label": "Right"}
           ]
          },*/
          {
           "type": "select","id": "pos_size","label": "Pin size","default": "medium",
           "options": [
              { "value": "small", "label": "Small"},
              { "value": "medium", "label": "Medium"},
              { "value": "exmedium", "label": "Large"},
              { "value": "large", "label": "Extra large"}
           ]
          },
          
          {
           "type": "color","id": "bg_cl","label": "Background color","default": "#65affa"
          },
          {
            "type": "color","id": "cl_text","label": "Icon/Text color","default": "#fff" 
          },
          {
            "type": "header","content": "+ Product Settings"
          },
          {
             "type": "product","id": "product","label": "Choose product"
          }
        ]
      },
      {
        "type": "txt",
        "name": "Text",
        "settings": [
          {
            "type": "header","content": "+ Pin Settings"
          },
          {
            "type":"range","id":"pos_t","min":0,"max":100,"step":1,"unit":"%","label":"Position Top","default":50
          },
          {
            "type":"range","id":"pos_l","min":0,"max":100,"step":1,"unit":"%","label":"Position Left","default":50
          },
          {
             "type": "select","id": "type","label": "Title type",
             "options": [
                { "value": "1", "label": "icon 1"},
                { "value": "2", "label": "icon 2"},
                { "value": "3", "label": "Short Text"}
             ]
          },
         {
          "type":"text","id":"shorttxt","label":"Short Text","default":"$59"
          },
          /*{
           "type": "select","id": "pos_pin","label": "Position pin wrapper","default": "top",
           "options": [
              { "value": "top", "label": "Top"},
              { "value": "bottom", "label": "Bottom"},
              { "value": "left", "label": "Left"},
              { "value": "right", "label": "Right"}
           ]
          },*/
          {
           "type": "select","id": "pos_size","label": "Pin size","default": "medium",
           "options": [
              { "value": "small", "label": "Small"},
              { "value": "medium", "label": "Medium"},
              { "value": "exmedium", "label": "Large"},
              { "value": "large", "label": "Extra large"}
           ]
          },
          
          {
           "type": "color","id": "bg_cl","label": "Background color","default": "#65affa"
          },
          {
            "type": "color","id": "cl_text","label": "Icon/Text color","default": "#fff" 
          },
          {
            "type": "header","content": "+ Content Settings"
          },
         {
          "type":"text","id":"heading","label":"Heading","default":"01 - Water Resistance"
          },
         {
          "type":"richtext","id":"text","label":"Content","default":"<p>With groundbreaking water resistant capabilities, The Mission has the highest waterproof rating of any smartwatch on the market.</p>"
          }
         ]
      },
      {
        "type": "url",
        "name": "Link",
        "settings": [
          {
            "type": "header","content": "+ Pin Settings"
          },
          {
            "type":"range","id":"pos_t","min":0,"max":100,"step":1,"unit":"%","label":"Position Top","default":50
          },
          {
            "type":"range","id":"pos_l","min":0,"max":100,"step":1,"unit":"%","label":"Position Left","default":50
          },
          {
             "type": "select","id": "type","label": "Title type",
             "options": [
                { "value": "1", "label": "icon 1"},
                { "value": "2", "label": "icon 2"},
                { "value": "3", "label": "Short Text"}
             ]
          },
         {
          "type":"text","id":"shorttxt","label":"Short Text","default":"$59"
          },
          
          {
           "type": "select","id": "pos_size","label": "Pin size","default": "medium",
           "options": [
              { "value": "small", "label": "Small"},
              { "value": "medium", "label": "Medium"},
              { "value": "exmedium", "label": "Large"},
              { "value": "large", "label": "Extra large"}
           ]
          },
          
          {
           "type": "color","id": "bg_cl","label": "Background color","default": "#65affa"
          },
          {
            "type": "color","id": "cl_text","label": "Icon/Text color","default": "#fff" 
          },
          {
            "type": "header","content": "+ Link Settings"
          },
         {
          "type":"url","id":"url","label":"Link"
          },
          {
            "type": "select",
            "id": "open_link",
            "options": [
              {
                "value": "_self",
                "label": "Current window (_self)"
              },
             {
                "value": "_blank",
                "label": "New window (_blank)"
              }
            ],
            "label": "Open link in",
            "default": "_blank"
          }
         ]
      }
    ],
    "presets": [
      {
        "name": "Lookbook Slider",
        "category": "VIII. Lookbook",
        "blocks": [
          {
            "type": "img"
          },
          {
            "type": "txt"
          },
          {
            "type": "img"
          },
          {
            "type": "txt"
          }
        ]
      }
    ]
  }
{% endschema %}