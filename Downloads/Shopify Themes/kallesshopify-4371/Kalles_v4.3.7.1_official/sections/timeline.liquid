<!-- sections/timeline.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'timeline.css' | asset_url | stylesheet_tag }}
{{ 'button-style.css' | asset_url | stylesheet_tag }}
<link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid
    assign image_fix = image_nt | image_tag
    assign sid = section.id
    assign se_stts = section.settings
    assign stt_layout = se_stts.layout
    assign stt_image_bg = se_stts.image_bg
    if stt_layout == 't4s-se-container' 
      assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
    elsif stt_layout == 't4s-container-wrap'
      assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
    else
      assign html_layout = '__' | split: '__'
    endif
    assign use_button = false
    assign t4s_se_class = 't4s_nt_se_' | append: sid
    if se_stts.use_cus_css and se_stts.code_cus_css != blank
        render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
    endif 
 -%}
<div class="t4s-section-inner {{ t4s_se_class }} t4s_nt_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
    {{- html_layout[0] -}}
        {%- if stt_layout == 't4s-se-container' -%}
        <div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
        {%- render 'section_tophead', se_stts: se_stts -%}
        <div class="t4s-timeline-wrapper t4s-pr t4s-timeline__line-{{ se_stts.line_style }} {% if se_stts.enable_shadow %}t4s-item-shadow{% endif %}">
            <div class="t4s-timeline-line">
                <span class="t4s-line-dot t4s-dot-start"></span>
                <span class="t4s-line-dot t4s-dot-end"></span>
            </div>
            <div class="t4s-timeline-list">
                {%- for block in section.blocks -%}    
                    {%- assign bk_stts = block.settings -%}   
                    {%- assign image = bk_stts.image -%} 
                    {%- assign button_style = bk_stts.button_style -%}
                    <div class="t4s-timeline-item">
                        {%- if bk_stts.step_label != blank -%}
                            <div class="t4s-timeline-breakpoint">
                                <span class="t4s-timeline-breakpoint-title">{{ bk_stts.step_label }}</span>
                            </div>
                        {%- endif -%}
                        <div class="t4s-timeline-inner t4s-item-position-{{ bk_stts.img_pos }}">
                            <div class="t4s-timeline-item-dot"></div>
                            <div class="t4s-timeline-col t4s-timeline-col-primary">
                                <div class="t4s-timeline-arrow"></div>
                                <div class="t4s-timeline-image t4s_{{ bk_stts.image_size }} t4s_{{ bk_stts.image_ratio }} t4s_position_{{ bk_stts.image_position }} t4s-eff t4s-eff-{{ bk_stts.b_effect }} t4s-eff-img-{{ bk_stts.img_effect }}">
                                    <div class="t4s_ratio" style="--aspect-ratioapt: {{ image.aspect_ratio | default: 1.2 }}">
                                        {%- if image != blank -%}
                                            <img {% if image.presentation.focal_point != '50.0% 50.0%' %} style="object-position: {{ image.presentation.focal_point }}"{% endif %} class="lazyloadt4s t4s-lz--fadeIn t4s-obj-eff" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                                            <span class="lazyloadt4s-loader is-bg-img" style="background: url({{ image | image_url: width: 1 }});"></span>
                                        {%- else -%}
                                            {{ 'image' | placeholder_svg_tag: 't4s-placeholder-svg obj-eff' }}
                                        {%- endif -%}
                                    </div>
                                </div>
                            </div>
                            <div class="t4s-timeline-col t4s-timeline-col-secondary">
                                <div class="t4s-timeline-arrow"></div>
                                <div class="t4s-timeline-content t4s-rte">
                                    {%- if bk_stts.sub_heading -%}
                                        <h5 class="t4s-timeline-subheading">{{ bk_stts.sub_heading }}</h5>
                                    {%- endif -%}
                                    {%- if bk_stts.heading -%}
                                        <h2 class="t4s-timeline-heading">{{ bk_stts.heading }}</h2>
                                    {%- endif -%}
                                    {%- if bk_stts.description -%}
                                        <p class="t4s-timeline-description">{{ bk_stts.description }}</p>
                                    {%- endif -%}
                                    {%- if bk_stts.url != blank and bk_stts.btn_label != blank -%}
                                        {% assign use_button = true %}
                                        <a class="t4s-btn t4s-btn-base t4s-btn-style-{{ button_style }} t4s-btn-size-{{ bk_stts.btn_size }} t4s-btn-color-{{ bk_stts.btn_cl }} {% if button_style == 'default' or button_style == 'outline' %} t4s-btn-effect-{{ bk_stts.button_effect }} t4s-btn-radius-{{ bk_stts.btn_bdr }} {% endif %}" href="{{ bk_stts.url }}" target="_blank">{{ bk_stts.btn_label }} {%- if bk_stts.btn_icon -%}<svg  class="t4s-icon-arrow" viewBox="0 0 14 10"><use xlink:href="#t4s-icon-btn"></use></svg>{%- endif -%}</a>
                                    {%- endif -%}
                                </div>
                            </div>
                        </div>
                    </div>
                {%- endfor -%}
            </div>
        </div>
    {{- html_layout[1] -}}
</div>
{%- if use_button -%}
    {{ 'button-style.css' | asset_url | stylesheet_tag }}
    <link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- endif -%}
{%- schema -%}
{
    "name":"Timeline",
    "tag":"section",
    "class":"t4s-section t4s-section-all",
    "settings":[
        {
            "type": "header",
            "content": "1. Heading options"
        },
        {
            "type": "select",
            "id": "design_heading",
            "label": "+ Design heading",
            "default": "1",
            "options": [
                {
                    "value": "1",
                    "label": "Design 01"
                },
                {
                    "value": "2",
                    "label": "Design 02"
                },
                {
                    "value": "3",
                    "label": "Design 03"
                },
                {
                    "value": "4",
                    "label": "Design 04"
                },
                {
                    "value": "5",
                    "label": "Design 05"
                },
                {
                    "value": "6",
                    "label": "Design 06 (width line-awesome)"
                },
                {
                    "value": "7",
                    "label": "Design 07"
                },
                {
                    "value": "8",
                    "label": "Design 08"
                },
                {
                    "value": "9",
                    "label": "Design 09"
                },
                {
                    "value": "10",
                    "label": "Design 10"
                },
                {
                    "value": "11",
                    "label": "Design 11"
                },
                {
                    "value": "12",
                    "label": "Design 12"
                },
                {
                    "value": "13",
                    "label": "Design 13"
                },
                {
                    "value": "14",
                    "label": "Design 14"
                },
                {
                    "value": "15",
                    "label": "Design 15"
                },
                {
                    "value": "16",
                    "label": "Design 16"
                }
            ]
        },
        {
            "type": "select",
            "id": "heading_align",
            "label": "+ Heading align",
            "default": "t4s-text-center",
            "options": [
                {
                    "value": "t4s-text-start",
                    "label": "Left"
                },
                {
                    "value": "t4s-text-center",
                    "label": "Center"
                },
                {
                    "value": "t4s-text-end",
                    "label": "Right"
                }
            ]
        },
        {
            "type": "text",
            "id": "top_heading",
            "label": "+ Heading"
        },
        {
            "type": "text",
            "id": "icon_heading",
            "label": "Enter a name icon [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
            "info": "Only used for design 6",
            "default": "las la-gem"
        },
        {
            "type": "textarea",
            "id": "top_subheading",
            "label": "+ Subheading"
        },
        {
            "type": "number",
            "id": "tophead_mb",
            "label": "+ Space bottom (px)",
            "info": "The vertical spacing between heading and content.",
            "default": 30
        },
        {
            "type": "header",
            "content": "2. General options"
        },
        {
            "type":"select",
            "id":"line_style",
            "default":"dashed",
            "label":"Line timeline style",
            "options":[
                {
                    "label":"Solid",
                    "value":"solid"
                },
                {
                    "label":"Dashed",
                    "value":"dashed"
                },
                {
                    "label":"Dotted",
                    "value":"dotted"
                }
            ]
        },
        {
            "type":"checkbox",
            "id":"enable_shadow",
            "label":"Enable box shadow",
            "default":false
        },
        {
            "type": "header",
            "content": "3. Design options"
        },
        {
            "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
            "options": [
                { "value": "t4s-se-container", "label": "Container"},
                { "value": "t4s-container-wrap", "label": "Wrapped container"},
                { "value": "t4s-container-fluid", "label": "Full width"}
            ]
        },
        {
            "type": "color",
            "id": "cl_bg",
            "label": "Background"
        },
        {
            "type": "color_background",
            "id": "cl_bg_gradient",
            "label": "Background gradient"
        },
        {
            "type": "image_picker",
            "id": "image_bg",
            "label": "Background image"
        },
        {
            "type": "text",
            "id": "mg",
            "label": "Margin",
            "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
            "default": ",,50px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd",
            "label": "Padding",
            "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
            "placeholder": "50px,,50px,"
        },
        {
          "type": "header",
          "content": "+ Design Tablet Options"
        },
        {
          "type": "text",
          "id": "mg_tb",
          "label": "Margin",
          "placeholder": ",,50px,"
        },
        {
          "type": "text",
          "id": "pd_tb",
          "label": "Padding",
          "placeholder": ",,50px,"
        },
        {
            "type": "header",
            "content": "+ Design mobile options"
        },
        {
            "type": "text",
            "id": "mg_mb",
            "label": "Margin",
            "default": ",,30px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_mb",
            "label": "Padding",
            "placeholder": ",,50px,"
        },
        {
            "type": "header",
            "content": "4. Custom css"
        },
        {
            "id": "use_cus_css",
            "type": "checkbox",
            "label": "Use custom css",
            "default":false,
            "info": "If you want custom style for this section."
        },
        { 
            "id": "code_cus_css",
            "type": "textarea",
            "label": "Code custom css",
            "info": "Use selector .SectionID to style css"
            
        }
    ],
    "blocks":[
        {
            "name":"Timeline",
            "type":"timeline",
            "settings":[
                {
                    "type":"header",
                    "content":"+ Image"
                },
                {
                    "type":"image_picker",
                    "id":"image",
                    "label":"Image"
                },
                {
                    "type":"select",
                    "id":"img_pos",
                    "label":"Image position",
                    "options":[
                        {
                            "label":"Left",
                            "value":"left"
                        },
                        {
                            "label":"Right",
                            "value":"right"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "image_ratio",
                    "label": "Aspect ratio",
                    "default": "ratioadapt",
                    "info": "Aspect ratio custom will settings in general panel.",
                    "options": [
                    {
                        "group": "Auto",
                        "value": "ratioadapt",
                        "label": "Adapt to image"
                    },
                    {
                        "group": "Landscape",
                        "value": "ratio2_1",
                        "label": "2:1"
                    },
                    {
                        "group": "Landscape",
                        "value": "ratio16_9",
                        "label": "16:9"
                    },
                    {
                        "group": "Landscape",
                        "value": "ratio8_5",
                        "label": "8:5"
                    },
                    {
                        "group": "Landscape",
                        "value": "ratio3_2",
                        "label": "3:2"
                    },
                    {
                        "group": "Landscape",
                        "value": "ratio4_3",
                        "label": "4:3"
                    },
                    {
                        "group": "Landscape",
                        "value": "rationt",
                        "label": "Ratio ASOS"
                    },
                    {
                        "group": "Squared",
                        "value": "ratio1_1",
                        "label": "1:1"
                    },
                    {
                        "group": "Portrait",
                        "value": "ratio2_3",
                        "label": "2:3"
                    },
                    {
                        "group": "Portrait",
                        "value": "ratio1_2",
                        "label": "1:2"
                    },
                    {
                        "group": "Custom",
                        "value": "ratiocus1",
                        "label": "Ratio custom 1"
                    },
                    {
                        "group": "Custom",
                        "value": "ratiocus2",
                        "label": "Ratio custom 2"
                    },
                    {
                        "group": "Custom",
                        "value": "ratiocus3",
                        "label": "Ratio custom 3"
                    },
                    {
                        "group": "Custom",
                        "value": "ratiocus4",
                        "label": "Ratio custom 4"
                    }
                    ]
                },
                {
                    "type": "select",
                    "id": "image_position",
                    "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'.And 'Not works when use 'Focal point' on image'.And 'Not works when use 'Focal point' on image'",
                    "options": [
                    {
                        "value": "default",
                        "label": "Default"
                    },
                    {
                        "value": "1",
                        "label": "Left top"
                    },
                    {
                        "value": "2",
                        "label": "Left center"
                    },
                    {
                        "value": "3",
                        "label": "Left bottom"
                    },
                    {
                        "value": "4",
                        "label": "Right top"
                    },
                    {
                        "value": "5",
                        "label": "Right center"
                    },
                    {
                        "value": "6",
                        "label": "Right bottom"
                    },
                    {
                        "value": "7",
                        "label": "Center top"
                    },
                    {
                        "value": "8",
                        "label": "Center center"
                    },
                    {
                        "value": "9",
                        "label": "Center bottom"
                    }
                    ],
                    "label": "Image position",
                    "default": "8"
                },
                {
                    "type": "select",
                    "id": "image_size",
                    "label": "Image size",
                    "default": "cover",
                    "info": "This settings apply only if the image ratio is not set to 'Adapt to image'.",
                    "options": [
                        {
                            "value": "cover",
                            "label": "Full"
                        },
                        {
                            "value": "contain",
                            "label": "Auto"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "img_effect",
                    "label": "Image hover effect",
            "info": "Waring: Hovering effect will resize your images",
                    "default": "none",
                    "options": [
                        {
                            "value": "none",
                            "label": "None"
                        },
                        {
                            "value": "zoom",
                            "label": "Zoom in"
                        },
                        {
                            "value": "rotate",
                            "label": "Rotate"
                        },
                        {
                            "value": "translateToTop",
                            "label": "Move to top "
                        },
                        {
                            "value": "translateToRight",
                            "label": "Move to right"
                        },
                        {
                            "value": "translateToBottom",
                            "label": "Move to bottom"
                        },
                        {
                            "value": "translateToLeft",
                            "label": "Move to left"
                        },
                        {
                            "value": "filter",
                            "label": "Filter"
                        },
                        {
                            "value": "bounceIn",
                            "label": "BounceIn"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "b_effect",
                    "label": "Effect",
                    "default": "none",
                    "options": [
                        {
                            "value": "none",
                            "label": "None"
                        },
                        {
                            "value": "border-run",
                            "label": "Border run"
                        },
                        {
                            "value": "pervasive-circle",
                            "label": "Pervasive circle"
                        },
                        {
                            "value": "plus-zoom-overlay",
                            "label": "Plus zoom overlay"
                        },
                        {
                            "value": "dark-overlay",
                            "label": "Dark overlay"
                        },
                        {
                            "value": "light-overlay",
                            "label": "Light overlay"
                        } 
                    ]
                },
                {
                    "type":"header",
                    "content":"+ Content"
                },
                {
                    "type":"text",
                    "id":"step_label",
                    "label":"Step label",
                    "default":"1999"
                },
                {
                    "type":"text",
                    "id":"sub_heading",
                    "label":"Subheading",
                    "default":"Condimentum fames egestas ad potenti"
                },
                {
                    "type":"textarea",
                    "id":"heading",
                    "label":"Heading",
                    "default":"Lementum musat dignissim"
                },
                {
                    "type":"richtext",
                    "id":"description",
                    "label":"Description",
                    "default":"<p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s.</p>"
                },
                {
                    "type":"url",
                    "id":"url",
                    "label":"Link to",
                    "info":"If set blank will not show"
                },
                {
                    "type": "text",
                    "id": "btn_label",
                    "label": "Button label",
                    "default":"Read more",
                    "info":"If set blank will not show"
                },
                {
                    "type":"checkbox",
                    "id":"btn_icon",
                    "label":"Enable button icon",
                    "default":false
                },
                {
                    "type": "select",
                    "id": "button_style",
                    "label": "Button style",
                    "options": [
                        {
                            "label": "Default",
                            "value": "default"
                        },
                        {
                            "label": "Outline",
                            "value": "outline"
                        },
                        {
                            "label": "Bordered bottom",
                            "value": "bordered"
                        },
                        {
                            "label": "Link",
                            "value": "link"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "btn_size",
                    "label": "Button Size",
                    "default":"large",
                    "options": [
                        {
                            "label": "Extra small",
                            "value": "small"
                        },
                        {
                            "label": "Small",
                            "value": "extra-small"
                        },
                        {
                            "label": "Medium",
                            "value": "medium"
                        },
                        {
                            "label": "Large",
                            "value": "extra-medium"
                        },
                        {
                            "label": "Extra large",
                            "value": "large"
                        },
                        {
                            "label": "Extra extra large",
                            "value": "extra-large"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "btn_cl",
                    "label": "Button color",
                    "default": "dark",
                    "options": [
                        {
                            "value": "light",
                            "label": "Light"
                        },
                        {
                            "value": "dark",
                            "label": "Dark"
                        },
                        {
                            "value": "primary",
                            "label": "Primary"
                        },
                        {
                            "value": "custom1",
                            "label": "Custom color 1"
                        },
                        {
                            "value": "custom2",
                            "label": "Custom color 2"
                        }
                    ]
                },
                {
                    "type":"select",
                    "id":"button_effect",
                    "label":"Button hover effect",
                    "default":"default",
                    "info":"Only working button style default, outline",
                    "options":[
                        {
                            "label":"Default",
                            "value":"default"
                        },
                        {
                            "label":"Fade",
                            "value":"fade"
                        },
                        {
                            "label":"Rectangle out",
                            "value":"rectangle-out"
                        },
                        {
                            "label":"Sweep to right",
                            "value":"sweep-to-right"
                        },
                        {
                            "label":"Sweep to left",
                            "value":"sweep-to-left"
                        },
                        {
                            "label":"Sweep to bottom",
                            "value":"sweep-to-bottom"
                        },
                        {
                            "label":"Sweep to top",
                            "value":"sweep-to-top"
                        },
                        {
                            "label":"Shutter out horizontal",
                            "value":"shutter-out-horizontal"
                        },
                        {
                            "label":"Outline",
                            "value":"outline"
                        },
                        {
                            "label":"Shadow",
                            "value":"shadow"
                        }
                    ]
                }
            ]
        }
    ],
    "presets": [
        {
            "name": "Timeline",
            "category": "homepage",
            "blocks":[
                {"type":"timeline","type":"timeline","type":"timeline"}
            ]
        }
    ]
}
{% endschema %}