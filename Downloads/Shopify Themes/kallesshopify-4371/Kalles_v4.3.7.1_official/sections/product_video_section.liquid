{% comment %} // CSS File In Here {% endcomment %}
{{ 'slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickityt4s.min.css' | asset_url | stylesheet_tag }}
{{ 'product-video-section.css' | asset_url | stylesheet_tag }}
{% comment %} // End Added CSS {% endcomment %}

{% comment %} ! HTML Code In Here {% endcomment %}
{% # theme-check-disable %}
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  assign layout_des = se_stts.layout_des
  assign limit = se_stts.limit

  if stt_layout == 't4s-se-container'
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif

  assign t4s_se_class = 't4s_nt_se_' | append: sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
  endif
  if section.settings.center_slide
    echo 't4s-carousel-center.css' | asset_url | stylesheet_tag
  endif
-%}


<div
  data-not-main
  data-ntajax-options='{"id":"{{ sid }}","type":"{{ typeAjax }}","isProduct":false}'
  class="t4s-section-inner {{ t4s_se_class }} t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}"
  {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %}
    data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto" data-optimumx="2"
  {% endif %}
  {% render 'section_style', se_stts: se_stts -%}
>
  {{- html_layout[0] -}}

  {%- if stt_layout == 't4s-se-container' -%}
    <div
      class="t4s-container-inner {% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s {% endif %}"
      {% if stt_image_bg != blank %}
        data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto" data-optimumx="2"
      {% endif %}
    >
  {%- endif -%}
  {%- render 'section_tophead', se_stts: se_stts -%}

  {%- if layout_des == '1' -%}
    {{ 'button-style.css' | asset_url | stylesheet_tag }}
    <link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
    <div
      style="--color-hover: {{ se_stts.color_hover }};"
      class="t4s_box_pr_grid t4s-products t4s-justify-content-center t4s-row t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }} t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }}"
    >
  {%- elsif layout_des == '2' -%}
    <div
      data-t4s-resizeobserver
      class="t4s-flicky-slider t4s_box_pr_slider t4s-products {% if se_stts.nav_btn %}  t4s-slider-btn-style-{{ se_stts.btn_owl }} t4s-slider-btn-pos-{{ se_stts.btns_pos }} t4s-slider-btn-{{ se_stts.btn_shape }} t4s-slider-btn-{{ se_stts.btn_size }} t4s-slider-btn-cl-{{ se_stts.btn_cl }} t4s-slider-btn-vi-{{ se_stts.btn_vi }} t4s-slider-btn-hidden-mobile-{{ se_stts.btn_hidden_mobile }} {% endif %} {% if se_stts.nav_dot == true %}   t4s-dots-style-{{ se_stts.dot_owl }} t4s-dots-cl-{{ se_stts.dots_cl }} t4s-dots-round-{{ se_stts.dots_round }} t4s-dots-hidden-mobile-{{ se_stts.dots_hidden_mobile }} {% endif %}  t4s-row t4s-row-cols-lg-{{ se_stts.col_dk }} t4s-row-cols-md-{{ se_stts.col_tb }} t4s-row-cols-{{ se_stts.col_mb }} t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }} {% if section.settings.center_slide %} is-carousel-center{% endif %} flickityt4s"
      data-flickityt4s-js='{"cellsLengthLG": {{ se_stts.col_dk }}, "cellsLengthMD": {{ se_stts.col_tb }}, "cellsLength": {{ se_stts.col_mb }},"setPrevNextButtons":true,"arrowIcon":"{{ arrow_icon }}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": {{ se_stts.loop }},"prevNextButtons": {{ se_stts.nav_btn }},"percentPosition": 1,"pageDots": {{ se_stts.nav_dot }}, "autoPlay" : {{ se_stts.au_time | times: 1000 }},"centerSlide": {{ se_stts.center_slide }} }'
      style="--space-dots: {{ se_stts.dots_space }}px;--flickity-btn-pos: {{ se_stts.space_h_item }}px;--flickity-btn-pos-mb: {{ se_stts.space_h_item_mb }}px;--tophead_mb: {{ se_stts.tophead_mb }}px;--item-width: {{ limit }};--item-width-tablet: {{ se_stts.limit-tablet }};--color-hover: {{ se_stts.color_hover }};--page-dot: {{ se_stts.pt_page_dot }}px"
    >
  {%- endif -%}

  {% for block in section.blocks %}
    {%- liquid
      assign product = block.settings.product
      assign video = block.settings.video
      assign video_url = block.settings.external_video_url
    -%}
    <div
      class="t4s-product-video-inner t4s-d-flex t4s-flex-column t4s-col-item"
      style="--aspect-ratio: {{ se_stts.select_aspect_ratio }}"
    >
      <div class="t4s-video">
        {%- if video -%}
          {{
            video
            | video_tag:
              image_size: '1500x',
              playsinline: true,
              muted: true,
              loop: true,
              controls: false,
              preload: 'metadata',
              autoplay: true
          }}
        {%- else -%}
          {%- if video_url.type == 'youtube' -%}
            <iframe
              id="iframe-{{ section.id }}"
              src="https://www.youtube.com/embed/{{ video_url.id }}?playsinline=1&autoplay=1&controls=0&mute=1&loop=1&playlist={{ video_url.id }}&enablejsapi=1&rel=0&modestbranding=1&origin={{ 'https://' | append: request.host | url_encode }}"
              allow="autoplay; encrypted-media"
              allowfullscreen="allowfullscreen"
            ></iframe>
          {%- elsif video_url.type == 'vimeo' -%}
            <iframe
              src="https://player.vimeo.com/video/{{ video_url.id }}?autoplay=1&autopause=1&background=1&loop=1&muted=1&transparent=0&responsive=1&portrait=0&title=0&byline=0&color={{ settings.color_accent | remove_first: '#' }}"
              allow="autoplay; encrypted-media;"
              allowfullscreen="allowfullscreen"
            ></iframe>
          {%- endif -%}
        {%- endif -%}
      </div>
      <div class="t4s-w-100">
        <div
          class="t4s-video-label t4s-d-flex t4s-w-100"
        >
          {%- assign product_image = product.images[0] -%}
          {%- if product != blank -%}
            <div class="t4s-image-product t4s-d-flex t4s-justify-content-center">
              {%- if product_image -%}
              {{ product_image | img_url: '60x60' | img_tag: product_image.alt }} 
              {%- else -%}
               {{ 'hero-apparel-1' | placeholder_svg_tag: 't4s-collection-item-image t4s-oh' }}
              {%- endif -%}
            </div>
            <div
              class="t4s-d-flex t4s-justify-content-center t4s-align-items-start t4s-flex-column"
              style="{%- if se_stts.col_dk == '1' -%}width: 80%;{%- elsif se_stts.col_dk == '2' -%}
              width: 70%;{%- elsif se_stts.col_dk == '3' -%}width: 60%;{%- else -%}width: 50%;{%- endif -%}
              padding-left: 10px"
            >
              <div class="t4s-truncate" style="width: 100%;">
                <a href="{{ product.url }}">
                  {{ product.title | truncate: 20}}
                </a>
              </div>
              <div class="t4s-price">
                {{ product.price | money }}
              </div>
              <div class="t4s-price">
                {%- if product.compare_at_price -%}
                  <span style="text-decoration: line-through; color: red">{{ product.compare_at_price | money }}</span>
                {%- endif -%}
              </div>
            </div>
            {%- if se_stts.show_button_view == true -%}
              <div class="t4s-button-product">
                <a
                  class="t4s-button-product-item"
                  href="{{ product.url }}"
                  rel="nofollow"
                  data-id="{{ product.id }}"
                  data-tooltip
                  data-action-quickview
                >
                  <svg
                    class="t4s-icon-view"
                    width="19"
                    height="12"
                    viewBox="0 0 19 12"
                    fill="currentColor"
                    aria-hidden="true"
                    focusable="false"
                  >
                    <path d="M18.7079 5.6338C18.5397 5.40371 14.5321 0 9.4137 0C4.29527 0 0.287485 5.40371 0.119471 5.63358C0.041836 5.73994 0 5.86821 0 5.99989C0 6.13157 0.041836 6.25984 0.119471 6.3662C0.287485 6.59629 4.29527 12 9.4137 12C14.5321 12 18.5397 6.59625 18.7079 6.36638C18.7857 6.26008 18.8276 6.13179 18.8276 6.00009C18.8276 5.86839 18.7857 5.74011 18.7079 5.6338ZM9.4137 10.7586C5.64343 10.7586 2.37798 7.17207 1.41133 5.99958C2.37673 4.82605 5.63534 1.24137 9.4137 1.24137C13.1838 1.24137 16.449 4.8273 17.4161 6.00042C16.4507 7.17391 13.1921 10.7586 9.4137 10.7586Z"></path>
                    <path d="M9.4137 2.27586C7.36024 2.27586 5.68954 3.94656 5.68954 6.00002C5.68954 8.05348 7.36024 9.72417 9.4137 9.72417C11.4672 9.72417 13.1379 8.05348 13.1379 6.00002C13.1379 3.94656 11.4672 2.27586 9.4137 2.27586ZM9.4137 8.48276C8.04465 8.48276 6.93095 7.36903 6.93095 6.00002C6.93095 4.63101 8.04469 3.51727 9.4137 3.51727C10.7827 3.51727 11.8964 4.63101 11.8964 6.00002C11.8964 7.36903 10.7827 8.48276 9.4137 8.48276Z"></path>
                  </svg>
                </a>
              </div>
            {%- endif -%}

          {%- else -%}
            <div class="t4s-image-product t4s-d-flex t4s-justify-content-center">
              {{ 'hero-apparel-1' | placeholder_svg_tag: 't4s-collection-item-image t4s-oh' }}
            </div>
            <div
              class="t4s-d-flex t4s-justify-content-center t4s-align-items-start t4s-flex-column"
              style="{%- if se_stts.col_dk == '1' -%}width: 90%;{%- elsif se_stts.col_dk == '2' -%}
              width: 70%;{%- elsif se_stts.col_dk == '3' -%}width: 60%;{%- else -%}width: 50%;{%- endif -%}
              padding-left: 10px"
            >
              <div class="t4s-truncate">Product name</div>
              <div>10.00$</div>
            </div>
            {%- if se_stts.show_button_view == true -%}
              <div class="t4s-button-product">
                <a class="t4s-button-product-item">
                  <svg
                    class="t4s-icon-view"
                    width="19"
                    height="12"
                    viewBox="0 0 19 12"
                    fill="currentColor"
                    aria-hidden="true"
                    focusable="false"
                  >
                    <path d="M18.7079 5.6338C18.5397 5.40371 14.5321 0 9.4137 0C4.29527 0 0.287485 5.40371 0.119471 5.63358C0.041836 5.73994 0 5.86821 0 5.99989C0 6.13157 0.041836 6.25984 0.119471 6.3662C0.287485 6.59629 4.29527 12 9.4137 12C14.5321 12 18.5397 6.59625 18.7079 6.36638C18.7857 6.26008 18.8276 6.13179 18.8276 6.00009C18.8276 5.86839 18.7857 5.74011 18.7079 5.6338ZM9.4137 10.7586C5.64343 10.7586 2.37798 7.17207 1.41133 5.99958C2.37673 4.82605 5.63534 1.24137 9.4137 1.24137C13.1838 1.24137 16.449 4.8273 17.4161 6.00042C16.4507 7.17391 13.1921 10.7586 9.4137 10.7586Z"></path>
                    <path d="M9.4137 2.27586C7.36024 2.27586 5.68954 3.94656 5.68954 6.00002C5.68954 8.05348 7.36024 9.72417 9.4137 9.72417C11.4672 9.72417 13.1379 8.05348 13.1379 6.00002C13.1379 3.94656 11.4672 2.27586 9.4137 2.27586ZM9.4137 8.48276C8.04465 8.48276 6.93095 7.36903 6.93095 6.00002C6.93095 4.63101 8.04469 3.51727 9.4137 3.51727C10.7827 3.51727 11.8964 4.63101 11.8964 6.00002C11.8964 7.36903 10.7827 8.48276 9.4137 8.48276Z"></path>
                  </svg>
                </a>
              </div>
            {%- endif -%}
          {%- endif -%}
        </div>
      </div>
    </div>
  {% endfor %}

  {%- if layout_des == '1' -%}
    </div>
  {%- else -%}
    </div>
  {%- endif -%}

  {%- if isLoadmore -%}
    <div class="t4s-prs-footer {{ se_stts.btn_pos }}">
      {%- if paginate.next.is_link -%}
        <div data-wrap-lm class="t4s-pagination-wrapper t4s-w-100">
          <a
            data-load-more
            timeline
            hdt-reveal="slide-in"
            href="{{ paginate.next.url }}"
            class="t4s-pr t4s-loadmore-btn t4s-btn-loading__svg t4s-btn t4s-btn-base"
          >
            <span class="t4s-btn-atc_text">{{ 'collections.pagination.load_more' | t }}</span>
            <div class="t4s-loading__spinner t4s-dn">
              <svg
                width="16"
                height="16"
                aria-hidden="true"
                focusable="false"
                role="presentation"
                class="t4s-svg__spinner"
                viewBox="0 0 66 66"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle class="t4s-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
              </svg>
            </div>
          </a>
        </div>
      {%- endif -%}
    </div>
  {%- endif -%}

  {{- html_layout[1] -}}
</div>

{% # theme-check-enable %}
{% comment %} ! End HTML Code {% endcomment %}

{% comment %} * Schema In Here {% endcomment %}

{%- schema -%}
{
  "name": "Product Video",
  "tag": "section",
  "class": "t4s-section t4s_bk_flickity t4s-section-all t4s_tp_cdt t4s-product-video t4s_tp_istope",
  "settings": [
    {
      "type": "header",
      "content": "1. Heading options"
    },
    {
      "type": "text",
      "id": "top_heading",
      "label": "+ Heading",
      "default": "Product Video"
    },
    {
      "type": "select",
      "id": "design_heading",
      "label": "+ Design heading",
      "default": "1",
      "options": [
        {
          "value": "1",
          "label": "Design 01"
        },
        {
          "value": "2",
          "label": "Design 02"
        },
        {
          "value": "3",
          "label": "Design 03"
        },
        {
          "value": "4",
          "label": "Design 04"
        },
        {
          "value": "5",
          "label": "Design 05"
        },
        {
          "value": "6",
          "label": "Design 06 (with line-awesome)"
        },
        {
          "value": "7",
          "label": "Design 07"
        },
        {
          "value": "8",
          "label": "Design 08"
        },
        {
          "value": "9",
          "label": "Design 09"
        },
        {
          "value": "10",
          "label": "Design 10"
        },
        {
          "value": "11",
          "label": "Design 11"
        },
        {
          "value": "12",
          "label": "Design 12"
        },
        {
          "value": "13",
          "label": "Design 13"
        },
        {
          "value": "14",
          "label": "Design 14"
        },
        {
          "value": "15",
          "label": "Design 15"
        },
        {
          "value": "16",
          "label": "Design 16"
        }
      ]
    },
    {
      "type": "select",
      "id": "heading_align",
      "label": "+ Heading align",
      "default": "t4s-text-center",
      "options": [
        {
          "value": "t4s-text-start",
          "label": "Left"
        },
        {
          "value": "t4s-text-center",
          "label": "Center"
        },
        {
          "value": "t4s-text-end",
          "label": "Right"
        }
      ]
    },
    {
      "type": "text",
      "id": "icon_heading",
      "label": "Enter an icon name",
      "info": "Only used for design 6 [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
      "default": "las la-gem"
    },
    {
      "type": "textarea",
      "id": "top_subheading",
      "label": "+ Subheading"
    },
    {
      "type": "number",
      "id": "tophead_mb",
      "label": "+ Space bottom (px)",
      "info": "The vertical spacing between heading and content",
      "default": 30
    },

    {
      "type": "header",
      "content": "2. General options"
    },
    {
      "type": "checkbox",
      "id": "show_button_view",
      "label": "Show Quickview button",
      "default": true
    },
    {
      "type": "color",
      "id": "color_hover",
      "label": "Quickview button hover color"
    },
    {
      "type": "select",
      "id": "select_aspect_ratio",
      "label": "Aspect ratio",
      "options": [
        {
          "value": "56.25%",
          "label": "16:9"
        },
        {
          "value": "177.78%",
          "label": "9:16"
        },
        {
          "value": "100%",
          "label": "1:1"
        },
        {
          "value": "75%",
          "label": "4:3"
        }
      ]
    },
    {
      "type": "select",
      "id": "col_dk",
      "label": "Items per row",
      "default": "3",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        },
        {
          "value": "3",
          "label": "3"
        },
        {
          "value": "4",
          "label": "4"
        }
      ]
    },
    {
      "type": "select",
      "id": "col_tb",
      "label": "Items per row (Tablet)",
      "default": "2",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        },
        {
          "value": "3",
          "label": "3"
        },
        {
          "value": "4",
          "label": "4"
        }
      ]
    },
    {
      "type": "select",
      "id": "col_mb",
      "label": "Items per row (Mobile)",
      "default": "1",
      "options": [
        {
          "value": "1",
          "label": "1"
        }
      ]
    },

    {
      "type": "select",
      "id": "space_h_item",
      "options": [
        {
          "value": "0",
          "label": "0"
        },
        {
          "value": "2",
          "label": "2px"
        },
        {
          "value": "4",
          "label": "4px"
        },
        {
          "value": "6",
          "label": "6px"
        },
        {
          "value": "8",
          "label": "8px"
        },
        {
          "value": "10",
          "label": "10px"
        },
        {
          "value": "20",
          "label": "20px"
        },
        {
          "value": "30",
          "label": "30px"
        }
      ],
      "label": "Space horizontal items",
      "default": "30"
    },
    {
      "type": "select",
      "id": "space_v_item",
      "options": [
        {
          "value": "0",
          "label": "0"
        },
        {
          "value": "2",
          "label": "2px"
        },
        {
          "value": "4",
          "label": "4px"
        },
        {
          "value": "6",
          "label": "6px"
        },
        {
          "value": "8",
          "label": "8px"
        },
        {
          "value": "10",
          "label": "10px"
        },
        {
          "value": "20",
          "label": "20px"
        },
        {
          "value": "30",
          "label": "30px"
        }
      ],
      "label": "Space vertical items",
      "default": "30"
    },
    {
      "type": "select",
      "id": "space_h_item_mb",
      "options": [
        {
          "value": "0",
          "label": "0"
        },
        {
          "value": "2",
          "label": "2px"
        },
        {
          "value": "4",
          "label": "4px"
        },
        {
          "value": "6",
          "label": "6px"
        },
        {
          "value": "8",
          "label": "8px"
        },
        {
          "value": "10",
          "label": "10px"
        },
        {
          "value": "20",
          "label": "20px"
        },
        {
          "value": "30",
          "label": "30px"
        }
      ],
      "label": "Space horizontal items (Mobile)",
      "default": "10"
    },
    {
      "type": "select",
      "id": "space_v_item_mb",
      "options": [
        {
          "value": "0",
          "label": "0"
        },
        {
          "value": "2",
          "label": "2px"
        },
        {
          "value": "4",
          "label": "4px"
        },
        {
          "value": "6",
          "label": "6px"
        },
        {
          "value": "8",
          "label": "8px"
        },
        {
          "value": "10",
          "label": "10px"
        },
        {
          "value": "20",
          "label": "20px"
        },
        {
          "value": "30",
          "label": "30px"
        }
      ],
      "label": "Space vertical items (Mobile)",
      "default": "10"
    },
    {
      "type": "header",
      "content": "--Box options--"
    },
    {
      "type": "select",
      "id": "layout_des",
      "options": [
        {
          "value": "1",
          "label": "Grid"
        },
        {
          "value": "2",
          "label": "Carousel"
        }
      ],
      "label": "Layout design",
      "default": "2"
    },
    {
      "type": "header",
      "content": "Options for Carousel layout"
    },
    {
      "type": "checkbox",
      "id": "center_slide",
      "label": "Enable center slide",
      "info": "Support maximum 5 columns. Only working when enable loop and should only be used when has the next slide.",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "loop",
      "label": "Enable loop",
      "info": "At the end of cells, wrap-around to the other end for infinite scrolling",
      "default": true
    },
    {
      "type": "range",
      "id": "au_time",
      "min": 0,
      "max": 30,
      "step": 0.5,
      "label": "Autoplay speed in second.",
      "info": "Set is '0' to disable autoplay",
      "unit": "s",
      "default": 0
    },

    {
      "type": "paragraph",
      "content": "—————————————————"
    },
    {
      "type": "paragraph",
      "content": "Prev next button"
    },
    {
      "type": "checkbox",
      "id": "nav_btn",
      "label": "Use prev next button",
      "info": "Works with Carousel Layout",
      "default": false
    },
    {
      "type": "select",
      "id": "btn_vi",
      "label": "Visible",
      "default": "hover",
      "options": [
        {
          "value": "always",
          "label": "Always"
        },
        {
          "value": "hover",
          "label": "Only hover"
        }
      ]
    },
    {
      "type": "select",
      "id": "btn_owl",
      "label": "Button style",
      "default": "default",
      "options": [
        {
          "value": "default",
          "label": "Default"
        },
        {
          "value": "outline",
          "label": "Outline"
        },
        {
          "value": "simple",
          "label": "Simple"
        }
      ]
    },
    {
      "type": "select",
      "id": "btn_shape",
      "label": "Button shape",
      "info": "Not working with button style 'Simple'",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "Default"
        },
        {
          "value": "round",
          "label": "Round"
        },
        {
          "value": "rotate",
          "label": "Rotate"
        }
      ]
    },
    {
      "type": "select",
      "id": "btn_cl",
      "label": "Button color",
      "default": "dark",
      "options": [
        {
          "value": "light",
          "label": "Light"
        },
        {
          "value": "dark",
          "label": "Dark"
        },
        {
          "value": "primary",
          "label": "Primary"
        },
        {
          "value": "custom1",
          "label": "Custom color 1"
        },
        {
          "value": "custom2",
          "label": "Custom color 2"
        }
      ]
    },
    {
      "type": "select",
      "id": "btn_size",
      "label": "Button size",
      "default": "small",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "btn_hidden_mobile",
      "label": "Hidden buttons on mobile ",
      "default": true
    },
    {
      "type": "paragraph",
      "content": "—————————————————"
    },
    {
      "type": "paragraph",
      "content": "Page dots"
    },
    {
      "type": "checkbox",
      "id": "nav_dot",
      "label": "Use page dots",
      "info": "Works with Carousel Layout",
      "default": false
    },
    {
      "type": "range",
      "id": "pt_page_dot",
      "label": "Space dot",
      "min": 0,
      "max": 50,
      "unit": "px",
      "default": 10
    },
    {
      "type": "select",
      "id": "dot_owl",
      "label": "Dots style",
      "default": "default",
      "options": [
        {
          "value": "default",
          "label": "Default"
        },
        {
          "value": "outline",
          "label": "Outline"
        },
        {
          "value": "elessi",
          "label": "Elessi"
        }
      ]
    },
    {
      "type": "select",
      "id": "dots_cl",
      "label": "Dots color",
      "default": "dark",
      "options": [
        {
          "value": "light",
          "label": "Light (Best on dark background)"
        },
        {
          "value": "dark",
          "label": "Dark"
        },
        {
          "value": "primary",
          "label": "Primary"
        },
        {
          "value": "custom1",
          "label": "Custom color 1"
        },
        {
          "value": "custom2",
          "label": "Custom color 2"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "dots_round",
      "label": "Enable dots round",
      "default": true
    },
    {
      "type": "range",
      "id": "dots_space",
      "min": 2,
      "max": 20,
      "step": 1,
      "label": "Dot between horizontal",
      "unit": "px",
      "default": 10
    },
    {
      "type": "checkbox",
      "id": "dots_hidden_mobile",
      "label": "Hidden dots on mobile ",
      "default": false
    },
    {
      "type": "header",
      "content": "3. Design options"
    },

    {
      "type": "select",
      "id": "layout",
      "default": "t4s-container-wrap",
      "label": "Layout",
      "options": [
        { "value": "t4s-se-container", "label": "Container" },
        { "value": "t4s-container-wrap", "label": "Wrapped container" },
        { "value": "t4s-container-fluid", "label": "Full width" }
      ]
    },
    {
      "type": "color",
      "id": "cl_bg",
      "label": "Background",
      "default": "#F5F5F5"
    },
    {
      "type": "color_background",
      "id": "cl_bg_gradient",
      "label": "Background gradient"
    },
    {
      "type": "image_picker",
      "id": "image_bg",
      "label": "Background image"
    },
    {
      "type": "text",
      "id": "mg",
      "label": "Margin",
      "info": "Margin top, margin right, margin bottom, margin left. If you not use to blank",
      "default": ",,50px,",
      "placeholder": ",,50px,"
    },
    {
      "type": "text",
      "id": "pd",
      "label": "Padding",
      "info": "Padding top, padding right, padding bottom, padding left. If you not use to blank",
      "placeholder": "50px,,50px,"
    },
    {
      "type": "header",
      "content": "+ Design Tablet options"
    },
    {
      "type": "text",
      "id": "mg_tb",
      "label": "Margin",
      "placeholder": ",,50px,"
    },
    {
      "type": "text",
      "id": "pd_tb",
      "label": "Padding",
      "placeholder": ",,50px,"
    },

    {
      "type": "header",
      "content": "+ Design Mobile options"
    },

    {
      "type": "text",
      "id": "mg_mb",
      "label": "Margin",
      "default": ",,30px,",
      "placeholder": ",,50px,"
    },
    {
      "type": "text",
      "id": "pd_mb",
      "label": "Padding",
      "placeholder": ",,50px,"
    },
    {
      "type": "header",
      "content": "4. Custom css"
    },

    {
      "id": "use_cus_css",
      "type": "checkbox",
      "label": "Use custom css",
      "default": false,
      "info": "If you want custom style for this section."
    },
    {
      "id": "code_cus_css",
      "type": "textarea",
      "label": "Code custom css",
      "info": "Use selector. SectionID to style css"
    }
  ],
  "blocks": [
    {
      "type": "product",
      "name": "Product",
      "settings": [
        {
          "type": "video",
          "id": "video",
          "label": "Video",
          "info": "Add video."
        },
        {
          "type": "video_url",
          "id": "external_video_url",
          "accept": ["vimeo", "youtube"],
          "label": "Video url",
          "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc"
        },
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Product Video",
      "blocks": [{ "type": "product" }, { "type": "product" }, { "type": "product" }, { "type": "product" }]
    }
  ]
}
{%- endschema -%}

{% comment %} * End Schema {% endcomment %}
