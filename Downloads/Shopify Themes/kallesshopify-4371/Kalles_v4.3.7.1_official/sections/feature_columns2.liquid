<!-- sections/feature_columns2.liquid -->
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'feature-columns.css' | asset_url | stylesheet_tag }}
{%- liquid
    assign image_fix = image_nt | image_tag
    assign sid = section.id
    assign se_stts = section.settings
    assign se_blocks = section.blocks
    assign stt_layout = se_stts.layout
    assign stt_image_bg = se_stts.image_bg
    assign b_effect = se_stts.b_effect
    assign img_effect = se_stts.img_effect
    if se_stts.btn_owl == "outline"
        assign arrow_icon = 1
    else
        assign arrow_icon = 2
    endif
    if stt_layout == 't4s-se-container' 
        assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
    elsif stt_layout == 't4s-container-wrap'
        assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
    else
        assign html_layout = '__' | split: '__'
    endif
  
    assign image_ratio = se_stts.image_ratio

    assign use_button = false
    assign t4s_se_class = 't4s_nt_se_' | append: sid
    if se_stts.use_cus_css and se_stts.code_cus_css != blank
        render 'se_cus_css', code_cus_css: se_stts.code_cus_css, t4s_se_class: t4s_se_class
    endif 
 -%}   
<div class="t4s-section-inner {{ t4s_se_class }} t4s_nt_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank and stt_layout != 't4s-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style', se_stts: se_stts -%} >
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}
        <div class="t4s-container-inner{% if stt_image_bg != blank %} t4s-has-imgbg lazyloadt4s{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
    {%- render 'section_tophead', se_stts: se_stts -%}
        <div class="t4s_{{ image_ratio }} t4s_position_{{ se_stts.image_position }} t4s_{{ se_stts.image_size }} t4s-row t4s-gx-md-{{ se_stts.space_h_item }} t4s-gy-md-{{ se_stts.space_v_item }} t4s-gx-{{ se_stts.space_h_item_mb }} t4s-gy-{{ se_stts.space_v_item_mb }}">
        {%- if se_blocks.size > 0 -%}
            {%- for block in se_blocks -%}
                {%- liquid
                    assign bk_stts = block.settings 
                    assign image = bk_stts.image 
                    assign open_link = bk_stts.open_link 
                    assign button_link = bk_stts.button_link
                    assign button_style = bk_stts.button_style
               -%}
                {%- if button_link != blank -%}
                    <div class="t4s-col-item t4s-col-lg-{{ bk_stts.col_dk }} t4s-col-md-{{ bk_stts.col_tb }} t4s-col-{{ bk_stts.col_mb }} t4s-pos-text-{{ se_stts.pos_text }} t4s-text-{{ se_stts.text_align }}">
                        {%- if bk_stts.enable_image -%}
                            <a href="{{ button_link }}" class="t4s-d-block t4s-eff t4s-eff-{{ b_effect }} t4s-eff-img-{{ img_effect }}" target="{{ open_link }}" timeline hdt-reveal="slide-in">
                                {%- if image != blank -%}
                                    <div class="t4s_ratio t4s-bg-11" style="background: url({{ image | image_url: width: 1 }});--aspect-ratioapt: {{ image.aspect_ratio | default: 1.7777 }};--max-width:{{ image.width }}px" data-cacl-slide>
                                        <img {% if image.presentation.focal_point != '50.0% 50.0%' %} style="object-position: {{ image.presentation.focal_point }}"{% endif %} class="lazyloadt4s t4s-lz--fadeIn t4s-obj-eff" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                                    </div>
                                {%- else -%}
                                    <div class="t4s_ratio" style="--aspect-ratioapt: 1.7777;" data-cacl-slide>
                                        {{ 'image' | placeholder_svg_tag: 't4s-placeholder-svg t4s-obj-eff' }}
                                    </div>
                                {%- endif -%}
                            </a>
                        {%- endif -%}
                        <div class="t4s-feature-columns__content" timeline hdt-reveal="slide-in">
                            {%- if bk_stts.title != blank -%}
                                <h3 class="t4s-feature-columns__title" style="--fs-title:{{ se_stts.fs_title }}px;--fs-title-mb:{{ se_stts.fs_title_mb }}px;"><a href="{{ button_link }}" target="{{ open_link }}">{{ bk_stts.title | escape }}</a></h3>
                            {%- endif -%}
                            {%- if bk_stts.text != blank -%}
                                <div class="t4s-feature-columns__text t4s-rte" style="--fs-text:{{ se_stts.fs_text }}px;--fs-text-mb:{{ se_stts.fs_text_mb }}px;">{{ bk_stts.text }}</div>
                            {%- endif -%}
                            {%- if button_link != blank and bk_stts.button_label != blank -%}
                                {%- assign use_button = true -%}
                                <a class="t4s-btn t4s-btn-base t4s-btn-style-{{ button_style }} t4s-btn-size-{{ bk_stts.btn_size }} t4s-btn-color-{{ bk_stts.btn_cl }} {% if button_style == 'default' or button_style == 'outline' %} t4s-btn-effect-{{ bk_stts.button_effect }}{% endif %}" href="{{ button_link }}" target="{{ bk_stts.open_link }}">{{ bk_stts.button_label }} {%- if bk_stts.btn_icon -%}<svg class="t4s-btn-icon" width="14"><use xlink:href="#t4s-icon-btn"></use></svg>{%- endif -%}</a>
                            {%- endif -%}
                        </div>
                    </div>
                {%- else -%}
                    <div class="t4s-col-item t4s-col-lg-{{ bk_stts.col_dk }} t4s-col-md-{{ bk_stts.col_tb }} t4s-col-{{ bk_stts.col_mb }} t4s-pos-text-{{ se_stts.pos_text }} t4s-text-{{ se_stts.text_align }}">
                        {%- if bk_stts.enable_image -%}
                            <div class="t4s-eff t4s-eff-{{ b_effect }} t4s-eff-img-{{ img_effect }}" timeline hdt-reveal="slide-in">
                                {%- if image != blank -%}
                                    <div class="t4s_ratio t4s-bg-11" style="background: url({{ image | image_url: width: 1 }});--aspect-ratioapt: {{ image.aspect_ratio | default: 1.7777 }};--max-width:{{ image.width }}px" data-cacl-slide>
                                        <img {% if image.presentation.focal_point != '50.0% 50.0%' %} style="object-position: {{ image.presentation.focal_point }}"{% endif %} class="lazyloadt4s t4s-lz--fadeIn t4s-obj-eff" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                                    </div>
                                {%- else -%}
                                    <div class="t4s_ratio" style="--aspect-ratioapt: 1.7777;" data-cacl-slide>
                                        {{ 'image' | placeholder_svg_tag: 't4s-placeholder-svg t4s-obj-eff' }}
                                    </div>
                                {%- endif -%}
                            </div>
                        {%- endif -%}
                        <div class="t4s-feature-columns__content" timeline hdt-reveal="slide-in">
                            {%- if bk_stts.title != blank -%}
                                <h3 class="t4s-feature-columns__title" style="--fs-title:{{ se_stts.fs_title }}px;--fs-title-mb:{{ se_stts.fs_title_mb }}px;">{{ bk_stts.title | escape }}</h3>
                            {%- endif -%}
                            {%- if bk_stts.text != blank -%}
                                <div class="t4s-feature-columns__text t4s-rte" style="--fs-text:{{ se_stts.fs_text }}px;--fs-text-mb:{{ se_stts.fs_text_mb }}px;">{{ bk_stts.text }}</div>
                            {%- endif -%}
                        </div>
                    </div>               
                {%- endif -%}
            {%- endfor -%}
        {%- endif -%}
    </div>
    {{- html_layout[1] -}}
</div>
{%- if use_button -%}
    {{ 'button-style.css' | asset_url | stylesheet_tag }}
    <link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- endif -%}
{%- schema -%}
    {
        "name": "Text columns with image 2",
        "tag": "section",
        "class": "t4s-section t4s-section-all t4s-feature-columns",
        "settings": [
        {
            "type": "header",
            "content": "1. Heading options"
        },
        {
            "type": "select",
            "id": "design_heading",
            "label": "+ Design heading",
            "default": "1",
            "options": [
                {
                    "value": "1",
                    "label": "Design 01"
                },
                {
                    "value": "2",
                    "label": "Design 02"
                },
                {
                    "value": "3",
                    "label": "Design 03"
                },
                {
                    "value": "4",
                    "label": "Design 04"
                },
                {
                    "value": "5",
                    "label": "Design 05"
                },
                {
                    "value": "6",
                    "label": "Design 06 (width line-awesome)"
                },
                {
                    "value": "7",
                    "label": "Design 07"
                },
                {
                    "value": "8",
                    "label": "Design 08"
                },
                {
                    "value": "9",
                    "label": "Design 09"
                },
                {
                    "value": "10",
                    "label": "Design 10"
                },
                {
                    "value": "11",
                    "label": "Design 11"
                },
                {
                    "value": "12",
                    "label": "Design 12"
                },
                {
                    "value": "13",
                    "label": "Design 13"
                },
                {
                    "value": "14",
                    "label": "Design 14"
                },
                {
                    "value": "15",
                    "label": "Design 15"
                },
                {
                  "value": "16",
                  "label": "Design 16"
                }
            ]
        },
        {
            "type": "select",
            "id": "heading_align",
            "label": "+ Heading align",
            "default": "t4s-text-center",
            "options": [
                {
                    "value": "t4s-text-start",
                    "label": "Left"
                },
                {
                    "value": "t4s-text-center",
                    "label": "Center"
                },
                {
                    "value": "t4s-text-end",
                    "label": "Right"
                }
            ]
        },
        {
            "type": "text",
            "id": "top_heading",
            "label": "+ Heading"
        },
        {
            "type": "text",
            "id": "icon_heading",
            "label": "Enter a name icon [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
            "info": "Only used for design 6",
            "default": "las la-gem"
        },
        {
            "type": "textarea",
            "id": "top_subheading",
            "label": "+ Subheading"
        },
        {
            "type": "number",
            "id": "tophead_mb",
            "label": "+ Space bottom (px)",
            "info": "The vertical spacing between heading and content.",
            "default": 30
        },
        {
            "type": "header",
            "content": "2. General options"
        },
        {
            "type": "select",
            "id": "text_align",
            "label": "Text alignment",
            "default": "center",
            "options":[
                {
                    "label":"Left",
                    "value":"start"
                },
                {
                    "label":"Center",
                    "value":"center"
                },
                {
                    "label":"Right",
                    "value":"end"
                }
            ]
        },
        {
            "type": "select",
            "id": "pos_text",
            "label": "Position content",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "alt",
                    "label": "Alternative"
                }
            ]
        },
        {
            "type": "range",
            "id": "fs_title",
            "min": 8,
            "max": 35,
            "step": 0.5,
            "label": "Font size title",
            "unit": "px",
            "default": 22
        },
        {
            "type": "range",
            "id": "fs_text",
            "min": 8,
            "max": 30,
            "step": 0.5,
            "label": "Font size text",
            "unit": "px",
            "default": 14
        },
        {
            "type": "range",
            "id": "fs_title_mb",
            "min": 8,
            "max": 35,
            "step": 0.5,
            "label": "Font size title (Mobile)",
            "unit": "px",
            "default": 22
        },
        {
            "type": "range",
            "id": "fs_text_mb",
            "min": 8,
            "max": 30,
            "step": 0.5,
            "label": "Font size text (Mobile)",
            "unit": "px",
            "default": 12
        },
        {
            "type": "header",
            "content": "+ Setting for image"
        },
        {
            "type": "select",
            "id": "image_ratio",
            "label": "Image ratio",
            "default": "ratio16_9",
            "info": "Aspect Ratio Custom will settings in General panel.",
            "options": [
            {
                "group": "Natural",
                "value": "ratioadapt",
                "label": "Adapt to image"
            },
            {
                "group": "Landscape",
                "value": "ratio2_1",
                "label": "2:1"
            },
            {
                "group": "Landscape",
                "value": "ratio16_9",
                "label": "16:9"
            },
            {
                "group": "Landscape",
                "value": "ratio8_5",
                "label": "8:5"
            },
            {
                "group": "Landscape",
                "value": "ratio3_2",
                "label": "3:2"
            },
            {
                "group": "Landscape",
                "value": "ratio4_3",
                "label": "4:3"
            },
            {
                "group": "Landscape",
                "value": "rationt",
                "label": "Ratio ASOS"
            },
            {
                "group": "Squared",
                "value": "ratio1_1",
                "label": "1:1"
            },
            {
                "group": "Portrait",
                "value": "ratio2_3",
                "label": "2:3"
            },
            {
                "group": "Portrait",
                "value": "ratio1_2",
                "label": "1:2"
            },
            {
                "group": "Custom",
                "value": "ratiocus1",
                "label": "Ratio custom 1"
            },
            {
                "group": "Custom",
                "value": "ratiocus2",
                "label": "Ratio custom 2"
            },
            {
                "group": "Custom",
                "value": "ratio_us3",
                "label": "Ratio custom 3"
            },
            {
                "group": "Custom",
                "value": "ratiocus4",
                "label": "Ratio custom 4"
            }
            ]
        },
        {
            "type": "select",
            "id": "image_size",
            "label": "Image size",
            "default": "cover",
            "info": "This settings apply only if the image ratio is not set to 'Adapt to image'.",
            "options": [
                {
                    "value": "cover",
                    "label": "Full"
                },
                {
                    "value": "contain",
                    "label": "Auto"
                }
            ]
        },
        {
            "type": "select",
            "id": "image_position",
            "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'.And 'Not works when use 'Focal point' on image'",
            "options": [
            {
                "value": "default",
                "label": "Default"
            },
            {
                "value": "1",
                "label": "Left top"
            },
            {
                "value": "2",
                "label": "Left center"
            },
            {
                "value": "3",
                "label": "Left bottom"
            },
            {
                "value": "4",
                "label": "Right top"
            },
            {
                "value": "5",
                "label": "Right center"
            },
            {
                "value": "6",
                "label": "Right bottom"
            },
            {
                "value": "7",
                "label": "Center top"
            },
            {
                "value": "8",
                "label": "Center center"
            },
            {
                "value": "9",
                "label": "Center bottom"
            }
            ],
            "label": "Image position",
            "default": "8"
        },
        {
            "type": "select",
            "id": "img_effect",
            "label": "Image hover effect",
            "info": "Waring: Hovering effect will resize your images",
            "default": "none",
            "options": [
                {
                    "value": "none",
                    "label": "None"
                },
                {
                    "value": "zoom",
                    "label": "Zoom in"
                },
                {
                    "value": "rotate",
                    "label": "Rotate"
                },
                {
                    "value": "translateToTop",
                    "label": "Move to top "
                },
                {
                    "value": "translateToRight",
                    "label": "Move to right"
                },
                {
                    "value": "translateToBottom",
                    "label": "Move to bottom"
                },
                {
                    "value": "translateToLeft",
                    "label": "Move to left"
                },
                {
                    "value": "filter",
                    "label": "Filter"
                },
                {
                    "value": "bounceIn",
                    "label": "BounceIn"
                }
            ]
        },
        {
            "type": "select",
            "id": "b_effect",
            "label": "Effect",
            "default": "none",
            "options": [
                {
                    "value": "none",
                    "label": "None"
                },
                {
                    "value": "border-run",
                    "label": "Border run"
                },
                {
                    "value": "pervasive-circle",
                    "label": "Pervasive circle"
                },
                {
                    "value": "plus-zoom-overlay",
                    "label": "Plus zoom overlay"
                },
                {
                    "value": "dark-overlay",
                    "label": "Dark overlay"
                },
                {
                    "value": "light-overlay",
                    "label": "Light overlay"
                } 
            ]
        },
        {
            "type": "header",
            "content": "--Box options--"
        },
        {
            "type": "select",
            "id": "space_h_item",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                }
            ],
            "label": "Space horizontal between items",
            "default": "30"
        },
        {
            "type": "select",
            "id": "space_v_item",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                }
            ],
            "label": "Space vertical vertical items",
            "default": "30"
        },
        {
            "type": "select",
            "id": "space_h_item_mb",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                }
            ],
            "label": "Space horizontal between items (Mobile)",
            "default": "10"
        },
        {
            "type": "select",
            "id": "space_v_item_mb",
            "options": [
                {
                    "value": "0", 
                    "label": "0"
                },
                {
                    "value": "2", 
                    "label": "2px"
                },
                {
                    "value": "4", 
                    "label": "4px"
                },
                {
                    "value": "6", 
                    "label": "6px"
                },
                {
                    "value": "8", 
                    "label": "8px"
                },
                {
                    "value": "10", 
                    "label": "10px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "30",
                    "label": "30px"
                }
            ],
            "label": "Space vertical vertical items (Mobile)",
            "default": "10"
        },          
        {
            "type": "header",
            "content": "3. Design options"
        },
        {
            "type": "select","id": "layout","default": "t4s-container-wrap","label": "Layout",
            "options": [
                { "value": "t4s-se-container", "label": "Container"},
                { "value": "t4s-container-wrap", "label": "Wrapped container"},
                { "value": "t4s-container-fluid", "label": "Full width"}
            ]
        },
        {
            "type": "color",
            "id": "cl_bg",
            "label": "Background"
        },
        {
            "type": "color_background",
            "id": "cl_bg_gradient",
            "label": "Background gradient"
        },
        {
            "type": "image_picker",
            "id": "image_bg",
            "label": "Background Image"
        },
        {
            "type": "text",
            "id": "mg",
            "label": "Margin",
            "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
            "default": ",,50px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd",
            "label": "Padding",
            "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
            "placeholder": "50px,,50px,"
        },
        {
            "type": "header",
            "content": "+ Design Tablet Options"
        },
        {
            "type": "text",
            "id": "mg_tb",
            "label": "Margin",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_tb",
            "label": "Padding",
            "placeholder": ",,50px,"
        }, 
        {
            "type": "header",
            "content": "+ Design Mobile Options"
        },
        {
            "type": "text",
            "id": "mg_mb",
            "label": "Margin",
            "default": ",,30px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_mb",
            "label": "Padding",
            "placeholder": ",,50px,"
        },
        {
            "type": "header",
            "content": "4. Custom css"
        },
        {
            "id": "use_cus_css",
            "type": "checkbox",
            "label": "Use custom css",
            "default":false,
            "info": "If you want custom style for this section."
        },
        { 
            "id": "code_cus_css",
            "type": "textarea",
            "label": "Code custom css",
            "info": "Use selector .SectionID to style css"
            
        }
    ],
    "blocks": [
        {
            "type": "text_block",
            "name": "Column",
            "settings": [
                {
                    "type": "checkbox",
                    "id": "enable_image",
                    "label": "Enable image",
                    "default": true
                },
                { 
                    "type": "image_picker",
                    "id": "image",
                    "label": "Image"
                },
                {
                    "type": "text",
                    "id": "title",
                    "label": "Heading",
                    "default": "Add a title or tagline"
                },
                {
                    "type": "richtext",
                    "id": "text",
                    "label": "Text",
                    "default": "<p>Share blog posts, products, or promotions with your customers. Use this text to describe products, share details on availability and style, or as a space to display recent reviews or FAQs.</p>"
                },
                {
                    "type": "text",
                    "id": "button_label",
                    "label": "Button label",
                    "info":"If set blank will not show"
                },
                {
                    "type": "url",
                    "id": "button_link",
                    "label": "Link to",
                    "info":"If set blank will not show"
                },
                {
                    "type": "select",
                    "id": "open_link",
                    "label": "Open link in",
                    "default": "_blank",
                    "info":"Only working when has a link",
                    "options": [
                        {
                            "value": "_self",
                            "label": "Current window"
                        },
                        {
                            "value": "_blank",
                            "label": "New window"
                        }
                    ]
                },
                {
                    "type":"checkbox",
                    "id":"btn_icon",
                    "label":"Enable button icon",
                    "default":false
                },
                {
                    "type": "select",
                    "id": "button_style",
                    "label": "Button style",
                    "options": [
                        {
                            "label": "Default",
                            "value": "default"
                        },
                        {
                            "label": "Outline",
                            "value": "outline"
                        },
                        {
                            "label": "Bordered bottom",
                            "value": "bordered"
                        },
                        {
                            "label": "Link",
                            "value": "link"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "btn_size",
                    "label": "Button size",
                    "default":"large",
                    "options": [
                        {
                            "label": "Extra small",
                            "value": "small"
                        },
                        {
                            "label": "Small",
                            "value": "extra-small"
                        },
                        {
                            "label": "Medium",
                            "value": "medium"
                        },
                        {
                            "label": "Large",
                            "value": "extra-medium"
                        },
                        {
                            "label": "Extra large",
                            "value": "large"
                        },
                        {
                            "label": "Extra extra large",
                            "value": "extra-large"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "btn_cl",
                    "label": "Button color",
                    "default": "dark",
                    "options": [
                        {
                            "value": "light",
                            "label": "Light"
                        },
                        {
                            "value": "dark",
                            "label": "Dark"
                        },
                        {
                            "value": "primary",
                            "label": "Primary"
                        },
                        {
                            "value": "custom1",
                            "label": "Custom color 1"
                        },
                        {
                            "value": "custom2",
                            "label": "Custom color 2"
                        }
                    ]
                },
                {
                    "type":"select",
                    "id":"button_effect",
                    "label":"Button hover effect",
                    "default":"fade",
                    "info":"Only working button style default, outline",
                    "options":[
                        {
                            "label":"Default",
                            "value":"default"
                        },
                        {
                            "label":"Fade",
                            "value":"fade"
                        },
                        {
                            "label":"Rectangle out",
                            "value":"rectangle-out"
                        },
                        {
                            "label":"Sweep to right",
                            "value":"sweep-to-right"
                        },
                        {
                            "label":"Sweep to left",
                            "value":"sweep-to-left"
                        },
                        {
                            "label":"Sweep to bottom",
                            "value":"sweep-to-bottom"
                        },
                        {
                            "label":"Sweep to top",
                            "value":"sweep-to-top"
                        },
                        {
                            "label":"Shutter out horizontal",
                            "value":"shutter-out-horizontal"
                        },
                        {
                            "label":"Outline",
                            "value":"outline"
                        },
                        {
                            "label":"Shadow",
                            "value":"shadow"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "col_dk",
                    "label": "Block width (desktop)",
                    "default": "4",
                    "options": [
                        {
                            "value": "12",
                            "label": "100%"
                        },
                        {
                            "value": "8",
                            "label": "66,6%"
                        },
                        {
                            "value": "7",
                            "label": "58,3%"
                        },
                        {
                            "value": "6",
                            "label": "50%"
                        },
                        {
                            "value": "5",
                            "label": "41,6%"
                        },
                        {
                            "value": "4",
                            "label": "33,3%"
                        },
                        {
                            "value": "3",
                            "label": "25%"
                        },
                        {
                            "value": "15",
                            "label": "20%"
                        },
                        {
                            "value": "2",
                            "label": "16,6%"
                        }              
                    ]
                },
                {
                    "type": "select",
                    "id": "col_tb",
                    "label": "Block width (tablet)",
                    "default": "4",
                    "options": [
                        {
                            "value": "12",
                            "label": "100%"
                        },
                        {
                            "value": "8",
                            "label": "66,6%"
                        },
                        {
                            "value": "7",
                            "label": "58,3%"
                        },
                        {
                            "value": "6",
                            "label": "50%"
                        },
                        {
                            "value": "5",
                            "label": "41,6%"
                        },
                        {
                            "value": "4",
                            "label": "33,3%"
                        },
                        {
                            "value": "3",
                            "label": "25%"
                        },
                        {
                            "value": "15",
                            "label": "20%"
                        },
                        {
                            "value": "2",
                            "label": "16,6%"
                        }   
                    ]
                },
                {
                    "type": "select",
                    "id": "col_mb",
                    "label": "Block width (mobile)",
                    "default": "12",
                    "options": [
                        {
                            "value": "12",
                            "label": "100%"
                        },
                        {
                            "value": "6",
                            "label": "50%"
                        }
                    ]
                }
            ]
        }
    ],
    "presets": [
        {
            "name": "Text columns with image 2",
            "category": "homepage",
            "blocks": [
                {"type": "text_block"},
                {"type": "text_block"},
                {"type": "text_block"}
            ]
        }
    ]
}
{% endschema %}

{%- javascript -%}
{%- endjavascript -%}