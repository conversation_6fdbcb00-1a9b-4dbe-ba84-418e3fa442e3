{{ 'lookbook.css' | asset_url | stylesheet_tag }} 
{{ 'section.css' | asset_url | stylesheet_tag }}
{{ 'collection-products.css' | asset_url | stylesheet_tag }}
<link href="{{ 'base_drop.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid 
  assign image_fix = image_nt | image_tag
  assign rtl = settings.use_rtl
  if rtl 
    assign ltr = false 
  else 
    assign ltr = true  
  endif
  assign sid = section.id
  assign section_blocks = section.blocks
  assign se_stts = section.settings

  assign stt_layout = se_stts.layout
  if stt_layout == 't4s-se-container' 
    assign html_layout = '<div class="t4s-container">__</div></div>' | split: '__'
  elsif stt_layout == 't4s-container-wrap'
    assign html_layout = '<div class="t4s-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign arr_img = section_blocks | where: "type", "img" 
  assign routes_local = routes.cart_url | split: 'cart' | first 
  assign index = 1
  assign offset = 0 
  assign root_url = routes.root_url
  if root_url != '/'
    assign root_url = root_url | append: '/'
  endif
 -%}

<div class="t4s-section-inner t4s_nt_se_{{ sid }} t4s_se_{{ sid }} {{ stt_layout }}" {% render 'section_style', se_stts: se_stts %}>
    {{- html_layout[0] -}}
    {%- if stt_layout == 't4s-se-container' -%}<div class="t4s-container-inner">{%- endif -%}
     {%- render 'section_tophead', se_stts: se_stts -%}
     
    <div class="isotopet4s t4s_box_pr_masonry t4s-products has_pr_packery_item t4s-row t4s-gx-md-{{ se_stts.space }} t4s-gy-md-{{ se_stts.space }} t4s-gx-{{ se_stts.space_mb }} t4s-gy-{{ se_stts.space_mb }} t4s_{{ se_stts.image_size }} t4s_{{ se_stts.image_ratio }} t4s_position_{{ se_stts.image_position }}" data-isotopet4s-js='{ "itemSelector": ".t4s-pin-packery", "layoutMode": "masonry" }'>

      {%- if arr_img.size > 0 -%}
          {%- for bl in arr_img -%}{% assign offset = offset | plus: 1 %}{% assign image = bl.settings.image -%} 

            <div class="t4s-pin-packery t4s-pr t4s-lookbook-wrapper t4s-col-item t4s-col-lg-{{ bl.settings.col_pr }} t4s-col-md-{{ bl.settings.col_pr_tb }} t4s-col-{{ bl.settings.col_pr_mb }}">
              <div class="t4s-lookbook-img t4s-pr t4s-oh t4s_{{ se_stts.image_size }} t4s_{{ se_stts.image_ratio }} t4s_position_{{ se_stts.image_position }}" timeline hdt-reveal="slide-in">
              {%- if image -%}{%- assign ratio = image.aspect_ratio -%}
               <div class="t4s-lookbook-img-wrap t4s_ratio" style="--aspect-ratioapt: {{ ratio | default: 1.7777 }}">
                  <img {% if image.presentation.focal_point != '50.0% 50.0%' %}style="object-position: {{ image.presentation.focal_point }}"{% endif %} class="lazyloadt4s t4s-lz--fadeIn t4s-obj-eff t4s-img-as-bg pin__image" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                  <span class="lazyloadt4s-loader is-bg-img" style="background: url({{ image | image_url: width: 1 }});"></span>  
                </div>
              {%- else -%}
                {%- capture current -%}{% cycle 1, 2 %}{%- endcapture -%}
                {{ 'lifestyle-' | append: current | placeholder_svg_tag: 't4s-placeholder-svg t4s-svg-bg1' }}  
              {%- endif -%}
              {%- if section_blocks.size > 0 -%}
                {%- for block in section_blocks offset: index -%}{%- assign index = index | plus: 1 -%}{%- assign bk_stts = block.settings -%}
                  {%- case block.type -%}
                    {%- when 'img' -%}{% break %}
                    {%- when 'pr' -%}{%- if bk_stts.product == blank -%}{%- continue -%}{%- endif -%}
                      <span data-bid="t4s_{{ se_id }}{{ block.id }}" data-pin-popup data-position="{{ bk_stts.pos_popup }}" data-is-pr data-href="{{ bk_stts.product.url }}" data-sid="render-pr_lb{{ se_stts.pr_pin_des }}" class="t4s-lookbook-pin is-type__pr pin__size--{{ bk_stts.pos_size }} pin_ic_{{ bk_stts.type }} pin__type_{{ block.id }}" {{ block.shopify_attributes }} {% render 'pin_position', bk_stts: bk_stts %}>
                        <span class="t4s-zoompin"></span>
                        <span class="t4s-pin-tt">
                          {%- if bk_stts.type != '3' -%}<i class="t4s-nav-link-icon"></i>
                          {%- else -%}<span class="t4s-truncate">{{ bk_stts.shorttxt }}</span>
                          {%- endif -%}
                        </span>
                      </span>
                    {%- when 'txt' -%}
                      <span data-bid="t4s_{{ se_id }}{{ block.id }}" data-pin-popup data-position="{{ bk_stts.pos_popup }}" class="t4s-lookbook-pin is-type__text pin__size--{{ bk_stts.pos_size }} pin_ic_{{ bk_stts.type }} pin__type_{{ block.id }}" {{ block.shopify_attributes }} {% render 'pin_position', bk_stts: bk_stts %}>
                        <span class="t4s-zoompin"></span>
                        <span class="t4s-pin-tt">
                          {%- if bk_stts.type != '3' -%}<i class="t4s-nav-link-icon"></i>
                          {%- else -%}<span class="t4s-truncate">{{ bk_stts.shorttxt }}</span>
                          {%- endif -%}
                        </span>
                      </span>
                      <template id="temt4s_{{ se_id }}{{ block.id }}">
                        <div data-pin-wrapper id="" class="t4s-lb__wrapper t4s-lb-txt-wrapper">
                           <div class="t4s-lb-arrow"></div>
                           <div class="t4s-lb__header">
                             <span class="t4s-lb__title">{{ 'sections.lookbook.title.text' | t }}</span>
                             <button data-pin-close aria-label="{{ 'general.aria.close' | t }}"><svg role="presentation" class="t4s-iconsvg-close" viewBox="0 0 16 14" width="16"><path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path></svg></button>
                           </div>
                           <div class="t4s-lb__content">
                              <div class="t4s-lb-title">{{ bk_stts.heading }}</div>
                              <div class="t4s-lb-content t4s-rte">{{ bk_stts.text }}</div>
                           </div>
                        </div>
                      </template>
                    {%- else -%}
                      <a href="{{ bk_stts.url }}" target="{{ bk_stts.open_link }}" class="t4s-lookbook-pin is-type__ink pin__size--{{ bk_stts.pos_size }} pin_ic_{{ bk_stts.type }} pin__type_{{ block.id }}" {% render 'pin_position', bk_stts: bk_stts %} >
                        <span class="t4s-zoompin"></span>
                         <span class="t4s-pin-tt">
                          {%- if bk_stts.type != '3' -%}<i class="t4s-nav-link-icon"></i>
                          {%- else -%}<span class="t4s-truncate">{{ bk_stts.shorttxt }}</span>
                          {%- endif -%}
                        </span>
                      </a>
                  {%- endcase -%}
                {%- endfor -%}
              {%- endif -%}    
              </div>  
            </div> 
          {%- endfor -%}
        {%- else -%}
        <div class="t4s-col-12 t4s-text-center">{%- render 'no-blocks' -%}</div> 
        {%- endif -%}
    </div>
    {{- html_layout[1] -}}
  </div>
{%- schema -%}
  {
    "name": "Lookbook Packery",
    "class": "t4s-section t4s-section-all t4s_tp_lb t4s_tp_istope",
    "max_blocks": 20,
    "settings": [
      {
                "type": "header",
                "content": "1. Heading options"
            },
            {
                "type": "select",
                "id": "design_heading",
                "label": "+ Design heading",
                "default": "1",
                "options": [
                    {
                        "value": "1",
                        "label": "Design 01"
                    },
                    {
                        "value": "2",
                        "label": "Design 02"
                    },
                    {
                        "value": "3",
                        "label": "Design 03"
                    },
                    {
                        "value": "4",
                        "label": "Design 04"
                    },
                    {
                        "value": "5",
                        "label": "Design 05"
                    },
                    {
                        "value": "6",
                        "label": "Design 06 (width line-awesome)"
                    },
                    {
                        "value": "7",
                        "label": "Design 07"
                    },
                    {
                        "value": "8",
                        "label": "Design 08"
                    },
                    {
                        "value": "9",
                        "label": "Design 09"
                    },
                    {
                        "value": "10",
                        "label": "Design 10"
                    },
                    {
                        "value": "11",
                        "label": "Design 11"
                    },
                    {
                        "value": "12",
                        "label": "Design 12"
                    },
                    {
                        "value": "13",
                        "label": "Design 13"
                    },
                    {
                        "value": "14",
                        "label": "Design 14"
                    },
                    {
                      "value": "15",
                      "label": "Design 15"
                    },
                    {
                      "value": "16",
                      "label": "Design 16"
                    }
                ]
            },
            {
                "type": "select",
                "id": "heading_align",
                "label": "+ Heading align",
                "default": "t4s-text-center",
                "options": [
                    {
                        "value": "t4s-text-start",
                        "label": "Left"
                    },
                    {
                        "value": "t4s-text-center",
                        "label": "Center"
                    },
                    {
                        "value": "t4s-text-end",
                        "label": "Right"
                    }
                ]
            },
            {
                "type": "text",
                "id": "top_heading",
                "label": "+ Heading"
            },
            {
                "type": "text",
                "id": "icon_heading",
                "label": "Enter a name icon [LineAwesome](https://kalles.the4.co/font-lineawesome/)",
                "info": "Only used for design 6",
                "default": "las la-gem"
            },
            {
                "type": "textarea",
                "id": "top_subheading",
                "label": "+ Subheading"
            },
            {
                "type": "number",
                "id": "tophead_mb",
                "label": "+ Space bottom (px)",
                "info": "The vertical spacing between heading and content.",
                "default": 30
            },
      {
        "type": "header",
        "content": "2. General Settings"
      },
      {
       "type": "select",
       "id": "space",
       "options": [
         {
           "value": "30",
           "label": "30"
         },
         {
           "value": "25",
           "label": "25"
         },
         {
           "value": "20",
           "label": "20"
         },
         {
           "value": "15",
           "label": "15"
         },
         {
           "value": "10",
           "label": "10"
         },
         {
           "value": "6",
           "label": "6"
         },
         {
           "value": "3",
           "label": "3"
         },
         {
           "value": "2",
           "label": "2"
         },
         {
           "value": "0",
           "label": "0"
         }
       ],
       "label": "Spaces between photos ", 
       "default": "20" 
      },
      {
       "type": "select",
       "id": "space_mb",
       "options": [
         {
           "value": "30",
           "label": "30"
         },
         {
           "value": "25",
           "label": "25"
         },
         {
           "value": "20",
           "label": "20"
         },
         {
           "value": "15",
           "label": "15"
         },
         {
           "value": "10",
           "label": "10"
         },
         {
           "value": "6",
           "label": "6"
         },
         {
           "value": "3",
           "label": "3"
         },
         {
           "value": "2",
           "label": "2"
         },
         {
           "value": "0",
           "label": "0"
         }
       ],
       "label": "spaces between photos mobile",
       "default": "20" 
      },
      {
        "type": "header",
        "content": "+ Options image products"
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Image ratio",
        "default": "ratioadapt",
        "info": "Aspect ratio custom will settings in general panel",
        "options": [
          {
            "group": "Natural",
            "value": "ratioadapt",
            "label": "Adapt to image"
          },
          {
            "group": "Landscape",
            "value": "ratio2_1",
            "label": "2:1"
          },
          {
            "group": "Landscape",
            "value": "ratio16_9",
            "label": "16:9"
          },
          {
            "group": "Landscape",
            "value": "ratio8_5",
            "label": "8:5"
          },
          {
            "group": "Landscape",
            "value": "ratio3_2",
            "label": "3:2"
          },
          {
            "group": "Landscape",
            "value": "ratio4_3",
            "label": "4:3"
          },
          {
            "group": "Landscape",
            "value": "rationt",
            "label": "Ratio ASOS"
          },
          {
            "group": "Squared",
            "value": "ratio1_1",
            "label": "1:1"
          },
          {
            "group": "Portrait",
            "value": "ratio2_3",
            "label": "2:3"
          },
          {
            "group": "Portrait",
            "value": "ratio1_2",
            "label": "1:2"
          },
          {
            "group": "Custom",
            "value": "ratiocus1",
            "label": "Ratio custom 1"
          },
          {
            "group": "Custom",
            "value": "ratiocus2",
            "label": "Ratio custom 2"
          },
          {
            "group": "Custom",
            "value": "ratio_us3",
            "label": "Ratio custom 3"
          },
          {
            "group": "Custom",
            "value": "ratiocus4",
            "label": "Ratio custom 4"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_size",
        "label": "Image size",
        "default": "cover",
        "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "cover",
            "label": "Full"
          },
          {
            "value": "contain",
            "label": "Auto"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "info": "The first value is the horizontal position and the second value is the vertical. These settings apply only if the image ratio is not set to 'Adapt to image', it also does not work when using 'Focal point' on the image.",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "1",
            "label": "Left top"
          },
          {
            "value": "2",
            "label": "Left center"
          },
          {
            "value": "3",
            "label": "Left bottom"
          },
          {
            "value": "4",
            "label": "Right top"
          },
          {
            "value": "5",
            "label": "Right center"
          },
          {
            "value": "6",
            "label": "Right bottom"
          },
          {
            "value": "7",
            "label": "Center top"
          },
          {
            "value": "8",
            "label": "Center center"
          },
          {
            "value": "9",
            "label": "Center bottom"
          }
        ],
        "label": "Image position",
        "default": "8"
      },
      {
        "type": "header","content": "3. Pin product design"
      },
      {
        "type": "select",
        "id": "pr_pin_des",
        "options": [
            {
                "value": "1",
                "label": "Pin product design 1"
            },
            {
                "value": "2",
                "label": "Pin product design 2"
            },
            {
                "value": "3",
                "label": "Pin product design 3"
            },
            {
                "value": "4",
                "label": "Pin product design 4"
            },
            {
                "value": "5",
                "label": "Pin product design 5"
            },
            {
                "value": "6",
                "label": "Pin product design 6"
            }
        ],
        "label": "Select design",
          "default": "1"
      },
      {
        "type": "header",
        "content": "4. Design Settings"
      },
      {
        "type": "select",
        "id": "layout",
        "default": "t4s-se-container",
        "label": "Section Layout",
        "options": [
            { "value": "t4s-se-container", "label": "Container"},
            { "value": "t4s-container-wrap", "label": "Wrapped Container"},
            { "value": "t4s-container-fluid", "label": "Full width"}
        ]
      },
      {
          "type": "color",
          "id": "cl_bg",
          "label": "Background"
      },
      {
          "type": "color_background",
          "id": "cl_bg_gradient",
          "label": "Background gradient"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
        "type": "text",
        "id": "mg_tb",
        "label": "Margin",
        "placeholder": ",,50px,"
      },
      {
        "type": "text",
        "id": "pd_tb",
        "label": "Padding",
        "placeholder": ",,50px,"
      },
      {
          "type": "header",
          "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      }
    ],
    "blocks": [
      {
        "type": "img",
        "name": "Image (parent)",
        "limit": 10,
        "settings": [
          {
            "type": "image_picker","id": "image","label": "Choose image"
          },
          {
            "type": "select",
            "id": "col_pr",
            "label": "Width",
            "default": "3",
            "options": [
              {
                "value": "12",
                "label": "100%"
              },
              {
                "value": "9",
                "label": "75%"
              },
              {
                "value": "8",
                "label": "66%"
              },
              {
                "value": "6",
                "label": "50%"
              },
              {
                "value": "4",
                "label": "33%"
              },
              {
                "value": "3",
                "label": "25%"
              },
              {
                "value": "15",
                "label": "20%"
              },
              {
                "value": "2",
                "label": "16%"
              }
            ]
          },
          {
            "type": "select",
            "id": "col_pr_tb",
            "label": "Width (Tablet)",
            "default": "3",
            "options": [
              {
                "value": "12",
                "label": "100%"
              },
              {
                "value": "9",
                "label": "75%"
              },
              {
                "value": "8",
                "label": "66%"
              },
              {
                "value": "6",
                "label": "50%"
              },
              {
                "value": "4",
                "label": "33%"
              },
              {
                "value": "3",
                "label": "25%"
              }
            ]
          },
          {
            "type": "select",
            "id": "col_pr_mb",
            "label": "Width (Mobile)",
            "default": "6",
            "options": [
              {
                "value": "12",
                "label": "100%"
              },
              {
                "value": "6",
                "label": "50%"
              }
            ]
          }
        ]
      },
      {
        "type": "pr",
        "name": "Product",
        "settings": [
          {
            "type": "header","content": "+ Pin Settings"
          },
          {
            "type":"range","id":"pos_t","min":0,"max":100,"step":1,"unit":"%","label":"Position Top","default":50
          },
          {
            "type":"range","id":"pos_l","min":0,"max":100,"step":1,"unit":"%","label":"Position Left","default":50
          },
          {
             "type": "select","id": "type","label": "Title type",
             "options": [
                { "value": "1", "label": "icon 1"},
                { "value": "2", "label": "icon 2"},
                { "value": "3", "label": "Short Text"}
             ]
          },
         {
          "type":"text","id":"shorttxt","label":"Short Text","default":"$59"
          },
          {
           "type": "select","id": "pos_pin","label": "Position pin wrapper","default": "top",
           "options": [
              { "value": "top", "label": "Top"},
              { "value": "bottom", "label": "Bottom"},
              { "value": "left", "label": "Left"},
              { "value": "right", "label": "Right"}
           ]
          },
          {
           "type": "select","id": "pos_size","label": "Pin size","default": "medium",
           "options": [
              { "value": "small", "label": "Small"},
              { "value": "medium", "label": "Medium"},
              { "value": "exmedium", "label": "Large"},
              { "value": "large", "label": "Extra large"}
           ]
          },
          
          {
           "type": "color","id": "bg_cl","label": "Background color","default": "#65affa"
          },
          {
            "type": "color","id": "cl_text","label": "Icon/Text color","default": "#fff" 
          },
          {
            "type": "header","content": "+ Product Settings"
          },
          {
             "type": "product","id": "product","label": "Choose product"
          }
        ]
      },
      {
        "type": "txt",
        "name": "Text",
        "settings": [
          {
            "type": "header","content": "+ Pin Settings"
          },
          {
            "type":"range","id":"pos_t","min":0,"max":100,"step":1,"unit":"%","label":"Position Top","default":50
          },
          {
            "type":"range","id":"pos_l","min":0,"max":100,"step":1,"unit":"%","label":"Position Left","default":50
          },
          {
             "type": "select","id": "type","label": "Title type",
             "options": [
                { "value": "1", "label": "icon 1"},
                { "value": "2", "label": "icon 2"},
                { "value": "3", "label": "Short Text"}
             ]
          },
         {
          "type":"text","id":"shorttxt","label":"Short Text","default":"$59"
          },
          {
           "type": "select","id": "pos_pin","label": "Position pin wrapper","default": "top",
           "options": [
              { "value": "top", "label": "Top"},
              { "value": "bottom", "label": "Bottom"},
              { "value": "left", "label": "Left"},
              { "value": "right", "label": "Right"}
           ]
          },
          {
           "type": "select","id": "pos_size","label": "Pin size","default": "medium",
           "options": [
              { "value": "small", "label": "Small"},
              { "value": "medium", "label": "Medium"},
              { "value": "exmedium", "label": "Large"},
              { "value": "large", "label": "Extra large"}
           ]
          },
        
          {
           "type": "color","id": "bg_cl","label": "Background color","default": "#65affa"
          },
          {
            "type": "color","id": "cl_text","label": "Icon/Text color","default": "#fff" 
          },
          {
            "type": "header","content": "+ Content Settings"
          },
         {
          "type":"text","id":"heading","label":"Heading","default":"01 - Water Resistance"
          },
         {
          "type":"richtext","id":"text","label":"Content","default":"<p>With groundbreaking water resistant capabilities, The Mission has the highest waterproof rating of any smartwatch on the market.</p>"
          }
         ]
      },
      {
        "type": "url",
        "name": "Link",
        "settings": [
          {
            "type": "header","content": "+ Pin Settings"
          },
          {
            "type":"range","id":"pos_t","min":0,"max":100,"step":1,"unit":"%","label":"Position Top","default":50
          },
          {
            "type":"range","id":"pos_l","min":0,"max":100,"step":1,"unit":"%","label":"Position Left","default":50
          },
          {
             "type": "select","id": "type","label": "Title type",
             "options": [
                { "value": "1", "label": "icon 1"},
                { "value": "2", "label": "icon 2"},
                { "value": "3", "label": "Short Text"}
             ]
          },
         {
          "type":"text","id":"shorttxt","label":"Short Text","default":"$59"
          },
          {
           "type": "select","id": "pos_pin","label": "Position pin wrapper","default": "top",
           "options": [
              { "value": "top", "label": "Top"},
              { "value": "bottom", "label": "Bottom"},
              { "value": "left", "label": "Left"},
              { "value": "right", "label": "Right"}
           ]
          },
          {
           "type": "select","id": "pos_size","label": "Pin size","default": "medium",
           "options": [
              { "value": "small", "label": "Small"},
              { "value": "medium", "label": "Medium"},
              { "value": "exmedium", "label": "Large"},
              { "value": "large", "label": "Extra large"}
           ]
          },
          
          {
           "type": "color","id": "bg_cl","label": "Background color","default": "#65affa"
          },
          {
            "type": "color","id": "cl_text","label": "Icon/Text color","default": "#fff" 
          },
          {
            "type": "header","content": "+ Link Settings"
          },
         {
          "type":"url","id":"url","label":"Link"
          },
          {
            "type": "select",
            "id": "open_link",
            "options": [
              {
                "value": "_self",
                "label": "Current window (_self)"
              },
             {
                "value": "_blank",
                "label": "New window (_blank)"
              }
            ],
            "label": "Open link in",
            "default": "_blank"
          }
         ]
      }
    ],
    "presets": [
      {
        "name": "Lookbook Packery",
        "category": "VIII. Lookbook",
        "blocks": [
          {
            "type": "img",
            "settings": {
              "col_pr": "3",
              "col_pr_tb": "3",
              "col_pr_mb": "6"
            }
          },
          {
            "type": "img",
            "settings": {
              "col_pr": "6",
              "col_pr_tb": "6",
              "col_pr_mb": "12"
            }
          },
          {
            "type": "img",
            "settings": {
              "col_pr": "3",
              "col_pr_tb": "3",
              "col_pr_mb": "6"
            }
          },
          {
            "type": "img",
            "settings": {
              "col_pr": "3",
              "col_pr_tb": "3",
              "col_pr_mb": "6"
            }
          },
          {
            "type": "img",
            "settings": {
              "col_pr": "3",
              "col_pr_tb": "3",
              "col_pr_mb": "6"
            }
          }
        ]
      }
    ]
  }
{% endschema %}
