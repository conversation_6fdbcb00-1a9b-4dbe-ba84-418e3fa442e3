/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "top": {
      "type": "top-collections",
      "settings": {
        "show_top_collections": true,
        "source": "3",
        "menu": "footer",
        "box_border": "top",
        "content_align": "center",
        "hidden_mobile": false,
        "space_item": 30,
        "fontf": "inherit",
        "text_cl": "#222222",
        "text_cl_hover": "#56cfe1",
        "text_fs": 14,
        "text_lh": 45,
        "text_fw": 400,
        "text_ls": 0,
        "font_italic": false,
        "nav_btn": true,
        "layout": "t4s-container-fluid",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "",
        "pd": "",
        "mg_mb": "",
        "pd_mb": ""
      }
    },
    "heading_template": {
      "type": "heading-template",
      "blocks": {
        "3b0cc506-49ea-4851-b5a6-ff7c2153b0e6": {
          "type": "1",
          "settings": {
            "heading": "",
            "fontf": "inherit",
            "text_cl": "#ffffff",
            "text_fs": 20,
            "text_lh": 20,
            "text_fw": 500,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 10,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 20,
            "text_ls_mb": 0,
            "text_mgb_mobile": 5
          }
        },
        "a0013fd9-28c1-4d81-889c-f91fdd2e4ab7": {
          "type": "4",
          "settings": {
            "fontf": "inherit",
            "text_cl": "#ffffff",
            "text_fs": 16,
            "text_lh": 0,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 0,
            "hidden_mobile": false,
            "text_fs_mb": 16,
            "text_lh_mb": 0,
            "text_ls_mb": 0,
            "text_mgb_mobile": 0
          }
        }
      },
      "block_order": [
        "3b0cc506-49ea-4851-b5a6-ff7c2153b0e6",
        "a0013fd9-28c1-4d81-889c-f91fdd2e4ab7"
      ],
      "settings": {
        "image": "shopify:\/\/shop_images\/shop-banner.jpg",
        "parallax": false,
        "color": "#000000",
        "overlay": 20,
        "padding": 55,
        "paddingmb": 50,
        "mg_b": 50,
        "mg_bmb": 50,
        "content_align": "t4s-text-center",
        "bg_pos": "center center",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "1650341675018b864a": {
      "type": "collections-list",
      "blocks": {
        "1650341675afa01110-0": {
          "type": "collection_item",
          "settings": {
            "collection": "default",
            "image": "shopify:\/\/shop_images\/cl10_400x_63592bfa-0886-46bc-b3e4-d3453fdf1d04.jpg",
            "icon": "las la-gem",
            "collection_title": "New Arrival",
            "collection_link": "https:\/\/demo-kalles-4-1.myshopify.com\/collections\/default\/?view=sub"
          }
        },
        "1650341675afa01110-1": {
          "type": "collection_item",
          "settings": {
            "collection": "women",
            "image": "shopify:\/\/shop_images\/cl11_400x_3bed1d7b-4322-42a4-bd9a-53f51eb1b092.jpg",
            "icon": "las la-gem",
            "collection_title": "Bottom",
            "collection_link": "https:\/\/demo-kalles-4-1.myshopify.com\/collections\/women\/?view=sub"
          }
        },
        "1650341675afa01110-2": {
          "type": "collection_item",
          "settings": {
            "collection": "dress",
            "image": "shopify:\/\/shop_images\/cl12_400x_355a8f09-7c0b-4283-917b-1f3848912ea5.jpg",
            "icon": "las la-gem",
            "collection_title": "Dress",
            "collection_link": "https:\/\/demo-kalles-4-1.myshopify.com\/collections\/dress\/?view=sub"
          }
        },
        "1650341675afa01110-3": {
          "type": "collection_item",
          "settings": {
            "collection": "accessories",
            "image": "shopify:\/\/shop_images\/cl13_400x_fe793a6a-c1b2-46bc-9753-c1c760ebaf52.jpg",
            "icon": "las la-gem",
            "collection_title": "Accessories",
            "collection_link": "https:\/\/demo-kalles-4-1.myshopify.com\/collections\/accessories\/?view=sub"
          }
        }
      },
      "block_order": [
        "1650341675afa01110-0",
        "1650341675afa01110-1",
        "1650341675afa01110-2",
        "1650341675afa01110-3"
      ],
      "settings": {
        "design_heading": "1",
        "heading_align": "t4s-text-center",
        "top_heading": "",
        "icon_heading": "las la-gem",
        "top_subheading": "",
        "tophead_mb": 30,
        "collection_des": "1",
        "title_cl": "#ffffff",
        "title_cl_hover": "#222222",
        "subtitle_cl": "#878787",
        "count_cl": "#222222",
        "border_cl": "#e5e5e5",
        "collection_subtitle": "Products",
        "open_link": "_self",
        "source": "image",
        "space_bottom": 20,
        "space_bottom_mb": 10,
        "border": false,
        "item_pd": 0,
        "item_rd": 0,
        "img_effect": "none",
        "b_effect": "none",
        "image_ratio": "rationt",
        "image_size": "cover",
        "image_position": "8",
        "layout_des": "1",
        "col_dk": "4",
        "col_tb": "2",
        "col_mb": "2",
        "space_h_item": "30",
        "space_v_item": "30",
        "space_h_item_mb": "10",
        "space_v_item_mb": "10",
        "loop": true,
        "au_time": 0,
        "au_hover": true,
        "nav_btn": false,
        "btn_vi": "hover",
        "btn_owl": "default",
        "btn_shape": "none",
        "btn_cl": "dark",
        "btn_size": "small",
        "btn_hidden_mobile": true,
        "nav_dot": false,
        "dot_owl": "default",
        "dots_cl": "dark",
        "dots_round": true,
        "dots_space": 10,
        "dots_hidden_mobile": false,
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": ",,80px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": "",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "main": {
      "type": "main-collection",
      "blocks": {
        "filter_id": {
          "type": "filter",
          "settings": {
          }
        },
        "740319a4-8e19-4c70-b8a9-ada7cf235cbc": {
          "type": "layout",
          "settings": {
          }
        },
        "30594d23-d644-4653-9545-9ff769d2e6ab": {
          "type": "sortby",
          "settings": {
          }
        }
      },
      "block_order": [
        "filter_id",
        "740319a4-8e19-4c70-b8a9-ada7cf235cbc",
        "30594d23-d644-4653-9545-9ff769d2e6ab"
      ],
      "settings": {
        "product_des": "1",
        "show_vendor": false,
        "image_ratio": "rationt",
        "image_size": "cover",
        "image_position": "8",
        "content_align": "default",
        "limit": 12,
        "enable_listing": true,
        "enable_listing_default": false,
        "col_dk": "4",
        "col_tb": "2",
        "col_mb": "2",
        "space_h_item": "30",
        "space_v_item": "30",
        "space_h_item_mb": "10",
        "space_v_item_mb": "10",
        "layout_des": "1",
        "use_pagination": "default",
        "enable_bar_lm": true,
        "btn_icon": false,
        "button_style": "outline",
        "btns_size": "large",
        "btns_cl": "primary",
        "button_effect": "rectangle-out",
        "btn_pos": "t4s-text-center",
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": ",,30px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": ""
      }
    },
    "sidebar": {
      "type": "sidebar-collection",
      "blocks": {
        "c7bdf308-8019-4991-b58c-6d3e205a52eb": {
          "type": "category",
          "settings": {
            "heading": "Product Categories",
            "cat_source": "1",
            "cat_link_list": "",
            "count": true
          }
        },
        "9d37e80a-1eda-4a65-bb08-199e6a933a16": {
          "type": "collection",
          "settings": {
            "heading": "Sale Products",
            "collection": "frontpage",
            "limit_pr": 3,
            "image_ratio": "t4s_ratioadapt",
            "image_size": "t4s_cover",
            "image_position": "t4s_position_8"
          }
        },
        "c090f36e-d6ef-4a2a-ab04-cc78211dadb7": {
          "type": "image",
          "settings": {
            "heading": "",
            "img_effect": "zoom",
            "b_link": "",
            "open_link": "_self",
            "content_align": "center",
            "date": "2022\/12\/26",
            "cdt_des": "1",
            "countdown_pos": "2",
            "cdt_size": "small",
            "box_bdr": 0,
            "bd_width": 0,
            "space_item": 10,
            "number_cl": "#ffffff",
            "text_cl": "#222222",
            "border_cl": "#000000",
            "bg_cl": "#000000"
          }
        }
      },
      "block_order": [
        "c7bdf308-8019-4991-b58c-6d3e205a52eb",
        "9d37e80a-1eda-4a65-bb08-199e6a933a16",
        "c090f36e-d6ef-4a2a-ab04-cc78211dadb7"
      ],
      "disabled": true,
      "settings": {
        "enable_drawer": false,
        "space": 50,
        "size": "medium",
        "br_style": "none",
        "brcolor": "#222222",
        "sidebar_bdr": 0,
        "sidebar_pd": 15
      }
    },
    "1650341841f037d7dd": {
      "type": "shipping",
      "blocks": {
        "1650341840067ee0d3-0": {
          "type": "shipping",
          "settings": {
            "icon_themes": "none",
            "icon": "tractor",
            "title": "",
            "text": "<p><strong>FREE SHIPPING <\/strong>ALL ORDER OVER $100<\/p>"
          }
        },
        "1650341840067ee0d3-1": {
          "type": "shipping",
          "settings": {
            "icon_themes": "none",
            "icon": "life-ring",
            "title": "",
            "text": "<p>SUMMER <strong>SALE OFF <\/strong>TO<strong> 50%<\/strong><\/p>"
          }
        },
        "1650341840067ee0d3-2": {
          "type": "shipping",
          "settings": {
            "icon_themes": "none",
            "icon": "undo-alt",
            "title": "",
            "text": "<p>WE SUPPORT <strong>24 HOURS <\/strong>A DAY<\/p>"
          }
        }
      },
      "block_order": [
        "1650341840067ee0d3-0",
        "1650341840067ee0d3-1",
        "1650341840067ee0d3-2"
      ],
      "settings": {
        "design_heading": "1",
        "heading_align": "t4s-text-center",
        "top_heading": "",
        "icon_heading": "las la-gem",
        "top_subheading": "",
        "tophead_mb": 30,
        "content_align": "text-center",
        "align_vertical": true,
        "design_padding": "1",
        "source": "themes_icon",
        "icon_des": "deafult",
        "icon_size": "medium",
        "col_dk": "3",
        "col_tb": "2",
        "col_mb": "1",
        "border": true,
        "cl_bd": "#f2f2f4",
        "cl_ic": "#9e9e9e",
        "cl_hd": "#222222",
        "cl_cot": "#222222",
        "bg_item": "#f2f2f4",
        "space_h_item": "30",
        "space_v_item": "30",
        "space_h_item_mb": "10",
        "space_v_item_mb": "10",
        "carousel_mobile": true,
        "dots_cl": "dark",
        "dots_round": true,
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": ",,30px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": "",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    }
  },
  "order": [
    "top",
    "heading_template",
    "1650341675018b864a",
    "main",
    "sidebar",
    "1650341841f037d7dd"
  ]
}
