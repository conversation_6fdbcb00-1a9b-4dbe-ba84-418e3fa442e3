/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "heading": {
      "type": "heading-template",
      "blocks": {
        "ba0c8e30-c11a-4ab7-97c4-4acc1a053a3e": {
          "type": "1",
          "settings": {
            "heading": "FAQ",
            "fontf": "inherit",
            "text_cl": "#fff",
            "text_fs": 20,
            "text_lh": 20,
            "text_fw": 500,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 15,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 20,
            "text_ls_mb": 0,
            "text_mgb_mobile": 10
          }
        },
        "ceaf2fff-8462-4040-83d5-7f7019d65b8d": {
          "type": "3",
          "settings": {
            "breadcrumb_color": "#f2f2f2",
            "brc_mgb": 5
          }
        }
      },
      "block_order": [
        "ba0c8e30-c11a-4ab7-97c4-4acc1a053a3e",
        "ceaf2fff-8462-4040-83d5-7f7019d65b8d"
      ],
      "settings": {
        "image": "shopify:\/\/shop_images\/bg-heading.jpg",
        "parallax": false,
        "color": "#000000",
        "overlay": 54,
        "padding": 50,
        "paddingmb": 50,
        "mg_b": 70,
        "mg_bmb": 50,
        "content_align": "t4s-text-center",
        "bg_pos": "center center",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "60px,,60px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": ""
      }
    },
    "sidebar-page": {
      "type": "sidebar-page",
      "blocks": {
        "f69681ed-b75e-42de-be58-fdf73b3b7e08": {
          "type": "blog_cate",
          "settings": {
            "title": "Blog categories",
            "cat_link_list": "",
            "count": true
          }
        }
      },
      "block_order": [
        "f69681ed-b75e-42de-be58-fdf73b3b7e08"
      ],
      "disabled": true,
      "settings": {
        "enable_drawer": false,
        "space": 50,
        "size": "medium",
        "br_style": "none",
        "brcolor": "#222222",
        "sidebar_bdr": 5,
        "sidebar_pd": 10
      }
    },
    "165214825814e830e7": {
      "type": "accordion",
      "blocks": {
        "1652148258305a99c1-0": {
          "type": "accor_item",
          "settings": {
            "icon": "none",
            "title": "Do I need to open an account in order to shop with you?",
            "content": "<p>No, you don’t need to. You can make purchases and check out as a guest everytime.<\/p><p>However, by setting up an account with us, it will allow you to order without having to enter your details every time you shop with us. You can sign up right now, or you can first start shopping and create your account before you check out at the shopping cart page.<\/p>"
          }
        },
        "1652148258305a99c1-1": {
          "type": "accor_item",
          "settings": {
            "icon": "none",
            "title": "How do I \/create an account?",
            "content": "<p>Please click on “Login\/Register” followed by ‘Create An Account’ and fill in your personal particulars.<\/p>"
          }
        },
        "1652148258305a99c1-2": {
          "type": "accor_item",
          "settings": {
            "icon": "none",
            "title": "How do I order?",
            "content": "<p>Shop for the items you want and add it to your shopping cart. When you have finished, you can proceed to your shopping cart and check out. Check and ensure that all information is correct before confirming your purchases and payment.<\/p>"
          }
        },
        "0c4e86da-f870-4d3c-abce-b988cae18d6c": {
          "type": "accor_item",
          "settings": {
            "icon": "none",
            "title": "How do I pay for my orders?",
            "content": "<p>We accept payments via Paypal and all major credit and debit cards such as Mastercard, VISA and American Express.<\/p>"
          }
        },
        "cb973ab0-b192-4c97-b7ba-6b8acbf93e5f": {
          "type": "accor_item",
          "settings": {
            "icon": "none",
            "title": "Can I amend and cancel my order?",
            "content": "<p>Unfortunately we are unable to cancel an order once it has been placed. This will allow us to pack your orders efficiently and to minimize errors. It is advisable to check your order before placing it.<\/p>"
          }
        },
        "f9fbcebb-a809-4cca-8806-c102d1de1c0b": {
          "type": "accor_item",
          "settings": {
            "icon": "none",
            "title": "I have a discount code, how can I use it?",
            "content": "<p>Unfortunately we are unable to cancel an order once it has been placed. This will allow us to pack your orders efficiently and to minimize errors. It is advisable to check your order before placing it.<\/p>"
          }
        },
        "9aa601e3-a39f-431e-aaa9-de35ae6945fb": {
          "type": "accor_item",
          "settings": {
            "icon": "none",
            "title": "How will I know if my order is confirmed?",
            "content": "<p>After you have placed your order, you will receive an acknowledgement e-mail from us to confirm that your orders have been received. However, do note that orders will only be shipped when your credit card payment has been approved and billing and delivery address is verified. Alternatively, you may check the status of your order in “My Account” if you are a registered user.<\/p>"
          }
        },
        "15f6d610-63c6-4503-8a7f-4a1774dcf9ed": {
          "type": "accor_item",
          "settings": {
            "icon": "none",
            "title": "I have problems adding items to my shopping cart?",
            "content": "<p>You will be able to add the items as long as it is available. There could be an instance where the item is in someone else’s shopping cart hence the status of the items is reflected as “Temporarily Unavailable”.<\/p>"
          }
        }
      },
      "block_order": [
        "1652148258305a99c1-0",
        "1652148258305a99c1-1",
        "1652148258305a99c1-2",
        "0c4e86da-f870-4d3c-abce-b988cae18d6c",
        "cb973ab0-b192-4c97-b7ba-6b8acbf93e5f",
        "f9fbcebb-a809-4cca-8806-c102d1de1c0b",
        "9aa601e3-a39f-431e-aaa9-de35ae6945fb",
        "15f6d610-63c6-4503-8a7f-4a1774dcf9ed"
      ],
      "settings": {
        "design_heading": "1",
        "heading_align": "t4s-text-center",
        "top_heading": "",
        "icon_heading": "las la-gem",
        "top_subheading": "",
        "tophead_mb": 30,
        "accor_des": "1",
        "title_cl": "#222",
        "bg_title_cl": "#f6f6f8",
        "title_active_cl": "#000",
        "bg_title_active_cl": "#f6f6f8",
        "content_cl": "#878787",
        "bg_content_cl": "#fff",
        "content_align": "t4s-text-start",
        "layout": "t4s-container-wrap",
        "custom_width": 1000,
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": ",,80px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": "",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    }
  },
  "order": [
    "heading",
    "main",
    "sidebar-page",
    "165214825814e830e7"
  ]
}
