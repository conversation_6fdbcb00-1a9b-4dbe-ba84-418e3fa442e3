/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "heading": {
      "type": "heading-template",
      "blocks": {
        "18952756-58af-4afa-a0cc-b1b54300fb8b": {
          "type": "1",
          "settings": {
            "heading": "",
            "fontf": "inherit",
            "text_cl": "#ffffff",
            "text_fs": 20,
            "text_lh": 20,
            "text_fw": 500,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 15,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 20,
            "text_ls_mb": 0,
            "text_mgb_mobile": 10
          }
        },
        "cc993342-0dd0-41d6-a6c4-a5f3c87e6f96": {
          "type": "3",
          "settings": {
            "breadcrumb_color": "#f2f2f2",
            "brc_mgb": 5
          }
        }
      },
      "block_order": [
        "18952756-58af-4afa-a0cc-b1b54300fb8b",
        "cc993342-0dd0-41d6-a6c4-a5f3c87e6f96"
      ],
      "settings": {
        "image": "shopify:\/\/shop_images\/bg-heading.jpg",
        "parallax": false,
        "color": "#000000",
        "overlay": 54,
        "padding": 50,
        "paddingmb": 50,
        "mg_b": 50,
        "mg_bmb": 50,
        "content_align": "t4s-text-center",
        "bg_pos": "center center",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "main": {
      "type": "main-page",
      "settings": {
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "60px,,60px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": ""
      }
    },
    "sidebar-page": {
      "type": "sidebar-page",
      "disabled": true,
      "settings": {
        "enable_drawer": true,
        "space": 50,
        "size": "medium",
        "br_style": "none",
        "brcolor": "#222222",
        "sidebar_bdr": 5,
        "sidebar_pd": 10
      }
    }
  },
  "order": [
    "heading",
    "main",
    "sidebar-page"
  ]
}
