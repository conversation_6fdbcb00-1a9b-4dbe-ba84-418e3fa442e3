/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "heading": {
      "type": "heading-template",
      "blocks": {
        "c04f75e6-dfcb-4b9e-a3e7-ca7583d285b5": {
          "type": "1",
          "settings": {
            "heading": "",
            "fontf": "inherit",
            "text_cl": "#fff",
            "text_fs": 30,
            "text_lh": 36,
            "text_fw": 300,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 5,
            "hidden_mobile": false,
            "text_fs_mb": 28,
            "text_lh_mb": 32,
            "text_ls_mb": 0,
            "text_mgb_mobile": 5
          }
        },
        "7cd255d5-40e9-4da7-8d3d-be1407a27726": {
          "type": "3",
          "settings": {
            "breadcrumb_color": "#fff",
            "brc_mgb": 5
          }
        }
      },
      "block_order": [
        "c04f75e6-dfcb-4b9e-a3e7-ca7583d285b5",
        "7cd255d5-40e9-4da7-8d3d-be1407a27726"
      ],
      "settings": {
        "heading_fullwidth": false,
        "image": "shopify:\/\/shop_images\/bg-heading.jpg",
        "use_specify_image": true,
        "parallax": false,
        "color": "#222222",
        "overlay": 50,
        "padding": 35,
        "paddingmb": 30,
        "mg_b": 0,
        "mg_bmb": 0,
        "content_align": "t4s-text-center",
        "bg_pos": "center center",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
        "use_cus_css": false,
        "code_cus_css": ""
      }
    },
    "main": {
      "type": "main-store-locator",
      "blocks": {
        "2ce63f38-0ba2-4853-852b-537b0fe2eb33": {
          "type": "store",
          "settings": {
            "heading": "1471 P St NW",
            "latitude": "38.909882",
            "longitude": "-77.034149",
            "content": "<p>Washington' DC <br\/>(202) 234-7336<br\/>at 15th St NW<\/p>"
          }
        },
        "ebe283f8-17e8-4ea4-a66c-edb505cbef27": {
          "type": "store",
          "settings": {
            "heading": "2221 I St NW",
            "latitude": "38.900772",
            "longitude": "-77.049766",
            "content": "<p>Washington'\" DC<br\/>(202) 507-8357 <br\/>at 22nd St NW<\/p>"
          }
        },
        "ecd43add-2a22-46d3-884d-97e0962d9461": {
          "type": "store",
          "settings": {
            "heading": "Ha Noi",
            "latitude": "21.027763",
            "longitude": "105.834160",
            "content": "<p>Viet Nam<br\/>012345678<\/p>"
          }
        }
      },
      "block_order": [
        "2ce63f38-0ba2-4853-852b-537b0fe2eb33",
        "ebe283f8-17e8-4ea4-a66c-edb505cbef27",
        "ecd43add-2a22-46d3-884d-97e0962d9461"
      ],
      "settings": {
        "layout": "t4s-container-wrap",
        "access_token": "pk.eyJ1IjoibmhvY2xlb3BybyIsImEiOiJja3pncWhsaXUxNDBiMnducnN1cWZsNmZwIn0.7RBTwnj9Dl_SPhKDcY0rYw",
        "enable_searchbox": true,
        "style_map": "streets",
        "zoom": 12,
        "pitch": 60,
        "color_marker": "#0ec1ae",
        "mg": "60px,,60px,",
        "pd": "",
        "mg_tb": ",,50px,",
        "pd_tb": "",
        "mg_mb": ",,30px,",
        "pd_mb": ""
      }
    }
  },
  "order": [
    "heading",
    "main"
  ]
}