/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "heading": {
      "type": "heading-template",
      "blocks": {
        "92ba55a1-d595-4ca7-b00d-e472dd234792": {
          "type": "1",
          "settings": {
            "heading": "Page Heading",
            "fontf": "inherit",
            "text_cl": "#fff",
            "text_fs": 20,
            "text_lh": 0,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 0,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 0,
            "text_ls_mb": 0,
            "text_mgb_mobile": 0
          }
        }
      },
      "block_order": [
        "92ba55a1-d595-4ca7-b00d-e472dd234792"
      ],
      "settings": {
        "image": "shopify:\/\/shop_images\/collection.jpg",
        "use_specify_image": true,
        "parallax": false,
        "color": "#000",
        "overlay": 54,
        "padding": 70,
        "paddingmb": 50,
        "mg_b": 50,
        "mg_bmb": 50,
        "content_align": "t4s-text-center",
        "bg_pos": "center center",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
        "use_cus_css": false,
        "code_cus_css": ""
      }
    },
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "60px,,60px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": ""
      }
    },
    "1659629440d3109507": {
      "type": "timeline",
      "blocks": {
        "1659629440d4fc3bac-0": {
          "type": "timeline",
          "settings": {
            "img_pos": "left",
            "image_ratio": "ratioadapt",
            "image_position": "8",
            "image_size": "cover",
            "img_effect": "none",
            "b_effect": "none",
            "step_label": "1999",
            "sub_heading": "Condimentum fames egestas ad potenti",
            "heading": "Lementum musat dignissim",
            "description": "<p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s.<\/p>",
            "url": "",
            "btn_label": "Read more",
            "btn_icon": false,
            "button_style": "default",
            "btn_size": "large",
            "btn_cl": "dark",
            "button_effect": "default"
          }
        },
        "77b7d254-643a-473b-b41d-cb73ea47f7b7": {
          "type": "timeline",
          "settings": {
            "img_pos": "right",
            "image_ratio": "ratioadapt",
            "image_position": "8",
            "image_size": "cover",
            "img_effect": "none",
            "b_effect": "none",
            "step_label": "2000",
            "sub_heading": "Condimentum fames egestas ad potenti",
            "heading": "Lementum musat dignissim",
            "description": "<p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s.<\/p>",
            "url": "",
            "btn_label": "Read more",
            "btn_icon": false,
            "button_style": "default",
            "btn_size": "large",
            "btn_cl": "dark",
            "button_effect": "default"
          }
        },
        "c88c1e89-44b7-4c41-81cc-fc2d97e8c819": {
          "type": "timeline",
          "settings": {
            "img_pos": "left",
            "image_ratio": "ratioadapt",
            "image_position": "8",
            "image_size": "cover",
            "img_effect": "none",
            "b_effect": "none",
            "step_label": "2001",
            "sub_heading": "Condimentum fames egestas ad potenti",
            "heading": "Lementum musat dignissim",
            "description": "<p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s.<\/p>",
            "url": "",
            "btn_label": "Read more",
            "btn_icon": false,
            "button_style": "default",
            "btn_size": "large",
            "btn_cl": "dark",
            "button_effect": "default"
          }
        }
      },
      "block_order": [
        "1659629440d4fc3bac-0",
        "77b7d254-643a-473b-b41d-cb73ea47f7b7",
        "c88c1e89-44b7-4c41-81cc-fc2d97e8c819"
      ],
      "settings": {
        "design_heading": "1",
        "heading_align": "t4s-text-center",
        "top_heading": "",
        "icon_heading": "las la-gem",
        "top_subheading": "",
        "tophead_mb": 30,
        "line_style": "dashed",
        "enable_shadow": false,
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": ",,50px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": "",
        "use_cus_css": false,
        "code_cus_css": ""
      }
    }
  },
  "order": [
    "heading",
    "main",
    "1659629440d3109507"
  ]
}
