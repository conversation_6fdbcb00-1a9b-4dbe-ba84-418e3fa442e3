/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "wrapper": "div#theme-configs",
  "sections": {
    "main-qv": {
      "type": "main-qv",
      "blocks": {
        "main-qv-0": {
          "type": "title",
          "settings": {
            "txt_tr_pr": "none",
            "fnt_df_pr": "1",
            "size_pr": 16,
            "fw_pr": 600,
            "lh_pr": 0,
            "ls_pr": 0,
            "pr_title_color": "#222222",
            "pr_title_color_hover": "#56cfe1"
          }
        },
        "main-qv-1": {
          "type": "price_review",
          "settings": {
            "price": "0",
            "type_sale": "0",
            "tax_ship": true,
            "rating": true,
            "review_liquid": "",
            "size_price_pr": 22,
            "fw_price_pr": 400,
            "price_color": "#696969",
            "price_sale_color": "#ec0101"
          }
        },
        "main-qv-2": {
          "type": "description",
          "settings": {
            "des": "2",
            "text": "<p>Go kalles this summer with this vintage navy and white striped v-neck t-shirt from the Nike. Perfect for pairing with denim and white kicks for a stylish kalles vibe.<\/p>",
            "length": 31
          }
        },
        "main-qv-3": {
          "type": "form",
          "settings": {
            "selector_mode": "circle",
            "color_mode": "color is-sw-cl__round",
            "enable_fit_ratio_img": false,
            "color_size": "medium",
            "show_qty": true,
            "enable_wishlist": true,
            "enable_compare": true,
            "btn_atc_full": false,
            "pr_btn_round": 40,
            "ani": "t4s-ani-tada",
            "time": 6,
            "btn_txt": "3",
            "btn_icon": false,
            "button_style": "default",
            "button_color": "primary",
            "button_effect": "sweep-to-bottom",
            "wishlist_color": "#222222",
            "wishlist_color_hover": "#56cfe1",
            "wishlist_color_active": "#e81e1e",
            "compare_color": "#222222",
            "compare_color_hover": "#56cfe1",
            "compare_color_active": "#222222",
            "show_dynamic_checkout": true,
            "btn_txt2": "3",
            "button_color_payment": "dark",
            "show_gift_card_recipient": true,
            "product_list": [

            ],
            "show_product_current": true,
            "qty_val": "0",
            "advance_label": "Choose style",
            "advance_pr_list": [

            ]
          }
        },
        "main-qv-4": {
          "type": "size_delivery_ask",
          "settings": {
            "size_chart": "3",
            "pos_sizeg": "1",
            "sc_type": "1",
            "page": "size-chart",
            "size_ck": "",
            "delivery": true,
            "page_dr": "delivery-return",
            "ask": true,
            "phone": true
          }
        },
        "main-qv-5": {
          "type": "meta",
          "settings": {
            "show_options": true,
            "show_vendor": false,
            "show_type": false,
            "show_sku": true,
            "show_barcode": false,
            "show_available": true,
            "show_category": true,
            "show_tags": true
          }
        },
        "main-qv-6": {
          "type": "social",
          "settings": {
            "socials_align": "start",
            "social_mode": "2",
            "social_style": "1",
            "social_size": "extra_small",
            "bd_radius": 0,
            "use_color_set": true,
            "icon_cl": "#222222",
            "bg_cl": "#56cfe1",
            "space_h_item": "20",
            "space_v_item": "5",
            "space_h_item_mb": "6",
            "space_v_item_mb": "2"
          }
        }
      },
      "block_order": [
        "main-qv-0",
        "main-qv-1",
        "main-qv-2",
        "main-qv-3",
        "main-qv-4",
        "main-qv-5",
        "main-qv-6"
      ],
      "settings": {
        "image_ratio": "ratioadapt",
        "image_size": "cover",
        "image_position": "8",
        "enable_video_looping": false,
        "enable_video_muting": true,
        "enable_video_autoplaying": true,
        "eff": "fade",
        "nav_btn": true,
        "btn_vi": "always",
        "btn_owl": "outline",
        "btn_shape": "round",
        "btn_cl": "dark",
        "btn_size": "small",
        "btn_hidden_mobile": false,
        "nav_dot": false,
        "dot_owl": "default",
        "dots_cl": "dark",
        "dots_round": true,
        "dots_space": 10,
        "dots_hidden_mobile": false
      }
    },
    "main-qs": {
      "type": "main-qs",
      "blocks": {
        "main-qs-0": {
          "type": "title",
          "settings": {
            "txt_tr_pr": "none",
            "fnt_df_pr": "1",
            "size_pr": 16,
            "fw_pr": 600,
            "lh_pr": 0,
            "ls_pr": 0,
            "pr_title_color": "#222222",
            "pr_title_color_hover": "#56cfe1"
          }
        },
        "main-qs-1": {
          "type": "price",
          "settings": {
            "price": "0",
            "type_sale": "2",
            "size_price_pr": 22,
            "fw_price_pr": 400,
            "price_color": "#696969",
            "price_sale_color": "#ec0101"
          }
        },
        "main-qs-2": {
          "type": "form",
          "settings": {
            "selector_mode": "block",
            "color_mode": "variant_image",
            "enable_fit_ratio_img": true,
            "color_size": "large",
            "show_qty": true,
            "enable_wishlist": true,
            "enable_compare": true,
            "pr_btn_round": 40,
            "ani": "t4s-ani-shake",
            "time": 3,
            "btn_txt": "3",
            "btn_icon": false,
            "button_style": "default",
            "button_color": "primary",
            "button_effect": "sweep-to-top",
            "wishlist_color": "#222222",
            "wishlist_color_hover": "#56cfe1",
            "wishlist_color_active": "#e81e1e",
            "compare_color": "#222222",
            "compare_color_hover": "#56cfe1",
            "compare_color_active": "#222222",
            "show_dynamic_checkout": true,
            "btn_txt2": "3",
            "button_color_payment": "dark"
          }
        }
      },
      "block_order": [
        "main-qs-0",
        "main-qs-1",
        "main-qs-2"
      ],
      "settings": {
        "demo_pr": ""
      }
    }
  },
  "order": [
    "main-qv",
    "main-qs"
  ]
}
