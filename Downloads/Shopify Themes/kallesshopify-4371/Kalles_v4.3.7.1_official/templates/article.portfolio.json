/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "heading": {
      "type": "heading-article",
      "blocks": {
        "6c7e7290-ccf4-43d2-9cca-8b6defb822d0": {
          "type": "1",
          "settings": {
            "fontf": "1",
            "text_cl": "#ffffff",
            "text_fs": 20,
            "text_lh": 0,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 10,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 0,
            "text_ls_mb": 0,
            "text_mgb_mobile": 10
          }
        },
        "dc3e7611-c82d-48c5-bc94-b71307050d93": {
          "type": "2",
          "settings": {
            "fontf": "3",
            "font_italic": true
          }
        }
      },
      "block_order": [
        "6c7e7290-ccf4-43d2-9cca-8b6defb822d0",
        "dc3e7611-c82d-48c5-bc94-b71307050d93"
      ],
      "settings": {
        "use_specify_image": true,
        "parallax": false,
        "color": "#000000",
        "overlay": 54,
        "padding": 50,
        "paddingmb": 50,
        "mg_b": 50,
        "mg_bmb": 50,
        "content_align": "t4s-text-center",
        "bg_pos": "center center",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
        "bg_att": "scroll",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "main": {
      "type": "main-article-portfolio",
      "blocks": {
        "76194e16-b3ca-47c7-a49b-2225094a4639": {
          "type": "image",
          "settings": {
            "cate_des": "none"
          }
        },
        "ff4537d6-dd2c-47c9-aec4-76742fa00896": {
          "type": "content",
          "settings": {
          }
        },
        "f333f3fd-c7bf-4671-aa6b-b388006365f1": {
          "type": "tags",
          "settings": {
          }
        },
        "9320ae25-4d06-4c95-9ceb-683ac8a6f179": {
          "type": "infos",
          "settings": {
            "show_cate": true,
            "show_author": true,
            "show_tags": true
          }
        },
        "af274b12-25fe-4de7-912f-f4fcde7664b7": {
          "type": "socials",
          "settings": {
            "social_mode": "2",
            "social_style": "2",
            "social_size": "small",
            "bd_radius": 30,
            "use_color_set": true,
            "icon_cl": "#000000",
            "bg_cl": "#fff",
            "space_h_item": "20",
            "space_v_item": "5",
            "space_h_item_mb": "10",
            "space_v_item_mb": "2"
          }
        },
        "45eccd34-9365-4b0c-a0f7-134f2d1686c4": {
          "type": "navigation",
          "settings": {
          }
        },
        "6db2accf-45b2-46a9-80e0-3b8d48d81b31": {
          "type": "related",
          "settings": {
            "hd_related": "RELATED PORFOLIO",
            "hd_align": "center",
            "limit_related": 6,
            "space_h_item": "30",
            "space_v_item": "30",
            "space_h_item_mb": "10",
            "space_v_item_mb": "10",
            "col_dk": "3",
            "col_tb": "2",
            "col_mb": "1",
            "image_ratio": "ratio4_3",
            "image_position": "8",
            "image_size": "cover",
            "loop": true,
            "au_time": 0,
            "au_hover": true,
            "nav_btn": false,
            "btn_vi": "hover",
            "btn_owl": "default",
            "btn_shape": "none",
            "btn_cl": "dark",
            "btn_size": "small",
            "btn_hidden_mobile": true,
            "nav_dot": false,
            "dot_owl": "default",
            "dots_cl": "dark",
            "dots_round": true,
            "dots_space": 10,
            "dots_hidden_mobile": false
          }
        }
      },
      "block_order": [
        "76194e16-b3ca-47c7-a49b-2225094a4639",
        "ff4537d6-dd2c-47c9-aec4-76742fa00896",
        "f333f3fd-c7bf-4671-aa6b-b388006365f1",
        "9320ae25-4d06-4c95-9ceb-683ac8a6f179",
        "af274b12-25fe-4de7-912f-f4fcde7664b7",
        "45eccd34-9365-4b0c-a0f7-134f2d1686c4",
        "6db2accf-45b2-46a9-80e0-3b8d48d81b31"
      ],
      "settings": {
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": ",,50px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": ""
      }
    },
    "sidebar": {
      "type": "sidebar-article-portfolio",
      "disabled": true,
      "settings": {
        "enable_drawer": true,
        "space": 50,
        "size": "medium",
        "br_style": "none",
        "brcolor": "#222222",
        "sidebar_bdr": 5,
        "sidebar_pd": 10
      }
    }
  },
  "order": [
    "heading",
    "main",
    "sidebar"
  ]
}
