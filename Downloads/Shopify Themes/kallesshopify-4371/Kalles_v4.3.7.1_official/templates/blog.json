/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "heading": {
      "type": "heading-template",
      "blocks": {
        "3fcc5c0e-3b55-4490-bb0f-778533d18142": {
          "type": "1",
          "settings": {
            "heading": "Page Heading",
            "fontf": "inherit",
            "text_cl": "#ffffff",
            "text_fs": 20,
            "text_lh": 0,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 15,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 0,
            "text_ls_mb": 0,
            "text_mgb_mobile": 10
          }
        },
        "190dccc0-921d-4bc6-8dbb-51e27461095d": {
          "type": "2",
          "settings": {
            "content": "<p>Sub title page content<\/p>",
            "fontf": "inherit",
            "text_cl": "#ffffff",
            "text_fs": 16,
            "text_lh": 0,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 0,
            "hidden_mobile": false,
            "text_fs_mb": 16,
            "text_lh_mb": 0,
            "text_ls_mb": 0,
            "text_mgb_mobile": 0
          }
        },
        "9c516f81-c531-4ce6-903a-a0493715b39a": {
          "type": "3",
          "settings": {
            "breadcrumb_color": "#f2f2f2",
            "brc_mgb": 5
          }
        }
      },
      "block_order": [
        "3fcc5c0e-3b55-4490-bb0f-778533d18142",
        "190dccc0-921d-4bc6-8dbb-51e27461095d",
        "9c516f81-c531-4ce6-903a-a0493715b39a"
      ],
      "disabled": true,
      "settings": {
        "parallax": false,
        "color": "#000000",
        "overlay": 54,
        "padding": 50,
        "paddingmb": 50,
        "mg_b": 50,
        "mg_bmb": 50,
        "content_align": "t4s-text-center",
        "bg_pos": "center center",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "slider": {
      "type": "blog_slider",
      "settings": {
        "blog": "fashion",
        "limit": 6,
        "date": "date",
        "col_dk": "3",
        "col_tb": "2",
        "col_mb": "1",
        "show_author": true,
        "show_date": true,
        "show_comment": false,
        "image_ratio": "ratio4_3",
        "image_position": "8",
        "image_size": "cover",
        "img_effect": "zoom",
        "b_effect": "pervasive-circle",
        "cl": "#ffffff",
        "cl2": "#878787",
        "bg": "#000000",
        "overlay": 80,
        "loop": true,
        "au_time": 0,
        "au_hover": true,
        "nav_btn": true,
        "btn_vi": "hover",
        "btn_owl": "outline",
        "btn_shape": "round",
        "btn_cl": "dark",
        "btn_size": "small",
        "btn_hidden_mobile": false,
        "nav_dot": false,
        "dot_owl": "default",
        "dots_cl": "dark",
        "dots_round": false,
        "dots_space": 10,
        "dots_hidden_mobile": false,
        "layout": "t4s-container-fluid",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": ",,,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": "",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "main": {
      "type": "main-blog",
      "settings": {
        "layout_des": "1",
        "art_des": "1",
        "limit": 6,
        "date": "abbreviated_date",
        "show_cate": false,
        "show_tags": false,
        "show_cnt": false,
        "show_au": true,
        "show_dt": true,
        "show_cm": false,
        "show_rm": false,
        "text_align": "start",
        "col_dk": "2",
        "col_tb": "2",
        "col_mb": "1",
        "space_h_item": "30",
        "space_v_item": "40",
        "space_h_item_mb": "30",
        "space_v_item_mb": "40",
        "use_pagination": "default",
        "use_bar_lm": true,
        "btn_icon": true,
        "button_style": "default",
        "btn_size": "large",
        "btn_cl": "dark",
        "button_effect": "fade",
        "btn_pos": "t4s-text-center",
        "image_ratio": "ratio4_3",
        "image_position": "8",
        "image_size": "cover",
        "img_effect": "zoom",
        "b_effect": "pervasive-circle",
        "source": "0",
        "show_count": true,
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "60px,,60px,",
        "pd": "",
        "mg_mb": "30px,,30px,",
        "pd_mb": ""
      }
    },
    "sidebar": {
      "type": "sidebar-blog",
      "blocks": {
        "d8aac4b9-39f0-4d61-bda2-147ff729bd69": {
          "type": "blog_cate",
          "settings": {
            "title": "Blog categories",
            "cat_link_list": "main-menu",
            "count": true
          }
        },
        "aa039b15-ee15-46c7-9edd-fab236515c2a": {
          "type": "tags",
          "settings": {
            "title": "Blog tags",
            "count": true
          }
        },
        "3f3fa516-998a-40b8-80f9-bd2783aae214": {
          "type": "collection",
          "settings": {
            "title": "Sale Products",
            "collection": "women",
            "limit_pr": 3,
            "image_ratio": "t4s_ratioadapt",
            "image_size": "t4s_cover",
            "image_position": "t4s_position_8"
          }
        }
      },
      "block_order": [
        "d8aac4b9-39f0-4d61-bda2-147ff729bd69",
        "aa039b15-ee15-46c7-9edd-fab236515c2a",
        "3f3fa516-998a-40b8-80f9-bd2783aae214"
      ],
      "disabled": true,
      "settings": {
        "enable_drawer": true,
        "space": 50,
        "size": "medium",
        "br_style": "none",
        "brcolor": "#222222",
        "sidebar_bdr": 5,
        "sidebar_pd": 10
      }
    }
  },
  "order": [
    "heading",
    "slider",
    "main",
    "sidebar"
  ]
}
