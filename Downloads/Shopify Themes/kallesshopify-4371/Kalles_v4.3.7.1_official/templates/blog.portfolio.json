/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "heading": {
      "type": "heading-template",
      "blocks": {
        "bcb179dc-6215-4b6d-9fcb-1e17b5f7889c": {
          "type": "1",
          "settings": {
            "heading": "Page Heading",
            "fontf": "inherit",
            "text_cl": "#ffffff",
            "text_fs": 20,
            "text_lh": 0,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 0,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 0,
            "text_ls_mb": 0,
            "text_mgb_mobile": 0
          }
        }
      },
      "block_order": [
        "bcb179dc-6215-4b6d-9fcb-1e17b5f7889c"
      ],
      "settings": {
        "image": "shopify:\/\/shop_images\/bg-heading_2afcfe30-9436-4439-bebb-09f4c602eae5.jpg",
        "parallax": false,
        "color": "#000000",
        "overlay": 54,
        "padding": 50,
        "paddingmb": 50,
        "mg_b": 50,
        "mg_bmb": 50,
        "content_align": "t4s-text-center",
        "bg_pos": "center center",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "main": {
      "type": "main-portfolio",
      "settings": {
        "layout_des": "2",
        "limit": 6,
        "col_dk": "3",
        "col_tb": "2",
        "col_mb": "1",
        "space_h_item": "30",
        "space_v_item": "30",
        "space_h_item_mb": "30",
        "space_v_item_mb": "30",
        "use_pagination": "load-more",
        "use_bar_lm": false,
        "btn_icon": false,
        "button_style": "outline",
        "btn_size": "large",
        "btn_cl": "dark",
        "button_effect": "fade",
        "btn_pos": "t4s-text-center",
        "image_ratio": "ratioadapt",
        "image_position": "8",
        "image_size": "cover",
        "source": "1",
        "show_count": true,
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": ",,50px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": ""
      }
    },
    "sidebar": {
      "type": "sidebar-portfolio",
      "disabled": true,
      "settings": {
        "enable_drawer": true,
        "space": 50,
        "size": "medium",
        "br_style": "none",
        "brcolor": "#222222",
        "sidebar_bdr": 5,
        "sidebar_pd": 10
      }
    }
  },
  "order": [
    "heading",
    "main",
    "sidebar"
  ]
}
