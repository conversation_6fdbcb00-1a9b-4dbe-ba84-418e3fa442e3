/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
    "sections": {
        "1646028739ae283905": {
            "type": "slideshow",
            "blocks": {
                "164602873867eb3815-0": {
                    "type": "image_parent",
                    "settings": {
                        "image_position": "8",
                        "cl_img": "",
                        "link_img1": "",
                        "open_link": "_blank",
                        "animate_slide": "none",
                        "text_align": "start",
                        "text_align_mb": "start",
                        "content_width": "container",
                        "cv_pos": 50,
                        "ch_pos": 0,
                        "cv_pos_mb": 50,
                        "ch_pos_mb": 0,
                        "bg_overlay": "#000000",
                        "bg_opacity": 0,
                        "bg_content_cl": "rgba(0,0,0,0)",
                        "bg_content_op": 0,
                        "content_radius": 0,
                        "box_square": false,
                        "content_pd_tb": 15,
                        "content_pd_lr": 15,
                        "content_pd_tb_tb": 15,
                        "content_pd_lr_tb": 15,
                        "content_pd_tb_mb": 10,
                        "content_pd_lr_mb": 10,
                        "border_bl": false,
                        "br_color": "#222222",
                        "br_bg": "#ffffff",
                        "br_opacity": 50,
                        "br_style": "solid",
                        "br_pd": 20,
                        "br_pd_mb": 10,
                        "time_animation": 1,
                        "animation_delay": 30
                    }
                },
                "164602873867eb3815-1": {
                    "type": "custom_text",
                    "settings": {
                        "text": "SUMMER 2023",
                        "remove_br_tag": false,
                        "tag": "p",
                        "fontf": "inherit",
                        "text_cl": "#222222",
                        "text_fs": 18,
                        "text_lh": 0,
                        "text_fw": 500,
                        "text_ls": 1,
                        "text_mgb": 5,
                        "font_italic": false,
                        "text_shadow": false,
                        "hidden_mobile": false,
                        "text_fs_mb": 12,
                        "text_lh_mb": 0,
                        "text_ls_mb": 0,
                        "text_mgb_mobile": 5,
                        "animation": "fadeInUp"
                    }
                },
                "164602873867eb3815-2": {
                    "type": "custom_text",
                    "settings": {
                        "text": "New Arrival Collection",
                        "remove_br_tag": false,
                        "tag": "p",
                        "fontf": "inherit",
                        "text_cl": "#222222",
                        "text_fs": 55,
                        "text_lh": 55,
                        "text_fw": 600,
                        "text_ls": 0,
                        "text_mgb": 10,
                        "font_italic": false,
                        "text_shadow": false,
                        "hidden_mobile": false,
                        "text_fs_mb": 18,
                        "text_lh_mb": 18,
                        "text_ls_mb": 0,
                        "text_mgb_mobile": 10,
                        "animation": "fadeInUp"
                    }
                },
                "390cb6f8-c5fc-44ed-badf-074a03818e5a": {
                    "type": "custom_button",
                    "settings": {
                        "button_text": "Explore Now",
                        "button_link": "shopify://collections",
                        "target_link": "_self",
                        "fontf": "inherit",
                        "button_icon_w": 0,
                        "button_style": "default",
                        "button_effect": "sweep-to-top",
                        "pri_cl": "#222222",
                        "second_cl": "#ffffff",
                        "pri_cl_hover": "#56cfe1",
                        "second_cl_hover": "#ffffff",
                        "fsbutton": 14,
                        "fwbutton": 600,
                        "button_ls": 0,
                        "button_mh": 40,
                        "button_bdr": 0,
                        "button_pd_lr": 32,
                        "button_mgb": 0,
                        "hidden_mobile": false,
                        "button_icon_w_mb": 0,
                        "fsbutton_mb": 12,
                        "button_mh_mb": 36,
                        "button_pd_lr_mb": 22,
                        "button_ls_mb": 0,
                        "button_mgb_mb": 0,
                        "animation": "fadeInUp"
                    }
                },
                "aca13d02-fdca-4dd7-b455-cc71ffabb08a": {
                    "type": "image_parent",
                    "settings": {
                        "image_position": "8",
                        "cl_img": "",
                        "link_img1": "",
                        "open_link": "_blank",
                        "animate_slide": "none",
                        "text_align": "end",
                        "text_align_mb": "end",
                        "content_width": "container",
                        "cv_pos": 50,
                        "ch_pos": 100,
                        "cv_pos_mb": 50,
                        "ch_pos_mb": 100,
                        "bg_overlay": "#000000",
                        "bg_opacity": 0,
                        "bg_content_cl": "#ffffff",
                        "bg_content_op": 0,
                        "content_radius": 0,
                        "box_square": false,
                        "content_pd_tb": 15,
                        "content_pd_lr": 15,
                        "content_pd_tb_tb": 15,
                        "content_pd_lr_tb": 15,
                        "content_pd_tb_mb": 10,
                        "content_pd_lr_mb": 10,
                        "border_bl": false,
                        "br_color": "#222222",
                        "br_bg": "#ffffff",
                        "br_opacity": 50,
                        "br_style": "solid",
                        "br_pd": 20,
                        "br_pd_mb": 10,
                        "time_animation": 1,
                        "animation_delay": 40
                    }
                },
                "b069b77f-6f77-41d8-839e-5f21d12217db": {
                    "type": "custom_text",
                    "settings": {
                        "text": "NEW SEASON",
                        "remove_br_tag": false,
                        "tag": "p",
                        "fontf": "inherit",
                        "text_cl": "#222222",
                        "text_fs": 18,
                        "text_lh": 0,
                        "text_fw": 500,
                        "text_ls": 0,
                        "text_mgb": 5,
                        "font_italic": false,
                        "text_shadow": false,
                        "hidden_mobile": false,
                        "text_fs_mb": 12,
                        "text_lh_mb": 0,
                        "text_ls_mb": 0,
                        "text_mgb_mobile": 5,
                        "animation": "fadeInUp"
                    }
                },
                "ff196d3a-1239-4855-9969-90603c8cb52d": {
                    "type": "custom_text",
                    "settings": {
                        "text": "Lookbook Collection",
                        "remove_br_tag": false,
                        "tag": "p",
                        "fontf": "inherit",
                        "text_cl": "#222222",
                        "text_fs": 55,
                        "text_lh": 55,
                        "text_fw": 600,
                        "text_ls": 0,
                        "text_mgb": 10,
                        "font_italic": false,
                        "text_shadow": false,
                        "hidden_mobile": false,
                        "text_fs_mb": 18,
                        "text_lh_mb": 18,
                        "text_ls_mb": 0,
                        "text_mgb_mobile": 10,
                        "animation": "fadeInUp"
                    }
                },
                "efa3ea83-c43c-48b4-85ed-90ed7642e2f7": {
                    "type": "custom_button",
                    "settings": {
                        "button_text": "Explore Now",
                        "button_link": "shopify://collections",
                        "target_link": "_self",
                        "fontf": "inherit",
                        "button_icon_w": 0,
                        "button_style": "default",
                        "button_effect": "sweep-to-top",
                        "pri_cl": "#222222",
                        "second_cl": "#ffffff",
                        "pri_cl_hover": "#56cfe1",
                        "second_cl_hover": "#ffffff",
                        "fsbutton": 14,
                        "fwbutton": 600,
                        "button_ls": 0,
                        "button_mh": 40,
                        "button_bdr": 0,
                        "button_pd_lr": 32,
                        "button_mgb": 0,
                        "hidden_mobile": false,
                        "button_icon_w_mb": 0,
                        "fsbutton_mb": 12,
                        "button_mh_mb": 36,
                        "button_pd_lr_mb": 22,
                        "button_ls_mb": 0,
                        "button_mgb_mb": 0,
                        "animation": "fadeInRight"
                    }
                },
                "164602873867eb3815-4": {
                    "type": "image_parent",
                    "settings": {
                        "image": "shopify://shop_images/2.jpg",
                        "image_position": "8",
                        "cl_img": "",
                        "link_img1": "",
                        "open_link": "_blank",
                        "animate_slide": "none",
                        "text_align": "start",
                        "text_align_mb": "center",
                        "content_width": "container",
                        "cv_pos": 50,
                        "ch_pos": 0,
                        "cv_pos_mb": 50,
                        "ch_pos_mb": 0,
                        "bg_overlay": "#000000",
                        "bg_opacity": 0,
                        "bg_content_cl": "#ffffff",
                        "bg_content_op": 0,
                        "content_radius": 0,
                        "box_square": false,
                        "content_pd_tb": 15,
                        "content_pd_lr": 15,
                        "content_pd_tb_tb": 15,
                        "content_pd_lr_tb": 15,
                        "content_pd_tb_mb": 10,
                        "content_pd_lr_mb": 10,
                        "border_bl": false,
                        "br_color": "#222222",
                        "br_bg": "#ffffff",
                        "br_opacity": 50,
                        "br_style": "solid",
                        "br_pd": 20,
                        "br_pd_mb": 10,
                        "time_animation": 1,
                        "animation_delay": 40
                    }
                },
                "164602873867eb3815-5": {
                    "type": "custom_text",
                    "settings": {
                        "text": "SUMMER SALE",
                        "remove_br_tag": false,
                        "tag": "p",
                        "fontf": "inherit",
                        "text_cl": "#222222",
                        "text_fs": 18,
                        "text_lh": 0,
                        "text_fw": 500,
                        "text_ls": 1,
                        "text_mgb": 5,
                        "font_italic": false,
                        "text_shadow": false,
                        "hidden_mobile": false,
                        "text_fs_mb": 12,
                        "text_lh_mb": 0,
                        "text_ls_mb": 0,
                        "text_mgb_mobile": 5,
                        "animation": "fadeInDown"
                    }
                },
                "164602873867eb3815-6": {
                    "type": "custom_text",
                    "settings": {
                        "text": "Save up to 70%",
                        "remove_br_tag": false,
                        "tag": "p",
                        "fontf": "inherit",
                        "text_cl": "#222222",
                        "text_fs": 55,
                        "text_lh": 55,
                        "text_fw": 600,
                        "text_ls": 0,
                        "text_mgb": 10,
                        "font_italic": false,
                        "text_shadow": false,
                        "hidden_mobile": false,
                        "text_fs_mb": 28,
                        "text_lh_mb": 28,
                        "text_ls_mb": 0,
                        "text_mgb_mobile": 10,
                        "animation": "fadeInDown"
                    }
                },
                "164602873867eb3815-7": {
                    "type": "custom_button",
                    "settings": {
                        "button_text": "Explore Now",
                        "button_link": "shopify://collections",
                        "target_link": "_self",
                        "fontf": "inherit",
                        "button_icon_w": 0,
                        "button_style": "default",
                        "button_effect": "sweep-to-top",
                        "pri_cl": "#222222",
                        "second_cl": "#ffffff",
                        "pri_cl_hover": "#56cfe1",
                        "second_cl_hover": "#ffffff",
                        "fsbutton": 14,
                        "fwbutton": 600,
                        "button_ls": 0,
                        "button_mh": 40,
                        "button_bdr": 0,
                        "button_pd_lr": 32,
                        "button_mgb": 0,
                        "hidden_mobile": false,
                        "button_icon_w_mb": 0,
                        "fsbutton_mb": 12,
                        "button_mh_mb": 36,
                        "button_pd_lr_mb": 22,
                        "button_ls_mb": 0,
                        "button_mgb_mb": 0,
                        "animation": "fadeInDown"
                    }
                }
            },
            "block_order": [
                "164602873867eb3815-0",
                "164602873867eb3815-1",
                "164602873867eb3815-2",
                "390cb6f8-c5fc-44ed-badf-074a03818e5a",
                "aca13d02-fdca-4dd7-b455-cc71ffabb08a",
                "b069b77f-6f77-41d8-839e-5f21d12217db",
                "ff196d3a-1239-4855-9969-90603c8cb52d",
                "efa3ea83-c43c-48b4-85ed-90ed7642e2f7",
                "164602873867eb3815-4",
                "164602873867eb3815-5",
                "164602873867eb3815-6",
                "164602873867eb3815-7"
            ],
            "settings": {
                "se_height": "t4s_ratio_cuspx",
                "custom_dk": true,
                "height_dk": 700,
                "custom_tb": true,
                "height_tb": 500,
                "custom_mb": true,
                "height_mb": 250,
                "eff": "fade",
                "loop": true,
                "au_time": 8.5,
                "au_hover": true,
                "nav_btn": false,
                "btn_vi": "hover",
                "btn_owl": "default",
                "btn_icon": "0",
                "btn_shape": "none",
                "btn_cl": "dark",
                "btn_size": "small",
                "btn_hidden_mobile": false,
                "nav_dot": true,
                "dot_owl": "default",
                "dots_position": "default",
                "dots_cl": "dark",
                "dots_round": true,
                "dots_space": 10,
                "dots_hidden_mobile": false,
                "layout": "t4s-container-fluid",
                "cl_bg": "",
                "cl_bg_gradient": "",
                "mg": ",,30px,",
                "pd": "",
                "mg_tb": ",50px",
                "pd_tb": "",
                "mg_mb": ",,10px,",
                "pd_mb": "",
                "use_cus_css": false,
                "code_cus_css": ".SectionID {\nbackground-color:red\n}"
            }
        },
        "164603504020ce33da": {
            "type": "collections-list-manual",
            "blocks": {
                "1646035040fee424aa-0": {
                    "type": "collection_item",
                    "settings": {
                        "collection": "women",
                        "collection_title": "Women",
                        "collection_link": ""
                    }
                },
                "1646035040fee424aa-1": {
                    "type": "collection_item",
                    "settings": {
                        "collection": "accessories",
                        "collection_title": "Accessories",
                        "collection_link": ""
                    }
                },
                "1646035040fee424aa-2": {
                    "type": "collection_item",
                    "settings": {
                        "collection": "shoes",
                        "collection_title": "Footwear",
                        "collection_link": ""
                    }
                },
                "fb81d2b5-38b4-4322-93fc-cd722f8758f3": {
                    "type": "collection_item",
                    "settings": {
                        "collection": "watch",
                        "collection_title": "Watches",
                        "collection_link": ""
                    }
                }
            },
            "block_order": [
                "1646035040fee424aa-0",
                "1646035040fee424aa-1",
                "1646035040fee424aa-2",
                "fb81d2b5-38b4-4322-93fc-cd722f8758f3"
            ],
            "settings": {
                "design_heading": "2",
                "heading_align": "t4s-text-center",
                "top_heading": "",
                "icon_heading": "gem",
                "top_subheading": "",
                "tophead_mb": 30,
                "collection_des": "1",
                "title_cl": "#ffffff",
                "title_cl_hover": "#222222",
                "subtitle_cl": "#878787",
                "count_cl": "#222222",
                "border_cl": "#e5e5e5",
                "collection_subtitle": "Products",
                "open_link": "_self",
                "space_bottom": 20,
                "space_bottom_mb": 10,
                "border": false,
                "item_rd": 0,
                "item_rd_unit": "%",
                "img_effect": "zoom",
                "b_effect": "none",
                "image_position": "7",
                "coll_layout": "1",
                "layout": "t4s-container-wrap",
                "cl_bg": "",
                "cl_bg_gradient": "",
                "mg": ",,110px,",
                "pd": "",
                "mg_tb": "",
                "pd_tb": "",
                "mg_mb": ",,50px,",
                "pd_mb": "",
                "use_cus_css": false,
                "code_cus_css": ".SectionID {\nbackground-color:red\n}"
            }
        },
        "16460362229768af39": {
            "type": "banner",
            "blocks": {
                "164603622209bd8d44-0": {
                    "type": "b_item",
                    "settings": {
                        "custom_dk": true,
                        "height_dk": 600,
                        "custom_tb": true,
                        "height_tb": 400,
                        "custom_mb": true,
                        "height_mb": 300,
                        "b_link": "",
                        "open_link": "_self",
                        "img_effect": "rotate",
                        "b_effect": "none",
                        "col_dk": "6",
                        "col_tb": "6",
                        "col_mb": "12",
                        "content_align": "center",
                        "content_align_mobile": "center",
                        "content_width": "auto",
                        "cv_pos": 50,
                        "ch_pos": 50,
                        "cv_pos_mb": 50,
                        "ch_pos_mb": 50,
                        "bg_overlay": "#000000",
                        "bg_opacity": 0,
                        "bg_content_bl": false,
                        "bg_content_cl": "#ffffff",
                        "bg_content_op": 50,
                        "content_pd_tb": 20,
                        "content_pd_lr": 20,
                        "content_pd_tb_mb": 10,
                        "content_pd_lr_mb": 10,
                        "border_bl": false,
                        "br_color": "#222222",
                        "br_bg": "#ffffff",
                        "br_opacity": 50,
                        "br_style": "solid",
                        "br_pd": 20,
                        "br_pd_mb": 10,
                        "time_animation": 1,
                        "animation_delay": 40
                    }
                },
                "164603622209bd8d44-1": {
                    "type": "custom_text",
                    "settings": {
                        "text": "LOOKBOOK 2023",
                        "remove_br_tag": false,
                        "tag": "p",
                        "fontf": "inherit",
                        "text_cl": "#ffffff",
                        "text_fs": 24,
                        "text_lh": 24,
                        "text_fw": 500,
                        "text_ls": 0,
                        "font_italic": false,
                        "text_shadow": false,
                        "text_mgb": 5,
                        "hidden_mobile": false,
                        "text_fs_mb": 24,
                        "text_lh_mb": 24,
                        "text_ls_mb": 0,
                        "text_mgb_mobile": 5,
                        "animation": "fadeInDownBig"
                    }
                },
                "164603622209bd8d44-2": {
                    "type": "custom_text",
                    "settings": {
                        "text": "MAKE LOVE THIS LOOK",
                        "remove_br_tag": false,
                        "tag": "p",
                        "fontf": "inherit",
                        "text_cl": "#ffffff",
                        "text_fs": 14,
                        "text_lh": 24,
                        "text_fw": 600,
                        "text_ls": 0,
                        "font_italic": false,
                        "text_shadow": false,
                        "text_mgb": 5,
                        "hidden_mobile": false,
                        "text_fs_mb": 14,
                        "text_lh_mb": 24,
                        "text_ls_mb": 0,
                        "text_mgb_mobile": 10,
                        "animation": "fadeInDownBig"
                    }
                },
                "164603622209bd8d44-3": {
                    "type": "b_item",
                    "settings": {
                        "custom_dk": true,
                        "height_dk": 600,
                        "custom_tb": true,
                        "height_tb": 400,
                        "custom_mb": true,
                        "height_mb": 300,
                        "b_link": "",
                        "open_link": "_self",
                        "img_effect": "translateToTop",
                        "b_effect": "none",
                        "col_dk": "6",
                        "col_tb": "6",
                        "col_mb": "12",
                        "content_align": "center",
                        "content_align_mobile": "center",
                        "content_width": "auto",
                        "cv_pos": 50,
                        "ch_pos": 50,
                        "cv_pos_mb": 50,
                        "ch_pos_mb": 50,
                        "bg_overlay": "#000000",
                        "bg_opacity": 0,
                        "bg_content_bl": false,
                        "bg_content_cl": "#ffffff",
                        "bg_content_op": 50,
                        "content_pd_tb": 20,
                        "content_pd_lr": 20,
                        "content_pd_tb_mb": 10,
                        "content_pd_lr_mb": 10,
                        "border_bl": false,
                        "br_color": "#222222",
                        "br_bg": "#ffffff",
                        "br_opacity": 50,
                        "br_style": "solid",
                        "br_pd": 20,
                        "br_pd_mb": 10,
                        "time_animation": 1,
                        "animation_delay": 40
                    }
                },
                "164603622209bd8d44-4": {
                    "type": "custom_text",
                    "settings": {
                        "text": "SUMMER SALE",
                        "remove_br_tag": false,
                        "tag": "p",
                        "fontf": "inherit",
                        "text_cl": "#ffffff",
                        "text_fs": 18,
                        "text_lh": 24,
                        "text_fw": 500,
                        "text_ls": 0,
                        "font_italic": false,
                        "text_shadow": false,
                        "text_mgb": 5,
                        "hidden_mobile": false,
                        "text_fs_mb": 18,
                        "text_lh_mb": 24,
                        "text_ls_mb": 0,
                        "text_mgb_mobile": 5,
                        "animation": "fadeInUpBig"
                    }
                },
                "164603622209bd8d44-5": {
                    "type": "custom_text",
                    "settings": {
                        "text": "UP TO 70%",
                        "remove_br_tag": false,
                        "tag": "p",
                        "fontf": "inherit",
                        "text_cl": "#ffffff",
                        "text_fs": 50,
                        "text_lh": 50,
                        "text_fw": 600,
                        "text_ls": 0,
                        "font_italic": false,
                        "text_shadow": false,
                        "text_mgb": 0,
                        "hidden_mobile": false,
                        "text_fs_mb": 50,
                        "text_lh_mb": 50,
                        "text_ls_mb": 0,
                        "text_mgb_mobile": 0,
                        "animation": "fadeInUpBig"
                    }
                }
            },
            "block_order": [
                "164603622209bd8d44-0",
                "164603622209bd8d44-1",
                "164603622209bd8d44-2",
                "164603622209bd8d44-3",
                "164603622209bd8d44-4",
                "164603622209bd8d44-5"
            ],
            "settings": {
                "image_ratio": "ratioadapt",
                "image_size": "cover",
                "image_position": "8",
                "space_h_item": "30",
                "space_v_item": "30",
                "space_h_item_mb": "10",
                "space_v_item_mb": "10",
                "hidden_content_first": false,
                "layout": "t4s-container-wrap",
                "cl_bg": "",
                "cl_bg_gradient": "",
                "mg": ",,110px,",
                "pd": "",
                "mg_tb": "",
                "pd_tb": "",
                "mg_mb": ",,30px,",
                "pd_mb": "",
                "use_cus_css": false,
                "code_cus_css": ".SectionID {\nbackground-color:red\n}"
            }
        },
        "164603686708691778": {
            "type": "featured-collection",
            "settings": {
                "design_heading": "2",
                "heading_align": "t4s-text-center",
                "top_heading": "BEST SELLER",
                "icon_heading": "gem",
                "top_subheading": "Top sale in this week",
                "tophead_mb": 30,
                "head_btn_label": "View All",
                "head_btn_url": "",
                "head_btn_icon": false,
                "head_btn_style": "default",
                "head_btn_size": "medium",
                "head_btn_cl": "dark",
                "head_btn_effect": "default",
                "head_btn_hidden_mb": false,
                "collection": "default-2",
                "product_des": "1",
                "show_vendor": false,
                "use_cdt": false,
                "image_ratio": "rationt",
                "image_size": "cover",
                "image_position": "8",
                "content_align": "default",
                "limit": 8,
                "col_dk": "4",
                "col_tb": "2",
                "col_mb": "2",
                "space_h_item": "30",
                "space_v_item": "30",
                "space_h_item_mb": "10",
                "space_v_item_mb": "10",
                "layout_des": "1",
                "loop": true,
                "au_time": 0,
                "au_hover": true,
                "center_slide": false,
                "nav_btn": true,
                "btns_pos": "default",
                "btn_vi": "hover",
                "btn_owl": "default",
                "btn_shape": "none",
                "btn_cl": "dark",
                "btn_size": "small",
                "btn_hidden_mobile": false,
                "nav_dot": true,
                "dot_owl": "default",
                "dots_cl": "dark",
                "dots_round": false,
                "dots_space": 10,
                "dots_hidden_mobile": false,
                "use_pagination": "none",
                "enable_bar_lm": true,
                "btn_icon": false,
                "button_style": "default",
                "btns_size": "medium",
                "btns_cl": "default",
                "button_effect": "fade",
                "btn_pos": "t4s-text-center",
                "layout": "t4s-container-wrap",
                "cl_bg": "",
                "cl_bg_gradient": "",
                "mg": ",,85px,",
                "pd": "",
                "mg_tb": "",
                "pd_tb": "",
                "mg_mb": ",,25px,",
                "pd_mb": "",
                "use_cus_css": false,
                "code_cus_css": ".SectionID {\nbackground-color:red\n}"
            }
        },
        "16460368750174c54a": {
            "type": "blog-post",
            "settings": {
                "design_heading": "2",
                "heading_align": "t4s-text-center",
                "top_heading": "LATES FROM BLOG",
                "bt_lb": "Button label",
                "icon_heading": "gem",
                "top_subheading": "The freshest and most exciting news",
                "tophead_mb": 30,
                "blog": "fashion",
                "post_des": "1",
                "show_cate": false,
                "show_tags": false,
                "show_cnt": true,
                "show_au": true,
                "show_dt": true,
                "show_cm": false,
                "show_rm": false,
                "show_irm": false,
                "date": "date",
                "content_align": "t4s-text-start",
                "limit": 3,
                "img_effect": "translateToTop",
                "b_effect": "border-run",
                "image_ratio": "ratio4_3",
                "image_size": "cover",
                "image_position": "8",
                "col_dk": "3",
                "col_tb": "2",
                "col_mb": "1",
                "space_h_item": "30",
                "space_v_item": "30",
                "space_h_item_mb": "10",
                "space_v_item_mb": "10",
                "layout_des": "2",
                "loop": false,
                "au_time": 0,
                "au_hover": true,
                "nav_btn": true,
                "btn_vi": "always",
                "btn_owl": "outline",
                "btn_shape": "round",
                "btn_cl": "dark",
                "btn_size": "small",
                "btn_hidden_mobile": false,
                "nav_dot": false,
                "dot_owl": "default",
                "dots_cl": "dark",
                "dots_round": false,
                "dots_space": 10,
                "dots_hidden_mobile": false,
                "use_pagination": "none",
                "enable_bar_lm": true,
                "btn_icon": true,
                "button_style": "outline",
                "btns_size": "medium",
                "btns_cl": "dark",
                "button_effect": "fade",
                "btn_pos": "t4s-text-center",
                "layout": "t4s-container-wrap",
                "cl_bg": "",
                "cl_bg_gradient": "",
                "mg": ",,105px,",
                "pd": "",
                "mg_tb": "",
                "pd_tb": "",
                "mg_mb": ",,30px,",
                "pd_mb": "",
                "use_cus_css": false,
                "code_cus_css": ".SectionID {\nbackground-color:red\n}"
            }
        },
        "1646209857bf69e76a": {
            "type": "instagram-shop",
            "blocks": {
                "1646209857c01ad255-1": {
                    "type": "img",
                    "settings": {
                        "url": "",
                        "product_1": "ribbed-stripe-top",
                        "pos_t_1": 39,
                        "pos_l_1": 38,
                        "pos_popup_1": "top",
                        "cl_pin_1": "dark",
                        "product_2": "",
                        "pos_t_2": 50,
                        "pos_l_2": 50,
                        "pos_popup_2": "top",
                        "cl_pin_2": "dark",
                        "product_3": "",
                        "pos_t_3": 50,
                        "pos_l_3": 50,
                        "pos_popup_3": "top",
                        "cl_pin_3": "light"
                    }
                },
                "1646209857c01ad255-4": {
                    "type": "img",
                    "settings": {
                        "url": "",
                        "product_1": "wide-fit-dusty",
                        "pos_t_1": 74,
                        "pos_l_1": 37,
                        "pos_popup_1": "top",
                        "cl_pin_1": "dark",
                        "product_2": "",
                        "pos_t_2": 50,
                        "pos_l_2": 50,
                        "pos_popup_2": "top",
                        "cl_pin_2": "dark",
                        "product_3": "",
                        "pos_t_3": 50,
                        "pos_l_3": 50,
                        "pos_popup_3": "top",
                        "cl_pin_3": "light"
                    }
                },
                "1646209857c01ad255-2": {
                    "type": "img",
                    "settings": {
                        "url": "",
                        "product_1": "short-sleeved-hoodie",
                        "pos_t_1": 30,
                        "pos_l_1": 60,
                        "pos_popup_1": "top",
                        "cl_pin_1": "dark",
                        "product_2": "retro-lace-up-sneakers",
                        "pos_t_2": 95,
                        "pos_l_2": 43,
                        "pos_popup_2": "top",
                        "cl_pin_2": "dark",
                        "product_3": "",
                        "pos_t_3": 50,
                        "pos_l_3": 50,
                        "pos_popup_3": "top",
                        "cl_pin_3": "light"
                    }
                },
                "1646209857c01ad255-3": {
                    "type": "img",
                    "settings": {
                        "url": "",
                        "product_1": "striped-long-sleeve-top",
                        "pos_t_1": 50,
                        "pos_l_1": 50,
                        "pos_popup_1": "top",
                        "cl_pin_1": "dark",
                        "product_2": "",
                        "pos_t_2": 50,
                        "pos_l_2": 50,
                        "pos_popup_2": "top",
                        "cl_pin_2": "dark",
                        "product_3": "",
                        "pos_t_3": 50,
                        "pos_l_3": 50,
                        "pos_popup_3": "top",
                        "cl_pin_3": "light"
                    }
                },
                "1646209857c01ad255-5": {
                    "type": "img",
                    "settings": {
                        "url": "",
                        "product_1": "long-sleeve-tie-front-top",
                        "pos_t_1": 34,
                        "pos_l_1": 74,
                        "pos_popup_1": "top",
                        "cl_pin_1": "dark",
                        "product_2": "",
                        "pos_t_2": 50,
                        "pos_l_2": 50,
                        "pos_popup_2": "top",
                        "cl_pin_2": "dark",
                        "product_3": "",
                        "pos_t_3": 50,
                        "pos_l_3": 50,
                        "pos_popup_3": "top",
                        "cl_pin_3": "light"
                    }
                },
                "1646209857c01ad255-0": {
                    "type": "img",
                    "settings": {
                        "url": "",
                        "product_1": "high-waist-straight-leg",
                        "pos_t_1": 65,
                        "pos_l_1": 50,
                        "pos_popup_1": "top",
                        "cl_pin_1": "dark",
                        "product_2": "",
                        "pos_t_2": 50,
                        "pos_l_2": 50,
                        "pos_popup_2": "top",
                        "cl_pin_2": "dark",
                        "product_3": "",
                        "pos_t_3": 50,
                        "pos_l_3": 50,
                        "pos_popup_3": "top",
                        "cl_pin_3": "light"
                    }
                },
                "0051dfa6-432d-4e58-b343-7e0752f68b80": {
                    "type": "img",
                    "settings": {
                        "url": "",
                        "product_1": "high-waist-skinny-jean",
                        "pos_t_1": 50,
                        "pos_l_1": 56,
                        "pos_popup_1": "top",
                        "cl_pin_1": "dark",
                        "product_2": "",
                        "pos_t_2": 50,
                        "pos_l_2": 50,
                        "pos_popup_2": "top",
                        "cl_pin_2": "dark",
                        "product_3": "",
                        "pos_t_3": 50,
                        "pos_l_3": 50,
                        "pos_popup_3": "top",
                        "cl_pin_3": "light"
                    }
                },
                "ae083dc7-4338-494b-8f4b-e764a38d2a97": {
                    "type": "img",
                    "settings": {
                        "url": "",
                        "product_1": "woleen-tee",
                        "pos_t_1": 45,
                        "pos_l_1": 36,
                        "pos_popup_1": "top",
                        "cl_pin_1": "dark",
                        "product_2": "",
                        "pos_t_2": 50,
                        "pos_l_2": 50,
                        "pos_popup_2": "top",
                        "cl_pin_2": "dark",
                        "product_3": "",
                        "pos_t_3": 50,
                        "pos_l_3": 50,
                        "pos_popup_3": "top",
                        "cl_pin_3": "light"
                    }
                }
            },
            "block_order": [
                "1646209857c01ad255-1",
                "1646209857c01ad255-4",
                "1646209857c01ad255-2",
                "1646209857c01ad255-3",
                "1646209857c01ad255-5",
                "1646209857c01ad255-0",
                "0051dfa6-432d-4e58-b343-7e0752f68b80",
                "ae083dc7-4338-494b-8f4b-e764a38d2a97"
            ],
            "settings": {
                "design_heading": "2",
                "heading_align": "t4s-text-center",
                "top_heading": "@ FOLLOW US ON INSTAGRAM",
                "icon_heading": "gem",
                "top_subheading": "",
                "tophead_mb": 20,
                "layout_des": "2",
                "pr_pin_des": "6",
                "open_link": "_self",
                "space": "0",
                "space_mb": "0",
                "col_dk": "6",
                "col_tb": "3",
                "col_mb": "2",
                "image_ratio": "ratio1_1",
                "image_size": "cover",
                "image_position": "8",
                "image_bdr": 0,
                "loop": false,
                "au_time": 0,
                "au_hover": true,
                "nav_btn": true,
                "btn_vi": "always",
                "btn_owl": "outline",
                "btn_shape": "round",
                "btn_cl": "default",
                "btn_size": "small",
                "btn_hidden_mobile": false,
                "nav_dot": false,
                "dot_owl": "default",
                "dots_cl": "dark",
                "dots_round": false,
                "dots_space": 10,
                "dots_hidden_mobile": false,
                "layout": "t4s-container-fluid",
                "cl_bg": "",
                "cl_bg_gradient": "",
                "mg": "",
                "pd": "",
                "mg_tb": "",
                "pd_tb": "",
                "mg_mb": "",
                "pd_mb": ""
            }
        },
        "164620932069107a3a": {
            "type": "shipping",
            "blocks": {
                "164620932047c7965f-0": {
                    "type": "shipping",
                    "settings": {
                        "icon_themes": "car",
                        "icon": "las la-shipping-fast",
                        "title": "FREE SHIPPING",
                        "text": "<p>Free shipping on all US order or order above $100</p>",
                        "html": ""
                    }
                },
                "164620932047c7965f-1": {
                    "type": "shipping",
                    "settings": {
                        "icon_themes": "help",
                        "icon": "las la-life-ring",
                        "title": "SUPPORT 24/7",
                        "text": "<p>Contact us 24 hours a day, 7 days a week</p>",
                        "html": ""
                    }
                },
                "164620932047c7965f-2": {
                    "type": "shipping",
                    "settings": {
                        "icon_themes": "refesh",
                        "icon": "las la-undo-alt",
                        "title": "30 DAYS RETURN",
                        "text": "<p>Simply return it within 30 days for an exchange.</p>",
                        "html": ""
                    }
                },
                "164620932047c7965f-3": {
                    "type": "shipping",
                    "settings": {
                        "icon_themes": "door-lock",
                        "icon": "las la-lock",
                        "title": "100% PAYMENT SECURE",
                        "text": "<p>We ensure secure payment with PEV</p>",
                        "html": ""
                    }
                }
            },
            "block_order": [
                "164620932047c7965f-0",
                "164620932047c7965f-1",
                "164620932047c7965f-2",
                "164620932047c7965f-3"
            ],
            "settings": {
                "design_heading": "2",
                "heading_align": "t4s-text-center",
                "top_heading": "",
                "icon_heading": "gem",
                "top_subheading": "",
                "tophead_mb": 30,
                "content_align": "text-start",
                "align_vertical": false,
                "design_padding": "1",
                "source": "themes_icon",
                "icon_des": "default",
                "icon_size": "medium",
                "col_dk": "4",
                "col_tb": "2",
                "col_mb": "1",
                "border": false,
                "cl_bd": "#dddddd",
                "cl_ic": "#9e9e9e",
                "cl_hd": "#222222",
                "cl_cot": "#878787",
                "bg_item": "#ffffff",
                "hd_fs": 16,
                "hd_fw": 500,
                "txt_fs": 13,
                "space_h_item": "30",
                "space_v_item": "30",
                "space_h_item_mb": "30",
                "space_v_item_mb": "20",
                "carousel_mobile": true,
                "dots_cl": "dark",
                "dots_round": true,
                "layout": "t4s-container-wrap",
                "cl_bg": "",
                "cl_bg_gradient": "",
                "mg": "",
                "pd": "55px,,25px,",
                "mg_tb": ",,50px,",
                "pd_tb": "",
                "mg_mb": "",
                "pd_mb": "55px,,25px,",
                "use_cus_css": false,
                "code_cus_css": ".SectionID {\nbackground-color:red\n}"
            }
        }
    },
    "order": [
        "1646028739ae283905",
        "164603504020ce33da",
        "16460362229768af39",
        "164603686708691778",
        "16460368750174c54a",
        "1646209857bf69e76a",
        "164620932069107a3a"
    ]
}
