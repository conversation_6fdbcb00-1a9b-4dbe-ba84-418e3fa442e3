/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "heading": {
      "type": "heading-template",
      "blocks": {
        "19597bfc-5c00-4420-aec0-d72dde32a1e7": {
          "type": "1",
          "settings": {
            "heading": "About us",
            "fontf": "inherit",
            "text_cl": "#ffffff",
            "text_fs": 20,
            "text_lh": 20,
            "text_fw": 500,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 10,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 20,
            "text_ls_mb": 0,
            "text_mgb_mobile": 10
          }
        },
        "c1ab6fbb-940f-4eb4-b374-ce3370b4382f": {
          "type": "2",
          "settings": {
            "content": "<p>Follow your passion, and success will follow you<\/p>",
            "fontf": "inherit",
            "text_cl": "#ffffff",
            "text_fs": 14,
            "text_lh": 24,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 0,
            "hidden_mobile": false,
            "text_fs_mb": 14,
            "text_lh_mb": 24,
            "text_ls_mb": 0,
            "text_mgb_mobile": 0
          }
        }
      },
      "block_order": [
        "19597bfc-5c00-4420-aec0-d72dde32a1e7",
        "c1ab6fbb-940f-4eb4-b374-ce3370b4382f"
      ],
      "settings": {
        "image": "shopify:\/\/shop_images\/shop-banner.jpg",
        "parallax": false,
        "color": "#000000",
        "overlay": 54,
        "padding": 50,
        "paddingmb": 50,
        "mg_b": 60,
        "mg_bmb": 30,
        "content_align": "t4s-text-center",
        "bg_pos": "center top",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "main": {
      "type": "main-page",
      "settings": {
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": ""
      }
    },
    "sidebar-page": {
      "type": "sidebar-page",
      "blocks": {
        "f69681ed-b75e-42de-be58-fdf73b3b7e08": {
          "type": "blog_cate",
          "settings": {
            "title": "Blog categories",
            "cat_link_list": "",
            "count": true
          }
        }
      },
      "block_order": [
        "f69681ed-b75e-42de-be58-fdf73b3b7e08"
      ],
      "disabled": true,
      "settings": {
        "enable_drawer": false,
        "space": 50,
        "size": "medium",
        "br_style": "none",
        "brcolor": "#222222",
        "sidebar_bdr": 5,
        "sidebar_pd": 10
      }
    },
    "1652061282d05ea8a5": {
      "type": "custom-section",
      "blocks": {
        "16520612827b9f51d5-0": {
          "type": "bl_col",
          "settings": {
            "col_dk": "6",
            "col_tb": "6",
            "col_mb": "12",
            "align_vertical": false,
            "content_align": "start",
            "content_align_mobile": "start",
            "padding_inner": ",,40px,",
            "padding_inner_mb": ",,20px,",
            "bg_cl": "rgba(0,0,0,0)"
          }
        },
        "16520612827b9f51d5-1": {
          "type": "custom_text",
          "settings": {
            "text": "OUR MISSION",
            "remove_br_tag": false,
            "fontf": "1",
            "text_cl": "#222222",
            "text_fs": 20,
            "text_lh": 28,
            "text_fw": 600,
            "text_ls": 0,
            "font_italic": false,
            "text_shadow": false,
            "text_mgb": 10,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 28,
            "text_ls_mb": 0,
            "text_mgb_mobile": 10
          }
        },
        "16520612827b9f51d5-2": {
          "type": "custom_text",
          "settings": {
            "text": "Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, <em>totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae <\/em> vitae dicta sunt explicabo nemo enim ipsam.",
            "remove_br_tag": true,
            "fontf": "inherit",
            "text_cl": "#878787",
            "text_fs": 14,
            "text_lh": 24,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "text_shadow": false,
            "text_mgb": 0,
            "hidden_mobile": false,
            "text_fs_mb": 14,
            "text_lh_mb": 24,
            "text_ls_mb": 0,
            "text_mgb_mobile": 0
          }
        },
        "16520612827b9f51d5-3": {
          "type": "bl_col",
          "settings": {
            "col_dk": "6",
            "col_tb": "6",
            "col_mb": "12",
            "align_vertical": false,
            "content_align": "start",
            "content_align_mobile": "start",
            "padding_inner": ",,40px,",
            "padding_inner_mb": ",,20px,",
            "bg_cl": "rgba(0,0,0,0)"
          }
        },
        "2a09a4c8-b8db-4637-b944-87d5e190d9b2": {
          "type": "custom_text",
          "settings": {
            "text": "OUR STORIES",
            "remove_br_tag": true,
            "fontf": "1",
            "text_cl": "#222222",
            "text_fs": 20,
            "text_lh": 28,
            "text_fw": 600,
            "text_ls": 0,
            "font_italic": false,
            "text_shadow": false,
            "text_mgb": 10,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 28,
            "text_ls_mb": 0,
            "text_mgb_mobile": 10
          }
        },
        "dbb9472b-71cc-423e-87c4-f311cfcf2f6a": {
          "type": "custom_text",
          "settings": {
            "text": "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
            "remove_br_tag": true,
            "fontf": "inherit",
            "text_cl": "#878787",
            "text_fs": 14,
            "text_lh": 24,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "text_shadow": false,
            "text_mgb": -2,
            "hidden_mobile": false,
            "text_fs_mb": 14,
            "text_lh_mb": 20,
            "text_ls_mb": 0,
            "text_mgb_mobile": 0
          }
        }
      },
      "block_order": [
        "16520612827b9f51d5-0",
        "16520612827b9f51d5-1",
        "16520612827b9f51d5-2",
        "16520612827b9f51d5-3",
        "2a09a4c8-b8db-4637-b944-87d5e190d9b2",
        "dbb9472b-71cc-423e-87c4-f311cfcf2f6a"
      ],
      "settings": {
        "space_h_item": "30",
        "space_v_item": "30",
        "space_h_item_mb": "10",
        "space_v_item_mb": "10",
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "",
        "pd": "",
        "mg_mb": "",
        "pd_mb": "",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "1652061282d05ea8a6": {
      "type": "custom-section",
      "blocks": {
        "16520612827b9f51d5-0": {
          "type": "bl_col",
          "settings": {
            "col_dk": "6",
            "col_tb": "6",
            "col_mb": "12",
            "align_vertical": false,
            "content_align": "start",
            "content_align_mobile": "start",
            "padding_inner": ",,40px,",
            "padding_inner_mb": ",,20px,",
            "bg_cl": "rgba(0,0,0,0)"
          }
        },
        "16520612827b9f51d5-1": {
          "type": "custom_text",
          "settings": {
            "text": "OUR APPROACH",
            "remove_br_tag": false,
            "fontf": "1",
            "text_cl": "#222222",
            "text_fs": 20,
            "text_lh": 28,
            "text_fw": 600,
            "text_ls": 0,
            "font_italic": false,
            "text_shadow": false,
            "text_mgb": 10,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 30,
            "text_ls_mb": 0,
            "text_mgb_mobile": 10
          }
        },
        "16520612827b9f51d5-2": {
          "type": "custom_text",
          "settings": {
            "text": "Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit,<em> sed quia non numquam eius modi tempora incidunt ut labore <\/em>et dolore magnam aliquam quaerat voluptatem.",
            "remove_br_tag": true,
            "fontf": "inherit",
            "text_cl": "#878787",
            "text_fs": 14,
            "text_lh": 24,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "text_shadow": false,
            "text_mgb": 0,
            "hidden_mobile": false,
            "text_fs_mb": 14,
            "text_lh_mb": 20,
            "text_ls_mb": 0,
            "text_mgb_mobile": 0
          }
        },
        "16520612827b9f51d5-3": {
          "type": "bl_col",
          "settings": {
            "col_dk": "6",
            "col_tb": "6",
            "col_mb": "12",
            "align_vertical": false,
            "content_align": "start",
            "content_align_mobile": "start",
            "padding_inner": ",,40px,",
            "padding_inner_mb": ",,40px,",
            "bg_cl": "rgba(0,0,0,0)"
          }
        },
        "2a09a4c8-b8db-4637-b944-87d5e190d9b2": {
          "type": "custom_text",
          "settings": {
            "text": "OUR PHILOSOPHY",
            "remove_br_tag": true,
            "fontf": "1",
            "text_cl": "#222222",
            "text_fs": 20,
            "text_lh": 28,
            "text_fw": 600,
            "text_ls": 0,
            "font_italic": false,
            "text_shadow": false,
            "text_mgb": 10,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 24,
            "text_ls_mb": 0,
            "text_mgb_mobile": 10
          }
        },
        "dbb9472b-71cc-423e-87c4-f311cfcf2f6a": {
          "type": "custom_text",
          "settings": {
            "text": "Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur? Quis nostrum exercitationem ullam.",
            "remove_br_tag": true,
            "fontf": "inherit",
            "text_cl": "#878787",
            "text_fs": 14,
            "text_lh": 24,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "text_shadow": false,
            "text_mgb": -2,
            "hidden_mobile": false,
            "text_fs_mb": 14,
            "text_lh_mb": 20,
            "text_ls_mb": 0,
            "text_mgb_mobile": 0
          }
        }
      },
      "block_order": [
        "16520612827b9f51d5-0",
        "16520612827b9f51d5-1",
        "16520612827b9f51d5-2",
        "16520612827b9f51d5-3",
        "2a09a4c8-b8db-4637-b944-87d5e190d9b2",
        "dbb9472b-71cc-423e-87c4-f311cfcf2f6a"
      ],
      "settings": {
        "space_h_item": "30",
        "space_v_item": "30",
        "space_h_item_mb": "10",
        "space_v_item_mb": "10",
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "",
        "pd": "",
        "mg_mb": "",
        "pd_mb": "",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "1652061697dd10b7fc": {
      "type": "quote",
      "settings": {
        "design_heading": "1",
        "heading_align": "t4s-text-center",
        "top_heading": "",
        "icon_heading": "las la-gem",
        "top_subheading": "",
        "tophead_mb": 30,
        "content_quote": "<p><em>I am text block. Click edit button to change this text. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo. Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur? Quis nostrum exercitationem ullam.<\/em><\/p>",
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": ",,30px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": "",
        "use_cus_css": false,
        "code_cus_css": ""
      }
    },
    "1652060940154cd301": {
      "type": "our_team",
      "blocks": {
        "165206094067bbb516-0": {
          "type": "member",
          "settings": {
            "image": "shopify:\/\/shop_images\/mem-01.jpg",
            "name": "Lisa John",
            "position": "Fashion Design",
            "social_facebook_link": "https:\/\/facebook.com\/",
            "social_twitter_link": "https:\/\/twitter.com",
            "social_instagram_link": "https:\/\/instagram.com",
            "social_dribbble_link": "",
            "social_behance_link": "https:\/\/behance.net\/",
            "social_tiktok_link": ""
          }
        },
        "165206094067bbb516-1": {
          "type": "member",
          "settings": {
            "image": "shopify:\/\/shop_images\/mem-02.jpg",
            "name": "Jane Doe",
            "position": "Director",
            "social_facebook_link": "https:\/\/facebook.com\/",
            "social_twitter_link": "https:\/\/twitter.com\/",
            "social_instagram_link": "https:\/\/instagram.com",
            "social_dribbble_link": "https:\/\/dribbble.com",
            "social_behance_link": "",
            "social_tiktok_link": ""
          }
        },
        "165206094067bbb516-2": {
          "type": "member",
          "settings": {
            "image": "shopify:\/\/shop_images\/mem-03.jpg",
            "name": "Cartherin Forres",
            "position": "Marketing Director",
            "social_facebook_link": "https:\/\/facebook.com\/",
            "social_twitter_link": "https:\/\/twitter.com",
            "social_instagram_link": "https:\/\/instagram.com",
            "social_dribbble_link": "",
            "social_behance_link": "",
            "social_tiktok_link": "https:\/\/tiktok.com\/"
          }
        }
      },
      "block_order": [
        "165206094067bbb516-0",
        "165206094067bbb516-1",
        "165206094067bbb516-2"
      ],
      "settings": {
        "design_heading": "1",
        "heading_align": "t4s-text-center",
        "top_heading": "",
        "icon_heading": "las la-gem",
        "top_subheading": "",
        "tophead_mb": 30,
        "layout_des": "2",
        "col_dk": "3",
        "col_tb": "2",
        "col_mb": "1",
        "space_item": "30",
        "space_item_mb": "30",
        "loop": true,
        "au_time": 0,
        "au_hover": true,
        "nav_btn": true,
        "btn_vi": "hover",
        "btn_owl": "outline",
        "btn_shape": "round",
        "btn_cl": "dark",
        "btn_size": "small",
        "btn_hidden_mobile": true,
        "nav_dot": false,
        "dot_owl": "default",
        "dots_cl": "dark",
        "dots_round": true,
        "dots_space": 10,
        "dots_hidden_mobile": false,
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": ",,70px,",
        "pd": "",
        "mg_mb": ",,40px,",
        "pd_mb": "",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    }
  },
  "order": [
    "heading",
    "main",
    "sidebar-page",
    "1652061282d05ea8a5",
    "1652061282d05ea8a6",
    "1652061697dd10b7fc",
    "1652060940154cd301"
  ]
}
