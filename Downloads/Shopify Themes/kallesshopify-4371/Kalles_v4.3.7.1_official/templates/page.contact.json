/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "heading": {
      "type": "heading-template",
      "blocks": {
        "d0d2c35c-3f4c-4081-91ad-0758affed801": {
          "type": "1",
          "settings": {
            "heading": "",
            "fontf": "inherit",
            "text_cl": "#fff",
            "text_fs": 20,
            "text_lh": 0,
            "text_fw": 500,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": true,
            "text_shadow": false,
            "text_mgb": 0,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 0,
            "text_ls_mb": 0,
            "text_mgb_mobile": 0
          }
        }
      },
      "block_order": [
        "d0d2c35c-3f4c-4081-91ad-0758affed801"
      ],
      "settings": {
        "image": "shopify:\/\/shop_images\/shop-category.jpg",
        "use_specify_image": true,
        "parallax": false,
        "color": "#000000",
        "overlay": 54,
        "padding": 50,
        "paddingmb": 50,
        "mg_b": 0,
        "mg_bmb": 0,
        "content_align": "t4s-text-center",
        "bg_pos": "center center",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "60px,,60px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": ""
      }
    },
    "1648884254eae8e0bd": {
      "type": "contact-form",
      "blocks": {
        "1648884254cd155bea-0": {
          "type": "bl_form",
          "settings": {
            "heading": "DROP US A LINE",
            "enable_phone": true,
            "enable_full_btn": true,
            "button_style": "outline",
            "btn_size": "medium",
            "btn_cl": "dark"
          }
        },
        "1648884254cd155bea-1": {
          "type": "bl_info",
          "settings": {
            "heading": "CONTACT INFORMATION",
            "text": "<p>We love to hear from you on our customer service, merchandise, website or any topics you want to share with us. Your comments and suggestions will be appreciated. Please complete the form below.<\/p><p><i class=\"las la-home fs__16\"><\/i> 184 Main Rd E, St Albans Victoria 3021, Australia<\/p><p><i class=\"las la-phone fs__16\"><\/i> 1800-123-222 \/ 1900-1570-230<\/p><p><i class=\"las la-envelope fs__16\"><\/i> <EMAIL><\/p><i class=\"las la-clock fs__16\"><\/i> Everyday 9:00-17:00"
          }
        }
      },
      "block_order": [
        "1648884254cd155bea-0",
        "1648884254cd155bea-1"
      ],
      "settings": {
        "enable_map": true,
        "map": "<iframe src=\"https:\/\/www.google.com\/maps\/embed?pb=!1m18!1m12!1m3!1d3154.8939060848147!2d144.81158271584684!3d-37.74563313792195!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6ad65fa6debeb781%3A0xe1d23f5d1759961e!2s184%20Main%20Rd%20E%2C%20St%20Albans%20VIC%203021%2C%20%C3%9Ac!5e0!3m2!1svi!2s!4v1618277125252!5m2!1svi!2s\" width=\"600\" height=\"450\" style=\"border:0;\" allowfullscreen=\"\" loading=\"lazy\"><\/iframe>",
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "50px,,50px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": "",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "sidebar-page": {
      "type": "sidebar-page",
      "disabled": true,
      "settings": {
        "enable_drawer": true,
        "space": 50,
        "size": "medium",
        "br_style": "none",
        "brcolor": "#222222",
        "sidebar_bdr": 5,
        "sidebar_pd": 10
      }
    }
  },
  "order": [
    "heading",
    "main",
    "1648884254eae8e0bd",
    "sidebar-page"
  ]
}
