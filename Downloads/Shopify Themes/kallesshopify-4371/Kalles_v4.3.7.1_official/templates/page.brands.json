/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "heading": {
      "type": "heading-template",
      "blocks": {
        "70d1bfdc-2a82-475d-a6ca-877254daaefa": {
          "type": "1",
          "settings": {
            "heading": "",
            "fontf": "inherit",
            "text_cl": "#ffffff",
            "text_fs": 20,
            "text_lh": 0,
            "text_fw": 500,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": true,
            "text_shadow": false,
            "text_mgb": 10,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 0,
            "text_ls_mb": 0,
            "text_mgb_mobile": 5
          }
        },
        "a72506d6-aaeb-4848-95a4-417fa5d0db1a": {
          "type": "4",
          "settings": {
            "design_des": "excerpt",
            "length": 10,
            "view_more": false,
            "viewm_txt": "View more",
            "fontf": "inherit",
            "text_cl": "#ffffff",
            "text_fs": 14,
            "text_lh": 0,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 0,
            "hidden_mobile": false,
            "text_fs_mb": 14,
            "text_lh_mb": 0,
            "text_ls_mb": 0,
            "text_mgb_mobile": 0
          }
        }
      },
      "block_order": [
        "70d1bfdc-2a82-475d-a6ca-877254daaefa",
        "a72506d6-aaeb-4848-95a4-417fa5d0db1a"
      ],
      "settings": {
        "image": "shopify:\/\/shop_images\/slide4_1950x_03e744aa-de55-4806-88a3-57cbc24d355d.jpg",
        "use_specify_image": true,
        "parallax": false,
        "color": "#000000",
        "overlay": 54,
        "padding": 50,
        "paddingmb": 50,
        "mg_b": 0,
        "mg_bmb": 0,
        "content_align": "t4s-text-center",
        "bg_pos": "center center",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "35px,,60px,",
        "pd": "",
        "mg_mb": ",,30px,",
        "pd_mb": ""
      }
    },
    "1648874226829fc9d5": {
      "type": "main-pagebrands",
      "settings": {
        "options": "linklist",
        "link_list": "t4_brands_page",
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "35px,,50px,",
        "pd": "",
        "mg_mb": "35px,,35px,",
        "pd_mb": ""
      }
    }
  },
  "order": [
    "heading",
    "main",
    "1648874226829fc9d5"
  ]
}
