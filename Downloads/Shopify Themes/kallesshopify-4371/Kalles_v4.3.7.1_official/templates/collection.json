/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "top": {
      "type": "top-collections",
      "blocks": {
        "69bbe0dd-0a50-42e8-986b-0d5e6c5cfa67": {
          "type": "cat",
          "settings": {
            "collection": "default-3",
            "icon": ""
          }
        },
        "863873b6-012e-4a34-90e3-eead750fa3ce": {
          "type": "cat",
          "disabled": true,
          "settings": {
            "collection": "bag",
            "icon": ""
          }
        },
        "fc07bff9-6d08-423c-8802-9a03c34f3a31": {
          "type": "cat",
          "settings": {
            "collection": "decor",
            "icon": ""
          }
        },
        "a286cc0b-0c90-4b1a-862c-a1136f283aac": {
          "type": "cat",
          "settings": {
            "collection": "denim",
            "icon": ""
          }
        },
        "23c84729-8fcb-488c-9541-00e283af6afe": {
          "type": "cat",
          "settings": {
            "collection": "dress",
            "icon": ""
          }
        },
        "5f1d751a-fdac-43d4-ad9d-55da0c297244": {
          "type": "cat",
          "settings": {
            "collection": "hats",
            "icon": ""
          }
        },
        "0f0e1f7c-788c-46c0-9fb4-1ec7ce88da01": {
          "type": "cat",
          "settings": {
            "collection": "frontpage",
            "icon": ""
          }
        },
        "70189b51-5562-4924-b642-a1deba7741ee": {
          "type": "cat",
          "settings": {
            "collection": "sale",
            "icon": ""
          }
        },
        "560f5822-3eeb-4070-a2cd-b5738881a4c1": {
          "type": "cat",
          "settings": {
            "collection": "shoes",
            "icon": ""
          }
        },
        "be9b8b8f-6cac-47ab-b809-a2fe640a75a7": {
          "type": "cat",
          "settings": {
            "collection": "women",
            "icon": ""
          }
        }
      },
      "block_order": [
        "69bbe0dd-0a50-42e8-986b-0d5e6c5cfa67",
        "863873b6-012e-4a34-90e3-eead750fa3ce",
        "fc07bff9-6d08-423c-8802-9a03c34f3a31",
        "a286cc0b-0c90-4b1a-862c-a1136f283aac",
        "23c84729-8fcb-488c-9541-00e283af6afe",
        "5f1d751a-fdac-43d4-ad9d-55da0c297244",
        "0f0e1f7c-788c-46c0-9fb4-1ec7ce88da01",
        "70189b51-5562-4924-b642-a1deba7741ee",
        "560f5822-3eeb-4070-a2cd-b5738881a4c1",
        "be9b8b8f-6cac-47ab-b809-a2fe640a75a7"
      ],
      "settings": {
        "show_top_collections": true,
        "source": "2",
        "menu": "main-menu",
        "sort_collections": "alphabetical",
        "box_border": "top",
        "content_align": "center",
        "hidden_mobile": false,
        "space_item": 30,
        "fontf": "inherit",
        "text_cl": "#222222",
        "text_cl_hover": "#56cfe1",
        "text_fs": 14,
        "text_lh": 45,
        "text_fw": 400,
        "text_ls": 0,
        "font_italic": false,
        "nav_btn": true,
        "layout": "t4s-container-fluid",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": "",
        "pd": "",
        "mg_tb": "",
        "pd_tb": "",
        "mg_mb": "",
        "pd_mb": ""
      }
    },
    "heading_template": {
      "type": "heading-template",
      "blocks": {
        "3b0cc506-49ea-4851-b5a6-ff7c2153b0e6": {
          "type": "1",
          "settings": {
            "heading": "",
            "fontf": "inherit",
            "text_cl": "#ffffff",
            "text_fs": 20,
            "text_lh": 20,
            "text_fw": 500,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 5,
            "hidden_mobile": false,
            "text_fs_mb": 20,
            "text_lh_mb": 20,
            "text_ls_mb": 0,
            "text_mgb_mobile": 5
          }
        },
        "68582c09-d394-43f0-b988-c2b38412941b": {
          "type": "4",
          "disabled": true,
          "settings": {
            "design_des": "excerpt",
            "length": 10,
            "view_more": false,
            "viewm_txt": "View more",
            "fontf": "inherit",
            "text_cl": "#ededed",
            "text_fs": 14,
            "text_lh": 24,
            "text_fw": 400,
            "text_ls": 0,
            "font_italic": false,
            "font_uppercase": false,
            "text_shadow": false,
            "text_mgb": 0,
            "hidden_mobile": false,
            "text_fs_mb": 14,
            "text_lh_mb": 20,
            "text_ls_mb": 0,
            "text_mgb_mobile": 0
          }
        },
        "ce2a8456-1d79-4523-8329-3afa244c8b98": {
          "type": "3",
          "settings": {
            "breadcrumb_color": "#f2f2f2",
            "brc_mgb": 5
          }
        }
      },
      "block_order": [
        "3b0cc506-49ea-4851-b5a6-ff7c2153b0e6",
        "68582c09-d394-43f0-b988-c2b38412941b",
        "ce2a8456-1d79-4523-8329-3afa244c8b98"
      ],
      "settings": {
        "heading_fullwidth": false,
        "image": "shopify:\/\/shop_images\/shop-banner.jpg",
        "use_specify_image": true,
        "parallax": false,
        "color": "#000000",
        "overlay": 54,
        "padding": 80,
        "paddingmb": 50,
        "mg_b": 0,
        "mg_bmb": 0,
        "content_align": "t4s-text-center",
        "bg_pos": "center center",
        "bg_repeat": "inherit",
        "bg_size": "cover",
        "use_cus_css": false,
        "code_cus_css": ".SectionID {\nbackground-color:red\n}"
      }
    },
    "main": {
      "type": "main-collection",
      "blocks": {
        "filter_id": {
          "type": "filter",
          "settings": {
            "style_filters": "sidebar"
          }
        },
        "740319a4-8e19-4c70-b8a9-ada7cf235cbc": {
          "type": "layout",
          "settings": {}
        },
        "30594d23-d644-4653-9545-9ff769d2e6ab": {
          "type": "sortby",
          "settings": {}
        }
      },
      "block_order": [
        "filter_id",
        "740319a4-8e19-4c70-b8a9-ada7cf235cbc",
        "30594d23-d644-4653-9545-9ff769d2e6ab"
      ],
      "settings": {
        "show_desc": false,
        "position_desc": "2",
        "product_des": "1",
        "show_vendor": false,
        "use_cdt": false,
        "image_ratio": "rationt",
        "image_size": "cover",
        "image_position": "8",
        "content_align": "default",
        "limit": 12,
        "enable_listing": true,
        "enable_listing_default": false,
        "col_dk": "4",
        "col_tb": "2",
        "col_mb": "2",
        "space_h_item": "30",
        "space_v_item": "30",
        "space_h_item_mb": "10",
        "space_v_item_mb": "10",
        "layout_des": "1",
        "use_pagination": "default",
        "enable_bar_lm": true,
        "btn_icon": false,
        "button_style": "outline",
        "btns_size": "large",
        "btns_cl": "primary",
        "button_effect": "sweep-to-right",
        "btn_pos": "t4s-text-center",
        "type_filters": "facets",
        "layout": "t4s-container-wrap",
        "cl_bg": "",
        "cl_bg_gradient": "",
        "mg": ",,50px,",
        "pd": "",
        "mg_tb": "",
        "pd_tb": "",
        "mg_mb": ",,30px,",
        "pd_mb": ""
      }
    },
    "sidebar": {
      "type": "sidebar-collection",
      "blocks": {
        "c7bdf308-8019-4991-b58c-6d3e205a52eb": {
          "type": "category",
          "settings": {
            "heading": "Product Categories",
            "cat_source": "1",
            "cat_link_list": "",
            "count": true
          }
        },
        "9d37e80a-1eda-4a65-bb08-199e6a933a16": {
          "type": "collection",
          "settings": {
            "heading": "Sale Products",
            "collection": "frontpage",
            "limit_pr": 3,
            "image_ratio": "t4s_ratioadapt",
            "image_size": "t4s_cover",
            "image_position": "t4s_position_8"
          }
        },
        "c090f36e-d6ef-4a2a-ab04-cc78211dadb7": {
          "type": "image",
          "settings": {
            "heading": "",
            "img_effect": "zoom",
            "b_link": "",
            "open_link": "_self",
            "content_align": "center",
            "date": "2022\/12\/26",
            "cdt_des": "1",
            "countdown_pos": "2",
            "cdt_size": "small",
            "box_bdr": 0,
            "bd_width": 0,
            "space_item": 10,
            "number_cl": "#ffffff",
            "text_cl": "#222222",
            "border_cl": "#000000",
            "bg_cl": "#000000"
          }
        }
      },
      "block_order": [
        "c7bdf308-8019-4991-b58c-6d3e205a52eb",
        "9d37e80a-1eda-4a65-bb08-199e6a933a16",
        "c090f36e-d6ef-4a2a-ab04-cc78211dadb7"
      ],
      "disabled": true,
      "settings": {
        "enable_drawer": false,
        "space": 50,
        "size": "medium",
        "br_style": "none",
        "brcolor": "#222222",
        "sidebar_bdr": 0,
        "sidebar_pd": 15
      }
    }
  },
  "order": [
    "top",
    "heading_template",
    "main",
    "sidebar"
  ]
}